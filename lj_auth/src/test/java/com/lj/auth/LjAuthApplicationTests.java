//package com.lj.auth;
//
//import java.time.Duration;
//import java.time.Instant;
//import java.util.ArrayList;
//import java.util.Date;
//import java.util.List;
//import java.util.concurrent.ExecutionException;
//import java.util.concurrent.ExecutorService;
//import java.util.concurrent.Executors;
//import java.util.concurrent.Future;
//
//import javax.annotation.Resource;
//
//import org.junit.jupiter.api.Test;
//import org.springframework.boot.test.context.SpringBootTest;
//
//import com.baomidou.mybatisplus.core.toolkit.Wrappers;
//import com.lj.auth.domain.Test1;
//import com.lj.auth.exception.ResponseEnum;
//import com.lj.auth.mapper.Test1Mapper;
//import com.lj.auth.task.CheckTransactionState;
//
//import cn.hutool.core.lang.Assert;
//import lombok.extern.slf4j.Slf4j;
//
//@SpringBootTest
//@Slf4j
//class LjAuthApplicationTests {
//    @Resource
//    private CheckTransactionState checkTransactionState;
//    @Resource
//    private Test1Mapper test1Mapper;
//
//    @Test
//    void contextLoads() {
//        System.out.println("111111111");
//        // 补发能量签到
//        // checkTransactionState.checkSignHistState1();
//        // 资产校验
//        checkTransactionState.checkChainAssets();
//    }
//
//    @Test
//    void contextLoads1() {
//        System.out.println("111111111");
//        int n = 100000;
//        for (int i = 0; i < n; i++) {
//            log.info("循环次数： " + (i + 1));
//            Test1 test1 = new Test1();
//            test1.setCreateTime(new Date());
//            test1Mapper.insert(test1);
//            addInviteCode1(test1);
//        }
//    }
//
//
//
//    /**
//     * 添加用户邀请码
//     *
//     * @param test1
//     * <AUTHOR>
//     * @date 2024/05/27
//     */
//    public void addInviteCode1(Test1 test1) {
//        Assert.notNull(test1, ResponseEnum.UserDoesNotExist.getMsg());
//        Long id = test1.getId();
//        // 补零操作
//        String idSort = String.valueOf(id);
//        // 判断是否需要补零
//        String invicode = String.format("%06d", Long.parseLong(idSort));
//        invicode = checkInvicode(invicode);
//
//        log.info("插入邀请码" + invicode);
//        log.info("插入邀请码" + invicode);
//        log.info("插入邀请码" + invicode);
//
//
//
//        // 更新邀请码
//        test1Mapper.update(null,
//            Wrappers.<Test1>lambdaUpdate().eq(Test1::getId, id).set(Test1::getInviteCode, invicode));
//    }
//
//    private String checkInvicode(String invicode) {
//        // 判断验证码和旧数据是否会重复
//        Test1 parentAccount =
//            test1Mapper.selectOne(Wrappers.<Test1>lambdaQuery().eq(Test1::getInviteCode, invicode));
//        if (parentAccount != null) {
//            log.error("重复邀请码" + invicode);
//            log.error("重复邀请码" + invicode);
//            log.error("重复邀请码" + invicode);
//            Long id1 = parentAccount.getId();
//            // 补零操作
//            String idSort1 = String.valueOf(id1);
//            // 判断是否需要补零
//            invicode = String.format("%06d", Long.parseLong(idSort1));
//           return checkInvicode(invicode);
//        }else{
//            return invicode;
//        }
//
//    }
//
//}
