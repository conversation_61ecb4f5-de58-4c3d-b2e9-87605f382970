package com.lj.auth.sol;

import java.math.BigInteger;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import org.redisson.MapWriterTask;
import org.web3j.abi.TypeReference;
import org.web3j.abi.datatypes.Function;
import org.web3j.abi.datatypes.Type;
import org.web3j.abi.datatypes.Utf8String;
import org.web3j.crypto.Credentials;
import org.web3j.protocol.Web3j;
import org.web3j.protocol.core.RemoteCall;
import org.web3j.protocol.core.methods.response.TransactionReceipt;
import org.web3j.tx.Contract;
import org.web3j.tx.TransactionManager;
import org.web3j.tx.gas.ContractGasProvider;

/**
 * <p>
 * Auto generated code.
 * <p>
 * <strong>Do not modify!</strong>
 * <p>
 * Please use the <a href="https://docs.web3j.io/command_line.html">web3j command line tools</a>, or the
 * org.web3j.codegen.SolidityFunctionWrapperGenerator in the
 * <a href="https://github.com/web3j/web3j/tree/master/codegen">codegen module</a> to update.
 *
 * <p>
 * Generated with web3j version 3.6.0.
 */
public class DidRealName extends Contract {
    private static final String BINARY =
        "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";

    public static final String FUNC_ADDDID = "addDid";

    public static final String FUNC_GETSIGNEDVALUE = "getSignedValue";

    @Deprecated
    protected DidRealName(String contractAddress, Web3j web3j, Credentials credentials, BigInteger gasPrice,
        BigInteger gasLimit) {
        super(BINARY, contractAddress, web3j, credentials, gasPrice, gasLimit);
    }

    protected DidRealName(String contractAddress, Web3j web3j, Credentials credentials,
        ContractGasProvider contractGasProvider) {
        super(BINARY, contractAddress, web3j, credentials, contractGasProvider);
    }

    @Deprecated
    protected DidRealName(String contractAddress, Web3j web3j, TransactionManager transactionManager,
        BigInteger gasPrice, BigInteger gasLimit) {
        super(BINARY, contractAddress, web3j, transactionManager, gasPrice, gasLimit);
    }

    protected DidRealName(String contractAddress, Web3j web3j, TransactionManager transactionManager,
        ContractGasProvider contractGasProvider) {
        super(BINARY, contractAddress, web3j, transactionManager, contractGasProvider);
    }

    public static RemoteCall<DidRealName> deploy(Web3j web3j, Credentials credentials,
        ContractGasProvider contractGasProvider) {
        return deployRemoteCall(DidRealName.class, web3j, credentials, contractGasProvider, BINARY, "");
    }

    public static RemoteCall<DidRealName> deploy(Web3j web3j, TransactionManager transactionManager,
        ContractGasProvider contractGasProvider) {
        return deployRemoteCall(DidRealName.class, web3j, transactionManager, contractGasProvider, BINARY,
            "");
    }

    @Deprecated
    public static RemoteCall<DidRealName> deploy(Web3j web3j, Credentials credentials, BigInteger gasPrice,
        BigInteger gasLimit) {
        return deployRemoteCall(DidRealName.class, web3j, credentials, gasPrice, gasLimit, BINARY, "");
    }

    @Deprecated
    public static RemoteCall<DidRealName> deploy(Web3j web3j, TransactionManager transactionManager,
        BigInteger gasPrice, BigInteger gasLimit) {
        return deployRemoteCall(DidRealName.class, web3j, transactionManager, gasPrice, gasLimit, BINARY, "");
    }

    public RemoteCall<TransactionReceipt> addDid(String did, String nameOwner, String signedNameValue) {
        final Function function = new Function(FUNC_ADDDID,
            Arrays.<Type>asList(new org.web3j.abi.datatypes.Utf8String(did),
                new org.web3j.abi.datatypes.Address(nameOwner),
                new org.web3j.abi.datatypes.Utf8String(signedNameValue)),
            Collections.<TypeReference<?>>emptyList());
        return executeRemoteCallTransaction(function);
    }

    public RemoteCall<TransactionReceipt> getSignedValue(String did) {
        final Function function = new Function(FUNC_GETSIGNEDVALUE,
            Arrays.<Type>asList(new org.web3j.abi.datatypes.Utf8String(did)),
            Collections.<TypeReference<?>>emptyList());
        return executeRemoteCallTransaction(function);
    }

    public RemoteCall<Type> getSignedValue1(String did) {
        List<TypeReference<?>> typeReferences = Collections.singletonList(new TypeReference<Utf8String>() {});
        final Function function = new Function(
                FUNC_GETSIGNEDVALUE,
                Arrays.asList(new Utf8String(did)),
                typeReferences
        );
        return executeRemoteCallSingleValueReturn(function);
    }
    @Deprecated
    public static DidRealName load(String contractAddress, Web3j web3j, Credentials credentials,
        BigInteger gasPrice, BigInteger gasLimit) {
        return new DidRealName(contractAddress, web3j, credentials, gasPrice, gasLimit);
    }

    @Deprecated
    public static DidRealName load(String contractAddress, Web3j web3j, TransactionManager transactionManager,
        BigInteger gasPrice, BigInteger gasLimit) {
        return new DidRealName(contractAddress, web3j, transactionManager, gasPrice, gasLimit);
    }

    public static DidRealName load(String contractAddress, Web3j web3j, Credentials credentials,
        ContractGasProvider contractGasProvider) {
        return new DidRealName(contractAddress, web3j, credentials, contractGasProvider);
    }

    public static DidRealName load(String contractAddress, Web3j web3j, TransactionManager transactionManager,
        ContractGasProvider contractGasProvider) {
        return new DidRealName(contractAddress, web3j, transactionManager, contractGasProvider);
    }
}
