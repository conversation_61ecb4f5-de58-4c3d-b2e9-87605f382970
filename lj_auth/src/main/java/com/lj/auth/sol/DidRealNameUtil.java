package com.lj.auth.sol;

import com.lj.auth.util.PropertiesRead;
import com.lj.auth.util.SM2Util;
import org.web3j.abi.FunctionEncoder;
import org.web3j.abi.FunctionReturnDecoder;
import org.web3j.abi.TypeReference;
import org.web3j.abi.datatypes.Address;
import org.web3j.abi.datatypes.Function;
import org.web3j.abi.datatypes.Type;
import org.web3j.abi.datatypes.Utf8String;
import org.web3j.abi.datatypes.generated.Uint256;
import org.web3j.crypto.Credentials;
import org.web3j.protocol.Web3j;
import org.web3j.protocol.core.DefaultBlockParameter;
import org.web3j.protocol.core.DefaultBlockParameterName;
import org.web3j.protocol.core.RemoteCall;
import org.web3j.protocol.core.methods.request.Transaction;
import org.web3j.protocol.core.methods.response.EthCall;
import org.web3j.protocol.core.methods.response.TransactionReceipt;
import org.web3j.protocol.http.HttpService;
import org.web3j.tx.gas.DefaultGasProvider;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * 描述：实名DID合约调用工具方法 创建人: CFJ 创建时间: 2024/07/16
 */
public class DidRealNameUtil {

    private static long chainId;

    private static Web3j web3j;

    static {
        chainId = Long.parseLong(PropertiesRead.getYmlStringForActive("ylChainId"));
        web3j = Web3j.build(new HttpService(PropertiesRead.getYmlStringForActive("ylznlCurl")));
    }

    public static void main(String[] args) throws Exception {
        String getPriKey = "96d792a1eb39d677fca0993000bf8ec8b7b4466bcb490aeb5a696e0a132039b2";
        String did="did:ctid:bsn:A1D9FCB4DE7DB385D5C084EA7C46B28B408B1BF6B3A400E028AD1FD887096288";
        String getPubKey = "0426751e00139e4623f39b9c01fb5d023bed50567e205fd2cfb2ee7406e02ec5ef6f763b1953f0dfc319969b5066059fbea650886e35bba8dbcf4a59d5ecabe777";
        String hexEncrypt1 = SM2Util.hexEncrypt(getPubKey, "姓名：徐卓");
        System.out.println("加密数据: "+hexEncrypt1);

        // 部署合约地址
        String address = "0x7d03671464bd363528cb58a04f6a993dd90f8b7d";
        // 部署合约私钥
        String privateKey = "0f63149b944a842b74a5b661525b6439c7c4d50b72bf5d251d5452b05bd80884";
        // 合约地址
        String DidRealNameContractAddress = "0x3e0ab5fcc5bd41bdf57cb492e203e9a6996ecf51";
        // 部署合约hash
        String hash = "0xafb2a985401a2e602c1e169e49c71d821096ef35a44828ab3b936245e3611df4";
        // 信息凭证
        Credentials credentials = Credentials.create(privateKey);

        // String s = deployContract(credentials);
        // System.out.println(s);

        // 加载合约
        DidRealName didRealName =
            DidRealName.load(DidRealNameContractAddress, web3j, credentials, new DefaultGasProvider());
//        TransactionReceipt send = didRealName.addDid(did, address, hexEncrypt1).send();
//        String transactionHash = send.getTransactionHash();
//        System.out.println(transactionHash);
//        RemoteCall<TransactionReceipt> signedValue = didRealName.getSignedValue(did);
//        TransactionReceipt send = signedValue.send();
//        String transactionHash = send.getTransactionHash();
//        System.out.println(transactionHash);
        RemoteCall<Type> signedValue1 = didRealName.getSignedValue1(did);
        Type send = signedValue1.send();
        System.out.println(send.toString());

        String signedValue = getSignedValue(did,address,DidRealNameContractAddress);
        System.out.println(signedValue);

        String s = send.toString();
        //私钥解密
        String decrypt = SM2Util.hexDecrypt(getPriKey, s);
        System.out.println("解密数据："+decrypt);

    }

    /**
     * 部署合约
     * 
     * @param credentials 信息凭证
     * @return {@link String } 合约地址
     * <AUTHOR>
     * @date 2024/07/16
     */
    private static String deployContract(Credentials credentials) throws Exception {
        // 部署合约
        DefaultGasProvider defaultGasProvider = new DefaultGasProvider();
        RemoteCall<DidRealName> deploy = DidRealName.deploy(web3j, credentials, defaultGasProvider);
        DidRealName send = deploy.send();
        String contractAddress = send.getContractAddress();
        return contractAddress;
    }


    public static String getSignedValue(String  did, String chainAccountAddress, String contractAddress)
            throws IOException {
        String functionName = "getSignedValue";
        List<Type> inputParams = new ArrayList<>();
        List<TypeReference<?>> outputParams = new ArrayList<>();
        inputParams.add(new Utf8String(did));
        outputParams.add(new TypeReference<Utf8String>() {});
        Function function = new Function(functionName, inputParams, outputParams);
        EthCall ethCall = callContract(function, chainAccountAddress, contractAddress);
        String value = ethCall.getValue();
        List<Type> decode = FunctionReturnDecoder.decode(value, function.getOutputParameters());
        return decode.get(0).getValue().toString();
    }

    private static EthCall callContract(Function function, String chainAccountAddress, String contractAddress)
            throws IOException {
        String encode = FunctionEncoder.encode(function);
        Transaction ethCallTransaction =
                Transaction.createEthCallTransaction(chainAccountAddress, contractAddress, encode);
        return web3j.ethCall(ethCallTransaction, DefaultBlockParameterName.LATEST).send();
    }

}
