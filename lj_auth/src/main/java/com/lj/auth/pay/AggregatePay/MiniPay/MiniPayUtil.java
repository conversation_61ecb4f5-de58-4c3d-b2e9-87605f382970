package com.lj.auth.pay.AggregatePay.MiniPay;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.math.BigInteger;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lj.auth.domain.WeixinpayConfig;
import com.lj.auth.mapper.WeixinpayConfigMapper;
import com.lj.auth.pay.AggregatePay.AppPay.CommonParameters;
import com.lj.auth.pay.AggregatePay.H5Pay.H5PayUtil;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.digest.DigestUtils;

import com.ejlchina.okhttps.HTTP;
import com.ejlchina.okhttps.HttpResult;
import com.ejlchina.okhttps.fastjson.FastjsonMsgConvertor;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.JSONObject;

import lombok.extern.slf4j.Slf4j;

/**
 * @Author: zjd
 * @Description:
 * @Date: 2023/10/20 17:42
 */
@Slf4j
@Component
public class MiniPayUtil {

    @Resource
    private RestTemplate restTemplate;
    @Resource
    private WeixinpayConfigMapper weixinpayConfigMapper;

    private static WeixinpayConfig weixinpayConfig;

    @PostConstruct
    public void init() {
        log.info("微信小程序支付配置初始化开始------");
        weixinpayConfig = weixinpayConfigMapper.selectOne(
            Wrappers.<WeixinpayConfig>lambdaQuery().eq(WeixinpayConfig::getOperaterUuid, "YL88888888"));
        log.info("微信小程序支付配置初始化完成------");
    }

    /**
     * 微信下单
     * 
     * @param merOrderId
     * @param createTime
     * @param jsCode
     * @return
     * @throws Exception
     */
    public String unifiedOrder(String merOrderId, Date createTime, BigInteger totalAmount, String jsCode)
        throws Exception {
        // 1. 组建请求报文
        log.info("组建请求报文");
        JSONObject requestBody = new JSONObject();
        requestBody.put("requestTimestamp", CommonParameters.sdf1.format(createTime));
        requestBody.put("merOrderId", merOrderId);
        requestBody.put("mid", CommonParameters.mid);
        requestBody.put("tid", CommonParameters.tid);
        requestBody.put("instMid", CommonParameters.miniDefault);
        requestBody.put("totalAmount", totalAmount);
        requestBody.put("tradeType", "MINI");
        requestBody.put("subAppId", weixinpayConfig.getAppid());
        requestBody.put("notifyUrl", weixinpayConfig.getNotifyDomian());

        // 获取openId
        HTTP http = HTTP.builder().addMsgConvertor(new FastjsonMsgConvertor()).build();
        HttpResult httpResult =
            http.sync(weixinpayConfig.getOpenidUrl()).addPathPara("appid", weixinpayConfig.getAppid())
                .addPathPara("secret", weixinpayConfig.getSecret()).addPathPara("js_code", jsCode).get();
        HttpResult.Body body = httpResult.getBody();
        Map map = JSON.parseObject(body.toString(), Map.class);

        String openId = map.get(("openid")).toString();

        // String openId = "oso-Z6xFs7WOyySiXnt8lHGyOx90";

        log.info("openId获取成功，openId为{}", openId);
        requestBody.put("subOpenId", openId);
        String timestamp = CommonParameters.sdf3.format(createTime);
        String nonce = H5PayUtil.getNonce();
        // 2. 获取认证报文，timestamp为当前日期，老旧日期无法请求成功
        String authorization = getAuthorization(CommonParameters.appid, CommonParameters.appkey, timestamp,
            nonce, requestBody.toString());

        // String authorization = getAuthorization(CommonParameters.wechatAppId, CommonParameters.appkey, timestamp,
        // nonce, requestBody.toString());
        // 3. 调用天满微信下单接口
        HttpHeaders headers = new HttpHeaders();
        headers.add("authorization", authorization);
        HttpEntity<?> httpEntity = new HttpEntity<>(requestBody.toString(), headers);
        ResponseEntity<JSONObject> unifiedResponseEntity = restTemplate
            .exchange(CommonParameters.unifiedOrderUrl, HttpMethod.POST, httpEntity, JSONObject.class);
        log.info("天满微信预下单接口响应值：{}", unifiedResponseEntity.getBody());
        String s = unifiedResponseEntity.getBody().toString();
        return s;
    }

    /**
     * 获取签名头
     * 
     * @param appid
     * @param appkey
     * @param timestamp 格式:"yyyyMMddHHmmss"
     * @param nonce 随机字符串，
     * @param body 请求体
     * @return authorization 认证报文
     * @throws Exception
     */
    static String getAuthorization(String appid, String appkey, String timestamp, String nonce, String body)
        throws Exception {
        byte[] data = body.getBytes("utf-8");
        InputStream is = new ByteArrayInputStream(data);
        String testSH = DigestUtils.sha256Hex(is);
        String s1 = appid + timestamp + nonce + testSH;
        Mac mac = Mac.getInstance("HmacSHA256");
        mac.init(new SecretKeySpec(appkey.getBytes("utf-8"), "HmacSHA256"));
        byte[] localSignature = mac.doFinal(s1.getBytes("utf-8"));
        String localSignatureStr = Base64.encodeBase64String(localSignature);
        return "OPEN-BODY-SIG AppId=" + "\"" + appid + "\"" + ", Timestamp=" + "\"" + timestamp + "\""
            + ", Nonce=" + "\"" + nonce + "\"" + ", Signature=" + "\"" + localSignatureStr + "\"";
    }

    public String getLinkUrl(String accessToken, String merOrderId, String operateUuid, Integer paymentType) {
        String url = "https://api.weixin.qq.com/wxa/generatescheme?access_token=" + accessToken;
        com.alibaba.fastjson.JSONObject bodyParam = new com.alibaba.fastjson.JSONObject();
        com.alibaba.fastjson.JSONObject childParam = new com.alibaba.fastjson.JSONObject();
        childParam.put("path", "/pages_cashier/cashier_desk/cashier_desk");
        childParam.put("query",
            "merOrderId=" + merOrderId + "&operateUuid=" + operateUuid + "&paymentType=" + paymentType);
        childParam.put("env_version", "release");
        bodyParam.put("jump_wxa", childParam);
        bodyParam.put("is_expire", true);
        bodyParam.put("expire_type", 1);
        bodyParam.put("expire_interval", 1);
        HttpHeaders headers = new HttpHeaders();
        HttpEntity<?> httpEntity = new HttpEntity<>(bodyParam.toJSONString(), headers);
        ResponseEntity<com.alibaba.fastjson.JSONObject> exchange =
            restTemplate.exchange(url, HttpMethod.POST, httpEntity, com.alibaba.fastjson.JSONObject.class);
        com.alibaba.fastjson.JSONObject body = exchange.getBody();
        return (String)body.get("openlink");
    }

    public String getWeixinLink(String merOrderId, String operateUUID) {
        // 获取微信accessToken
        String url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid="
            + CommonParameters.wechatAppId + "&secret=" + CommonParameters.secret;
        ResponseEntity<JSONObject> exchange =
            restTemplate.exchange(url, HttpMethod.GET, null, JSONObject.class);
        String accessToken = (String)exchange.getBody().get("access_token");
        // 获取LinkUrl
        String linkUrl = getLinkUrl(accessToken, merOrderId, operateUUID, 0);
        return linkUrl;
    }

    /**
     * 得到开放id
     *
     * @param js_code js代码
     * @return {@link String}
     */
    public HttpResult.Body getOpenId(String js_code) {
        HttpResult result = null;
        try {
            String url = weixinpayConfig.getOpenidUrl();
            HTTP http = HTTP.builder().addMsgConvertor(new FastjsonMsgConvertor()).build();
            result = http.sync(url).addPathPara("appid", weixinpayConfig.getAppid())
                .addPathPara("secret", weixinpayConfig.getSecret()).addPathPara("js_code", js_code).get();

        } catch (Exception e) {
            log.error(e.getMessage());
        }
        HttpResult.Body body = result.getBody();
        return body;
    }

    /**
     * 游客模式获取APPID
     *
     * @param js_code
     * @return {@link Body }
     * <AUTHOR>
     * @date 2024/09/07
     */
    public HttpResult.Body getOpenIdTourist(String  appId,String js_code) {
        String secret=CommonParameters.secret;
        if(appId.equals(CommonParameters.appId)){
            secret=CommonParameters.DID_secret;
        }
        HttpResult result = null;
        try {
            String url = weixinpayConfig.getOpenidUrl();
            HTTP http = HTTP.builder().addMsgConvertor(new FastjsonMsgConvertor()).build();
            result = http.sync(url).addPathPara("appid", appId)
                .addPathPara("secret",secret).addPathPara("js_code", js_code).get();

        } catch (Exception e) {
            log.error(e.getMessage());
        }
        HttpResult.Body body = result.getBody();
        return body;
    }
}
