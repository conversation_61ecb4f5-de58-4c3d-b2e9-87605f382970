package com.lj.auth.pay.AggregatePay.AppPay;

import java.text.SimpleDateFormat;

/**
 * @author: wxm
 * @description:
 * @date: 2023/6/30 17:29
 */
public class CommonParameters {

    public static SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    public static SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd");
    public static SimpleDateFormat sdf3 = new SimpleDateFormat("yyyyMMddHHmmss");
    public static SimpleDateFormat sdf4 = new SimpleDateFormat("yyyyMMddHHmmssSSS");

    //======界外
     public static String appid = "8a81c1bd89b6cadb018e4f62757d1c37";
     public static String appkey = "059bf0aee6f542fdac89ecba00dbd47f";
     public static String sourceCode = "384G";//
     public static String mid = "89842017372UURY";//商户号
//     public static String mid = "89842017372UUTQ";//商户号
     public static String tid = "TYV4A8LH";//终端号


//    public static String appid = "8a81c1be831e62880188d682811b546a";
//    public static String appkey = "15fa484f7f7a4477ab8eadede001c8a4";
//    public static String sourceCode = "35H9";//
//    public static String mid = "89842017372TUWG";//商户号
//    public static String tid = "4BV68DXE";//终端号

    // =====测试参数
//    public static String appid = "10037e6f6a4e6da4016a67b9789c0013";
//    public static String appkey = "ac2e1c592cee4d1ba0913864dd7d0dda";
//    public static String sourceCode = "1017";//
//    public static String mid = "898201612345678";// 商户号
//    public static String tid = "88880001";// 终端号

    // --------------------H5 parameter-------------------------
    public static String instMid = "H5DEFAULT";// 业务类型 H5DEFAULT
    public static String url = "https://api-mop.chinaums.com/v1/netpay/trade/h5-pay";
    public static String returnUrl = "";
    public static String sceneType = "AND_WAP";
    public static String wechatUrl = "https://api-mop.chinaums.com/v1/netpay/wxpay/h5-pay";
    public static String closeUrl = "https://api-mop.chinaums.com/v1/netpay/close";
    public static String queryUrl = "https://api-mop.chinaums.com/v1/netpay/query";
    public static String notifyUrl = "http://dns.ylzh.pro/portal/aggregatePay/h5Notify";

    // --------------------C 扫 B parameter----------------------
    public static String getQrCodeUrl = "https://api-mop.chinaums.com/v1/netpay/bills/get-qrcode";
    public static String updateQrCodeUrl = "https://api-mop.chinaums.com/v1/netpay/bills/update-qrcode";
    public static String pcCloseQrCodeUrl = "https://api-mop.chinaums.com/v1/netpay/bills/close-qrcode";
    public static String pcQueryUrl = "https://api-mop.chinaums.com/v1/netpay/bills/query";
    public static String queryQrCodeInfoUrl =
        "https://api-mop.chinaums.com/v1/netpay/bills/query-qrcode-info";
    public static String pcNotifyUrl = "http://dns.ylzh.pro/portal/aggregatePay/pcNotify";

    // -------------------App parameter-------------------------
    public static String subAppId = "wxbf7415568493c5f8";
    public static String appInstMid = "APPDEFAULT";
    public static String appTradeType = "APP";
    public static String appWechatPreOrder = "https://api-mop.chinaums.com/v1/netpay/wx/app-pre-order";
     public static String appAliPreOrder = "https://api-mop.chinaums.com/v1/netpay/trade/precreate";
//    public static String appAliPreOrder = "https://test-api-mop.chinaums.com/v1/netpay/trade/precreate";
    public static String appCloseUrl = "https://api-mop.chinaums.com/v1/netpay/close";
    public static String appQueryUrl = "https://api-mop.chinaums.com/v1/netpay/query";
    // public static String appNotifyUrl = "http://dns.ylzh.pro/portal/aggregatePay/appNotify";
    public static String appNotifyUrl = "https://mysticring.jiewai.pro/lj-auth/aggregatePay/appNotify";
    public static String appNotifyUrlGX = "https://mysticring.jiewai.pro/lj-auth/contribution/appNotify";


    // -------------------MimiPay 小程序支付----------------------
//    public static String wechatAppId = "wxbac48c7613d15906";
//        public static String secret = "28382ecea4697023e060eae6ad7a8999";


    //界外
    public static String wechatAppId = "wxc4eaae9cddf1de0d";
    //界外
    public static String secret = "cc47d73579c6e3c7a38a0ac6ad03bd72";
    public static String wxOpenIdUrl =
        "https://api.weixin.qq.com/sns/jscode2session?appid={appid}&secret={secret}&js_code={js_code}&grant_type=authorization_code";
    public static String miniDefault = "MINIDEFAULT";
    public static String merchantId = "1646168607";
    public static String privateKeyPath = "apiclient_key.pem";
    public static String merchantSerialNumber = "38CD449A31530D58D78713F20833553D8CB395BE";
    public static String apiV3Key = "aaaa1111bbbb2222cccc3333dddd4444";
    public static String unifiedOrderUrl = "https://api-mop.chinaums.com/v1/netpay/wx/unified-order";
    public static String miniPayCloseUrl = "https://api-mop.chinaums.com/v1/netpay/close";
    public static String miniPayQueryUrl = "https://api-mop.chinaums.com/v1/netpay/query";
    public static String MiniPayNotifyUrl = "https://551043k3y2.vicp.fun/lj-auth/aggregatePay/appNotify";
    public static String miniRefundUrl = "https://api-mop.chinaums.com/v1/netpay/refund";
    public static String miniRefundQueryUrl = "https://api-mop.chinaums.com/v1/netpay/refund-query";


    // 实名DID小程序
    public static String appId = "wx476711f5cb1b717b";
    public static String DID_secret = "8a2b895d918fdbf7853a9013203c0bb4";



}
