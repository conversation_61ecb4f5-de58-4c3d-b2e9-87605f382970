package com.lj.auth.pay.AggregatePay.H5Pay;

import java.io.*;
import java.math.BigInteger;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLEncoder;
import java.util.Date;
import java.util.List;
import java.util.UUID;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;

import com.lj.auth.pay.AggregatePay.AppPay.CommonParameters;
import com.lj.auth.util.UUIdUtil;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.digest.DigestUtils;




/**
 * @author: wxm
 * @description:
 * @date: 2023/6/30 11:28
 */
public class H5PayUtil {

    public static void main(String[] args) throws Exception {
        Date createTime = new Date();
//        String merOrderId = generateMerOrderId(createTime);
        String merOrderId = "35H920230704154525058dc519be";
        System.out.println("merOrderId:"+merOrderId);
        BigInteger totalAmount = new BigInteger("1");
//        createH5WechatPay(merOrderId, totalAmount, createTime,"");
        createH5AliPay(merOrderId,totalAmount,createTime,"");
    }

    /**
     * 生成商户订单号
     *
     * @return
     */
    public static String generateMerOrderId(Date createTime) {
        String uuId = UUIdUtil.createUUId(7);
        return CommonParameters.sourceCode + CommonParameters.sdf4.format(createTime)+uuId;
    }

    /**
     * 获取nonce随机数
     * @return
     */
    public static String getNonce() {
        return "YL" + UUID.randomUUID().toString().replaceAll("-", "");
    }

    /**
     * h5支付
     *
     * @param totalAmount 订单总金额
     * @return
     * @throws Exception
     */
    public static String createH5AliPay(String merOrderId, BigInteger totalAmount, Date createTime,String returnUrl) throws Exception {
        //1. 组建请求报文
        H5PayBody reqBody = new H5PayBody();
        reqBody.requestTimestamp = CommonParameters.sdf1.format(createTime);
        reqBody.merOrderId = merOrderId;
        reqBody.mid = CommonParameters.mid;
        reqBody.tid = CommonParameters.tid;
        reqBody.instMid = CommonParameters.instMid;
        reqBody.totalAmount = totalAmount.toString();
        reqBody.notifyUrl = CommonParameters.notifyUrl;
        reqBody.returnUrl = returnUrl;
        String timestamp = CommonParameters.sdf3.format(createTime);
        //2. 获取认证报文
        String authorization = "OPEN-FORM-PARAM";
        String nonce = getNonce();
        String signature = getSignature(CommonParameters.appid, CommonParameters.appkey, timestamp, nonce, reqBody.toString());

        String URL = CommonParameters.url + "?" + "authorization=" + authorization + "&appId=" + CommonParameters.appid
                + "&timestamp=" + timestamp + "&nonce=" + nonce + "&content=" + URLEncoder.encode(reqBody.toString(), "UTF-8")
                + "&signature=" + URLEncoder.encode(signature, "UTF-8");
        System.out.println("URL:" + URL);
        return URL;
    }


    public static String createH5WechatPay(String merOrderId, BigInteger totalAmount, Date createTime,String returnUrl) throws Exception {
        //1. 组建请求报文
        H5PayBody reqBody = new H5PayBody();
        reqBody.requestTimestamp = CommonParameters.sdf1.format(createTime);
        reqBody.merOrderId = merOrderId;
        reqBody.mid = CommonParameters.mid;
        reqBody.tid = CommonParameters.tid;
        reqBody.instMid = CommonParameters.instMid;
        reqBody.totalAmount = totalAmount.toString();
        reqBody.notifyUrl = CommonParameters.notifyUrl;
        reqBody.returnUrl = returnUrl;
        System.out.println("request body:\n" + reqBody);
        String timestamp = CommonParameters.sdf3.format(createTime);
        //2. 获取认证报文
        String authorization = "OPEN-FORM-PARAM";
        System.out.println("authorization:\n" + authorization);
        String nonce = "YL" + UUID.randomUUID().toString().replaceAll("-", "");
        String signature = getSignature(CommonParameters.appid, CommonParameters.appkey, timestamp, nonce, reqBody.toString());

        String URL = CommonParameters.wechatUrl + "?" + "authorization=" + authorization + "&appId=" + CommonParameters.appid
                + "&timestamp=" + timestamp + "&nonce=" + nonce + "&content=" + URLEncoder.encode(reqBody.toString(), "UTF-8")
                + "&signature=" + URLEncoder.encode(signature, "UTF-8");
        System.out.println("URL:" + URL);
        return URL;
    }

    /**
     * 发送httpGET请求
     *
     * @param url 请求url
     * @return response
     */
    static String request(String url) {
        StringBuffer sbf = new StringBuffer();
        String response = "";
        PrintWriter out = null;
        BufferedReader in = null;
        try {
            URL realUrl = new URL(url);
            URLConnection conn = realUrl.openConnection();
            HttpURLConnection httpUrlConnection = (HttpURLConnection) conn;
            httpUrlConnection.setRequestMethod("GET");
            httpUrlConnection.setReadTimeout(15000);
            httpUrlConnection.setReadTimeout(60000);
            httpUrlConnection.connect();
            in = new BufferedReader(new InputStreamReader(httpUrlConnection.getInputStream(), "UTF-8"));
            String line;
            while ((line = in.readLine()) != null) {
                sbf.append(line);
                sbf.append("\r\n");
            }
            response = sbf.toString();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (out != null) {
                    out.close();
                }
                if (in != null) {
                    in.close();
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
        return response;
    }

    /**
     * 获取签名头
     *
     * @param appid
     * @param appkey
     * @param timestamp 格式:"yyyyMMddHHmmss"
     * @param nonce     随机字符串，
     * @param body      请求体
     * @return authorization 认证报文
     * @throws Exception
     */
    static String getSignature(String appid, String appkey, String timestamp, String nonce, String body) throws Exception {
        byte[] data = body.getBytes("utf-8");
        InputStream is = new ByteArrayInputStream(data);
        String testSH = DigestUtils.sha256Hex(is);
        String s1 = appid + timestamp + nonce + testSH;
        Mac mac = Mac.getInstance("HmacSHA256");
        mac.init(new SecretKeySpec(appkey.getBytes("utf-8"), "HmacSHA256"));
        byte[] localSignature = mac.doFinal(s1.getBytes("utf-8"));
        String localSignatureStr = Base64.encodeBase64String(localSignature);
        return localSignatureStr;
    }

    static class H5PayBody {
        //消息ID
        String msgId;
        //报文请求时间，格式yyyy-MM-ddHH:mm:ss
        String requestTimestamp;
        //商户订单号
        String merOrderId;
        //请求系统预留字段
        String srcReserve;
        //商户号
        String mid;
        //终端号
        String tid;
        //业务类型
        String instMid;
        //商品信息
        List<GoodsItem> goods;
        //商户附加数据
        String attachedData;
        //账单描述
        String orderDesc;
        //商品标记
        String goodsTag;
        //订单原始金额
        String originalAmount;
        //支付总金额
        String totalAmount;
        //订单过期时间
        String expireTime;
        //担保交易标识
        String secureTransaction;
        //支付结果通知地址
        String notifyUrl;
        //网页跳转地址
        String returnUrl;
        //系统ID
        String systemId;
        //业务应用类型
        String sceneType;
        //应用名称
        String merAppName;
        //应用标识
        String merAppId;
        //是否需要限制信用卡支付
        String limitCreditCard;
        //分账标记
        String divisionFlag;
        //平台商户分账金额
        String platformAmount;
        //子订单信息
        List<SubOrderItem> subOrders;
        //实名认证姓名
        String name;
        //实名认证手机号
        String mobile;
        //实名认证证件类型
        String certType;
        //实名认证证件号
        String certNo;
        //卡号
        String bankCardNo;

        String toJson() {
            StringBuilder sb = new StringBuilder();
            sb.append("{");
            if (this.msgId != null) sb.append("\"msgId\":\"" + this.msgId + "\",");
            if (this.requestTimestamp != null) sb.append("\"requestTimestamp\":\"" + this.requestTimestamp + "\",");
            if (this.merOrderId != null) sb.append("\"merOrderId\":\"" + this.merOrderId + "\",");
            if (this.srcReserve != null) sb.append("\"srcReserve\":\"" + this.srcReserve + "\",");
            if (this.mid != null) sb.append("\"mid\":\"" + this.mid + "\",");
            if (this.tid != null) sb.append("\"tid\":\"" + this.tid + "\",");
            if (this.instMid != null) sb.append("\"instMid\":\"" + this.instMid + "\",");
            if (this.goods != null && this.goods.size() > 0) {
                sb.append("\"goods\":[");
                for (int i = 0; i < goods.size(); i++) {
                    sb.append(goods.get(i));
                    sb.append(",");
                }
                if (sb.charAt(sb.length() - 1) == ',')
                    sb.deleteCharAt(sb.length() - 1);
                sb.append("],");
            }
            if (this.attachedData != null) sb.append("\"attachedData\":\"" + this.attachedData + "\",");
            if (this.orderDesc != null) sb.append("\"orderDesc\":\"" + this.orderDesc + "\",");
            if (this.goodsTag != null) sb.append("\"goodsTag\":\"" + this.goodsTag + "\",");
            if (this.originalAmount != null) sb.append("\"originalAmount\":\"" + this.originalAmount + "\",");
            if (this.totalAmount != null) sb.append("\"totalAmount\":\"" + this.totalAmount + "\",");
            if (this.expireTime != null) sb.append("\"expireTime\":\"" + this.expireTime + "\",");
            if (this.secureTransaction != null) sb.append("\"secureTransaction\":\"" + this.secureTransaction + "\",");
            if (this.notifyUrl != null) sb.append("\"notifyUrl\":\"" + this.notifyUrl + "\",");
            if (this.returnUrl != null) sb.append("\"returnUrl\":\"" + this.returnUrl + "\",");
            if (this.systemId != null) sb.append("\"systemId\":\"" + this.systemId + "\",");
            if (this.sceneType != null) sb.append("\"sceneType\":\"" + this.sceneType + "\",");
            if (this.merAppName != null) sb.append("\"merAppName\":\"" + this.merAppName + "\",");
            if (this.merAppId != null) sb.append("\"merAppId\":\"" + this.merAppId + "\",");
            if (this.limitCreditCard != null) sb.append("\"limitCreditCard\":\"" + this.limitCreditCard + "\",");
            if (this.divisionFlag != null) sb.append("\"divisionFlag\":\"" + this.divisionFlag + "\",");
            if (this.platformAmount != null) sb.append("\"platformAmount\":\"" + this.platformAmount + "\",");
            if (this.subOrders != null && this.subOrders.size() > 0) {
                sb.append("\"subOrders\":[");
                for (int i = 0; i < subOrders.size(); i++) {
                    sb.append(subOrders.get(i));
                    sb.append(",");
                }
                if (sb.charAt(sb.length() - 1) == ',')
                    sb.deleteCharAt(sb.length() - 1);
                sb.append("],");
            }
            if (this.name != null) sb.append("\"name\":\"" + this.name + "\",");
            if (this.mobile != null) sb.append("\"mobile\":\"" + this.mobile + "\",");
            if (this.certType != null) sb.append("\"certType\":\"" + this.certType + "\",");
            if (this.certNo != null) sb.append("\"certNo\":\"" + this.certNo + "\",");
            if (this.bankCardNo != null) sb.append("\"bankCardNo\":\"" + this.bankCardNo + "\",");
            if (sb.charAt(sb.length() - 1) == ',')
                sb.deleteCharAt(sb.length() - 1);
            sb.append("}");
            return sb.toString();
        }

        public String toString() {
            return this.toJson();
        }

        static class GoodsItem {
            //商品ID
            String goodsId;
            //商品名称
            String goodsName;
            //商品数量
            String quantity;
            //商品单价（分）
            String price;
            //商品分类
            String goodsCategory;
            //商品说明
            String body;
            //子商户号
            String subMerchantId;
            //子商户商品总额
            int subOrderAmount;

            String toJson() {
                StringBuilder sb = new StringBuilder();
                sb.append("{");
                if (this.goodsId != null) sb.append("\"goodsId\":\"" + this.goodsId + "\",");
                if (this.goodsName != null) sb.append("\"goodsName\":\"" + this.goodsName + "\",");

                if (this.quantity != null) {
                    sb.append("\"quantity\":\"" + this.quantity + "\",");
                }
                if (this.price != null) {
                    sb.append("\"price\":\"" + this.price + "\",");
                }
                if (this.goodsCategory != null) {
                    sb.append("\"goodsCategory\":\"" + this.goodsCategory + "\",");
                }
                if (this.body != null) {
                    sb.append("\"body\":\"" + this.body + "\",");
                }
                if (this.subMerchantId != null) {
                    sb.append("\"subMerchantId\":\"" + this.subMerchantId + "\",");
                }
                if (this.subOrderAmount != 0) {
                    sb.append("\"subOrderAmount\":\"" + this.subOrderAmount + "\",");
                }
                if (sb.charAt(sb.length() - 1) == ',')
                    sb.deleteCharAt(sb.length() - 1);
                sb.append("}");
                return sb.toString();
            }

            public String toString() {
                return this.toJson();
            }
        }

        static class SubOrderItem {
            //子商户号
            String mid;
            //子商户分账金额
            int totalAmount;

            String toJson() {
                StringBuilder sb = new StringBuilder();
                sb.append("{");
                if (this.mid != null) {
                    sb.append("\"mid\":\"" + this.mid + "\",");
                }
                if (this.totalAmount != 0) {
                    sb.append("\"totalAmount\":\"" + this.totalAmount + "\",");
                }
                if (sb.charAt(sb.length() - 1) == ',')
                    sb.deleteCharAt(sb.length() - 1);
                sb.append("}");
                return sb.toString();
            }

            public String toString() {
                return this.toJson();
            }
        }

    }

}
