package com.lj.auth;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.web.client.RestTemplate;

@SpringBootApplication
//@EnableScheduling
@EnableAsync
@EnableFeignClients(basePackages = {"com.lj.auth.*"})
@EnableDiscoveryClient(autoRegister = false)
@MapperScan(basePackages = {"com.lj.auth.mapper"})
public class LjAuthApplication {

    public static void main(String[] args) {
        SpringApplication.run(LjAuthApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ 个人模块服务启动成功 ლ(´ڡ`ლ)ﾞ ");
    }

    @Bean
    public RestTemplate restTemplate(RestTemplateBuilder builder) {
        return builder.build();
    }

}
