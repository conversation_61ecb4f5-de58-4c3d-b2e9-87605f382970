package com.lj.auth.event.listener;

import java.math.BigDecimal;
import java.util.Date;

import javax.annotation.Resource;

import cn.hutool.core.util.StrUtil;
import com.lj.auth.domain.RechargeAssets;
import com.lj.auth.domain.RechargeFlow;
import com.lj.auth.domain.RechargeWithdrawRecord;
import com.lj.auth.event.PlanStatusEvent;
import com.lj.auth.mapper.RechargeAssetsMapper;
import com.lj.auth.mapper.RechargeFlowMapper;
import com.lj.auth.mapper.RechargeWithdrawRecordMapper;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import lombok.extern.slf4j.Slf4j;

/**
 * @Author: zjd
 * @Description:
 * @Date: 2023/12/26 14:13
 */
@Slf4j
@Component
public class PlanStatusEventListener {

    @Resource
    private RechargeWithdrawRecordMapper rechargeWithdrawRecordMapper;
    @Resource
    private RechargeAssetsMapper rechargeAssetsMapper;
    @Resource
    private RechargeFlowMapper rechargeFlowMapper;
    @Resource
    private RedissonClient redissonClient;

    @EventListener(PlanStatusEvent.class)
    public void execute(PlanStatusEvent planStatusEvent) {
        JSONObject jsonObject = (JSONObject)planStatusEvent.getSource();
        // 订单唯一编号
        String bizOutNo = jsonObject.getString("bizOutNo");
        // 不是本系统订单，无需处理
        if (StrUtil.isEmpty(bizOutNo)) {
            return;
        }
        RLock lock = redissonClient.getLock(bizOutNo);
        lock.lock();
        try {
            // 审核状态 0-待审核 1-审核通过 2,3-审核拒绝
            Integer auditStatus = jsonObject.getInteger("auditStatus");
            // 发放状态 0-待发放 1-发放通过 2-发放拒绝
            Integer grantStatus = jsonObject.getInteger("grantStatus");
            // 支付状态 0-未处理 1-待打款 2-打款中 3-打款成功 4-打款失败 5-退票退款
            Integer payStatus = jsonObject.getInteger("payStatus");
            // 查找提现记录
            LambdaQueryWrapper<RechargeWithdrawRecord> queryWrapper1 = new LambdaQueryWrapper<>();
            queryWrapper1.eq(RechargeWithdrawRecord::getTranNumber, bizOutNo);
            RechargeWithdrawRecord rechargeWithdrawRecord =
                rechargeWithdrawRecordMapper.selectOne(queryWrapper1);
            if (rechargeWithdrawRecord.getState() != 1) {
                // 该订单已经处理，不重复处理
                return;
            }
            if (rechargeWithdrawRecord.getType() != 2) {
                // 该订单不是提现订单，不处理
                return;
            }
            String operateUuid = rechargeWithdrawRecord.getOperateUuid();
            String accountUuid = rechargeWithdrawRecord.getAccountUuid();
            BigDecimal amount = rechargeWithdrawRecord.getAmount();
            if (auditStatus == 1 && grantStatus == 1 && payStatus == 3) {
                log.error("打款成功，提现订单流水号：{}", bizOutNo);
                // 1.更新提现记录表状态
                rechargeWithdrawRecord.setState(3);
                rechargeWithdrawRecord.setUpdateTime(new Date());
                rechargeWithdrawRecordMapper.updateById(rechargeWithdrawRecord);
                // 2.减冻结
                LambdaQueryWrapper<RechargeAssets> queryWrapper2 = new LambdaQueryWrapper<>();
                queryWrapper2.eq(RechargeAssets::getOperateUuid, operateUuid);
                queryWrapper2.eq(RechargeAssets::getAccountUuid, accountUuid);
                RechargeAssets rechargeAssets = rechargeAssetsMapper.selectOne(queryWrapper2);
                BigDecimal freeze = rechargeAssets.getFreeze();
                rechargeAssets.setFreeze(freeze.subtract(amount));
                rechargeAssetsMapper.updateById(rechargeAssets);
                // 3.新增流水
                RechargeFlow rechargeFlow =new
                    RechargeFlow().setOperateUuid(operateUuid).setAccountUuid(accountUuid)
                        .setOrderId(rechargeWithdrawRecord.getId()).setAmount(rechargeWithdrawRecord.getAmount())
                        .setType(2).setRealAmount(rechargeWithdrawRecord.getRealAmount())
                        .setServiceCharge(new BigDecimal(rechargeWithdrawRecord.getRate()))
                        .setCreateTime(new Date());
                rechargeFlowMapper.insert(rechargeFlow);
                // 4.发消息 TODO 待定

                log.error("打款流程结束，提现订单流水号：{}", bizOutNo);
            } else if (auditStatus > 1 || grantStatus > 1 || payStatus > 3) {
                log.error("打款失败，提现订单流水号：{}", bizOutNo);
                // 1.更新提现记录表状态
                rechargeWithdrawRecord.setState(2);
                rechargeWithdrawRecord.setReason("打款失败");
                rechargeWithdrawRecord.setUpdateTime(new Date());
                rechargeWithdrawRecordMapper.updateById(rechargeWithdrawRecord);
                // 2.减冻结，加余额
                LambdaQueryWrapper<RechargeAssets> queryWrapper2 = new LambdaQueryWrapper<>();
                queryWrapper2.eq(RechargeAssets::getOperateUuid, operateUuid);
                queryWrapper2.eq(RechargeAssets::getAccountUuid, accountUuid);
                RechargeAssets rechargeAssets = rechargeAssetsMapper.selectOne(queryWrapper2);
                BigDecimal freeze = rechargeAssets.getFreeze();
                BigDecimal balance = rechargeAssets.getBalance();
                rechargeAssets.setFreeze(freeze.subtract(amount));
                rechargeAssets.setBalance(balance.add(amount));
                rechargeAssetsMapper.updateById(rechargeAssets);
                // 3.发送消息 TODO 待定
                log.error("打款失败流程结束，提现订单流水号：{}", bizOutNo);
            }
        } finally {
            log.info("解锁");
            lock.unlock();
        }
    }
}
