package com.lj.auth.event.listener;

import javax.annotation.Resource;

import com.lj.auth.domain.Account;
import com.lj.auth.event.PeopleStatusEvent;
import com.lj.auth.mapper.AccountMapper;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;


import lombok.extern.slf4j.Slf4j;


/**
 * @Author: zjd
 * @Description:
 * @Date: 2023/12/26 14:15
 */
@Slf4j
@Component
public class PeopleStatusEventListener {

    @Resource
    private AccountMapper accountMapper;

    @EventListener(PeopleStatusEvent.class)
    public void execute(PeopleStatusEvent peopleStatusEvent) {
        JSONObject jsonObject = (JSONObject) peopleStatusEvent.getSource();
        //签约人id
        String peopleId = jsonObject.getString("peopleId");
        //签约状态
        Integer signStatus = jsonObject.getInteger("signStatus");
        System.out.println("签约状态改变："+"peopleId:"+peopleId+",signStatus:"+signStatus);
        System.out.println("签约状态改变："+"peopleId:"+peopleId+",signStatus:"+signStatus);
        System.out.println("签约状态改变："+"peopleId:"+peopleId+",signStatus:"+signStatus);
        //更新用户签约状态
        LambdaUpdateWrapper<Account> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(Account::getPeopleId, peopleId)
                .set(Account::getSignStatus, signStatus);
        accountMapper.update(null, updateWrapper);

    }
}
