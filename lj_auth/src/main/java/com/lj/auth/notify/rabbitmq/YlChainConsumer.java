package com.lj.auth.notify.rabbitmq;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.lj.auth.common.CommonConstant;
import com.lj.auth.config.RabbitConfig;
import com.lj.auth.domain.Nft;
import com.lj.auth.domain.NftManualDispose;
import com.lj.auth.domain.NftTransferRecord;
import com.lj.auth.domain.PlatformAccount;
import com.lj.auth.domain.vo.UpChainDataVo;
import com.lj.auth.exception.ServiceException;
import com.lj.auth.mapper.GlobalConfigMapper;
import com.lj.auth.mapper.NftManualDisposeMapper;
import com.lj.auth.mapper.NftMapper;
import com.lj.auth.mapper.NftTransferRecordMapper;
import com.lj.auth.service.NftGetRecordService;
import com.lj.auth.service.NftService;
import com.lj.auth.util.BNBUtil;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.transaction.annotation.Transactional;
import org.web3j.abi.FunctionEncoder;
import org.web3j.abi.FunctionReturnDecoder;
import org.web3j.abi.TypeReference;
import org.web3j.abi.datatypes.*;
import org.web3j.abi.datatypes.generated.Uint256;
import org.web3j.crypto.Credentials;
import org.web3j.crypto.RawTransaction;
import org.web3j.crypto.TransactionEncoder;
import org.web3j.protocol.core.DefaultBlockParameterName;
import org.web3j.protocol.core.methods.request.Transaction;
import org.web3j.protocol.core.methods.response.EthCall;
import org.web3j.protocol.core.methods.response.EthGetTransactionReceipt;
import org.web3j.protocol.core.methods.response.TransactionReceipt;
import org.web3j.utils.Convert;
import org.web3j.utils.Numeric;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Author: zjd
 * @Description:
 * @Date:2024/04/11 09:38
 **/
@Slf4j
@Configuration
public class YlChainConsumer {

    //锁标识
    public static final String LOCK_SYMBOL = "mint";

    private static final BigInteger GAS_LIMIT = new BigInteger("3000000");

    //消息的前缀
    private static final String MESSAGE="message:";

    @Resource
    private RedissonClient redissonClient;
    @Resource
    private RedisTemplate<String, Integer> redisTemplate;
    @Resource
    private NftManualDisposeMapper nftManualDisposeMapper;
    @Resource
    private GlobalConfigMapper globalConfigMapper;
    @Resource
    private NftGetRecordService nftGetRecordService;
    @Resource
    private NftMapper nftMapper;
    @Resource
    private NftTransferRecordMapper nftTransferRecordMapper;

    @Value("${ipfs.url}")
    private String ipfsUrl;

    @Resource
    private NftService nftService;
    /**
     * 处理
     * @param content
     * @param message
     * @param channel
     * @throws IOException
     */
    @Value("${ipfs.imagePrefix}")
    private String imagePrefix;

    //@RabbitListener(queues = {"mint-up-chain-queue"})
    @Transactional
    public void upChainHandle(String content, Message message, Channel channel) throws IOException {
        channel.basicQos(1);
        MessageProperties messageProperties = message.getMessageProperties();
        long deliveryTag = messageProperties.getDeliveryTag();
        String messageId = messageProperties.getMessageId();
        UpChainDataVo upChainDataVo = JSON.parseObject(content, UpChainDataVo.class);
        String contractAddress = upChainDataVo.getContractAddress();
        String chainAccountAddress = upChainDataVo.getChainAccountAddress();
        PlatformAccount ylPlatformAccount = upChainDataVo.getYlPlatformAccount();
        Integer nftCount = upChainDataVo.getNftCount();
        String uuid = upChainDataVo.getUuid();
        Long nftRecordId = upChainDataVo.getNftRecordId();
        Integer opbChainId = upChainDataVo.getOpbChainId();
        Integer count = redisTemplate.opsForValue().get(MESSAGE + messageId);
        //如果重试次数大于3次，则不签收，人工处理
        if (count != null && count > 3) {
            redisTemplate.delete(MESSAGE + messageId);
            //不签收，人工处理
            channel.basicNack(deliveryTag, false, false);
            NftManualDispose nftManualDispose = NftManualDispose.builder().nftRecordId(nftRecordId)
                    .chainAccountAddress(chainAccountAddress)
                    .contractAddress(contractAddress)
                    .nftCount(nftCount)
                    .opbChainId(opbChainId)
                    .uuid(uuid)
                    .status(0)
                    .build();
            nftManualDisposeMapper.insert(nftManualDispose);
            return;
        }
        log.info("执行领NFT操作,铸币上链数据：nftRecordId：{}，nftCount：{}，uuid：{}，contractAddress：{}, chainAccountAddress:{}", nftRecordId, nftCount, uuid, contractAddress, chainAccountAddress);
        //加锁
        RLock lock = redissonClient.getLock(LOCK_SYMBOL);
        lock.lock();
        try {
            //获取token高度
            int currentNum = 0;
            try {
                String functionName = "currentNum";
                List<Type> inputParameters = new ArrayList<>();
                List<TypeReference<?>> outputParameters = new ArrayList<>();
                outputParameters.add(new TypeReference<Uint256>() {
                });
                Function function = new Function(functionName, inputParameters, outputParameters);
                List<Type> types = sendCallTransaction(ylPlatformAccount.getAddress(), contractAddress, function);
                BigInteger tem = (BigInteger) types.get(0).getValue();
                currentNum = tem.intValue();
            } catch (Exception e) {
                e.printStackTrace();
                throw new ServiceException("获取token高度失败");
            }
            List<Uint256> tokenIds = new ArrayList<>();
            //计算要铸币的tokenIds
            for (int i = 0; i < nftCount; i++) {
                currentNum++;
                tokenIds.add(new Uint256(BigInteger.valueOf(currentNum)));
            }
            //查询NFT默认价格
            String price = globalConfigMapper.queryConfig(CommonConstant.NFT_DEFAULT_PRICE);
            //新增NFT领取记录，处理IPFS相关内容，返回json文档
            BigDecimal nftPrice = new BigDecimal(price);
            List<Integer> collect = tokenIds.stream().map(x -> x.getValue().intValue()).collect(Collectors.toList());
            //插入nft领取记录，并获得ipfs信息
            String jsonStr = nftGetRecordService.insertNftRecord(uuid, chainAccountAddress, opbChainId, nftCount, 1, collect, contractAddress,nftRecordId);
            JSONObject jsonObject = JSON.parseObject(jsonStr);
            String pathCid = jsonObject.getString("pathCid");
            String baseUri = ipfsUrl + pathCid + "/";
            //新增nft数据
            JSONArray imageVos = jsonObject.getJSONArray("imageVos");
            for (int i = 0; i < imageVos.size(); i++) {
                JSONObject imageVo = imageVos.getJSONObject(i);
                String cid = imageVo.getString("cid");
                Integer tokenId = imageVo.getInteger("tokenId");
                String nftUrl = imagePrefix + cid;
                //获取json数据
                String jsonUrl = baseUri+tokenId;
                JSONObject jsObject = null;
                try {
                    URL url = new URL(jsonUrl);
                    jsObject = JSON.parseObject(url);
                } catch (MalformedURLException e) {
                    throw new ServiceException("ipfs访问失败");
                }
                String nftName = jsObject.getString("name");
                String description = jsObject.getString("description");
                //nft表新增数据
                Nft nft = Nft.builder().opbChainId(opbChainId)
                        .contractId(upChainDataVo.getContractId())
                        .tokenId(tokenId)
                        .contractAddress(contractAddress)
                        .creator(chainAccountAddress)
                        .holder(chainAccountAddress)
                       // .mintHash(transactionHash)
                        .mintDate(new Date())
                        .nftName(nftName)
                        .nftDescribe(description)
                        .nftType(1)
                        .nftImage(nftUrl)
                        .nftPrice(nftPrice)
                        .status(1)
                        .build();
                nftMapper.insert(nft);
                //nft交易记录表新增数据
               // BigInteger gasUsed = transactionReceipt.getGasUsed();
               // BigInteger blockNumber = transactionReceipt.getBlockNumber();
                NftTransferRecord nftTransferRecord = NftTransferRecord.builder().opbChainId(opbChainId)
                        .contractId(nft.getContractId())
                        .tokenId(tokenId)
                        .contractAddress(contractAddress)
                        .fromAddress("0x0000000000000000000000000000000000000000")
                        .toAddress(chainAccountAddress)
                        //.energyValue(gasUsed.toString())
                       // .transactionHash(transactionHash)
                        //.blockNumber(blockNumber.intValue())
                        .transactionType(1)
                        .status(1)
                        .build();
                nftTransferRecordMapper.insert(nftTransferRecord);
            }
            //铸币
            String transactionHash = null;
            String functionName = "batchMint";
            List<Type> inputParameters = new ArrayList<>();
            List<TypeReference<?>> outputParameters = new ArrayList<>();
            inputParameters.add(new Address(chainAccountAddress));
            inputParameters.add(new DynamicArray(Uint256.class, tokenIds));
            Function function = new Function(functionName, inputParameters, outputParameters);
            try {
                //数据上链
                transactionHash = sendTransaction(ylPlatformAccount.getAddress(), contractAddress, ylPlatformAccount.getPrivateKey(), function);
            } catch (Exception e) {
                e.printStackTrace();
                throw new ServiceException("铸币失败");
            }
            TransactionReceipt transactionReceipt = null;
            if (StrUtil.isNotEmpty(transactionHash)) {
                try {
                    //判断交易状态
                    transactionReceipt = getTransactionReceipt(transactionHash);
                    if (!transactionReceipt.isStatusOK()) {
                        redisTemplate.opsForValue().increment(MESSAGE + messageId);
                        channel.basicNack(deliveryTag, false, true);
                        throw new ServiceException("hash: "+transactionHash+"铸币失败");
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new ServiceException("铸币失败");
                }
            }
            String tsHash = transactionHash;
            TransactionReceipt tsReceipt = transactionReceipt;
            //更新NFT表和NFT记录表信息
            collect.forEach(tokenId -> {
                LambdaUpdateWrapper<Nft> updateWrapper1 = new LambdaUpdateWrapper<>();
                updateWrapper1.eq(Nft::getOpbChainId, opbChainId)
                        .eq(Nft::getContractAddress, contractAddress)
                        .eq(Nft::getTokenId, tokenId)
                        .set(Nft::getMintHash, tsHash);
                nftMapper.update(null, updateWrapper1);
                BigInteger gasUsed = tsReceipt.getGasUsed();
                BigInteger blockNumber = tsReceipt.getBlockNumber();
                LambdaUpdateWrapper<NftTransferRecord> updateWrapper2 = new LambdaUpdateWrapper<>();
                updateWrapper2.eq(NftTransferRecord::getOpbChainId, opbChainId)
                        .eq(NftTransferRecord::getContractAddress, contractAddress)
                        .eq(NftTransferRecord::getTokenId, tokenId)
                        .set(NftTransferRecord::getTransactionHash, tsHash)
                        .set(NftTransferRecord::getEnergyValue, gasUsed.toString())
                        .set(NftTransferRecord::getBlockNumber, blockNumber.intValue());
                nftTransferRecordMapper.update(null, updateWrapper2);
            });
            //更新baseURI
            String functionName2 = "setBaseURI";
            List<Type> inputParameters2 = new ArrayList<>();
            inputParameters2.add(new Utf8String(baseUri));
            List<TypeReference<?>> outputParameters2 = new ArrayList<>();
            Function function2 = new Function(functionName2, inputParameters2, outputParameters2);
            try {
                BigInteger gasPrice = Convert.toWei(BigDecimal.valueOf(1000), Convert.Unit.GWEI).toBigInteger();
                String transactionHash2 = sendTransaction(ylPlatformAccount.getAddress(), contractAddress, ylPlatformAccount.getPrivateKey(), function2,gasPrice);
                TransactionReceipt transactionReceipt2 = getTransactionReceipt(transactionHash2);
                if (!transactionReceipt2.isStatusOK()) {
                    throw new ServiceException("更新baseURI失败");
                }
            } catch (Exception e) {
                e.printStackTrace();
                throw new ServiceException("更新baseURI遇到网络错误");
            }
            channel.basicAck(deliveryTag, false);
        } catch (Exception e) {
            e.printStackTrace();
            redisTemplate.opsForValue().increment(MESSAGE + messageId);
            channel.basicNack(deliveryTag, false, true);
            throw new ServiceException("自动批量创建NFT失败");
        } finally {
            log.info("解锁，记录id：{}，账户地址：{}，领取数量：{}", nftRecordId,  chainAccountAddress, nftCount);
            lock.unlock();
        }
    }

    /**
     * NFT批量上链处理 旧
     * @param content
     * @param message
     * @param channel
     * @throws IOException
     */
    @RabbitListener(queues = {RabbitConfig.MINT_UP_CHAIN_QUEUE})
    @Transactional
    public void upChainHandleV2(String content, Message message, Channel channel) throws IOException {
        nftService.upChainHandleV2(content, message, channel);
    }

    /**
     * NFT单个上链处理
     * @param content
     * @param message
     * @param channel
     * @throws IOException
     */
    @RabbitListener(queues = {RabbitConfig.SINGLE_MINT_UP_CHAIN_QUEUE})
    @Transactional
    public void singleUpChainHandle(String content, Message message, Channel channel) throws IOException {
        nftService.singleUpChainHandle(content, message, channel);
    }



    /**
     * NFT单个上链处理
     * @param content
     * @param message
     * @param channel
     * @throws IOException
     */
    @RabbitListener(queues = {RabbitConfig.COMPENSATION_MINT_UP_CHAIN_QUEUE})
    @Transactional
    public void compensationMintUpChainHandle(String content, Message message, Channel channel) throws IOException {
        nftService.compensationMintUpChainHandle(content, message, channel);
    }




    public List<Type> sendCallTransaction(String from, String to, Function function) throws Exception{
        String encode = FunctionEncoder.encode(function);
        Transaction ethCallTransaction = Transaction.createEthCallTransaction(from, to, encode);
        EthCall ethCall = BNBUtil.ethCall(ethCallTransaction, DefaultBlockParameterName.LATEST);
        return FunctionReturnDecoder.decode(ethCall.getValue(), function.getOutputParameters());
    }


    private static Integer getNum(String platformAccountAddress,String contractAddress){
        int currentNum = 0;
        try {
            String functionName = "currentNum";
            List<Type> inputParameters = new ArrayList<>();
            List<TypeReference<?>> outputParameters = new ArrayList<>();
            outputParameters.add(new TypeReference<Uint256>() {
            });
            Function function = new Function(functionName, inputParameters, outputParameters);
            List<Type> types = sendCallTransactionTest(platformAccountAddress, contractAddress, function);
            BigInteger tem = (BigInteger) types.get(0).getValue();
            currentNum = tem.intValue();
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException("获取token高度失败");
        }
        return  currentNum;
    }



    public static List<Type> sendCallTransactionTest(String from, String to, Function function) throws Exception{
        String encode = FunctionEncoder.encode(function);
        Transaction ethCallTransaction = Transaction.createEthCallTransaction(from, to, encode);
        EthCall ethCall = BNBUtil.ethCall(ethCallTransaction, DefaultBlockParameterName.LATEST);
        return FunctionReturnDecoder.decode(ethCall.getValue(), function.getOutputParameters());
    }


    public static void main(String[] args) {
        String chainAccountAddress="******************************************";
        String platformAccountAddress="******************************************";
        String platformAccountPrivateKey="0f63149b944a842b74a5b661525b6439c7c4d50b72bf5d251d5452b05bd80884";
//        String platformAccountAddress="******************************************";
//        String platformAccountPrivateKey="2a854b6cddcb2170901c60fede958f5c713d6b16447151cbfe917fc508ad62da";
        String contractAddress="******************************************";
//        String contractAddress="******************************************";
        String functionName = "batchMint";

        Integer num = getNum(platformAccountAddress, contractAddress);
        System.out.println("当前token高度："+num);
        List<Uint256> tokenIds = new ArrayList<>();
        tokenIds.add(new Uint256(BigInteger.valueOf(num+1)));
        List<Type> inputParameters = new ArrayList<>();
        List<TypeReference<?>> outputParameters = new ArrayList<>();
        inputParameters.add(new Address(chainAccountAddress));
        inputParameters.add(new DynamicArray(Uint256.class, tokenIds));
        Function function = new Function(functionName, inputParameters, outputParameters);
        String sendTransactionTestHash= null;
            try {
                sendTransactionTestHash = sendTransactionTest(platformAccountAddress, contractAddress, platformAccountPrivateKey, function);
            } catch (Exception ex) {
                throw new RuntimeException(ex);
        }
        System.out.println("token Id "+(num+1)+"铸币hash："+sendTransactionTestHash);

    }



    public static String sendTransactionTest(String chainAccountAddress, String contractAddress, String privateKey, Function function) throws Exception{
        Credentials credentials = Credentials.create(privateKey);
        String encode = FunctionEncoder.encode(function);
        BigInteger nonce = BNBUtil.getTransactionNonce(chainAccountAddress);
        BigInteger gasPrice = BNBUtil.ethGasPrice().getGasPrice();
//        BigInteger gasPrice = Convert.toWei(BigDecimal.valueOf(10000), Convert.Unit.GWEI).toBigInteger();
        //BigInteger gasPrice = BigInteger.ZERO;
        RawTransaction transaction = RawTransaction.createTransaction(nonce, gasPrice, GAS_LIMIT, contractAddress, encode);
        byte[] signMessage = TransactionEncoder.signMessage(transaction, credentials);
        String hexValue = Numeric.toHexString(signMessage);
        return BNBUtil.ethSendRawTransaction(hexValue).getTransactionHash();
    }


    public String sendTransaction(String chainAccountAddress, String contractAddress, String privateKey, Function function) throws Exception{
        Credentials credentials = Credentials.create(privateKey);
        String encode = FunctionEncoder.encode(function);
        BigInteger nonce = BNBUtil.getTransactionNonce(chainAccountAddress);
        BigInteger gasPrice = BNBUtil.ethGasPrice().getGasPrice();
        //BigInteger gasPrice = BigInteger.ZERO;
        RawTransaction transaction = RawTransaction.createTransaction(nonce, gasPrice, GAS_LIMIT, contractAddress, encode);
        byte[] signMessage = TransactionEncoder.signMessage(transaction, credentials);
        String hexValue = Numeric.toHexString(signMessage);
        return BNBUtil.ethSendRawTransaction(hexValue).getTransactionHash();
    }


    public String sendTransaction(String chainAccountAddress, String contractAddress, String privateKey, Function function, BigInteger gasPrice) throws Exception{
        Credentials credentials = Credentials.create(privateKey);
        String encode = FunctionEncoder.encode(function);
        BigInteger nonce = BNBUtil.getTransactionNonce(chainAccountAddress);
        //BigInteger gasPrice = BigInteger.ZERO;
        RawTransaction transaction = RawTransaction.createTransaction(nonce, gasPrice, GAS_LIMIT, contractAddress, encode);
        byte[] signMessage = TransactionEncoder.signMessage(transaction, credentials);
        String hexValue = Numeric.toHexString(signMessage);
        return BNBUtil.ethSendRawTransaction(hexValue).getTransactionHash();
    }



    public TransactionReceipt getTransactionReceipt(String transactionHash) throws Exception{
        EthGetTransactionReceipt ethGetTransactionReceipt = BNBUtil.ethGetTransactionReceipt(transactionHash);
        Optional<TransactionReceipt> optional = ethGetTransactionReceipt.getTransactionReceipt();
        if (!optional.isPresent()) {
            while (true) {
                //等待1秒
                TimeUnit.SECONDS.sleep(1);
                ethGetTransactionReceipt = BNBUtil.ethGetTransactionReceipt(transactionHash);
                optional = ethGetTransactionReceipt.getTransactionReceipt();
                if (optional.isPresent()) {
                    break;
                }
            }
        }
        return optional.get();
    }
}
