package com.lj.auth.notify.sms.service;


import lombok.Data;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @describe
 */
@Data
@Validated
public class SMSTrunkReq extends SMSReq {

    /**
     * 短信API商品名称
     */
    @NotBlank
    private String product;

    /**
     * 短信API商品域名
     */
    @NotBlank
    private String domain;

    /**
     * accessKeyId
     */
    @NotBlank
    private String accesskeyid;

    /**
     * accessKeySecret
     */
    @NotBlank
    private String accesskeysecret;

    /**
     * 签名
     */
    @NotBlank
    private String signname;

    /**
     * 模板
     */
    @NotBlank
    private String templatecode;

    /**
     * 短信模板变量
     */
    @NotBlank
    private String variable;
}
