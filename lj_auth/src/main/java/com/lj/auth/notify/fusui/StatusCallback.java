package com.lj.auth.notify.fusui;

import java.util.HashMap;

import com.lj.auth.common.CommonConstant;
import com.lj.auth.event.PeopleStatusEvent;
import com.lj.auth.event.PlanStatusEvent;
import com.lj.auth.util.FusuiUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson2.JSONObject;


import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

/**
 * @Author: zjd
 * @Description:
 * @Date: 2023/12/25 15:36
 */
@Slf4j
@RestController
@RequestMapping("/callback")
public class StatusCallback {
    @Resource
    private ApplicationEventPublisher applicationEventPublisher;

    @Value("${fusui.secret}")
    private String secret;

    @PostMapping("/lingGongCallback")
    public String peopleStatusCallback(@RequestParam HashMap<String, String> params) {
        log.info("回调消息： {}", params);
        String sign = params.get("sign");
        String timestamp = params.get("timestamp");
        String appId = params.get("appId");
        String bizContent = params.get("bizContent");
        String msgMethod = params.get("msgMethod");
        log.info("业务数据：{}", bizContent);
        params.remove("sign");
        String sortParameters = FusuiUtil.sortParams(params);
        String sb = FusuiUtil.splice(secret, sortParameters);
        try {
            String campSign = FusuiUtil.sign(sb, timestamp, secret);
            log.info("比较结果：{}", sign.equals(campSign));
            if (!sign.equals(campSign)) {
                return "error";
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        JSONObject bizData = JSONObject.parseObject(bizContent);
        if (CommonConstant.PLAN_STATUS_CALLBACK.equals(msgMethod)) {
            //提现到账回调
            applicationEventPublisher.publishEvent(new PlanStatusEvent(bizData));
        }
        if (CommonConstant.PEOPLE_STATUS_CALLBACK.equals(msgMethod)) {
            //签约成功后的回调
            applicationEventPublisher.publishEvent(new PeopleStatusEvent(bizData));
        }
        return "success";
    }
}
