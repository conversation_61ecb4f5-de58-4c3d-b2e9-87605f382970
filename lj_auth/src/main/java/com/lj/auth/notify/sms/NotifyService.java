package com.lj.auth.notify.sms;


import cn.hutool.core.util.ObjectUtil;
import com.lj.auth.domain.SmsConfig;
import com.lj.auth.mapper.SmsConfigMapper;
import com.lj.auth.notify.sms.service.SMSReq;
import com.lj.auth.notify.sms.service.SmsSender;
import com.lj.auth.notify.sms.service.TencentSmsUtil;
import com.lj.auth.service.GlobalConfigService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.lj.auth.common.CommonConstant.*;

/**
 * @ClassName NotifyService
 * @Description 通知服务
 * @Authod yl
 * @Version 1.0
 **/

@Data
@Slf4j
public class NotifyService {
    @Autowired
    private SmsSender aliyunSmsDynamicSender;
    @Resource
    private GlobalConfigService  globalConfigService;

    @Resource
    private TencentSmsUtil tencentSmsUtil;

    /**
     * 短信服务商阿里云
     */
    private static final String smsServiceProviderAliyun="aliyun";

    /**
     * 短信服务商腾讯云
     */
    private static final String smsServiceProviderTxyun="txyun";


    @Resource
    private SmsConfigMapper smsConfigMapper;
    private List<Map<String, String>> smsTemplate = new ArrayList<>();

    private String getSmsServiceProvider(){
        String smsServiceProvider=smsServiceProviderAliyun;
        String smsServiceProviderType = globalConfigService.getGlobalConfig(SMS_SERVICE_PROVIDER);
        if(StringUtils.isNotBlank(smsServiceProviderType)){
            //阿里云
            if(ObjectUtil.equals("2",smsServiceProviderType)){
                smsServiceProvider=smsServiceProviderTxyun;
            }
        }
        return smsServiceProvider;
    }

    public boolean sendSms(Integer type, String phone, String value) {
        String smsServiceProvider = getSmsServiceProvider();
        String operaterUUID = globalConfigService.getGlobalConfig(YL_UUID);
        SmsConfig smsConfig = smsConfigMapper.queryByOperateUUIDAndType(operaterUUID, type,smsServiceProvider);
        if (smsConfig != null) {
            String smsserviceprovider = smsConfig.getSmsserviceprovider();
            if(ObjectUtil.equals(smsserviceprovider,smsServiceProviderAliyun)){
                SMSReq smsReq = new SMSReq();
                smsReq.setOperateUUID(operaterUUID);
                smsReq.setType(type);
                smsReq.setPhone(phone);
                smsReq.setValue(value);
                return aliyunSmsDynamicSender.dynamicSendSMS(smsReq);
            }else {
                // 走腾讯云短信逻辑
                Boolean b = tencentSmsUtil.sendSmsWichConfig(phone, value, smsConfig);
                log.info("阿里云发送短信，签名:{},模板{},手机号:{},短信:{},结果:{}",
                    new Object[] {smsConfig.getSignname(), smsConfig.getTemplatecode(), phone, value, b});
                return b;
            }

        } else {
            return false;
        }
    }
}
