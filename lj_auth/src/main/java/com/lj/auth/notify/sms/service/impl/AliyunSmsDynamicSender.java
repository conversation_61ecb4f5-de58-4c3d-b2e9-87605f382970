package com.lj.auth.notify.sms.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.lj.auth.domain.SmsConfig;
import com.lj.auth.exception.ResponseEnum;
import com.lj.auth.mapper.SmsConfigMapper;
import com.lj.auth.notify.sms.service.SMSReq;
import com.lj.auth.notify.sms.service.SMSTrunkReq;
import com.lj.auth.notify.sms.service.SmsAliUtils;
import com.lj.auth.notify.sms.service.SmsSender;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @describe
 */
@Slf4j
@Service
public class AliyunSmsDynamicSender implements SmsSender {

    @Resource
    private SmsConfigMapper smsConfigMapper;
    @Override
    public boolean dynamicSendSMS(SMSReq smsReq) {
        String operateUUID = smsReq.getOperateUUID();
        Integer type = smsReq.getType();
        Assert.isTrue(StrUtil.isNotBlank(operateUUID) && type != null, ResponseEnum.SMSTypeError.getMsg());
        SmsConfig smsConfig = smsConfigMapper.queryByOperateUUIDAndType(operateUUID, type,"aliyun");
        Assert.notNull(smsConfig, ResponseEnum.SMSConfigurationDoesNotExist.getMsg());
        SMSTrunkReq smsTrunkReq = assableParam(smsReq, smsConfig);
        Boolean b = SmsAliUtils.sendSMS(smsTrunkReq);
        log.info("阿里云发送短信，签名:{},模板{},手机号:{},短信:{},结果:{}", smsTrunkReq.getSignname(),
            smsTrunkReq.getTemplatecode(), smsTrunkReq.getPhone(), smsTrunkReq.getValue(), b);
        return b;
    }

    /**
     * 组装入参
     * 
     * @param smsReq
     * @param smsConfig
     * @return
     */
    private SMSTrunkReq assableParam(SMSReq smsReq, SmsConfig smsConfig) {
        SMSTrunkReq smsTrunkReq = new SMSTrunkReq();
        BeanUtil.copyProperties(smsReq, smsTrunkReq);
        smsTrunkReq.setProduct(smsConfig.getProduct());
        smsTrunkReq.setDomain(smsConfig.getDomain());
        smsTrunkReq.setAccesskeyid(smsConfig.getAccesskeyid());
        smsTrunkReq.setAccesskeysecret(smsConfig.getAccesskeysecret());
        smsTrunkReq.setSignname(smsConfig.getSignname());
        smsTrunkReq.setTemplatecode(smsConfig.getTemplatecode());
        smsTrunkReq.setVariable(smsConfig.getVariable());
        smsTrunkReq.setOperateUUID(smsConfig.getOperateUuid());
        smsTrunkReq.setType(smsConfig.getType());
        return smsTrunkReq;
    }

}
