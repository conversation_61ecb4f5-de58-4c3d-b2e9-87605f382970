package com.lj.auth.notify.sms.service;

import com.alibaba.fastjson.JSONObject;
import com.lj.auth.domain.SmsConfig;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.sms.v20210111.SmsClient;
import com.tencentcloudapi.sms.v20210111.models.SendSmsRequest;
import com.tencentcloudapi.sms.v20210111.models.SendSmsResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 腾讯云短信服务工具类
 */
@Slf4j
@Component
public class TencentSmsUtil {

    // 测试用常量配置
    private static final String SECRET_ID = "AKIDLZyRmwuiLI9xd8SbaO2W2cGl5D7rjS3o";
    private static final String SECRET_KEY = "kdP1TkogMo6ECyYv64rRo0zlKfniQuBE";
    private static final String APP_ID = "1400977474";
    private static final String SIGN_NAME = "实名DID";
//    private static final String TEMPLATE_ID = "2404040";
    private static final String TEMPLATE_ID = "2450153";

    public static void main(String[] args) {
        TencentSmsUtil tencentSmsUtil = new TencentSmsUtil();
        tencentSmsUtil.sendSms("13871457426", "1234");
//        tencentSmsUtil.sendSms("18567271330", "1234");//lx
//        tencentSmsUtil.sendSms("18727169231", "1234");//xz
//        tencentSmsUtil.sendSms("18674079522", "1234");//cfj
//        tencentSmsUtil.sendSms("15927511835", "1234");//jsf
//        tencentSmsUtil.sendSms("13387578795", "1234");//wxm
    }

    /**
     * 发送短信
     *
     * @param phoneNumber 手机号码
     * @param templateParam 模板参数
     * @return 发送结果
     */
    public SendSmsResponse sendSms(String phoneNumber, String templateParam) {
        try {
            // 实例化一个认证对象
            Credential cred = new Credential(SECRET_ID, SECRET_KEY);

            // 实例化一个http选项，可选的，无特殊需求时可以跳过
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint("sms.tencentcloudapi.com");

            // 实例化一个client选项，可选的，无特殊需求时可以跳过
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);

            // 实例化 SMS 的 client 对象
            SmsClient client = new SmsClient(cred, "ap-guangzhou", clientProfile);

            // 实例化一个请求对象
            SendSmsRequest req = new SendSmsRequest();
            req.setSmsSdkAppId(APP_ID);
            req.setSignName(SIGN_NAME);
            req.setTemplateId(TEMPLATE_ID);
//            req.setPhoneNumberSet(new String[]{"+86" + phoneNumber});
            req.setPhoneNumberSet(new String[]{phoneNumber});
            req.setTemplateParamSet(new String[]{templateParam});

            // 返回的resp是一个SendSmsResponse的实例，与请求对象对应
            SendSmsResponse resp = client.SendSms(req);
//            log.info("SendSms response: {}", resp);
            log.info("SendSms response: {}", resp.getSendStatusSet()[0].getMessage());
            return resp;
        } catch (TencentCloudSDKException e) {
            log.error("Send SMS failed", e);
            throw new RuntimeException("发送短信失败", e);
        }
    }

    /**
     * 发送短信
     *
     * @param phoneNumber 手机号码
     * @param templateParam 模板参数
     * @return 发送结果
     */
    public Boolean sendSmsWichConfig(String phoneNumber, String templateParam, SmsConfig smsConfig) {
        Boolean resultFlag = false;
        try {
            // 实例化一个认证对象
            Credential cred = new Credential(smsConfig.getAccesskeyid(), smsConfig.getAccesskeysecret());

            // 实例化一个http选项，可选的，无特殊需求时可以跳过
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint("sms.tencentcloudapi.com");

            // 实例化一个client选项，可选的，无特殊需求时可以跳过
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);

            // 实例化 SMS 的 client 对象
            SmsClient client = new SmsClient(cred, "ap-guangzhou", clientProfile);

            // 实例化一个请求对象
            SendSmsRequest req = new SendSmsRequest();
            req.setSmsSdkAppId(smsConfig.getAppid());
            req.setSignName(smsConfig.getSignname());
            req.setTemplateId(smsConfig.getTemplatecode());
//            req.setPhoneNumberSet(new String[]{"+86" + phoneNumber});
            req.setPhoneNumberSet(new String[]{phoneNumber});
            req.setTemplateParamSet(new String[]{templateParam});

            // 返回的resp是一个SendSmsResponse的实例，与请求对象对应
            SendSmsResponse resp = client.SendSms(req);
            log.info("SendSms response: {}", resp.getSendStatusSet()[0].getMessage());
            String responseString = JSONObject.toJSONString(resp);
            log.info("sms send response:{}", responseString);
            // Check if the first message in SendStatusSet has "Ok" status
            if (resp.getSendStatusSet() != null && resp.getSendStatusSet().length > 0 
                && "Ok".equals(resp.getSendStatusSet()[0].getCode())) {
                // 请求成功
                resultFlag = true;
            }
        } catch (TencentCloudSDKException e) {
            log.error("Send SMS failed", e);
            resultFlag = false;
        }
        return resultFlag;
    }
    /**
     * 发送验证码短信
     *
     * @param phoneNumber 手机号码
     * @param code 验证码
     * @return 发送结果
     */
    public SendSmsResponse sendVerificationCode(String phoneNumber, String code) {
        return sendSms(phoneNumber, code);
    }
} 