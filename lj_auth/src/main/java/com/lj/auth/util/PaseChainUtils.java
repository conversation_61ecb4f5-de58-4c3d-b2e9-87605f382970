package com.lj.auth.util;

import com.lj.auth.domain.Result.TransactionSimpleInfo;
import org.web3j.crypto.*;
import org.web3j.rlp.RlpDecoder;
import org.web3j.rlp.RlpList;
import org.web3j.rlp.RlpString;
import org.web3j.utils.Numeric;

import java.math.BigInteger;

/**
 * <AUTHOR>
 * @describe
 */
public class PaseChainUtils {

    /**
     * 解析签名交易数据并返回交易信息对象
     * @param signedData 签名的交易数据（十六进制字符串）
     * @return TransactionInfo 交易信息对象
     */
    public static TransactionSimpleInfo parseSignedTransaction(String signedData) {
        TransactionSimpleInfo txInfo = new TransactionSimpleInfo();

        if (signedData == null || signedData.trim().isEmpty()) {
            txInfo.setErrorMessage("签名数据不能为空");
            return txInfo;
        }

        try {
            // 确保数据格式正确
            String cleanSignedData = signedData.startsWith("0x") ? signedData : "0x" + signedData;

            // 解析交易数据
            parseTransactionData(cleanSignedData, txInfo);

            if (txInfo.isSuccess()) {
                // 恢复发送方地址
                String fromAddress = recoverFromAddress(txInfo, cleanSignedData);
                txInfo.setFrom(fromAddress);

                if (fromAddress == null || fromAddress.isEmpty()) {
                    txInfo.setSuccess(false);
                    txInfo.setErrorMessage("无法恢复发送方地址");
                }
            }

        } catch (Exception e) {
            txInfo.setSuccess(false);
            txInfo.setErrorMessage("解析交易失败: " + e.getMessage());
        }

        return txInfo;
    }

    private static void parseTransactionData(String signedData, TransactionSimpleInfo txInfo) {
        try {
            // 解码十六进制字符串
            byte[] signedBytes = Numeric.hexStringToByteArray(signedData);

            // 解码 RLP 数据
            RlpList rlpList = RlpDecoder.decode(signedBytes);
            RlpList values = (RlpList) rlpList.getValues().get(0);

            // 提取交易字段
            txInfo.setNonce(((RlpString) values.getValues().get(0)).asPositiveBigInteger());
            txInfo.setGasPrice(((RlpString) values.getValues().get(1)).asPositiveBigInteger());
            txInfo.setGasLimit(((RlpString) values.getValues().get(2)).asPositiveBigInteger());

            String to = ((RlpString) values.getValues().get(3)).asString();
            txInfo.setTo(to.isEmpty() ? null : to);

            txInfo.setValue(((RlpString) values.getValues().get(4)).asPositiveBigInteger());

            String data = ((RlpString) values.getValues().get(5)).asString();
            txInfo.setData(data.isEmpty() ? "0x" : data);

            // 提取签名数据
            byte[] vBytes = ((RlpString) values.getValues().get(6)).getBytes();
            byte[] rBytes = ((RlpString) values.getValues().get(7)).getBytes();
            byte[] sBytes = ((RlpString) values.getValues().get(8)).getBytes();

            BigInteger v = new BigInteger(1, vBytes);
            BigInteger r = new BigInteger(1, rBytes);
            BigInteger s = new BigInteger(1, sBytes);

            // 确定 Chain ID 和 Recovery ID
            BigInteger chainId = extractChainId(v);
            int recoveryId = calculateRecoveryId(v, chainId);

            txInfo.setChainId(chainId);
            txInfo.setSignature(new TransactionSimpleInfo.SignatureInfo(v, r, s, recoveryId));

            // 计算交易哈希
            String txHash = calculateTransactionHash(signedData);
            txInfo.setTransactionHash(txHash);

            txInfo.setSuccess(true);

        } catch (Exception e) {
            txInfo.setSuccess(false);
            txInfo.setErrorMessage("解析RLP数据失败: " + e.getMessage());
        }
    }

    private static BigInteger extractChainId(BigInteger v) {
        // EIP-155: v = chainId * 2 + 35 + {0,1}
        if (v.compareTo(BigInteger.valueOf(35)) >= 0) {
            return v.subtract(BigInteger.valueOf(35)).divide(BigInteger.valueOf(2));
        }
        // 传统格式 v = 27 或 28
        return BigInteger.ZERO;
    }

    private static int calculateRecoveryId(BigInteger v, BigInteger chainId) {
        if (chainId.equals(BigInteger.ZERO)) {
            // 传统格式
            return v.intValue() - 27;
        } else {
            // EIP-155 格式
            return v.subtract(chainId.multiply(BigInteger.valueOf(2))).subtract(BigInteger.valueOf(35)).intValue();
        }
    }

    private static String recoverFromAddress(TransactionSimpleInfo txInfo, String signedData) {
        try {
            // 构建交易对象
            RawTransaction rawTransaction = RawTransaction.createTransaction(
                    txInfo.getNonce(),
                    txInfo.getGasPrice(),
                    txInfo.getGasLimit(),
                    txInfo.getTo(),
                    txInfo.getValue(),
                    txInfo.getData()
            );

            // 编码交易用于哈希计算
            byte[] encodedTransaction;
            if (txInfo.getChainId().equals(BigInteger.ZERO)) {
                encodedTransaction = TransactionEncoder.encode(rawTransaction);
            } else {
                encodedTransaction = TransactionEncoder.encode(rawTransaction, txInfo.getChainId().longValue());
            }

            // 计算交易哈希
            byte[] transactionHash = Hash.sha3(encodedTransaction);

            // 恢复公钥
            TransactionSimpleInfo.SignatureInfo sig = txInfo.getSignature();
            BigInteger publicKey = Sign.recoverFromSignature(
                    sig.getRecoveryId(),
                    new ECDSASignature(sig.getR(), sig.getS()),
                    transactionHash
            );

            if (publicKey != null) {
                return "0x" + Keys.getAddress(publicKey);
            }

        } catch (Exception e) {
            // 如果失败，尝试所有可能的 recovery ID
            return tryAllRecoveryIds(txInfo, signedData);
        }

        return null;
    }

    private static String tryAllRecoveryIds(TransactionSimpleInfo txInfo, String signedData) {
        try {
            RawTransaction rawTransaction = RawTransaction.createTransaction(
                    txInfo.getNonce(), txInfo.getGasPrice(), txInfo.getGasLimit(),
                    txInfo.getTo(), txInfo.getValue(), txInfo.getData()
            );

            byte[] encodedTransaction;
            if (txInfo.getChainId().equals(BigInteger.ZERO)) {
                encodedTransaction = TransactionEncoder.encode(rawTransaction);
            } else {
                encodedTransaction = TransactionEncoder.encode(rawTransaction, txInfo.getChainId().longValue());
            }

            byte[] transactionHash = Hash.sha3(encodedTransaction);
            TransactionSimpleInfo.SignatureInfo sig = txInfo.getSignature();

            // 尝试所有可能的 recovery ID
            for (int i = 0; i < 4; i++) {
                try {
                    BigInteger publicKey = Sign.recoverFromSignature(
                            i, new ECDSASignature(sig.getR(), sig.getS()), transactionHash
                    );
                    if (publicKey != null) {
                        // 更新正确的 recovery ID
                        txInfo.setSignature(new TransactionSimpleInfo.SignatureInfo(sig.getV(), sig.getR(), sig.getS(), i));
                        return "0x" + Keys.getAddress(publicKey);
                    }
                } catch (Exception ignored) {
                }
            }
        } catch (Exception ignored) {
        }
        return null;
    }

    private static String calculateTransactionHash(String signedData) {
        try {
            byte[] signedBytes = Numeric.hexStringToByteArray(signedData);
            byte[] hash = Hash.sha3(signedBytes);
            return Numeric.toHexString(hash);
        } catch (Exception e) {
            return null;
        }
    }
}
