package com.lj.auth.util;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;

/**
 * @author: wxm
 * @description:
 * @date: 2024/4/17 11:26
 */
public class MovieUtil {

    private static String appSecret = "3a6aeeba479c61752a85fc2c059f9330812ef38f25ae0355d5f490eb1656a4e5";

    /**
     * 获取签名后的参数map
     * 
     * @return
     * @throws Exception
     */
    public static Map<String, Object> getSignedParamsMap(Map<String, Object> paramsMap) {
        try {
            // 定义需要MD5加密的字符串
            StringBuffer originStr = new StringBuffer();
            // 使用TreeMap进行排序
            Map<String, Object> sortParamsMap = new TreeMap<>(paramsMap);
            for (String keyStr : sortParamsMap.keySet()) {
                originStr.append(keyStr);
                originStr.append("=");
                if (StrUtil.isNotBlank(keyStr)) {
                    originStr.append(sortParamsMap.get(keyStr));
                }
                originStr.append("&");
            }
            String assembleStr = originStr.append("key=").append(appSecret).toString();
//            System.out.println("ping'jie"+assembleStr);
            String sign = md5(assembleStr);
            paramsMap.put("sign", sign.toUpperCase());
            return paramsMap;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static Map<String, Object> getSignedParamsMap(Map<String, Object> paramsMap, String appSecret) {
        try {
            // 定义需要MD5加密的字符串
            StringBuffer originStr = new StringBuffer();
            // 使用TreeMap进行排序
            Map<String, Object> sortParamsMap = new TreeMap<>(paramsMap);
            for (String keyStr : sortParamsMap.keySet()) {
                originStr.append(keyStr);
                originStr.append("=");
                if (StrUtil.isNotBlank(keyStr)) {
                    originStr.append(sortParamsMap.get(keyStr));
                }
                originStr.append("&");
            }
            String assembleStr = originStr.append("key=").append(appSecret).toString();
//            System.out.println("拼接:"+assembleStr);
//            System.out.println("1111111");
            String sign = md5(assembleStr);
            paramsMap.put("sign", sign.toUpperCase());
            return paramsMap;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static String md5(String data) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] md5 = md.digest(data.getBytes(StandardCharsets.UTF_8));

            // 将处理后的字节转成 16 进制，得到最终 32 个字符
            StringBuilder sb = new StringBuilder();
            for (byte b : md5) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        return "";
    }

    public static void main(String[] args) {
        List<String> list= Arrays.asList("did",
                "phoneNumber",
                "nickName",
                "didDocument",
                "headPortrait");
        String secret="f1a6306c41720787da6540a5ffe0ad6cf3e4dc1e56ae4a4744adbd6630d8f429";
        long time = DateUtil.date().getTime();
        System.out.println(time);
        Map<String, Object> params = new TreeMap<>();
        params.put("sn", "3_55245425");
//        params.put("did", "did:ctid:bsn:A1D9FCB4DE7DB385D5C084EA7C46B28B408B1BF6B3A400E028AD1FD887096288");
        params.put("noticeUrl", "http://127.0.0.1:10010/lj-auth/test/f");
        params.put("currentTimestamp", time);

        System.out.println(MovieUtil.getSignedParamsMap(params,
                secret));

    }

}
