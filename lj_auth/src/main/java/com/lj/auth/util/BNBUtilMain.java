package com.lj.auth.util;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;

import org.web3j.abi.FunctionEncoder;
import org.web3j.abi.FunctionReturnDecoder;
import org.web3j.abi.TypeReference;
import org.web3j.abi.datatypes.Address;
import org.web3j.abi.datatypes.Function;
import org.web3j.abi.datatypes.Type;
import org.web3j.abi.datatypes.Utf8String;
import org.web3j.abi.datatypes.generated.Uint256;
import org.web3j.crypto.*;
import org.web3j.protocol.Web3j;
import org.web3j.protocol.core.DefaultBlockParameter;
import org.web3j.protocol.core.DefaultBlockParameterName;
import org.web3j.protocol.core.methods.request.Transaction;
import org.web3j.protocol.core.methods.response.*;
import org.web3j.protocol.http.HttpService;
import org.web3j.tx.ChainId;
import org.web3j.utils.Convert;
import org.web3j.utils.Numeric;

import com.lj.auth.exception.ServiceException;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;

/**
 * 描述： 创建人: CFJ 创建时间: 2024/03/06
 */
public class BNBUtilMain {
    private static long chainId;

    private static Web3j web3j;

    static {
        chainId = Long.parseLong("240315");
        web3j = Web3j.build(new HttpService("http://***********:8560"));
    }

    public BNBUtilMain(Web3j web3j) {
        this.web3j = web3j;
    }

    public static void main(String[] args) throws IOException, InterruptedException {
        // String fromAddress="******************************************";
        // String contractAddress="******************************************";
        // String baseUri = getBaseUri(fromAddress, contractAddress);
        // System.out.println(baseUri);
        // EthBlockNumber ethBlockNumber = ethBlockNumber();
        // EthGetTransactionReceipt ethGetTransactionReceipt =
        // ethGetTransactionReceipt("0x29bbb3baac50618376e50055e57903494263b8b9fa587abf2edd7a30f3470f47");
        // System.out.println("11111111111");
        // EthGetCode send = web3j
        // .ethGetCode("******************************************", DefaultBlockParameter.valueOf("latest"))
        // .send();
        // BigInteger blockNumber = ethBlockNumber.getBlockNumber();
        // System.out.println(blockNumber);

        // EthTransaction ethTransaction =
        // ethGetTransactionByHash("0x86263735960c0ccfa98265506b58331e2f37beecf63671d8eeca9ab16b2ad842");
        // System.out.println(ethTransaction);
        // System.out.println(blockNumber);
        // eb47b13f34987fd56f689f0951d8618134f43513707c50ab04cb52c0a3f0d763
        // e7adbee588b0e883bde9878fe8b5a0e98081

        // EthBlock ethBlock = ethGetBlockByNumber(new BigInteger("2142615"), true);
        // EthBlock.Block block = ethBlock.getBlock();
        // List<EthBlock.TransactionResult> transactions = block.getTransactions();
        //
        // JSONArray transactions1 = JSON.parseArray(JSON.toJSONString(transactions));
        //
        // System.out.println(transactions1);
        // 0x6c6b6a6c6b6a6c6d6d616173646c6b61736b
        // String aa="33000000000000000000";
        // System.out.println(aa.length());

        // EthGetBalance ethGetBalance = ethGetBalance("******************************************");
        // System.out.println("第一次"+ethGetBalance.getBalance());
        // //
        // System.out.println((new BigDecimal(ethGetBalance.getBalance())
        // .divide(new BigDecimal(BigInteger.TEN.pow(18)), 4, RoundingMode.HALF_UP)).stripTrailingZeros());

        // String aaa = toHex("陈凤岗HELO word123?)*.");
        // System.out.println(aaa);
        //
        // System.out.println(toChinese("964851e45c9748454c4f20776f72643132333f292a2e"));
        //
        String hash = transaction("******************************************",
            "362de9dc588ffad9e02fefd1042e6b4707af7302d8db188a41e9662a7ef7c928", new BigDecimal("0.002"), "");
        System.out.println(hash);

        // EthTransaction ethTransaction =
        // ethGetTransactionByHash("0x2905b58a11b84b3fa6c0bec36924a1dd38db8a0e1be8ab6821e03884ae6d5734");
        // org.web3j.protocol.core.methods.response.Transaction result = ethTransaction.getResult();
        // BigInteger blockNumber = result.getBlockNumber();
        // System.out.println(blockNumber);

        // // 在这里编写你的程序代码
        // String s = signTransaction("******************************************",
        // "9be153b98de4d15b9f3f18688afa9fee4bc6c31ef4879b63f1f42e5a6655c2e5", new BigDecimal("5"),
        // "964851e45c97");
        // System.out.println(transaction(s));

        // Map<String, String> blockInfo = getBlockInfo("0x41e818c2c6d9287ee471961b269eb50bdbf12b71e9fa6494ca359cc15656b320");
        // System.out.println(blockInfo);

    }


    /**
     * 获取token 高度
     * @param ylPlatformAccountAddress  平台账户地址
     * @param contractAddress           NFT合约地址
     * @return
     * @throws Exception
     */
    public static String getNFTOwner (String ylPlatformAccountAddress, String contractAddress,Integer tokenId){
        String owner = null;
        try {
            String functionName = "ownerOf";

            Uint256 uinTokenId=new Uint256(BigInteger.valueOf(tokenId));

            List<Type> inputParameters = new ArrayList<>();
            inputParameters.add(uinTokenId);
            List<TypeReference<?>> outputParameters = new ArrayList<>();
            outputParameters.add(new TypeReference<Address>() {
            });
            Function function = new Function(functionName, inputParameters, outputParameters);
            List<Type> types = BNBUtilMain.sendCallTransaction(ylPlatformAccountAddress, contractAddress, function);
            Address tem = (Address) types.get(0);
            owner = tem.getValue();
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("获取token 所有者失败");
        }
        return owner;
    }

    public static String getTransactionTime(String hash){
        String strTime=null;
        try {
            BigInteger blockNumber = BNBUtilMain.getBlockNumber(hash);
            EthBlock ethBlock = BNBUtilMain.ethGetBlockByNumber(blockNumber, false);
            EthBlock.Block block = ethBlock.getBlock();
            BigInteger timestamp = block.getTimestamp();
            strTime = DateUtil.date(timestamp.multiply(new BigInteger("1000")).longValue()).toString("yyyy-MM-dd HH:mm:ss");
        } catch (IOException e) {
            e.printStackTrace();
            throw new ServiceException("获取交易时间失败");
        }
        return strTime;
    }


    public static  Date  getTransactionDate(String hash){
        Date date=null;
        try {
            BigInteger blockNumber = BNBUtilMain.getBlockNumber(hash);
            EthBlock ethBlock = BNBUtilMain.ethGetBlockByNumber(blockNumber, false);
            EthBlock.Block block = ethBlock.getBlock();
            BigInteger timestamp = block.getTimestamp();
            date = DateUtil.date(timestamp.multiply(new BigInteger("1000")).longValue());
        } catch (IOException e) {
            e.printStackTrace();
            throw new ServiceException("获取交易时间失败");
        }
        return date;
    }



    public static BigInteger getBlockNumber(String hash) {
        EthTransaction ethTransaction = null;
        try {
            ethTransaction = ethGetTransactionByHash(hash);
        } catch (IOException e) {
            e.printStackTrace();
        }
        org.web3j.protocol.core.methods.response.Transaction result = ethTransaction.getResult();
        BigInteger blockNumber = result.getBlockNumber();
        return blockNumber;
    }

    public static Map<String, String> getBlockInfo(String hash) {
        Map<String, String> result = new HashMap<>();
        EthTransaction ethTransaction = null;
        BigInteger blockNumber = null;
        try {
            ethTransaction = ethGetTransactionByHash(hash);
            org.web3j.protocol.core.methods.response.Transaction transactionInfo = ethTransaction.getResult();
            String to = transactionInfo.getTo();
            result.put("to", to);
            blockNumber = transactionInfo.getBlockNumber();
            result.put("blockNumber", blockNumber.toString());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    /**
     * @param
     * @return 返回节点每秒用于挖掘的哈希数。仅当节点正在挖矿时才适用。
     * <AUTHOR>
     * @date 2022/03/09 16:30
     */
    public static EthHashrate ethHashrate() throws IOException {
        EthHashrate send = web3j.ethHashrate().send();
        return send;
    }

    /**
     * @param
     * @return 返回当前gas价格以wei为单位
     * <AUTHOR>
     * @date 2022/03/09 16:27
     */
    public static EthGasPrice ethGasPrice() throws IOException {
        final EthGasPrice send = web3j.ethGasPrice().send();
        return send;
    }

    /**
     * @param
     * @return 返回当前最新块编号
     * <AUTHOR>
     * @date 2022/03/09 16:32
     */
    public static EthBlockNumber ethBlockNumber() throws IOException {
        EthBlockNumber send = web3j.ethBlockNumber().send();
        return send;
    }

    /**
     * @param xh 地址字符串
     * @return 返回给定地址余额
     * <AUTHOR>
     * @date 2022/03/09 16:36
     */
    public static EthGetBalance ethGetBalance(String xh) throws IOException {
        EthGetBalance latest = web3j.ethGetBalance(xh, DefaultBlockParameter.valueOf("latest")).send();
        return latest;
    }

    /**
     * @param xh 地址， BigInteger 下标 ， BigInteger 块高
     * @param index 地址， BigInteger 下标 ， BigInteger 块高
     * @return 从给定地址的存储位置返回值。
     * <AUTHOR>
     * @date 2022/03/09 16:46
     */
    public static EthGetStorageAt ethGetStorageAt(String xh, BigInteger index, BigInteger ID)
        throws IOException {
        EthGetStorageAt send = web3j.ethGetStorageAt(xh, index, DefaultBlockParameter.valueOf(ID)).send();
        return send;
    }

    /**
     * @param xh 交易地址
     * @return 返回从某个地址发送的交易数
     * <AUTHOR>
     * @date 2022/03/08 10:43
     */
    public static EthGetTransactionCount ethGetTransactionCount(String xh) throws IOException {
        EthGetTransactionCount latest =
            web3j.ethGetTransactionCount(xh, DefaultBlockParameter.valueOf("latest")).send();
        return latest;
    }

    /**
     * @param xh 块 hash
     * @return 返回具有给定区块哈希的区块中的交易数
     * <AUTHOR>
     * @date 2022/03/08 10:43
     */
    public static EthGetBlockTransactionCountByHash ethGetBlockTransactionCountByHash(String xh)
        throws IOException {
        EthGetBlockTransactionCountByHash send = web3j.ethGetBlockTransactionCountByHash(xh).send();
        return send;
    }

    /**
     * @param xh 块高
     * @return 返回区块中具有给定区块编号的交易数
     * <AUTHOR>
     * @date 2022/03/08 10:43
     */
    public static EthGetBlockTransactionCountByNumber ethGetBlockTransactionCountByNumber(BigInteger xh)
        throws IOException {
        EthGetBlockTransactionCountByNumber send =
            web3j.ethGetBlockTransactionCountByNumber(DefaultBlockParameter.valueOf(xh)).send();
        return send;
    }

    /**
     * @param xh 合约地址 ,BigInteger 块高
     * @return 合约代码
     * <AUTHOR>
     * @date 2022/03/09 17:01
     */

    public static EthGetCode ethGetCode(String xh, BigInteger addr) throws IOException {
        EthGetCode send = web3j.ethGetCode(xh, DefaultBlockParameter.valueOf(addr)).send();
        return send;
    }

    // TODO
    public static EthSendTransaction ethSendRawTransaction(String signed) throws IOException {
        EthSendTransaction send = web3j.ethSendRawTransaction(signed).send();
        return send;
    }

    public static EthCall ethCall(Transaction xh, DefaultBlockParameter defaultBlockParameter)
        throws IOException {
        return web3j.ethCall(xh, defaultBlockParameter).send();
    }

    /**
     * @param xh 交易请求 BigInteger 块编号
     * @return 立即执行新消息调用，而无需在区块链上创建事务。
     * <AUTHOR>
     * @date 2022/03/10 10:54
     */
    public static EthCall ethCall(Transaction xh, BigInteger id) throws IOException {
        EthCall send = web3j.ethCall(xh, DefaultBlockParameter.valueOf(id)).send();
        return send;
    }

    /**
     * @param xh 块hash boolean true 查询块中所有交易信息 false 查询块信息
     * @return 块信息
     * <AUTHOR>
     * @date 2022/03/08 10:43
     */
    public static EthBlock ethGetBlockByHash(String xh, boolean flag) throws IOException {
        EthBlock send = web3j.ethGetBlockByHash(xh, flag).send();
        return send;

    }

    /**
     * @param xh 块高 boolean true 查询块中所有交易信息 false 查询块信息
     * @return 快信息
     * <AUTHOR>
     * @date 2022/03/08 10:43
     */
    public static EthBlock ethGetBlockByNumber(BigInteger xh, boolean flag) throws IOException {
        EthBlock send = web3j.ethGetBlockByNumber(DefaultBlockParameter.valueOf(xh), flag).send();
        return send;
    }

    /**
     * @param xh 交易hash
     * @return 交易信息
     * <AUTHOR>
     * @date 2022/03/08 10:43
     */
    public static EthTransaction ethGetTransactionByHash(String xh) throws IOException {
        EthTransaction send = web3j.ethGetTransactionByHash(xh).send();
        return send;
    }

    /**
     * @param xh 块hash, BigInteger 交易下标
     * @return 按块编号和交易索引位置返回有关交易的信息。
     * <AUTHOR>
     * @date 2022/03/09 18:29
     */

    public static EthTransaction ethGetTransactionByBlockHashAndIndex(String xh, BigInteger index)
        throws IOException {
        EthTransaction send = web3j.ethGetTransactionByBlockHashAndIndex(xh, index).send();
        return send;
    }

    /**
     * @param xh 块高，BigInteger 交易下标
     * @return 按块编号和交易索引位置返回有关交易的信息。
     * <AUTHOR>
     * @date 2022/03/09 18:24
     */

    public static EthTransaction ethGetTransactionByBlockNumberAndIndex(BigInteger xh, BigInteger index)
        throws IOException {
        EthTransaction send =
            web3j.ethGetTransactionByBlockNumberAndIndex(DefaultBlockParameter.valueOf(xh), index).send();
        return send;
    }

    /**
     * @param xh 交易hash
     * @return 按事务哈希返回事务的接收。
     * <AUTHOR>
     * @date 2022/03/08 10:43
     */
    public static EthGetTransactionReceipt ethGetTransactionReceipt(String xh) throws IOException {
        EthGetTransactionReceipt send = web3j.ethGetTransactionReceipt(xh).send();
        return send;
    }

    // TODO
    public static EthBlock ethGetUncleByBlockHashAndIndex(String xh, BigInteger index) throws IOException {
        EthBlock send = web3j.ethGetUncleByBlockHashAndIndex(xh, index).send();
        return send;
    }

    // TODO
    public static EthBlock ethGetUncleByBlockNumberAndIndex(BigInteger xh, BigInteger index)
        throws IOException {
        EthBlock send = web3j.ethGetUncleByBlockNumberAndIndex((DefaultBlockParameter)xh, index).send();
        return send;
    }

    /**
     * @param xh 块hash
     * @return 从与给定块哈希匹配的块中返回块中的叔叔数。
     * <AUTHOR>
     * @date 2022/03/10 08:38
     */
    public static EthGetUncleCountByBlockHash ethGetUncleCountByBlockHash(String xh) throws IOException {
        EthGetUncleCountByBlockHash send = web3j.ethGetUncleCountByBlockHash(xh).send();
        return send;
    }

    /**
     * @param xh 块高
     * @return 从与给定块编号匹配的块中返回块中的叔叔数。
     * <AUTHOR>
     * @date 2022/03/10 08:32
     */
    public static EthGetUncleCountByBlockNumber ethGetUncleCountByBlockNumber(BigInteger xh)
        throws IOException {
        EthGetUncleCountByBlockNumber send =
            web3j.ethGetUncleCountByBlockNumber(DefaultBlockParameter.valueOf(xh)).send();
        return send;
    }

    /**
     * @param tr 交易对象
     * @return 生成并返回允许事务完成所需的 gas 量的估计值。
     * <AUTHOR>
     * @date 2022/03/10 10:45
     */

    public static EthEstimateGas ethEstimateGas(Transaction tr) throws IOException {
        EthEstimateGas send = web3j.ethEstimateGas(tr).send();
        return send;
    }

    /**
     * @param
     * @return 返回节点的同步状态
     * <AUTHOR>
     * @date 2022/03/10 08:24
     */
    public static EthSyncing ethSyncing() throws IOException {
        EthSyncing result = web3j.ethSyncing().send();
        return result;
    }

    // 获取签名
    public static String ethGetSign(String fromAddress, BigInteger gasLimit, String toAddress,
        BigInteger value, String data, String privateKey) throws IOException {
        Long chainId = 202202l;
        BigInteger nonce;
        EthGetTransactionCount ethGetTransactionCount = null;
        try {
            ethGetTransactionCount =
                web3j.ethGetTransactionCount(fromAddress, DefaultBlockParameterName.LATEST).send();
        } catch (IOException e) {
            e.printStackTrace();
        }
        if (ethGetTransactionCount == null)
            return null;
        nonce = ethGetTransactionCount.getTransactionCount();
        BigInteger gasPrice = web3j.ethGasPrice().send().getGasPrice();
        BigInteger balance = getBalance(fromAddress);
        BigInteger total = gasLimit.multiply(gasPrice).add(value);
        if (balance.compareTo(total) < 0) {
            return "";
        }
        System.out.println("余额充足");
        String s = signTransaction(nonce, gasPrice, gasLimit, toAddress.toLowerCase(), value, data, chainId,
            privateKey);
        System.out.println("交易签名：" + s);
        return s;
    }

    // 主币交易
    public static String transaction(String signedData) {
        String hash = null;
        if (signedData != null) {
            try {
                EthSendTransaction ethSendTransaction = web3j.ethSendRawTransaction(signedData).send();
                System.out.println("hash" + ethSendTransaction.getTransactionHash());
                hash = ethSendTransaction.getTransactionHash();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        System.out.println("交易hash:" + hash);
        return hash;
    }

    /**
     * 主币交易
     *
     *
     * @param toAddress 接收地址
     * @param privateKey 私钥
     * @param amount 数量
     */
    public static String transaction(String toAddress, String privateKey, BigDecimal amount, String data) {
        String fromAddress = getAddressByPrivateKey(privateKey);
        BigInteger nonce;
        EthGetTransactionCount ethGetTransactionCount = null;
        try {
            ethGetTransactionCount =
                web3j.ethGetTransactionCount(fromAddress, DefaultBlockParameterName.LATEST).send();
        } catch (IOException e) {
            e.printStackTrace();
        }
        if (ethGetTransactionCount == null)
            return null;
        nonce = ethGetTransactionCount.getTransactionCount();
        // BigInteger gasPrice = Convert.toWei(BigDecimal.valueOf(300), Convert.Unit.GWEI).toBigInteger();
        BigInteger gasPrice = null;
        try {
            gasPrice = BNBUtilMain.ethGasPrice().getGasPrice();
            System.out.println(gasPrice);
        } catch (IOException e) {
            e.printStackTrace();
        }
        gasPrice = Convert.toWei(BigDecimal.valueOf(20), Convert.Unit.GWEI).toBigInteger();
        // BigInteger gasLimit = BigInteger.valueOf(41000);
        BigInteger gasLimit = new BigInteger("300000");
        BigInteger value = Convert.toWei(amount, Convert.Unit.ETHER).toBigInteger();
        BigInteger balance = getBalance(fromAddress);
        BigInteger total = value.subtract(gasLimit.multiply(gasPrice));
        if (balance.compareTo(total) > 0) {
            System.out.println("余额充足");
        }
        String signedData;
        String hash = null;
        try {
            signedData = signTransaction(nonce, gasPrice, gasLimit, toAddress.toLowerCase(), value, data,
                chainId, privateKey);
            System.out.println("signedData:" + signedData);
            if (signedData != null) {
                EthSendTransaction ethSendTransaction = web3j.ethSendRawTransaction(signedData).send();
                System.out.println("hash: " + ethSendTransaction.getTransactionHash());
                hash = ethSendTransaction.getTransactionHash();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return hash;
    }

    /**
     * 能量补充
     * 
     * @param toAddress
     * @param privateKey
     * @param amount
     * @param data
     * @return {@link String }
     * <AUTHOR>
     * @date 2024/04/24
     */
    public static String transaction1(String toAddress, String privateKey, BigDecimal amount,
        BigInteger gasPrice, String data) {
        String fromAddress = getAddressByPrivateKey(privateKey);
        BigInteger nonce;
        EthGetTransactionCount ethGetTransactionCount = null;
        try {
            ethGetTransactionCount =
                web3j.ethGetTransactionCount(fromAddress, DefaultBlockParameterName.LATEST).send();
        } catch (IOException e) {
            e.printStackTrace();
        }
        if (ethGetTransactionCount == null)
            return null;
        nonce = ethGetTransactionCount.getTransactionCount();
        System.out.println("gasPrice:" + gasPrice);
        BigInteger gasLimit = new BigInteger("300000");
        BigInteger value = Convert.toWei(amount, Convert.Unit.ETHER).toBigInteger();
        BigInteger balance = getBalance(fromAddress);
        BigInteger total = value.subtract(gasLimit.multiply(gasPrice));
        if (balance.compareTo(total) > 0) {
            System.out.println("余额充足");
        }
        String signedData;
        String hash = null;
        try {
            signedData = signTransaction(nonce, gasPrice, gasLimit, toAddress.toLowerCase(), value, data,
                chainId, privateKey);
            System.out.println("signedData:" + signedData);
            if (signedData != null) {
                EthSendTransaction ethSendTransaction = web3j.ethSendRawTransaction(signedData).send();
                System.out.println("补发hash: " + ethSendTransaction.getTransactionHash());
                hash = ethSendTransaction.getTransactionHash();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return hash;
    }

    public static String signTransaction(String toAddress, String privateKey, BigDecimal amount,
        String data) {
        String fromAddress = getAddressByPrivateKey(privateKey);
        BigInteger nonce;
        EthGetTransactionCount ethGetTransactionCount = null;
        try {
            ethGetTransactionCount =
                web3j.ethGetTransactionCount(fromAddress, DefaultBlockParameterName.LATEST).send();
        } catch (IOException e) {
            e.printStackTrace();
        }
        if (ethGetTransactionCount == null)
            return null;
        nonce = ethGetTransactionCount.getTransactionCount();
        // BigInteger gasPrice = Convert.toWei(BigDecimal.valueOf(300), Convert.Unit.GWEI).toBigInteger();
        BigInteger gasPrice = Convert.toWei(BigDecimal.valueOf(10000), Convert.Unit.GWEI).toBigInteger();
        BigInteger gasLimit = BigInteger.valueOf(41000);
        BigInteger value = Convert.toWei(amount, Convert.Unit.ETHER).toBigInteger();
        BigInteger balance = getBalance(fromAddress);
        System.out.println("消耗前：" + balance);
        BigInteger total = value.subtract(gasLimit.multiply(gasPrice));
        if (balance.compareTo(total) > 0) {
            System.out.println("余额充足");
        }
        String signedData = null;
        try {
            signedData = signTransaction(nonce, gasPrice, gasLimit, toAddress.toLowerCase(), value, data,
                chainId, privateKey);
            System.out.println("signedData:" + signedData);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return signedData;
    }

    /**
     * 签名交易
     */
    public static String signTransaction(BigInteger nonce, BigInteger gasPrice, BigInteger gasLimit,
        String to, BigInteger value, String data, long chainId, String privateKey) throws IOException {
        byte[] signedMessage;
        RawTransaction rawTransaction =
            RawTransaction.createTransaction(nonce, gasPrice, gasLimit, to, value, data);

        if (privateKey.startsWith("0x")) {
            privateKey = privateKey.substring(2);
        }
        ECKeyPair ecKeyPair = ECKeyPair.create(new BigInteger(privateKey, 16));
        Credentials credentials = Credentials.create(ecKeyPair);

        if (chainId > ChainId.NONE) {
            signedMessage = TransactionEncoder.signMessage(rawTransaction, chainId, credentials);
        } else {
            signedMessage = TransactionEncoder.signMessage(rawTransaction, credentials);
        }

        String hexValue = Numeric.toHexString(signedMessage);
        return hexValue;
    }

    /**
     * 获取余额
     *
     * @param address 钱包地址
     * @return 余额
     */
    public static BigInteger getBalance(String address) {
        BigInteger balance = null;
        try {
            EthGetBalance ethGetBalance =
                web3j.ethGetBalance(address, DefaultBlockParameterName.LATEST).send();
            balance = ethGetBalance.getBalance();
        } catch (IOException e) {
            e.printStackTrace();
        }
        // System.out.println("address " + address + " balance " + balance + "wei");
        return balance;
    }

    // 通过私钥获取地址
    public static String getAddressByPrivateKey(String privateKey) {
        String address =
            Keys.toChecksumAddress(Keys.getAddress(ECKeyPair.create(Numeric.toBigInt(privateKey))));
        System.out.println("address:" + address);
        return address;
    }

    /**
     * 获取账号交易次数 nonce
     *
     * @param address 钱包地址
     * @return nonce
     */
    public static BigInteger getTransactionNonce(String address) {
        BigInteger nonce = BigInteger.ZERO;
        try {
            EthGetTransactionCount ethGetTransactionCount =
                web3j.ethGetTransactionCount(address, DefaultBlockParameterName.LATEST).send();
            nonce = ethGetTransactionCount.getTransactionCount();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return nonce;
    }

    public static String getBaseUri(String fromAddress, String contractAddress) throws IOException {
        String functionName = "baseURI";
        List<Type> inputParameters = new ArrayList<>();
        List<TypeReference<?>> outputParameters = new ArrayList<>();
        outputParameters.add(new TypeReference<Utf8String>() {});
        Function function = new Function(functionName, inputParameters, outputParameters);
        String encode = FunctionEncoder.encode(function);
        Transaction ethCallTransaction =
            Transaction.createEthCallTransaction(fromAddress, contractAddress, encode);
        EthCall ethCall = web3j.ethCall(ethCallTransaction, DefaultBlockParameterName.LATEST).send();
        String value = ethCall.getValue();
        List<Type> decode = FunctionReturnDecoder.decode(value, function.getOutputParameters());
        return (String)decode.get(0).getValue();
    }

    public static List<Type> sendCallTransaction(String from, String to, Function function) throws Exception {
        String encode = FunctionEncoder.encode(function);
        Transaction ethCallTransaction = Transaction.createEthCallTransaction(from, to, encode);
        EthCall ethCall = BNBUtilMain.ethCall(ethCallTransaction, DefaultBlockParameterName.LATEST);
        return FunctionReturnDecoder.decode(ethCall.getValue(), function.getOutputParameters());
    }

    public static String sendTransaction(String chainAccountAddress, String contractAddress,
        String privateKey, Function function, BigInteger GAS_LIMIT) throws Exception {
        Credentials credentials = Credentials.create(privateKey);
        String encode = FunctionEncoder.encode(function);
        BigInteger nonce = BNBUtilMain.getTransactionNonce(chainAccountAddress);
        BigInteger gasPrice = BNBUtilMain.ethGasPrice().getGasPrice();
        // BigInteger gasPrice = BigInteger.ZERO;
        RawTransaction transaction =
            RawTransaction.createTransaction(nonce, gasPrice, GAS_LIMIT, contractAddress, encode);
        byte[] signMessage = TransactionEncoder.signMessage(transaction, credentials);
        String hexValue = Numeric.toHexString(signMessage);
        EthSendTransaction ethSendTransaction = BNBUtilMain.ethSendRawTransaction(hexValue);
        String result = ethSendTransaction.getResult();
        String transactionHash = ethSendTransaction.getTransactionHash();
        return transactionHash;
    }

    public static String sendTransaction(String contractAddress, String privateKey, Function function,
        BigInteger GAS_LIMIT) throws Exception {
        String addressByPrivateKey = BNBUtilMain.getAddressByPrivateKey(privateKey);
        Credentials credentials = Credentials.create(privateKey);
        String encode = FunctionEncoder.encode(function);
        BigInteger nonce = BNBUtilMain.getTransactionNonce(addressByPrivateKey);
        BigInteger gasPrice = BNBUtilMain.ethGasPrice().getGasPrice();
        // BigInteger gasPrice = BigInteger.ZERO;
        RawTransaction transaction =
            RawTransaction.createTransaction(nonce, gasPrice, GAS_LIMIT, contractAddress, encode);
        byte[] signMessage = TransactionEncoder.signMessage(transaction, credentials);
        String hexValue = Numeric.toHexString(signMessage);
        return BNBUtilMain.ethSendRawTransaction(hexValue).getTransactionHash();
    }

    /**
     * @param
     * @return 16进制转10进制
     * <AUTHOR>
     * @date 2022/03/07 18:24
     */
    public static Long conversionDecimal(String xh) {
        long re = 0;
        if (xh.contains("0x") && StrUtil.isNotBlank(xh)) {
            re = Long.parseLong(xh.substring(2), 16);
        }
        return re;
    }

    public static String toHex(String chinese) throws UnsupportedEncodingException {
        StringBuilder hex = new StringBuilder();
        byte[] bytes = chinese.getBytes("UTF-8");
        for (byte b : bytes) {
            hex.append(String.format("%02x", b));
        }
        return hex.toString();
    }

    public static String toChinese(String hex) throws UnsupportedEncodingException {
        byte[] bytes = new byte[hex.length() / 2];
        for (int i = 0; i < hex.length(); i += 2) {
            bytes[i / 2] = (byte)Integer.parseInt(hex.substring(i, i + 2), 16);
        }
        return new String(bytes, "UTF-8");
    }

}
