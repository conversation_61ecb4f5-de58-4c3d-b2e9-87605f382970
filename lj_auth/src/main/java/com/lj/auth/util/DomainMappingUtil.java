package com.lj.auth.util;

import org.web3j.abi.FunctionEncoder;
import org.web3j.abi.FunctionReturnDecoder;
import org.web3j.abi.TypeReference;
import org.web3j.abi.datatypes.*;
import org.web3j.abi.datatypes.generated.Bytes32;
import org.web3j.abi.datatypes.generated.Uint256;
import org.web3j.abi.datatypes.generated.Uint64;
import org.web3j.crypto.*;
import org.web3j.protocol.Web3j;
import org.web3j.protocol.core.DefaultBlockParameterName;
import org.web3j.protocol.core.methods.request.Transaction;
import org.web3j.protocol.core.methods.response.EthCall;
import org.web3j.protocol.core.methods.response.TransactionReceipt;
import org.web3j.protocol.http.HttpService;
import org.web3j.utils.Numeric;

import java.io.IOException;
import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * @Author: zjd
 * @Description:
 * @Date:2024/04/08 13:58
 **/
public class DomainMappingUtil {

    private static long chainId;

    private static Web3j web3j;

    static {
        chainId = Long.parseLong(PropertiesRead.getYmlStringForActive("ylChainId"));
        web3j = Web3j.build(new HttpService(PropertiesRead.getYmlStringForActive("ylznlCurl")));
    }

    public static void main(String[] args) throws Exception {
        String chainName = "灵戒链";

        String chainAccountAddress = "******************************************";
        String privateKey = "303f461793c46fd3a755ca10f4fa4241785994f45f0c8eb0e4fe88764a0607da";
        String privateKey2 = "4536f6b5fbf245284b7dde8225b48f901c97be8d19a4782284ab1e60e8cab432";
        Credentials credentials = Credentials.create(privateKey);
        Credentials credentials2 = Credentials.create(privateKey2);
        // 注册管理器合约地址
        String contractAddress = "0x469c2630fF55687Ef2937dCe57DE197974255070";
        // 注册器合约地址
        String registryContractAddress = "0xbdDBfBBa4A481f794687Eb8d856dDa8c83925d39";
        //向注册管理合约  设置 注册合约
        String s = insertRegistryByManager(chainId, chainName, credentials, contractAddress);
        System.out.println(s);
        //查询 注册表合约地址
         String s1 = queryRegistry(chainId, chainAccountAddress, contractAddress);
         System.out.println(s1);
        //设置 域名   解析器合约   和拥有着
        String s2 = setRecordByManager(chainId, "hello.web3", "0x9b39b2ae91a8a10e07ec9546b26f12556bf61c55", "0x1B7075c626741a70dc6A593b11dBbcca0c4E92f2", credentials, contractAddress);
        System.out.println(s2);
        TransactionReceipt transactionReceipt = getTransactionReceipt(s2);
        System.out.println(transactionReceipt.isStatusOK());

        // 返回合约 所有者 地址
        String owner = getOwner("hello.web3", chainAccountAddress, registryContractAddress);
        System.out.println(owner);
          //查询指定域名 是否设置了解析记录
        String s3 = recordExists("hello.web3", chainAccountAddress, registryContractAddress);
        System.out.println(s3);
        // 获取  解析器合约地址
        String resolver = getResolver("hello.web3", chainAccountAddress, registryContractAddress);
        System.out.println(resolver);
        /*String s = setOwner("hello.web3", "******************************************", credentials2, registryContractAddress);
        System.out.println(s);*/
        /*String s = setAddr("hello.web3", "0x9b39b2ae91a8a10e07ec9546b26f12556bf61c55", credentials2, resolver);
        System.out.println(s);*/
        /*String addr = addr("hello.web3", chainAccountAddress, resolver);
        System.out.println(addr);*/
        /*String s = setText("hello.web3", "phone", "110", credentials, resolver);
        System.out.println(s);*/
        /*String phone = text("hello.web3", "phone", chainAccountAddress, resolver);
        System.out.println(phone);*/
        /*String s = approve("hello.web3", "0x9b39b2ae91a8a10e07ec9546b26f12556bf61c55", true, credentials, resolver);
        System.out.println(s);*/
        /*String approved = isApproved(chainAccountAddress, "hello.web3", "0x9b39b2ae91a8a10e07ec9546b26f12556bf61c55", "0x9b39b2ae91a8a10e07ec9546b26f12556bf61c55", resolver);
        System.out.println(approved);*/
        /*String s = clearRecords("hello.web3", credentials, resolver);
        System.out.println(s);*/
        /*String s1 = recordVersions("hello.web3", "0x9b39b2ae91a8a10e07ec9546b26f12556bf61c55", resolver);
        System.out.println(s1);*/

    }

    // --------------------------------------RegistryManager.sol--------------------------------------------

    /**
     * 通过管理员新增注册器。
     * 
     * @param chainId 链的ID，表示特定的链。
     * @param chainName 链的名称，用于标识链的名称。
     * @param credentials 管理员的凭证，用于授权操作。
     * @param contractAddress 合约的地址，表示要操作的智能合约所在的位置。
     * @return 返回调用合约方法的交易的哈希值。
     * @throws IOException 如果在与区块链交互过程中出现IO错误。
     */
    public static String insertRegistryByManager(long chainId, String chainName, Credentials credentials,
        String contractAddress) throws IOException {
        String functionName = "insertRegistry";
        List<Type> inputParams = new ArrayList<>();
        List<TypeReference<?>> outputParams = new ArrayList<>();
        inputParams.add(new Utf8String(chainName));
        inputParams.add(new Uint256(chainId));
        Function function = new Function(functionName, inputParams, outputParams);
        return invokeContract(function, credentials, contractAddress);
    }

    /**
     * 查询链对应的注册器。 通过调用指定链上的智能合约，来查询注册器。
     *
     * @param chainId 链的ID，用于标识不同的区块链网络。
     * @param chainAccountAddress 在链上的账户地址，用于进行合约调用。
     * @param contractAddress 合约的地址，指定要调用的智能合约。
     * @return 返回从合约中查询到的注册器。
     * @throws IOException 如果在与区块链网络通信过程中出现错误，则抛出IOException。
     */
    public static String queryRegistry(long chainId, String chainAccountAddress, String contractAddress)
        throws IOException {
        String functionName = "queryRegistry";
        List<Type> inputParams = new ArrayList<>();
        List<TypeReference<?>> outputParams = new ArrayList<>();
        inputParams.add(new Uint256(chainId));
        outputParams.add(new TypeReference<Address>() {});
        Function function = new Function(functionName, inputParams, outputParams);
        EthCall ethCall = callContract(function, chainAccountAddress, contractAddress);
        String value = ethCall.getValue();
        List<Type> decode = FunctionReturnDecoder.decode(value, function.getOutputParameters());
        return decode.get(0).getValue().toString();
    }

    /**
     * 通过管理员设置域名记录 （拥有者，域名解析器）
     * 
     * @param chainId 链ID，标识是在哪条链上进行操作
     * @param domain 域名
     * @param owner 记录的所有者地址
     * @param resolverAddress 解析器地址
     * @param credentials 管理员的凭证信息，用于授权操作
     * @param contractAddress 合约地址，指定操作的智能合约
     * @return 返回调用合约方法的交易hash
     * @throws IOException 如果发生I/O错误
     */
    public static String setRecordByManager(long chainId, String domain, String owner, String resolverAddress,
        Credentials credentials, String contractAddress) throws IOException {
        String functionName = "setRecord";
        List<Type> inputParams = new ArrayList<>();
        List<TypeReference<?>> outputParams = new ArrayList<>();
        inputParams.add(new Uint256(chainId));
        inputParams.add(new Bytes32(Hash.sha256(domain.getBytes(StandardCharsets.UTF_8))));
        inputParams.add(new Address(owner));
        inputParams.add(new Address(resolverAddress));
        Function function = new Function(functionName, inputParams, outputParams);
        return invokeContract(function, credentials, contractAddress);
    }

    /**
     * 通过管理员设置域名的所有者。 --平时不用，紧急情况使用
     * 
     * @param chainId 链ID，标识操作的区块链网络。
     * @param domain 需要设置所有者的域名
     * @param owner 新的所有者地址，将以太坊地址作为智能合约的输入参数。
     * @param credentials 管理员的凭证，用于签署交易。
     * @param contractAddress 智能合约的地址，交易将被发送到这个合约。
     * @return 返回调用智能合约函数的结果，通常为交易的hash
     * @throws IOException 如果在与区块链网络通信时发生IO错误。
     */
    public static String setOwnerByManager(long chainId, String domain, String owner, Credentials credentials,
        String contractAddress) throws IOException {
        String functionName = "setOwner";
        List<Type> inputParams = new ArrayList<>();
        List<TypeReference<?>> outputParams = new ArrayList<>();
        inputParams.add(new Uint256(chainId));
        inputParams.add(new Bytes32(Hash.sha256(domain.getBytes(StandardCharsets.UTF_8))));
        inputParams.add(new Address(owner));
        Function function = new Function(functionName, inputParams, outputParams);
        return invokeContract(function, credentials, contractAddress);
    }

    /**
     * 通过管理员设置解析器。 --平时不用，紧急情况使用
     *
     * @param chainId 链ID，指定操作的区块链网络。
     * @param domain 需要设置解析器的域名
     * @param resolverAddress 新的解析器合约地址。
     * @param credentials 执行操作的账户凭证。
     * @param contractAddress 目标合约的地址。
     * @return 返回调用合约方法的结果。
     * @throws Exception 如果调用合约或传输过程中出现错误，则抛出异常。
     */
    public static String setResolverByManager(long chainId, String domain, String resolverAddress,
        Credentials credentials, String contractAddress) throws Exception {
        String functionName = "setResolver";
        List<Type> inputParams = new ArrayList<>();
        List<TypeReference<?>> outputParams = new ArrayList<>();
        inputParams.add(new Uint256(chainId));
        inputParams.add(new Bytes32(Hash.sha256(domain.getBytes(StandardCharsets.UTF_8))));
        inputParams.add(new Address(resolverAddress));
        Function function = new Function(functionName, inputParams, outputParams);
        return invokeContract(function, credentials, contractAddress);
    }

    // ------------------------------------------Registry.sol--------------------------------------------------
    /**
     * 设置合约的所有者。
     * 
     * @param domain 域名
     * @param owner 新的所有者地址。
     * @param credentials 用户的凭证，用于签名交易。
     * @param contractAddress 合约的地址。
     * @return 调用合约方法的结果。
     * @throws IOException 如果在调用合约时发生IO错误。
     */
    public static String setOwner(String domain, String owner, Credentials credentials,
        String contractAddress) throws IOException {
        String functionName = "setOwner";
        List<Type> inputParams = new ArrayList<>();
        List<TypeReference<?>> outputParams = new ArrayList<>();
        inputParams.add(new Bytes32(Hash.sha256(domain.getBytes(StandardCharsets.UTF_8))));
        inputParams.add(new Address(owner));
        Function function = new Function(functionName, inputParams, outputParams);
        return invokeContract(function, credentials, contractAddress);
    }

    /**
     * 设置域名解析器 该方法用于向指定的智能合约设置一个新的解析器地址，用于解析给定的域名。
     *
     * @param domain 需要设置解析器的域名
     * @param resolverAddress 指定新的解析器地址。
     * @param credentials 用于对交易进行签名的账户凭证。
     * @param contractAddress 智能合约的地址。
     * @return 返回调用智能合约函数的结果，通常为交易的hash。
     * @throws IOException 如果在与区块链网络通信过程中发生错误，则抛出IOException。
     */
    public static String setResolver(String domain, String resolverAddress, Credentials credentials,
        String contractAddress) throws IOException {
        String functionName = "setResolver";
        List<Type> inputParams = new ArrayList<>();
        List<TypeReference<?>> outputParams = new ArrayList<>();
        inputParams.add(new Bytes32(Hash.sha256(domain.getBytes(StandardCharsets.UTF_8))));
        inputParams.add(new Address(resolverAddress));
        Function function = new Function(functionName, inputParams, outputParams);
        return invokeContract(function, credentials, contractAddress);
    }

    /**
     * 获取指定域名对应的合约所有者地址。
     * 
     * @param domain 需要查询的域名
     * @param chainAccountAddress 调用合约的链上账户地址。
     * @param contractAddress 合约的地址，即需要查询的合约在链上的位置。
     * @return 返回合约所有者的地址字符串。
     * @throws IOException 如果在与区块链交互过程中出现网络错误，则抛出IOException。
     */
    public static String getOwner(String domain, String chainAccountAddress, String contractAddress)
        throws IOException {
        String functionName = "owner";
        List<Type> inputParams = new ArrayList<>();
        List<TypeReference<?>> outputParams = new ArrayList<>();
        inputParams.add(new Bytes32(Hash.sha256(domain.getBytes(StandardCharsets.UTF_8))));
        outputParams.add(new TypeReference<Address>() {});
        Function function = new Function(functionName, inputParams, outputParams);
        EthCall ethCall = callContract(function, chainAccountAddress, contractAddress);
        String value = ethCall.getValue();
        List<Type> decode = FunctionReturnDecoder.decode(value, function.getOutputParameters());
        return decode.get(0).getValue().toString();
    }

    /**
     * 获取指定域名的解析器地址。
     * 
     * @param domain 需要解析的域名
     * @param chainAccountAddress 链上的账户地址，用于执行智能合约调用。
     * @param contractAddress 智能合约的地址，解析器合约的地址。
     * @return 解析器地址的字符串表示。
     * @throws IOException 如果在与区块链网络通信时发生错误。
     */
    public static String getResolver(String domain, String chainAccountAddress, String contractAddress)
        throws IOException {
        String functionName = "resolver";
        List<Type> inputParams = new ArrayList<>();
        List<TypeReference<?>> outputParams = new ArrayList<>();
        inputParams.add(new Bytes32(Hash.sha256(domain.getBytes(StandardCharsets.UTF_8))));
        outputParams.add(new TypeReference<Address>() {});
        Function function = new Function(functionName, inputParams, outputParams);
        EthCall ethCall = callContract(function, chainAccountAddress, contractAddress);
        String value = ethCall.getValue();
        List<Type> decode = FunctionReturnDecoder.decode(value, function.getOutputParameters());
        return decode.get(0).getValue().toString();
    }

    /**
     * 检查特定域名的记录是否存在于智能合约中。
     * 
     * @param domain 需要查询的域名
     * @param chainAccountAddress 链上的账户地址，用于进行智能合约调用。
     * @param contractAddress 智能合约的地址，指定要查询的智能合约。
     * @return 返回一个布尔值，表示记录是否存在（"true" 表示存在，"false" 表示不存在）。
     * @throws IOException 如果在与区块链交互过程中出现IO异常。
     */
    public static String recordExists(String domain, String chainAccountAddress, String contractAddress)
        throws IOException {
        String functionName = "recordExists";
        List<Type> inputParams = new ArrayList<>();
        List<TypeReference<?>> outputParams = new ArrayList<>();
        inputParams.add(new Bytes32(Hash.sha256(domain.getBytes(StandardCharsets.UTF_8))));
        outputParams.add(new TypeReference<Bool>() {});
        Function function = new Function(functionName, inputParams, outputParams);
        EthCall ethCall = callContract(function, chainAccountAddress, contractAddress);
        String value = ethCall.getValue();
        List<Type> decode = FunctionReturnDecoder.decode(value, function.getOutputParameters());
        return decode.get(0).getValue().toString();
    }

    // ----------------------------------------resolver--------------------------------------------

    /**
     * 为指定操作者设置代为操作域名映射的权限。
     * 
     * @param operator 操作者的地址，即授权给谁。
     * @param approved 是否批准。
     * @param credentials 用户的凭证信息，用于签名交易。
     * @param contractAddress 合约地址，指定哪个合约设置审批权限。
     * @return 返回交易的哈希值。
     * @throws IOException 如果发生IO异常。
     */
    public static String setApprovalForAll(String operator, boolean approved, Credentials credentials,
        String contractAddress) throws IOException {
        String functionName = "setApprovalForAll";
        List<Type> inputParams = new ArrayList<>();
        List<TypeReference<?>> outputParams = new ArrayList<>();
        inputParams.add(new Address(operator));
        inputParams.add(new Bool(approved));
        Function function = new Function(functionName, inputParams, outputParams);
        return invokeContract(function, credentials, contractAddress);
    }

    /**
     * 检查一个操作者是否被所有者授权管理所有域名。
     * 
     * @param owner 所有者的以太坊地址。
     * @param operator 操作者的以太坊地址。
     * @param chainAccountAddress 链上账户地址，用于执行此查询的账户地址。
     * @param contractAddress 合约地址，查询授权信息的代币合约地址。
     * @return 返回一个布尔值。
     * @throws IOException 如果在与区块链交互过程中出现IO异常。
     */
    public static String isApprovedForAll(String owner, String operator, String chainAccountAddress,
        String contractAddress) throws IOException {
        String functionName = "isApprovedForAll";
        List<Type> inputParams = new ArrayList<>();
        List<TypeReference<?>> outputParams = new ArrayList<>();
        inputParams.add(new Address(owner));
        inputParams.add(new Address(operator));
        outputParams.add(new TypeReference<Bool>() {});
        Function function = new Function(functionName, inputParams, outputParams);
        EthCall ethCall = callContract(function, chainAccountAddress, contractAddress);
        String value = ethCall.getValue();
        List<Type> decode = FunctionReturnDecoder.decode(value, function.getOutputParameters());
        return decode.get(0).getValue().toString();
    }

    /**
     * 将某一个域名对别人进行授权
     *
     * @param domain 域名。
     * @param operator 被授权对象。
     * @param approved 是否批准该操作。
     * @param credentials 执行操作所需的认证信息。
     * @param contractAddress 智能合约的地址。
     * @return 调用智能合约函数后的返回结果。
     * @throws Exception 如果调用智能合约或处理过程中出现错误，则抛出异常。
     */
    public static String approve(String domain, String operator, boolean approved, Credentials credentials,
        String contractAddress) throws Exception {
        String functionName = "approve";
        List<Type> inputParams = new ArrayList<>();
        List<TypeReference<?>> outputParams = new ArrayList<>();
        inputParams.add(new Bytes32(Hash.sha256(domain.getBytes(StandardCharsets.UTF_8))));
        inputParams.add(new Address(operator));
        inputParams.add(new Bool(approved));
        Function function = new Function(functionName, inputParams, outputParams);
        return invokeContract(function, credentials, contractAddress);
    }

    /**
     * 检查域名是否被授权。
     *
     * @param owner 域名所有者的以太坊地址。
     * @param domain 域名。
     * @param operator 被授权对象的以太坊地址。
     * @param chainAccountAddress 链上账户地址，用于执行此查询的账户地址。
     * @param contractAddress 合约地址，查询授权信息的代币合约地址。
     * @return 返回一个布尔值。
     * @throws Exception 如果在执行过程中出现错误，则抛出异常。
     */
    public static String isApproved(String owner, String domain, String operator, String chainAccountAddress,
        String contractAddress) throws Exception {
        String functionName = "isApproved";
        List<Type> inputParams = new ArrayList<>();
        List<TypeReference<?>> outputParams = new ArrayList<>();
        inputParams.add(new Address(owner));
        inputParams.add(new Bytes32(Hash.sha256(domain.getBytes(StandardCharsets.UTF_8))));
        inputParams.add(new Address(operator));
        outputParams.add(new TypeReference<Bool>() {});
        Function function = new Function(functionName, inputParams, outputParams);
        EthCall ethCall = callContract(function, chainAccountAddress, contractAddress);
        String value = ethCall.getValue();
        List<Type> decode = FunctionReturnDecoder.decode(value, function.getOutputParameters());
        return decode.get(0).getValue().toString();
    }

    /**
     * 为域名设置地址。
     *
     * @param domain 域名。
     * @param address 地址。
     * @param credentials 认证信息。
     * @param contractAddress 合约地址。
     * @return 调用智能合约函数后的返回结果。
     * @throws IOException 如果调用智能合约或处理过程中出现错误，则抛出异常。
     */
    public static String setAddr(String domain, String address, Credentials credentials,
        String contractAddress) throws IOException {
        String functionName = "setAddr";
        List<Type> inputParams = new ArrayList<>();
        List<TypeReference<?>> outputParams = new ArrayList<>();
        inputParams.add(new Bytes32(Hash.sha256(domain.getBytes(StandardCharsets.UTF_8))));
        inputParams.add(new Address(address));
        Function function = new Function(functionName, inputParams, outputParams);
        return invokeContract(function, credentials, contractAddress);
    }

    /**
     * 获取域名对应的地址。
     *
     * @param domain 域名。
     * @param chainAccountAddress 链上账户地址，用于执行此查询的账户地址。
     * @param contractAddress 合约地址，查询授权信息的代币合约地址。
     * @return 返回域名对应的地址。
     * @throws IOException 如果调用智能合约或处理过程中出现错误，则抛出异常。
     */
    public static String addr(String domain, String chainAccountAddress, String contractAddress)
        throws IOException {
        String functionName = "addr";
        List<Type> inputParams = new ArrayList<>();
        List<TypeReference<?>> outputParams = new ArrayList<>();
        inputParams.add(new Bytes32(Hash.sha256(domain.getBytes(StandardCharsets.UTF_8))));
        outputParams.add(new TypeReference<Address>() {});
        Function function = new Function(functionName, inputParams, outputParams);
        EthCall ethCall = callContract(function, chainAccountAddress, contractAddress);
        String value = ethCall.getValue();
        List<Type> decode = FunctionReturnDecoder.decode(value, function.getOutputParameters());
        return decode.get(0).getValue().toString();
    }

    /**
     * 为域名设置文本信息。
     *
     * @param domain 域名。
     * @param key 键。
     * @param value 值。
     * @param credentials 认证信息。
     * @param contractAddress 合约地址。
     * @return 调用智能合约函数后的返回结果。
     * @throws IOException 如果调用智能合约或处理过程中出现错误，则抛出异常。
     */
    public static String setText(String domain, String key, String value, Credentials credentials,
        String contractAddress) throws IOException {
        String functionName = "setText";
        List<Type> inputParams = new ArrayList<>();
        List<TypeReference<?>> outputParams = new ArrayList<>();
        inputParams.add(new Bytes32(Hash.sha256(domain.getBytes(StandardCharsets.UTF_8))));
        inputParams.add(new Utf8String(key));
        inputParams.add(new Utf8String(value));
        Function function = new Function(functionName, inputParams, outputParams);
        return invokeContract(function, credentials, contractAddress);
    }

    /**
     * 获取域名对应的文本信息。
     *
     * @param domain 域名。
     * @param key 键。
     * @param chainAccountAddress 链上账户地址，用于执行此查询的账户地址。
     * @param contractAddress 合约地址，查询授权信息的代币合约地址。
     * @return 返回域名对应的文本信息。
     * @throws IOException 如果调用智能合约或处理过程中出现错误，则抛出异常。
     */
    public static String text(String domain, String key, String chainAccountAddress, String contractAddress)
        throws IOException {
        String functionName = "text";
        List<Type> inputParams = new ArrayList<>();
        List<TypeReference<?>> outputParams = new ArrayList<>();
        inputParams.add(new Bytes32(Hash.sha256(domain.getBytes(StandardCharsets.UTF_8))));
        inputParams.add(new Utf8String(key));
        outputParams.add(new TypeReference<Utf8String>() {});
        Function function = new Function(functionName, inputParams, outputParams);
        EthCall ethCall = callContract(function, chainAccountAddress, contractAddress);
        String value = ethCall.getValue();
        List<Type> decode = FunctionReturnDecoder.decode(value, function.getOutputParameters());
        return decode.get(0).getValue().toString();
    }

    /**
     * 删除域名对应的文本信息。
     *
     * @param domain 域名。
     * @param credentials 认证信息。
     * @param contractAddress 合约地址。
     * @return 调用智能合约函数后的返回结果。
     * @throws IOException 如果调用智能合约或处理过程中出现错误，则抛出异常。
     */
    public static String clearRecords(String domain, Credentials credentials, String contractAddress)
        throws IOException {
        String functionName = "clearRecords";
        List<Type> inputParams = new ArrayList<>();
        List<TypeReference<?>> outputParams = new ArrayList<>();
        inputParams.add(new Bytes32(Hash.sha256(domain.getBytes(StandardCharsets.UTF_8))));
        Function function = new Function(functionName, inputParams, outputParams);
        return invokeContract(function, credentials, contractAddress);
    }

    /**
     * 获取域名对应的文本信息版本。 （删除一次+1）
     *
     * @param domain 域名。
     * @param chainAccountAddress 链上账户地址，用于执行此查询的账户地址。
     * @param contractAddress 合约地址，查询授权信息的代币合约地址。
     * @return 返回域名对应的文本信息版本。
     * @throws IOException 如果调用智能合约或处理过程中出现错误，则抛出异常。
     */
    public static String recordVersions(String domain, String chainAccountAddress, String contractAddress)
        throws IOException {
        String functionName = "recordVersions";
        List<Type> inputParams = new ArrayList<>();
        List<TypeReference<?>> outputParams = new ArrayList<>();
        inputParams.add(new Bytes32(Hash.sha256(domain.getBytes(StandardCharsets.UTF_8))));
        outputParams.add(new TypeReference<Uint64>() {});
        Function function = new Function(functionName, inputParams, outputParams);
        EthCall ethCall = callContract(function, chainAccountAddress, contractAddress);
        String value = ethCall.getValue();
        List<Type> decode = FunctionReturnDecoder.decode(value, function.getOutputParameters());
        return decode.get(0).getValue().toString();
    }

    private static String invokeContract(Function function, Credentials credentials, String contractAddress)
        throws IOException {
        String encode = FunctionEncoder.encode(function);
        BigInteger nonce =
            web3j.ethGetTransactionCount(credentials.getAddress(), DefaultBlockParameterName.LATEST).send()
                .getTransactionCount();
        BigInteger gasPrice = web3j.ethGasPrice().send().getGasPrice();
        BigInteger gasLimit = BigInteger.valueOf(3000000);
        RawTransaction transaction = RawTransaction.createTransaction(nonce, gasPrice, gasLimit,
            contractAddress, BigInteger.ZERO, encode);
        byte[] bytes = TransactionEncoder.signMessage(transaction, credentials);
        String hexValue = Numeric.toHexString(bytes);
        return web3j.ethSendRawTransaction(hexValue).send().getTransactionHash();
    }

    private static EthCall callContract(Function function, String chainAccountAddress, String contractAddress)
        throws IOException {
        String encode = FunctionEncoder.encode(function);
        Transaction ethCallTransaction =
            Transaction.createEthCallTransaction(chainAccountAddress, contractAddress, encode);
        return web3j.ethCall(ethCallTransaction, DefaultBlockParameterName.LATEST).send();
    }

    public static TransactionReceipt getTransactionReceipt(String transactionHash) throws Exception {
        int num = 0;
        while (true) {
            Optional<TransactionReceipt> transactionReceipt =
                web3j.ethGetTransactionReceipt(transactionHash).send().getTransactionReceipt();
            if (transactionReceipt.isPresent()) {
                return transactionReceipt.get();
            }
            Thread.sleep(1000);
            num++;
            if (num > 10) {
                return null;
            }
        }
    }
}
