package com.lj.auth.util;

import cn.hutool.json.JSONUtil;
import com.lj.auth.exception.ServiceException;
import io.ipfs.api.IPFS;
import io.ipfs.api.NamedStreamable;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * <AUTHOR>
 * @Description
 * @date 2024/3/6 17:18
 */
@Slf4j
public class IpfsUtil {
    // static final String multiaddr = "/ip4/************/tcp/5002";
    static String ipfsMultiAddr;
    static String jsonPath;
    static IPFS ipfs;

    static {
        try {
            ipfsMultiAddr = PropertiesRead.getYmlStringForActive("ipfsMultiAddr");
        } catch (Exception e) {
            log.error("Failed to read IPFS address from configuration", e);
        }
    }

    public static synchronized IPFS getIpfs() {
        if (ipfs == null && ipfsMultiAddr != null) {
            try {
                log.info("Initializing IPFS connection to address: {}", ipfsMultiAddr);
                ipfs = new IPFS(ipfsMultiAddr);
                String version = ipfs.version();
                log.info("Connected to IPFS. Version: {}", version);
            } catch (Exception e) {
                log.error("Failed to connect to IPFS daemon", e);
                throw new RuntimeException("IOException contacting IPFS daemon", e);
            }
        }
        return ipfs;
    }


    public static void main(String[] args) throws IOException {
        //System.out.println(filesLs("/"));
        String jsonStr = "hello2";
        NamedStreamable.ByteArrayWrapper byteArrayWrapper = new NamedStreamable.ByteArrayWrapper(String.valueOf("abcd"), jsonStr.getBytes(StandardCharsets.UTF_8));
        IPFS ipfs = new IPFS(ipfsMultiAddr);
        ipfs.files.write("/test/abcd", byteArrayWrapper, true, false);
    }
    
    /**
     * 获取指定目录的cid集合
     *
     * @param path '/'
     * @return cid集合
     * @throws IOException
     */
    public static List<String> filesLs(String path) throws IOException {
        List<String> hashList = new ArrayList<>();
        IPFS ipfs = getIpfs();
        List<Map> maps = ipfs.files.ls(path, true, false);
        for (Map map : maps) {
            String hash = map.get("Hash").toString();
            hashList.add(hash);
        }
        return hashList;
    }
    
    /**
     * 写入文件
     *
     * @param contractAddressPath 该合约对应的ipfs目录
     * @param tokenId
     * @param domain              域名
     * @param imageCid            图片CID
     */
    public static void fileWrite(String contractAddressPath, String tokenId, String domain, String imageCid) {
        Map<String, Object> map = new HashMap<>(3);
        map.put("image", imageCid);
        map.put("name", domain + "#" + tokenId);
        map.put("description", "Free domain name with purchase of " + domain);
        String jsonStr = JSONUtil.toJsonStr(map);
        NamedStreamable.ByteArrayWrapper byteArrayWrapper = new NamedStreamable.ByteArrayWrapper(tokenId, jsonStr.getBytes(StandardCharsets.UTF_8));
        IPFS ipfs = getIpfs();
        try {
            ipfs.files.write("/" + contractAddressPath + "-json/" + tokenId, byteArrayWrapper, true, false);
        } catch (IOException e) {
            throw new ServiceException("文件写入失败," + e.getMessage());
        }
    }
    
    /**
     * 写入文件NFT
     *
     * @param contractAddressPath 该合约对应的ipfs目录
     * @param tokenId
     * @param jsonStr            访问
     */
    public static void fileWriteNFT(String contractAddressPath, String tokenId,String jsonStr) {
        // Map<String, Object> map = new HashMap<>(3);
        // map.put("image", imageCid);
        // map.put("name", domain + "#" + tokenId);
        // map.put("description", "Free domain name with purchase of " + domain);
        // String jsonStr = JSONUtil.toJsonStr(map);
        NamedStreamable.ByteArrayWrapper byteArrayWrapper = new NamedStreamable.ByteArrayWrapper(tokenId, jsonStr.getBytes(StandardCharsets.UTF_8));
        IPFS ipfs = getIpfs();
        try {
            ipfs.files.write("/" + contractAddressPath + "-json/" + tokenId, byteArrayWrapper, true, false);
        } catch (IOException e) {
            throw new ServiceException("文件写入失败," + e.getMessage());
        }
    }

    /**
     * 获取jsonpath目录最新的CID
     *
     * @return
     * @throws IOException
     */
    public static String getJsonPathCid(String contractAddress) {
        List<Map> lsList = null;
        try {
            IPFS ipfs = getIpfs();
            lsList = ipfs.files.ls("/", true, false);
        } catch (IOException e) {
            throw new ServiceException("查询目录信息失败," + e.getMessage());
        }
        // String[] split = jsonPath.split("/");
        // String pathName = split[split.length - 1];
        String pathName = contractAddress + "-json";
        String dirHash = null;
        for (Map orgMap : lsList) {
            Object name = orgMap.get("Name");
            if (Objects.equals(pathName, name)) {
                dirHash = orgMap.get("Hash").toString();
            }
        }
        return dirHash;
    }


    /**
     * 获取jsonpath目录最新的CID
     *
     * @return
     * @throws IOException
     */
    public static String getJsonPathCid(String contractAddress,IPFS ipfs) {
        List<Map> lsList = null;
        try {
            lsList = ipfs.files.ls("/", true, false);
        } catch (IOException e) {
            throw new ServiceException("查询目录信息失败," + e.getMessage());
        }
        // String[] split = jsonPath.split("/");
        // String pathName = split[split.length - 1];
        String pathName = contractAddress + "-json";
        String dirHash = null;
        for (Map orgMap : lsList) {
            Object name = orgMap.get("Name");
            if (Objects.equals(pathName, name)) {
                dirHash = orgMap.get("Hash").toString();
            }
        }
        return dirHash;
    }
    
    
}
