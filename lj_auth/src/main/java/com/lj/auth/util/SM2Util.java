package com.lj.auth.util;

import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.asn1.gm.GMNamedCurves;
import org.bouncycastle.asn1.x9.X9ECParameters;
import org.bouncycastle.crypto.*;
import org.bouncycastle.crypto.digests.SM3Digest;
import org.bouncycastle.crypto.generators.ECKeyPairGenerator;
import org.bouncycastle.crypto.params.*;
import org.bouncycastle.crypto.signers.SM2Signer;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.math.ec.ECConstants;
import org.bouncycastle.math.ec.ECFieldElement;
import org.bouncycastle.math.ec.ECPoint;
import org.bouncycastle.util.Arrays;
import org.bouncycastle.util.BigIntegers;
import org.bouncycastle.util.Strings;
import org.bouncycastle.util.encoders.Hex;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.Security;

/**
 * <AUTHOR>
 * @Description
 * @date 2024/1/30 11:17
 */
@Slf4j
public class SM2Util {

    public SM2Util() {
    }

    static {
        // log = LoggerFactory.getLogger(SM2Util.class);
        Security.addProvider(new BouncyCastleProvider());
    }

    public static void main(String[] args) throws CryptoException {
//         PairKey sm2Keys = getSm2Keys(false);
//         System.out.println("getPriKey:"+sm2Keys.getPriKey());
//         System.out.println("getPubKey:"+sm2Keys.getPubKey());
        //getPriKey:1e491e4f674637c5dcc1ce34de92c3abcf4db8bea2b2ed91066f947b5e5539e5
        // getPubKey:04f8a96992f44bd65deb821a930f133f1ec161db2ab4ec52f75bbcb1897c9df0dd90894f1e975d7f0c186fd8407c5f65a9662544980b3acd9d455fd2ba35a46faf
         String getPriKey = "654650a4c792e1f7da626043821c0f101b3eb1276d60655e61893f16d78a55a9";
         String did="did:ctid:bsn:A1D9FCB4DE7DB385D5C084EA7C46B28B408B1BF6B3A400E028AD1FD887096288";
         String getPubKey = "041524b7b7f5a9a778106dd4433ee09802b791b10ae09e6ac80b085ba59652c0b580f32a65be487aded27bb6264107cb8d967490f0f508474789fe3895e7d3d431";
         String hexEncrypt = hexEncrypt(getPubKey, did);
        System.out.println("加密数据: "+hexEncrypt);
         String decrypt = hexDecrypt(getPriKey, hexEncrypt);
         System.out.println("解密数据："+decrypt);
        //
         String sign = hexSign(getPriKey, did);
         boolean b = hexVerify(getPubKey, did, sign);
         System.out.println("签名："+sign);
        System.out.println("验签结果："+b);
    

    }


    /**
     * 生成公私钥对
     * @param compressed 是否压缩公钥 false
     * @return
     */
    public static PairKey getSm2Keys(boolean compressed) {
        X9ECParameters sm2ECParameters = GMNamedCurves.getByName("sm2p256v1");
        ECDomainParameters domainParameters = new ECDomainParameters(sm2ECParameters.getCurve(), sm2ECParameters.getG(), sm2ECParameters.getN());
        ECKeyPairGenerator keyPairGenerator = new ECKeyPairGenerator();

        try {
            keyPairGenerator.init(new ECKeyGenerationParameters(domainParameters, SecureRandom.getInstance("SHA1PRNG")));
        } catch (NoSuchAlgorithmException var11) {
            log.error("生成公私钥对时出现异常:", var11);
        }

        AsymmetricCipherKeyPair asymmetricCipherKeyPair = keyPairGenerator.generateKeyPair();
        ECPublicKeyParameters publicKeyParameters = (ECPublicKeyParameters)asymmetricCipherKeyPair.getPublic();
        ECPoint ecPoint = publicKeyParameters.getQ();
        String publicKey = Hex.toHexString(ecPoint.getEncoded(compressed));
        ECPrivateKeyParameters privateKeyParameters = (ECPrivateKeyParameters)asymmetricCipherKeyPair.getPrivate();
        BigInteger intPrivateKey = privateKeyParameters.getD();
        String privateKey = intPrivateKey.toString(16);
        return new PairKey(privateKey, publicKey);
    }
    static class PairKey {
        private String priKey;
        private String pubKey;

        public PairKey() {
        }

        public PairKey(String priKey, String pubKey) {
            this.priKey = priKey;
            this.pubKey = pubKey;
        }

        public String getPriKey() {
            return this.priKey;
        }

        public void setPriKey(String priKey) {
            this.priKey = priKey;
        }

        public String getPubKey() {
            return this.pubKey;
        }

        public void setPubKey(String pubKey) {
            this.pubKey = pubKey;
        }
    }

    /**
     * 加密
     * @param publicKey 公钥
     * @param data 明文数据
     * @return
     */
    public static String hexEncrypt(String publicKey, String data) {
        return hexEncrypt(data, publicKey, SM2EngineExtend.CIPHERMODE_NORM);
    }

    private static String hexEncrypt(String data, String publicKey, int cipherMode) {
        X9ECParameters sm2ECParameters = GMNamedCurves.getByName("sm2p256v1");
        ECDomainParameters domainParameters = new ECDomainParameters(sm2ECParameters.getCurve(), sm2ECParameters.getG(), sm2ECParameters.getN());
        ECPoint pukPoint = sm2ECParameters.getCurve().decodePoint(Hex.decode(publicKey));
        ECPublicKeyParameters publicKeyParameters = new ECPublicKeyParameters(pukPoint, domainParameters);
        SM2EngineExtend sm2Engine = new SM2EngineExtend();
        sm2Engine.init(true, cipherMode, new ParametersWithRandom(publicKeyParameters, new SecureRandom()));
        byte[] arrayOfBytes = null;

        try {
            byte[] in = data.getBytes();
            arrayOfBytes = sm2Engine.processBlock(in, 0, in.length);
        } catch (Exception var10) {
            log.error("SM2加密时出现异常:{}", var10.getMessage(), var10);
        }

        return Hex.toHexString(arrayOfBytes);
    }


    /**
     * 解密
     * @param privateKey 私钥
     * @param cipherData 密文数据
     * @return
     */
    public static String hexDecrypt(String privateKey, String cipherData) {
        return hexDecrypt(privateKey, cipherData, SM2EngineExtend.CIPHERMODE_NORM);
    }

    private static String hexDecrypt(String privateKey, String cipherData, int cipherMode) {
        if (!cipherData.startsWith("04")) {
            cipherData = "04" + cipherData;
        }

        byte[] cipherDataByte = Hex.decode(cipherData);
        X9ECParameters sm2ECParameters = GMNamedCurves.getByName("sm2p256v1");
        ECDomainParameters domainParameters = new ECDomainParameters(sm2ECParameters.getCurve(), sm2ECParameters.getG(), sm2ECParameters.getN());
        BigInteger privateKeyD = new BigInteger(privateKey, 16);
        ECPrivateKeyParameters privateKeyParameters = new ECPrivateKeyParameters(privateKeyD, domainParameters);
        SM2EngineExtend sm2Engine = new SM2EngineExtend();
        sm2Engine.init(false, cipherMode, privateKeyParameters);
        String result = "";

        try {
            byte[] arrayOfBytes = sm2Engine.processBlock(cipherDataByte, 0, cipherDataByte.length);
            return new String(arrayOfBytes);
        } catch (Exception var11) {
            log.error("SM2解密时出现异常:{}", var11.getMessage(), var11);
            return result;
        }
    }


    static class SM2EngineExtend {
        public static int CIPHERMODE_BC = 0;
        public static int CIPHERMODE_NORM = 1;
        private final Digest digest;
        private boolean forEncryption;
        private ECKeyParameters ecKey;
        private ECDomainParameters ecParams;
        private int curveLength;
        private SecureRandom random;
        private int cipherMode;

        public SM2EngineExtend() {
            this(new SM3Digest());
        }

        public SM2EngineExtend(Digest digest) {
            this.digest = digest;
        }

        public void setCipherMode(int cipherMode) {
            this.cipherMode = cipherMode;
        }

        public void init(boolean forEncryption, CipherParameters param) {
            this.init(forEncryption, CIPHERMODE_NORM, param);
        }

        public void init(boolean forEncryption, int cipherMode, CipherParameters param) {
            this.forEncryption = forEncryption;
            this.cipherMode = cipherMode;
            if (forEncryption) {
                ParametersWithRandom rParam = (ParametersWithRandom)param;
                this.ecKey = (ECKeyParameters)rParam.getParameters();
                this.ecParams = this.ecKey.getParameters();
                ECPoint s = ((ECPublicKeyParameters)this.ecKey).getQ().multiply(this.ecParams.getH());
                if (s.isInfinity()) {
                    throw new IllegalArgumentException("invalid key: [h]Q at infinity");
                }

                this.random = rParam.getRandom();
            } else {
                this.ecKey = (ECKeyParameters)param;
                this.ecParams = this.ecKey.getParameters();
            }

            this.curveLength = (this.ecParams.getCurve().getFieldSize() + 7) / 8;
        }

        public byte[] processBlock(byte[] in, int inOff, int inLen) throws InvalidCipherTextException {
            return this.forEncryption ? this.encrypt(in, inOff, inLen) : this.decrypt(in, inOff, inLen);
        }

        private byte[] encrypt(byte[] in, int inOff, int inLen) throws InvalidCipherTextException {
            byte[] c2 = new byte[inLen];
            System.arraycopy(in, inOff, c2, 0, c2.length);

            byte[] c1;
            ECPoint kPB;
            do {
                BigInteger k = this.nextK();
                ECPoint c1P = this.ecParams.getG().multiply(k).normalize();
                c1 = c1P.getEncoded(false);
                kPB = ((ECPublicKeyParameters)this.ecKey).getQ().multiply(k).normalize();
                this.kdf(this.digest, kPB, c2);
            } while(this.notEncrypted(c2, in, inOff));

            byte[] c3 = new byte[this.digest.getDigestSize()];
            this.addFieldElement(this.digest, kPB.getAffineXCoord());
            this.digest.update(in, inOff, inLen);
            this.addFieldElement(this.digest, kPB.getAffineYCoord());
            this.digest.doFinal(c3, 0);
            return this.cipherMode == CIPHERMODE_NORM ? Arrays.concatenate(c1, c3, c2) : Arrays.concatenate(c1, c2, c3);
        }

        private byte[] decrypt(byte[] in, int inOff, int inLen) throws InvalidCipherTextException {
            byte[] c1 = new byte[this.curveLength * 2 + 1];
            System.arraycopy(in, inOff, c1, 0, c1.length);
            ECPoint c1P = this.ecParams.getCurve().decodePoint(c1);
            ECPoint s = c1P.multiply(this.ecParams.getH());
            if (s.isInfinity()) {
                throw new InvalidCipherTextException("[h]C1 at infinity");
            } else {
                c1P = c1P.multiply(((ECPrivateKeyParameters)this.ecKey).getD()).normalize();
                byte[] c2 = new byte[inLen - c1.length - this.digest.getDigestSize()];
                if (this.cipherMode == CIPHERMODE_BC) {
                    System.arraycopy(in, inOff + c1.length, c2, 0, c2.length);
                } else {
                    System.arraycopy(in, inOff + c1.length + this.digest.getDigestSize(), c2, 0, c2.length);
                }

                this.kdf(this.digest, c1P, c2);
                byte[] c3 = new byte[this.digest.getDigestSize()];
                this.addFieldElement(this.digest, c1P.getAffineXCoord());
                this.digest.update(c2, 0, c2.length);
                this.addFieldElement(this.digest, c1P.getAffineYCoord());
                this.digest.doFinal(c3, 0);
                int check = 0;
                int i;
                if (this.cipherMode == CIPHERMODE_BC) {
                    for(i = 0; i != c3.length; ++i) {
                        check |= c3[i] ^ in[c1.length + c2.length + i];
                    }
                } else {
                    for(i = 0; i != c3.length; ++i) {
                        check |= c3[i] ^ in[c1.length + i];
                    }
                }

                this.clearBlock(c1);
                this.clearBlock(c3);
                if (check != 0) {
                    this.clearBlock(c2);
                    throw new InvalidCipherTextException("invalid cipher text");
                } else {
                    return c2;
                }
            }
        }

        private boolean notEncrypted(byte[] encData, byte[] in, int inOff) {
            for(int i = 0; i != encData.length; ++i) {
                if (encData[i] != in[inOff]) {
                    return false;
                }
            }

            return true;
        }

        private void kdf(Digest digest, ECPoint c1, byte[] encData) {
            int ct = 1;
            int v = digest.getDigestSize();
            byte[] buf = new byte[digest.getDigestSize()];
            int off = 0;

            for(int i = 1; i <= (encData.length + v - 1) / v; ++i) {
                this.addFieldElement(digest, c1.getAffineXCoord());
                this.addFieldElement(digest, c1.getAffineYCoord());
                digest.update((byte)(ct >> 24));
                digest.update((byte)(ct >> 16));
                digest.update((byte)(ct >> 8));
                digest.update((byte)ct);
                digest.doFinal(buf, 0);
                if (off + buf.length < encData.length) {
                    this.xor(encData, buf, off, buf.length);
                } else {
                    this.xor(encData, buf, off, encData.length - off);
                }

                off += buf.length;
                ++ct;
            }

        }

        private void xor(byte[] data, byte[] kdfOut, int dOff, int dRemaining) {
            for(int i = 0; i != dRemaining; ++i) {
                data[dOff + i] ^= kdfOut[i];
            }

        }

        private BigInteger nextK() {
            int qBitLength = this.ecParams.getN().bitLength();

            BigInteger k;
            do {
                do {
                    k = new BigInteger(qBitLength, this.random);
                } while(k.equals(ECConstants.ZERO));
            } while(k.compareTo(this.ecParams.getN()) >= 0);

            return k;
        }

        private void addFieldElement(Digest digest, ECFieldElement v) {
            byte[] p = BigIntegers.asUnsignedByteArray(this.curveLength, v.toBigInteger());
            digest.update(p, 0, p.length);
        }

        private void clearBlock(byte[] block) {
            for(int i = 0; i != block.length; ++i) {
                block[i] = 0;
            }

        }
    }

    /**
     * 签名
     * @param privateKey 私钥
     * @param content 待签名数据
     * @return
     * @throws CryptoException
     */
    public static String hexSign(String privateKey, String content) throws CryptoException {
        byte[] message = content.getBytes(StandardCharsets.UTF_8);
        X9ECParameters sm2ECParameters = GMNamedCurves.getByName("sm2p256v1");
        ECDomainParameters domainParameters = new ECDomainParameters(sm2ECParameters.getCurve(), sm2ECParameters.getG(), sm2ECParameters.getN());
        BigInteger privateKeyD = new BigInteger(privateKey, 16);
        ECPrivateKeyParameters privateKeyParameters = new ECPrivateKeyParameters(privateKeyD, domainParameters);
        SM2Signer sm2Signer = new SM2Signer();

        try {
            sm2Signer.init(true, new ParametersWithID(new ParametersWithRandom(privateKeyParameters, SecureRandom.getInstance("SHA1PRNG")), Strings.toByteArray("1234567812345678")));
        } catch (NoSuchAlgorithmException var10) {
            log.error("签名时出现异常:", var10);
        }

        sm2Signer.update(message, 0, message.length);
        byte[] signBytes = sm2Signer.generateSignature();
        String sign = Hex.toHexString(signBytes);
        return sign;
    }

    /**
     * 验签
     * @param publicKey 公钥
     * @param content 签名的原数据
     * @param sign 签名值
     * @return
     */
    public static boolean hexVerify(String publicKey, String content, String sign) {
        byte[] message = content.getBytes(StandardCharsets.UTF_8);
        byte[] signData = Hex.decode(sign);
        X9ECParameters sm2ECParameters = GMNamedCurves.getByName("sm2p256v1");
        ECDomainParameters domainParameters = new ECDomainParameters(sm2ECParameters.getCurve(), sm2ECParameters.getG(), sm2ECParameters.getN());
        ECPoint pukPoint = sm2ECParameters.getCurve().decodePoint(Hex.decode(publicKey));
        ECPublicKeyParameters publicKeyParameters = new ECPublicKeyParameters(pukPoint, domainParameters);
        SM2Signer sm2Signer = new SM2Signer();
        ParametersWithID parametersWithID = new ParametersWithID(publicKeyParameters, Strings.toByteArray("1234567812345678"));
        sm2Signer.init(false, parametersWithID);
        sm2Signer.update(message, 0, message.length);
        boolean verify = sm2Signer.verifySignature(signData);
        return verify;
    }
}
