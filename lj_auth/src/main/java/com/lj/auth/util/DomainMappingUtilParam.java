package com.lj.auth.util;

import java.io.IOException;
import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.util.*;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lj.auth.domain.Account;
import com.lj.auth.domain.Chain;
import com.lj.auth.domain.DomainAccount;
import com.lj.auth.domain.PlatformAccount;
import com.lj.auth.mapper.AccountMapper;
import com.lj.auth.mapper.ChainMapper;
import com.lj.auth.mapper.DomainAccountMapper;
import com.lj.auth.mapper.PlatformAccountMapper;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.validator.constraints.Range;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.web3j.abi.FunctionEncoder;
import org.web3j.abi.FunctionReturnDecoder;
import org.web3j.abi.TypeReference;
import org.web3j.abi.datatypes.*;
import org.web3j.abi.datatypes.generated.Bytes32;
import org.web3j.abi.datatypes.generated.Uint256;
import org.web3j.abi.datatypes.generated.Uint64;
import org.web3j.crypto.Credentials;
import org.web3j.crypto.Hash;
import org.web3j.crypto.RawTransaction;
import org.web3j.crypto.TransactionEncoder;
import org.web3j.protocol.Web3j;
import org.web3j.protocol.core.DefaultBlockParameterName;
import org.web3j.protocol.core.methods.request.Transaction;
import org.web3j.protocol.core.methods.response.EthCall;
import org.web3j.protocol.core.methods.response.TransactionReceipt;
import org.web3j.protocol.http.HttpService;
import org.web3j.utils.Numeric;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * 描述： 创建人: CFJ 创建时间: 2024/04/19
 */

@Component
@Slf4j
public class DomainMappingUtilParam {

    @Value("${ylChainId}")
    private Integer chainId;

    @Resource
    private PlatformAccountMapper platformAccountMapper;
    @Resource
    private ChainMapper chainMapper;
    @Resource
    private DomainAccountMapper domainAccountMapper;
    @Resource
    private AccountMapper accountMapper;

    private static String chainName;

    // 部署注册管理合约账户
    private static String chainAccountAddress;
    // 部署注册管理合约私钥
    private static String privateKey;
    // 通过私钥创建凭证
    private static Credentials credentials;

    // 注册管理器合约地址
    @Value("${ylBesuChainContract.manmagerContractAddress}")
    private String manmagerContractAddress;

    // 解析合约地址
    @Value("${ylBesuChainContract.resolverContractAddress}")
    private String resolverContractAddress;

    // 注册表合约地址
    @Value("${ylBesuChainContract.registryContractAddress}")
    private String registryContractAddress;

    // 域名绑定应用名称 暂时 写在这里
    private static String key = "bindDID";
    private static String key1 = "bindChainAccount";
    private static String key2 = "bindNickName";

    @PostConstruct
    public void init() {
        log.info("初始化链解析合约参数");
        // 获取注册管理合约部署账户
        PlatformAccount platformAccount = platformAccountMapper
            .selectOne(Wrappers.<PlatformAccount>lambdaQuery().eq(PlatformAccount::getType, 5));
        chainAccountAddress = platformAccount.getAddress();
        privateKey = platformAccount.getPrivateKey();
        credentials = Credentials.create(privateKey);
        Chain chain = chainMapper.selectOne(Wrappers.<Chain>lambdaQuery().eq(Chain::getChainId, chainId));
        chainName = chain.getChainName();
        log.info("初始化链解析合约参数完成");

    }

    public static void main(String[] args) throws Exception {

        String domain = "redmi.bsn";

        String DID = "did:ctid:bsn:A1D9FCB4DE7DB385D5C084EA7C46B28B408B1BF6B3A400E028AD1FD887096288";

        // 域名 文本 映射 值 绑定DID
        // String s = setText(domain, key, DID, credentials, resolverContractAddress);
        // System.out.println(s);
        // 绑定链地址
        // String s1 = setText(domain, key1, "0x39cf25502a9E2623bA55df454C9ab4AC5c4E0a0b", credentials,
        // resolverContractAddress);
        //// System.out.println(s1);

        // String s2 = setText(domain, key2, "用户uuid", credentials, resolverContractAddress);
        // System.out.println(s2);

        // 域名 文本 查询绑定的值
        // String phone = text(domain, key2, chainAccountAddress, resolverContractAddress);
        // System.out.println(phone);

        // 对具体域名授权
        // String s = approve("hello.web3", "0x9b39b2ae91a8a10e07ec9546b26f12556bf61c55", true, credentials, resolver);
        // System.out.println(s);
        // 查询是否授权
        // String approved = isApproved(chainAccountAddress, "hello.web3", "0x9b39b2ae91a8a10e07ec9546b26f12556bf61c55",
        // "0x9b39b2ae91a8a10e07ec9546b26f12556bf61c55", resolver);
        // System.out.println(approved);

        // 一键解绑 解除所有
        // String s = clearRecords(domain, credentials, resolverContractAddress);
        // System.out.println(s);

        // 域名设置版本号
        // String s1 = recordVersions(domain, "0x39cf25502a9E2623bA55df454C9ab4AC5c4E0a0b", resolverContractAddress);
        // System.out.println(s1);

    }

    // 校验域名是否绑定
    public Map<String, Object> domainBindState(String domain) {
        Map<String, Object> domainkeyState = new HashMap<String, Object>();
        // 查询域名绑定DID状态
        DomainAccount domainAccount = domainAccountMapper
            .selectOne(Wrappers.<DomainAccount>lambdaQuery().eq(DomainAccount::getDomain, domain));
        // 查询域名昵称绑定
        List<Account> accounts =
            accountMapper.selectList(Wrappers.<Account>lambdaQuery().eq(Account::getDomainNickName, domain));
        // 获取DID绑定状态 did绑定状态 1-未绑定 2-已绑定
        Integer didBindState = domainAccount.getDidBindState();
        // 链地址绑定状态 1-未绑定 2-已绑定
        Integer chainAddressBindState = domainAccount.getChainAddressBindState();

        domainkeyState.put(key, didBindState == 2 ? true : false);
        // 域名绑定链地址状态
        // domainkeyState.put(key1, searchBindChainAccountAddressState(domain));
        domainkeyState.put(key1, chainAddressBindState == 2 ? true : false);
        // 查询专属昵称绑定状态
        // domainkeyState.put(key2, searchBindNickName(domain));
         domainkeyState.put(key2, accounts.size() == 0?false:true);
        return domainkeyState;
    }

    /**
     * 一键解绑
     * 
     * @param domain
     * @return boolean
     * <AUTHOR>
     * @date 2024/04/19
     */
    public boolean clearBinad(String domain) {
        // 一键解绑 解除所有
        String hash = null;
        // 判断是否设置成功
        TransactionReceipt transactionReceipt = null;
        try {
            hash = DomainMappingUtil.clearRecords(domain, credentials, resolverContractAddress);
            // transactionReceipt = DomainMappingUtil.getTransactionReceipt(hash);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return true;
        // return transactionReceipt.isStatusOK();
    }

    /**
     * 查询域名是否绑定链地址
     * 
     * @param domain
     * @return boolean
     * <AUTHOR>
     * @date 2024/04/19
     */
    private boolean searchBindChainAccountAddressState(String domain) {
        String source = "0x0000000000000000000000000000000000000000";
        // 查询 域名映射地址
        String addr = null;
        try {
            addr = DomainMappingUtil.addr(domain, chainAccountAddress, resolverContractAddress);
        } catch (IOException e) {
            e.printStackTrace();
        }
        // 未绑定
        if (addr.equals(source)) {
            return false;
        }
        return true;
    }

    /**
     * 查询域名是否绑定链地址
     *
     * @param domain
     * @return boolean
     * <AUTHOR>
     * @date 2024/04/19
     */
    private boolean searchBindNickName(String domain) {
        // 域名 文本 查询绑定的值
        String value = null;
        try {
            value = DomainMappingUtil.text(domain, key2, chainAccountAddress, resolverContractAddress);
        } catch (IOException e) {
            e.printStackTrace();
        }
        // 未绑定
        if (StrUtil.isBlank(value)) {
            return false;
        }
        return true;
    }

    /**
     * 域名绑定DID---暂时上的BSN链可能用不到
     *
     * @param domain 域名
     * @param did did
     * @return boolean 是否设置成功
     * <AUTHOR>
     * @date 2024/04/19
     */
    private boolean setTextDID(String domain, String did) {
        // 域名 文本 映射 值 绑定DID
        String hash = null;
        // 判断是否设置成功
        TransactionReceipt transactionReceipt = null;
        try {
            hash = DomainMappingUtil.setText(domain, key, did, credentials, resolverContractAddress);
            // transactionReceipt = DomainMappingUtil.getTransactionReceipt(hash);
        } catch (Exception e) {
            e.printStackTrace();
        }
        // return transactionReceipt.isStatusOK();
        return true;
    }

    /**
     * 域名绑定链账户
     *
     * @param domain
     * @param accountAddress
     * @return boolean
     * <AUTHOR>
     * @date 2024/04/19
     */
    public boolean setTextChainAccount(String domain, String accountAddress) {
        // 域名 文本 映射 值 绑定DID
        String hash = null;
        // 判断是否设置成功
        TransactionReceipt transactionReceipt = null;
        try {
            // 域名 地址 映射
            hash = DomainMappingUtil.setAddr(domain, accountAddress, credentials, resolverContractAddress);
            // transactionReceipt = DomainMappingUtil.getTransactionReceipt(hash);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return true;
        // return transactionReceipt.isStatusOK();
    }

    /**
     * 域名绑定 专属昵称
     *
     * @param domain 域名
     * @param accountUUID 用户 UUID
     * @return boolean
     * <AUTHOR>
     * @date 2024/04/19
     */
    public boolean setTextNicekName(String domain, String accountUUID) {
        // 域名 文本 映射 值 绑定DID
        String hash = null;
        // 判断是否设置成功
        TransactionReceipt transactionReceipt = null;
        try {
            hash = DomainMappingUtil.setText(domain, key2, accountUUID, credentials, resolverContractAddress);
            // transactionReceipt = DomainMappingUtil.getTransactionReceipt(hash);
        } catch (Exception e) {
            e.printStackTrace();
        }
        // return transactionReceipt.isStatusOK();
        return true;
    }

    /**
     * 在设置域名映射前执行 设置域名所有者记录
     *
     * <AUTHOR>
     * @date 2024/04/19
     */
    private boolean setDomainOwnerRecord(String domain) {
        // 设置 域名 解析器合约 和拥有着 ----这里默认拥有着都是 管理员 返回交易hash 后续再设置解析记录之前就要设置所有者 默认是管理员 后续可以改
        String hash = null;
        // 判断是否设置成功
        TransactionReceipt transactionReceipt = null;
        try {
            // 查询指定域名 是否设置了解析记录
            String flag =
                DomainMappingUtil.recordExists(domain, chainAccountAddress, registryContractAddress);
            if (flag.equals("false")) {
                hash = DomainMappingUtil.setRecordByManager(chainId, domain, chainAccountAddress,
                    resolverContractAddress, credentials, manmagerContractAddress);
                transactionReceipt = DomainMappingUtil.getTransactionReceipt(hash);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return transactionReceipt.isStatusOK();
    }

}
