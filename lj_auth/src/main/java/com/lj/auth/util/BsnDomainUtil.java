package com.lj.auth.util;

import static com.lj.auth.common.CommonConstant.*;

import java.util.HashMap;
import java.util.Map;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;

/**
 * <AUTHOR>
 * @Description
 * @date 2023/5/31 16:12
 */
public class BsnDomainUtil {
    public static void main(String[] args) {

        // Map<String, List<String>> map = new HashMap<>(1);
        // map.put("domains", Collections.singletonList("abylzh.bsn"));
        // JSONObject jsonObject = JSONObject.parseObject((String) BsnDomainUtil.domainAvailable(JSON.toJSONString(map)));
        // System.out.println(jsonObject);
        // {
        // "attach": "",
        // "domain": "aaa.com",
        // "level": 1,
        // "newOwner": "0x5519d460e7e08ac79ab942088cc09587558c0896",
        // "notifyUrl": "http://aaa.bbb.com/notifyUrl",
        // "outTradeNo": "D1002305201503010001",
        // "timestamp": "2322-05-11 12:12:12"
        // }
        // 国密
        // ******************************************
        // 8cd9c90fa0cc10924acffab7daf7f9c36cbb0715785a53fd64d95b8f7615a025
        // 6e9ebea8e0123c03a1a50a2b5bf1fa84986a8751020fa7100d8086bd4a160f6a1168725ea69e86c1ac1c28d4cf0d3457e5c8477b054bee2c8713363232828dd1

        // ******************************************
        Map<String, Object> map = new HashMap<>(5);
        // map.put("domain", "chaihome.bsn");
        // Map<String, Object> map1 = new HashMap<>(5);
        // map1.put("domain","xg.web3");
        // Map<String, Object> map2 = new HashMap<>(5);
        // map2.put("pageNum",1);
        // map2.put("pageSize",10);
        // List<Object> params = new ArrayList<>();
        // map1.put("address", "******************************************");
        // map1.put("chainId", 1002);
        // params.add(map1);
        // map2.put("address", params);
        // map.put("data", map1);
        // map.put("page", map2);
        // map.put("resolveInfos", map2);
        // map.put("owner", "******************************************");
        // map.put("level", 1);
        // map.put("newOwner", "******************************************");
        // map.put("notifyUrl", "https://5h5104r332.goho.co/wallet/test/b");
        // map.put("outTradeNo", "YM16987547718126b1");
        // map.put("timestamp", "2023-14-14 11:11:11");
        // map.put("domainServeType", 0);
        // map.put("data", map1);
        // map.put("page", map2);
        JSONObject jsonObject =
            JSONObject.parseObject((String)BsnDomainUtil.adaptedSearches(JSON.toJSONString(map)));
        // JSONObject.parseObject((String)BsnDomainUtil.setResolveInfo(JSON.toJSONString(map)));
        // JSONObject.parseObject((String)BsnDomainUtil.setAdmin(JSON.toJSONString(map)));
        // JSONObject.parseObject((String)BsnDomainUtil.domainSearch(JSON.toJSONString(map)));
        System.out.println(jsonObject);
    }

    /**
     * 查询域名年服务费
     */
    public static Object domainFee(String paramJson) {
        return HttpUtil.post(URL + DOMAIN_FEE, paramJson);
    }

    /**
     * 查询业务费
     */
    public static Object tradeFee(String paramJson) {
        return HttpUtil.post(URL + TRADE_FEE, paramJson);
    }

    /**
     * 查询域名状态
     */
    public static Object domainAvailable(String paramJson) {
        return HttpUtil.post(URL + DOMAIN_AVAILABLE, paramJson);
    }

    /**
     * 域名注册
     */
    public static Object serveRegister(String paramJson) {
        return HttpUtil.post(URL + SERVE_REGISTER, paramJson);
    }

    /**
     * 域名注销
     */
    public static Object serveDeregister(String paramJson) {
        return HttpUtil.post(URL + SERVE_DEREGISTER, paramJson);
    }

    /**
     * 域名所有者设置
     */
    public static Object setOwner(String paramJson) {
        return HttpUtil.post(URL + SET_OWNER, paramJson);
    }

    /**
     * 域名管理者设置
     */
    public static Object setAdmin(String paramJson) {
        return HttpUtil.post(URL + SET_ADMIN, paramJson);
    }

    /**
     * 查询域名服务结果
     */
    public static Object serveSearch(String paramJson) {
        return HttpUtil.post(URL + SERVE_SEARCH, paramJson);
    }

    /**
     * 查询域名详情
     */
    public static Object domainSearch(String paramJson) {
        return HttpUtil.post(URL + DOMAIN_SEARCH, paramJson);
    }

    /**
     * 查询域名详情
     */
    public static Object pubSearch(String paramJson) {
        return HttpUtil.post(URL + PUB_SEARCH, paramJson);
    }

    /**
     * ===============星级域名================
     */
    /**
     * 星级域名查询
     */
    public static String starDomainSearch(String paramJson) {
        return HttpUtil.postJson(URL + STAR_DOMAIN_SEARCHES, paramJson);
    }

    /**
     * 星级域名申请权购买
     */
    public static String starDomainBuy(String paramJson) {
        return HttpUtil.postJson(URL + STAR_DOMAIN_BUY, paramJson);
    }

    /**
     * 查询购买星级域名申请权结果
     */
    public static String starDomainBuyResult(String paramJson) {
        return HttpUtil.postJson(URL + STAR_DOMAIN_BUY_RESULT, paramJson);
    }

    /**
     * 查询星级域名等级以及申请权费用
     */
    public static String starDomainLevelFee(String paramJson) {
        return HttpUtil.postJson(URL + STAR_DOMAIN_LEVEL_FEE, paramJson);
    }

    /**
     * 星级域名锁定、解锁
     */
    public static String starDomainLock(String paramJson) {
        return HttpUtil.postJson(URL + STAR_DOMAIN_LOCK, paramJson);
    }

    /**
     * 星级账户信息查询
     */
    public static String starAccountSearch(String paramJson) {
        return HttpUtil.postJson(URL + STAR_ACCOUNT_SEARCH, paramJson);
    }

    /**
     * 查询已经适配链信息
     */
    public static String adaptedSearches(String paramJson) {
        return HttpUtil.postJson(URL + ADAPTED_SEARCHES, paramJson);
    }

    /**
     * 查询域名解析记录
     */
    public static String resolveSearches(String paramJson) {
        return HttpUtil.postJson(URL + RESOLVE_SEARCHES, paramJson);
    }

    /**
     * 域名解析信息设置
     */
    public static String setResolveInfo(String paramJson) {
        return HttpUtil.postJson(URL + SET_RESOLVE_INFO, paramJson);
    }

    /**
     * 域名解析信息设置
     */
    public static String searchsAdmin(String paramJson) {
        return HttpUtil.postJson(URL + SEARCHES_ADMIN, paramJson);
    }
}
