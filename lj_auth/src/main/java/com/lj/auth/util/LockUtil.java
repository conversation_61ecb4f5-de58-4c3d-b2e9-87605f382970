package com.lj.auth.util;

/**
 * <AUTHOR>
 * @describe
 */

import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

@Slf4j
@Component
public class LockUtil {

    private final RedissonClient redissonClient;

    public LockUtil(RedissonClient redissonClient) {
        this.redissonClient = redissonClient;
    }


    // 默认超时时间
    private static final long DEFAULT_WAIT_TIMEOUT = 10;
    private static final TimeUnit DEFAULT_TIME_UNIT = TimeUnit.SECONDS;

    // 使用默认超时时间的锁方法
    public <T> T executeWithLockDefaultGetTimeOut(String lockKey, Supplier<T> supplier) {
        return executeWithLockGetTimeout(lockKey, DEFAULT_WAIT_TIMEOUT, supplier);
    }



    // 允许指定超时时间的锁方法 单位秒
    public <T> T executeWithLockGetTimeout(String lockKey, long waitTimeout, Supplier<T> supplier) {
        RLock lock = redissonClient.getLock(lockKey);
        String currenThreadName = Thread.currentThread().getName();
        long currentTimeMillis = System.currentTimeMillis();
        boolean isLocked = false;
        try {
            log.info("超时方式 ThreadName:{},加锁：{},currentTime:{}", currenThreadName,lockKey,currentTimeMillis);
            // 尝试在 waitTimeout 时间内获取锁，启用看门狗机制
            isLocked = lock.tryLock(waitTimeout, DEFAULT_TIME_UNIT);
            if (!isLocked) {
                log.info("超时方式 ThreadName:{},获取锁失败：{},获取超时时间：{},currentTime:{}",currenThreadName, lockKey,waitTimeout,currentTimeMillis);
                throw new IllegalArgumentException("请求过快请稍后尝试");
            }
            return supplier.get();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("Interrupted while acquiring lock", e);
        } finally {
            long finishTimeMillis = System.currentTimeMillis();
            log.info("超时方式 ThreadName:{}, 释放锁：{},耗时{}", currenThreadName,lockKey,finishTimeMillis-currentTimeMillis);
            if (isLocked) {
                lock.unlock();
            }
        }
    }



    // 阻塞获取锁的方法
    public <T> T executeWithBlockingLock(String lockKey, Supplier<T> supplier) {
        String threadName = Thread.currentThread().getName();
        long startTimeMillis = System.currentTimeMillis();
        log.info("阻塞方式 线程：{}, 尝试获取锁：{}", threadName,lockKey);
        RLock lock = redissonClient.getLock(lockKey);
        lock.lock();  // 阻塞获取锁，启用看门狗机制
        long currentTime = System.currentTimeMillis();
        log.info("线程:{},等待:{}ms, 获取锁成功:{}",threadName,currentTime-startTimeMillis,lockKey);
        try {
            return supplier.get();
        } finally {
            long finalTime = System.currentTimeMillis();
            log.info("阻塞方式,执行完毕,耗时:{}ms,  线程:{}，释放锁：{}",finalTime-currentTime,threadName, lockKey);
            lock.unlock();
        }
    }


}
