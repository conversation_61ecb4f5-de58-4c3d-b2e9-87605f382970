package com.lj.auth.util;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.time.format.DateTimeFormatter;

public class MedicalOrderParser {
    private static final ObjectMapper MAPPER = new ObjectMapper();

    public static void main(String[] args) {
        String aa =
            "{\"mainInfo\": {\"key1\": \"合计\", \"amount\": \"4.5\", \"value1\": \"4.5\", \"doctorName\": \"疼痛科门诊\", \"deptmentName\": \"疼痛科门诊\", \"hospitalName\": \"武汉大学中南医院\", \"doctorPicture\": \"https://mysticring.jiewai.pro/hospital/doctor.png\", \"infoStateName\": \"\", \"positionalTitles\": \"主治医师\", \"infoStateNameColor\": \"\"}, \"stateInfo\": {\"amount\": 4.5, \"payState\": 3, \"orderState\": 6, \"accountUUID\": \"YLaadbc9e4fa\", \"orderNumber\": \"*********************\", \"refundState\": 0, \"payStateName\": \"支付超时\", \"orderStateName\": \"订单关闭\", \"applicationType\": 7, \"refundStateName\": \"未退款\", \"showExpirationTime\": 0, \"applicationTypeName\": \"预约挂号\"}, \"orderDetailJson\": [{\"rows\": [{\"copy\": 1, \"title\": \"订单编号\", \"value\": \"*********************\", \"hasDetail\": 0, \"valueStyle\": {\"bold\": 1, \"color\": \"#08090A\"}, \"copyContent\": \"*********************\"}, {\"copy\": 0, \"title\": \"创建时间\", \"value\": \"2025-02-05 15:13:26\", \"hasDetail\": 0, \"valueStyle\": {\"bold\": 1, \"color\": \"#08090A\"}}, {\"copy\": 0, \"title\": \"订单类型\", \"value\": \"预约挂号\", \"hasDetail\": 0, \"valueStyle\": {\"bold\": 1, \"color\": \"#08090A\"}}], \"title\": \"订单信息\"}, {\"rows\": [{\"copy\": 0, \"title\": \"就诊人\", \"value\": \"江*(did:ctid***24DC)\", \"hasDetail\": 0, \"valueStyle\": {\"bold\": 1, \"color\": \"#08090A\"}}, {\"copy\": 0, \"title\": \"就诊日期\", \"value\": \"2025-02-12\", \"hasDetail\": 0, \"valueStyle\": {\"bold\": 1, \"color\": \"#08090A\"}}, {\"copy\": 0, \"title\": \"就诊时间\", \"value\": \"16:55-17:00\", \"hasDetail\": 0, \"valueStyle\": {\"bold\": 1, \"color\": \"#08090A\"}}], \"title\": \"就诊信息\"}, {\"rows\": [{\"copy\": 0, \"title\": \"关闭原因\", \"value\": \"支付已超时，订单关闭\", \"hasDetail\": 0, \"valueStyle\": {\"bold\": 1, \"color\": \"#08090A\"}}, {\"copy\": 0, \"title\": \"关闭时间\", \"value\": \"2025-02-05 15:29:02\", \"hasDetail\": 0, \"valueStyle\": {\"bold\": 1, \"color\": \"#08090A\"}}], \"title\": \"关闭信息\"}]}";
        String visitPeriod = getVisitPeriod(aa);
        System.out.println(visitPeriod);
        String[] split = visitPeriod.split("/");
        System.out.println(split[0]);
        System.out.println(split[1]);
        String s = split[0];
        DateTime parse = DateUtil.parse(s, DatePattern.NORM_DATETIME_MINUTE_PATTERN);
        System.out.println(parse);
    }

    /**
     * 获取就诊信息详情
     * 
     * @param json 原始JSON
     * @param field 字段标题（如"就诊人"）
     * @return 字段值或null
     */
    public static String getMedicalInfo(String json, String field) {
        return findOrderDetail(json, "就诊信息", field);
    }

    /**
     * 获取完整就诊时间范围
     * 
     * @return ISO格式时间范围（如"2025-02-12T16:55/17:00"）
     */
    public static String getVisitPeriod(String json) {
        String date = getMedicalInfo(json, "就诊日期");
        String timeRange = getMedicalInfo(json, "就诊时间");
        if (date == null || timeRange == null)
            return null;

        String[] times = timeRange.split("-");
        return String.format("%s %s/%s %s", date, times[0], date, times[1]);
    }

    private static String findOrderDetail(String json, String section, String field) {
        try {
            JsonNode details = MAPPER.readTree(json).get("orderDetailJson");
            if (details == null)
                return null;

            for (JsonNode item : details) {
                if (section.equals(item.get("title").asText())) {
                    for (JsonNode row : item.get("rows")) {
                        if (field.equals(row.get("title").asText())) {
                            return row.get("value").asText();
                        }
                    }
                }
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }

}
