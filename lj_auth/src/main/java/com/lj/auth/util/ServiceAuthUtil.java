package com.lj.auth.util;

import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.interfaces.DecodedJWT;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.time.Duration;
import java.time.Instant;
import java.util.Date;

/**
 * <AUTHOR>
 * @describe
 */

@Component
public class ServiceAuthUtil {
    @Value("${serviceAuth.apiSecret}")
    private String serviceSecret;

    @Value("${serviceAuth.issuer}")
    private String issuer;

    private  Algorithm algorithm;

    @PostConstruct
    public void init() {
        this.algorithm = Algorithm.HMAC256(serviceSecret);
    }



    /**
     * 生成服务间调用Token
     */
    public String generateServiceToken(String serviceName) {
        return JWT.create()
                .withIssuer(issuer)
                .withSubject(serviceName)
                .withClaim("type", "service")
                .withExpiresAt(Date.from(Instant.now().plus(Duration.ofHours(1))))
                .withIssuedAt(new Date())
                .sign(algorithm);
    }

    /**
     * 验证服务Token
     */
    public boolean validateServiceToken(String token) {
        try {
            DecodedJWT jwt = JWT.require(algorithm)
                    .withIssuer(issuer)
                    .withClaim("type", "service")
                    .build()
                    .verify(token);

            return jwt.getExpiresAt().after(new Date());
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获取服务名
     */
    public String getServiceName(String token) {
        try {
            DecodedJWT jwt = JWT.require(algorithm).build().verify(token);
            return jwt.getSubject();
        } catch (Exception e) {
            return null;
        }
    }
}
