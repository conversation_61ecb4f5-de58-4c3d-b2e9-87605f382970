package com.lj.auth.util;

import java.io.BufferedReader;
import java.io.IOException;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import cn.hutool.http.HttpRequest;


/**
 * <AUTHOR>
 * @Description
 * @date 2023/5/23 17:25
 */
public class HttpUtil {
    public static Object postFrom(String url, Map<String,Object> map) {
        return HttpRequest.post(url)
                .form(map)
                .execute().body();
    }
    public static String postJson(String url, String paramJson) {
        return HttpRequest.post(url)
                .body(paramJson)
                .execute().body();
    }
    public static Object post(String url, String paramJson) {
        return HttpRequest.post(url)
                .body(paramJson)
                .execute().body();
    }
    
    public static Object get(String url, String paramJson) {
        return HttpRequest.get(url)
                .body(paramJson)
                .execute().body();
    }

    /**
     * 将通知参数转化为字符串
     * @param request
     * @return
     */
    public static String readData(HttpServletRequest request) {
        BufferedReader br = null;
        try {
            StringBuilder result = new StringBuilder();
            br = request.getReader();
            for (String line; (line = br.readLine()) != null; ) {
                if (result.length() > 0) {
                    result.append("\n");
                }
                result.append(line);
            }
            return result.toString();
        } catch (IOException e) {
            throw new RuntimeException(e);
        } finally {
            if (br != null) {
                try {
                    br.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }
    
}
