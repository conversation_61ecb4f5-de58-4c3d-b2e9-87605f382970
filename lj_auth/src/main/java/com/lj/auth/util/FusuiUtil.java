package com.lj.auth.util;

import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.*;

import javax.annotation.Resource;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import javax.xml.bind.DatatypeConverter;

import com.lj.auth.exception.ServiceException;
import com.lj.auth.mapper.GlobalConfigMapper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;


import cn.hutool.core.util.StrUtil;

/**
 * @Author: zjd
 * @Description:
 * @Date: 2023/12/25 10:04
 */
@Component
public class FusuiUtil {

    @Value("${fusui.appId}")
    private String appId;

    @Value("${fusui.taxId}")
    private String taxId;

    @Value("${fusui.secret}")
    private String secret;

    @Value("${fusui.gateWay}")
    private String gateWay;


    @Resource
    private RestTemplate restTemplate;
    @Resource
    private GlobalConfigMapper globalConfigMapper;


    private static final String CHARACTERS = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"; // 自定义字符集


    /**
     * 自由职业者签约接口
     * @param userName 用户名
     * @param phone 手机号
     * @param idCard 身份证号
     * @return 调用结果
     */
    public JSONObject uploadPeople(String userName, String phone, String idCard) {
        String taskNo = globalConfigMapper.queryConfig("fusui_task_no");
        String uri = "/api/open/uploadPeople";
        Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put(  "idCard", idCard);
        paramsMap.put(  "taskNo", taskNo);
        paramsMap.put(  "mobile", phone);
        paramsMap.put(  "userName", userName);
        String requestParam = getRequestParam(paramsMap);
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/json");
        HttpEntity<String> httpEntity = new HttpEntity<>(requestParam, headers);
        String url = gateWay + uri;
        System.out.println("远程调用地址:" + url);
        ResponseEntity<JSONObject> exchange = restTemplate.exchange(url, HttpMethod.POST, httpEntity, JSONObject.class);
        if (!exchange.getStatusCode().equals(HttpStatus.OK)) {
            throw new ServiceException("签约失败");
        }
        return exchange.getBody();
    }

    /**
     * 自由职业者查询接口
     * @param peopleId 签约人Id
     * @return 调用结果
     */
    public JSONObject queryPeople(String peopleId) {
        String uri = "/api/open/queryPeople";
        Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put(  "peopleId", peopleId);
        String requestParam = getRequestParam(paramsMap);
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/json");
        HttpEntity<String> httpEntity = new HttpEntity<>(requestParam, headers);
        String url = gateWay + uri;
        System.out.println("远程调用地址:" + url);
        ResponseEntity<JSONObject> exchange = restTemplate.exchange(url, HttpMethod.POST, httpEntity, JSONObject.class);
        if (!exchange.getStatusCode().equals(HttpStatus.OK)) {
            throw new ServiceException("查询失败");
        }
        return exchange.getBody();
    }

    /**
     * 自由职业者查询接口
     * @param idCard 签约人身份证号
     * @return 调用结果
     */
    public JSONObject queryPeopleByNameAndIdCard(String realName, String idCard) {
        String uri = "/api/open/queryPeople";
        Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("userName", realName);
        paramsMap.put(  "idCard", idCard);
        String requestParam = getRequestParam(paramsMap);
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/json");
        HttpEntity<String> httpEntity = new HttpEntity<>(requestParam, headers);
        String url = gateWay + uri;
        System.out.println("远程调用地址:" + url);
        ResponseEntity<JSONObject> exchange = restTemplate.exchange(url, HttpMethod.POST, httpEntity, JSONObject.class);
        if (!exchange.getStatusCode().equals(HttpStatus.OK)) {
            throw new ServiceException("查询失败");
        }
        return exchange.getBody();
    }

    public JSONObject queryPeopleByNameAndIdCardAndTaxID(String realName, String idCard) {
        String uri = "/api/open/queryPeopleList";
        Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put(  "taxId", taxId);
        paramsMap.put(  "appId", appId);
        paramsMap.put(  "userName", realName);
        paramsMap.put(  "idCard", idCard);
        String requestParam = getRequestParam(paramsMap);
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/json");
        HttpEntity<String> httpEntity = new HttpEntity<>(requestParam, headers);
        String url = gateWay + uri;
        System.out.println("远程调用地址:" + url);
        ResponseEntity<JSONObject> exchange = restTemplate.exchange(url, HttpMethod.POST, httpEntity, JSONObject.class);
        if (!exchange.getStatusCode().equals(HttpStatus.OK)) {
            throw new ServiceException("查询失败");
        }
        return exchange.getBody();
    }

    //----------------------------商户账户管理----------------------------

    /**
     * 余额查询接口
     * @return
     */
    public JSONObject queryAmount() {
        String uri = "/api/open/queryAmount";
        Map<String, Object> paramsMap = new HashMap<>();
        String requestParam = getRequestParam(paramsMap);
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/json");
        HttpEntity<String> httpEntity = new HttpEntity<>(requestParam, headers);
        String url = gateWay + uri;
        System.out.println("远程调用地址:" + url);
        ResponseEntity<JSONObject> exchange = restTemplate.exchange(url, HttpMethod.POST, httpEntity, JSONObject.class);
        if (!exchange.getStatusCode().equals(HttpStatus.OK)) {
            throw new ServiceException("余额查询失败");
        }
        return exchange.getBody();
    }

    //----------------------------结算发放----------------------------

    /**
     * 账户类型查询接口
     * @return 调用结果
     */
    public JSONObject queryPayMethod() {
        String taskNo = globalConfigMapper.queryConfig("fusui_task_no");
        String uri = "/api/open/queryPayMethod";
        Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("taskNo", taskNo);
        String requestParam = getRequestParam(paramsMap);
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/json");
        HttpEntity<String> httpEntity = new HttpEntity<>(requestParam, headers);
        String url = gateWay + uri;
        System.out.println("远程调用地址:" + url);
        ResponseEntity<JSONObject> exchange = restTemplate.exchange(url, HttpMethod.POST, httpEntity, JSONObject.class);
        if (!exchange.getStatusCode().equals(HttpStatus.OK)) {
            throw new ServiceException("账户类型查询失败");
        }
        return exchange.getBody();
    }

    /**
     * 结算发放接口
     * @param accountNumber 用户收款账号
     * @param amount 实发金额（单位：元）最多两位小数
     * @param grantType 账户类型（银行卡、支付宝、支付宝ISV、微信支付）
     * @param idCard 身份证号（若身份证最后一位为X，请大写）
     * @param mobile 手机号
     * @param outBizNo 外部流水号 订单唯一编号
     * @param userName 姓名
     * @return 调用结果
     */
    public JSONObject uploadPlan(String accountNumber, BigDecimal amount, String grantType, String idCard, String mobile, String outBizNo, String userName) {
        String taskNo = globalConfigMapper.queryConfig("fusui_task_no");
        String uri = "/api/open/uploadPlan";
        Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("accountNumber", accountNumber);
        paramsMap.put("afterTaxAmount", amount);
        paramsMap.put("grantType", grantType);
        paramsMap.put("idCard", idCard);
        paramsMap.put("mobile", mobile);
        paramsMap.put("outBizNo", outBizNo);
        paramsMap.put("taskNo", taskNo);
        paramsMap.put("userName", userName);
        String requestParam = getRequestParam(paramsMap);
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/json");
        HttpEntity<String> httpEntity = new HttpEntity<>(requestParam, headers);
        String url = gateWay + uri;
        System.out.println("远程调用地址:" + url);
        ResponseEntity<JSONObject> exchange = restTemplate.exchange(url, HttpMethod.POST, httpEntity, JSONObject.class);
        if (!exchange.getStatusCode().equals(HttpStatus.OK)) {
            throw new ServiceException("提现请求发送失败");
        }
        return exchange.getBody();
    }

    /**
     * 结算发放结果查询接口
     * @param outBizNo 外部流水号
     * @return 调用结果
     */
    public JSONObject queryPlan(String outBizNo) {
        String uri = "/api/open/queryPlan";
        Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("outBizNo", outBizNo);
        String requestParam = getRequestParam(paramsMap);
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/json");
        HttpEntity<String> httpEntity = new HttpEntity<>(requestParam, headers);
        String url = gateWay + uri;
        System.out.println("远程调用地址:" + url);
        ResponseEntity<JSONObject> exchange = restTemplate.exchange(url, HttpMethod.POST, httpEntity, JSONObject.class);
        if (!exchange.getStatusCode().equals(HttpStatus.OK)) {
            throw new ServiceException("提现发放结果查询失败");
        }
        return exchange.getBody();
    }

    /**
     * 结算回单下载接口
     * @param outBizNo 外部流水号
     * @return 调用结果
     */
    public JSONObject queryReceipt(String outBizNo) {
        String uri = "/api/open/queryReceipt";
        Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("outBizNo", outBizNo);
        String requestParam = getRequestParam(paramsMap);
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/json");
        HttpEntity<String> httpEntity = new HttpEntity<>(requestParam, headers);
        String url = gateWay + uri;
        System.out.println("远程调用地址:" + url);
        ResponseEntity<JSONObject> exchange = restTemplate.exchange(url, HttpMethod.POST, httpEntity, JSONObject.class);
        if (!exchange.getStatusCode().equals(HttpStatus.OK)) {
            throw new ServiceException("结算回单接口调用失败");
        }
        return exchange.getBody();
    }

    /**
     * 结算发放结果分页查询
     * @param page 当前页数
     * @param limit 分页大小 最大1000
     * @param outBizNo 外部流水号 非必填
     * @param auditStatus 审核状态 0-待审核 1-审核通过 2,3-人工审核拒绝  非必填
     * @param grantStatus 发放(推送)状态 1-发放通过 2-发放拒绝 非必填
     * @param payStatus 支付状态 0-未处理 1-待打款 2-打款中 3-打款成功 4-打款失败 5-退票退款 非必填
     * @param startTime 开始时间 格式'yyyy-MM-dd HH:mm:ss'
     * @param endTime 结束时间 格式'yyyy-MM-dd HH:mm:ss'
     * @return 调用结果
     */
    public JSONObject queryPlanPage(Integer page, Integer limit, String outBizNo, Integer auditStatus, Integer grantStatus, Integer payStatus, String startTime, String endTime) {
        String taskNo = globalConfigMapper.queryConfig("fusui_task_no");
        String uri = "/api/open/queryPlanPage";
        Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("page", page);
        paramsMap.put("limit", limit);
        paramsMap.put("taskNo", taskNo);
        paramsMap.put("startTime", startTime);
        paramsMap.put("endTime", endTime);
        if (StrUtil.isNotEmpty(outBizNo)) {
            paramsMap.put("outBizNo", outBizNo);
        }
        if (auditStatus != null) {
            paramsMap.put("auditStatus", auditStatus);
        }
        if (grantStatus != null) {
            paramsMap.put("grantStatus", grantStatus);
        }
        if (payStatus != null) {
            paramsMap.put("payStatus", payStatus);
        }
        String requestParam = getRequestParam(paramsMap);
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/json");
        HttpEntity<String> httpEntity = new HttpEntity<>(requestParam, headers);
        String url = gateWay + uri;
        System.out.println("远程调用地址:" + url);
        ResponseEntity<JSONObject> exchange = restTemplate.exchange(url, HttpMethod.POST, httpEntity, JSONObject.class);
        if (!exchange.getStatusCode().equals(HttpStatus.OK)) {
            throw new ServiceException("提现发放结果分页查询接口调用失败");
        }
        return exchange.getBody();
    }

    public String getRequestParam(Map<String, Object> paramsMap) {
        System.out.println("===========================================请求开始============================================");
        String json = JSON.toJSONString(paramsMap);
        System.out.println("业务参数: " + json);
        // 毫秒
        String currentTimeMillis = String.valueOf(System.currentTimeMillis());
        // 固定参数
        Map<String, String> param = new HashMap<String, String>();
        param.put("appId", appId);
        param.put("nonce", getNonce());
        param.put("timestamp", currentTimeMillis);
        param.put("bizContent", json);
        String sign = null;
        String sortParams = sortParams(param);
        System.out.println("排序字段:" + sortParams);
        String sb = splice(secret, sortParams);
        System.out.println("加签名字符串排序字段 :" + sb);
        try {
            sign = sign(sb, currentTimeMillis, secret);
        } catch (Exception e) {
            e.printStackTrace();
        }
        param.put("sign", sign);
        System.out.println("签名 : " + sign);
        String postJson = JSON.toJSONString(param);
        System.out.println("请求参数: " + postJson);
        return postJson;
    }


    /**
     * 将map排序为字符串
     * @param parameters
     * @return
     */
    public static String sortParams(Map<String, String> parameters) {

        if (parameters.isEmpty()) {
            return null;
        }
        List<String> removeKeys = new ArrayList<>();
        for (Map.Entry<String, String> entry : parameters.entrySet()) {
            if (isBlank(entry.getValue())) {
                removeKeys.add(entry.getKey());
            }
        }

        for (String key : removeKeys) {
            parameters.remove(key);
        }
        StringBuilder stringBuilder = new StringBuilder();
        SortedMap<String, String> paramMap = new TreeMap<>(parameters);
        int index = 0;
        for (Map.Entry<String, String> entry : paramMap.entrySet()) {
            stringBuilder.append(entry.getKey()).append("=").append(entry.getValue());
            index++;
            if (index != parameters.size()) {
                stringBuilder.append("&");
            }
        }
        return stringBuilder.toString();
    }


    public static byte[] hmac256(byte[] key, String msg) throws Exception {
        Mac mac = Mac.getInstance("HmacSHA256");
        SecretKeySpec secretKeySpec = new SecretKeySpec(key, mac.getAlgorithm());
        mac.init(secretKeySpec);
        return mac.doFinal(msg.getBytes(StandardCharsets.UTF_8));
    }

    public static String sha256Hex(String s) throws Exception {
        MessageDigest md = MessageDigest.getInstance("SHA-256");
        byte[] d = md.digest(s.getBytes(StandardCharsets.UTF_8));
        return DatatypeConverter.printHexBinary(d).toLowerCase();
    }

    /**
     * 拼接appSecret和
     * @param appSecret
     * @param content sortParams
     * @return
     */
    public static String splice(String appSecret, String content) {

        if (appSecret == null ) {
            return content;
        }
        if (appSecret.length() < 10){
            return "";
        }

        // 截取前5位和后5位
        String front = appSecret.substring(0, 5);
        String back = appSecret.substring(appSecret.length() - 5);

        return front + content + back;
    }


    /**
     * @param sortParam 排序map后得到的字符串
     * @param timestamp 时间戳
     * @param appSecret 应用秘钥
     * @return 签名值
     * @throws Exception 异常
     */
    public static String sign(String sortParam,
                              String timestamp,
                              String appSecret) throws Exception {
        //将排序后字符串转为sha256Hex
        String signText = sha256Hex(sortParam);
        System.out.println(signText);
        //计算签名
        byte[] secretSigning = hmac256((appSecret).getBytes(StandardCharsets.UTF_8), timestamp);
        //计算后得到签名
        return DatatypeConverter.printHexBinary(hmac256(secretSigning, signText)).toLowerCase();
    }

    public static boolean isBlank(CharSequence str) {
        int length;

        if ((str == null) || ((length = str.length()) == 0)) {
            return true;
        }

        for (int i = 0; i < length; i++) {
            // 只要有一个非空字符即为非空字符串
            if (false == isBlankChar(str.charAt(i))) {
                return false;
            }
        }

        return true;
    }

    public static boolean isBlankChar(int c) {
        return Character.isWhitespace(c)
                || Character.isSpaceChar(c)
                || c == '\ufeff'
                || c == '\u202a'
                || c == '\u0000';
    }

    public String getNonce() {
        int nonceLength = 20; // 设置 nonce 的长度
        return generateNonce(nonceLength);
    }

    public static String generateNonce(int length) {
        Random random = new Random();
        StringBuilder nonceBuilder = new StringBuilder(length);
        for (int i = 0; i < length; i++) {
            nonceBuilder.append(CHARACTERS.charAt(random.nextInt(CHARACTERS.length())));
        }
        return nonceBuilder.toString();
    }

    /**
     * @param userName
     * @param idCard
     * @return {@link JSONObject }
     * <AUTHOR>
     * @date 2024/07/09
     */
    public JSONObject queryPeopleList(String userName,String idCard) {
            String uri = "/api/open/queryPeopleList";
            Map<String, Object> paramsMap = new HashMap<>();
            paramsMap.put(  "taxId", taxId);
            paramsMap.put(  "appId", appId);
            paramsMap.put(  "userName", userName);
            paramsMap.put(  "idCard", idCard);
            String requestParam = getRequestParam(paramsMap);
            HttpHeaders headers = new HttpHeaders();
            headers.add("Content-Type", "application/json");
            HttpEntity<String> httpEntity = new HttpEntity<>(requestParam, headers);
            String url = gateWay + uri;
            System.out.println("远程调用地址:" + url);
            ResponseEntity<JSONObject> exchange = restTemplate.exchange(url, HttpMethod.POST, httpEntity, JSONObject.class);
            if (!exchange.getStatusCode().equals(HttpStatus.OK)) {
                throw new ServiceException("查询失败");
            }
            return exchange.getBody();

    }
}
