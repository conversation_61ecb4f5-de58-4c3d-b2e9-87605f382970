package com.lj.auth.util;

import static com.lj.auth.common.CommonConstant.*;

import java.util.Objects;
import java.util.Optional;

import javax.servlet.http.HttpServletRequest;

import cn.hutool.core.util.StrUtil;

import com.lj.auth.exception.ServiceException;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

public class HeadUtil {
    public static HttpServletRequest getRequest() {
        return Optional.ofNullable(((ServletRequestAttributes)RequestContextHolder.getRequestAttributes()))
            .orElseThrow(() -> new RuntimeException("未获取到当前请求信息")).getRequest();
    }

    /**
     * 获取Key获取Value
     *
     * @return String
     */
    public static String getHeadKey(String key) {
        String value = HeadUtil.getRequest().getHeader(key);
        if (StrUtil.isEmpty(value)) {
            throw new ServiceException("无法获取经销商标识:" + key);
        }
        return value;
    }

    /**
     * 获取经销商UUID
     *
     * @return
     */
    public static String getOperaterUUID() {
        return getHeadKey(OPERATE_UUID);
    }

    /**
     * 获取IP
     */
    public static String getIP() {
        HttpServletRequest request = HeadUtil.getRequest();
        return IpUtil.getRealIp(request);
    }

    /**
     * 获取应用标识
     */
    public static String getApplicationSymbol() {
        return HeadUtil.getRequest().getHeader("application_symbol");
    }
    
    /**
     * 平台来源 PC、APP、H5、WXMINI
     * @return
     */
    public static String getPlatformSource(){
        return getHeadKey("platformSource");
    }
}
