package com.lj.auth.util;

import java.util.UUID;

public class UUIdUtil {
    /**
     * 创建uuid
     *
     * @return {@link String}
     */
    public static String createUUId() {
        UUID uuid = UUID.randomUUID();
        String uuidValue = uuid.toString().replaceAll("-", "").substring(0, 10);
        return uuidValue;
    }
    
    /**
     * 创建uuid
     * @param length 指定长度(最长32位)
     * @return
     */
    public static String createUUId(int length) {
        if (length > 32){
            throw new RuntimeException("UUID最长32位");
        }
        UUID uuid = UUID.randomUUID();
        String uuidValue = uuid.toString().replaceAll("-", "").substring(0, length);
        return uuidValue;
    }
}
