package com.lj.auth.util;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2023/10/18
 */
@Slf4j
public class UploadUtils {

    // 字符编码格式
    private static String charsetCode = "utf-8";

    /**
     * 图片大小限制
     */
    public static final long IMAGE_MAX_SIZE = 30 * 1024 * 1024;// 30M

    /**
     * 图片类型
     */
    public static final List<String> IMAGE_TYPE = Arrays.asList(".jpg", ".jpeg", ".png");

    /**
     * 单图片上传
     *
     * @param file 图片文件
     * @param filePath 图片存放的地址
     * @param folderName 文件夹文件，可为空，如果不为空，需要带“/”
     * @return 返回图片的名字+后缀
     */
    public static String upload(@RequestParam("file") MultipartFile file,
        @RequestParam("filePath") String filePath, String folderName) {
        try {
            if (file.isEmpty()) {
                return null;
            }

            String fileName = file.getOriginalFilename();// 获取文件名
            String suffixName = fileName.substring(fileName.lastIndexOf("."));// 获取文件的后缀名
            if (!IMAGE_TYPE.contains(suffixName)) {// 判断图片后缀是否正确
                throw new IllegalArgumentException("图片格式错误");
            }
            long size = file.getSize();// 获取文件大小
            if (size > IMAGE_MAX_SIZE) {// 判断文件大小
                throw new IllegalArgumentException("图片文件过大");
            }

            String name = UUID.randomUUID().toString() + System.currentTimeMillis();// 设置新的文件名
            String path = filePath + "/" + folderName + name + suffixName;// 拼接完整的存放路径地址
            log.debug("上传图片路径:{}", path);
            File dest = new File(path);
            if (!dest.getParentFile().exists()) {// 检测是否存在目录
                dest.getParentFile().mkdirs();// 新建文件夹
            }
            file.transferTo(dest);// 文件写入
            return folderName + name + suffixName;// 返回文件名字
        } catch (Exception e) {
            e.printStackTrace();
            log.error("单图上传失败:{}", e.getMessage());
            return null;
        }
    }

    /**
     * 多图片上传
     *
     * @param files 图片文件
     * @param filePath 图片存放的地址
     * @param folderName 文件夹文件，可为空，如果不为空，需要带“/”
     * @return 返回图片的名字+后缀
     */
    public static String uploads(List<MultipartFile> files, String filePath, String folderName) {
        try {
            String fileUrl = "";
            for (MultipartFile file : files) {
                if (file.isEmpty()) {
                    return null;
                }
                String fileName = file.getOriginalFilename();// 获取文件名
                String suffixName = fileName.substring(fileName.lastIndexOf("."));// 获取文件的后缀名
                if (!IMAGE_TYPE.contains(suffixName)) {// 判断图片后缀是否正确
                    throw new IllegalArgumentException("图片格式错误");
                }
                long size = file.getSize();// 获取文件大小
                if (size > IMAGE_MAX_SIZE) {// 判断文件大小
                    throw new IllegalArgumentException("图片文件过大");
                }
                String name = UUID.randomUUID().toString() + System.currentTimeMillis();// 设置新的文件名
                String path = filePath + "/" + folderName + name + suffixName;// 拼接完整的存放路径地址
                log.info("上传图片路径:{}", path);
                File dest = new File(path);
                if (!dest.getParentFile().exists()) {// 检测是否存在目录
                    dest.getParentFile().mkdirs();// 新建文件夹
                }
                file.transferTo(dest);// 文件写入
                // 逗号分隔,并去掉最后一个逗号
                fileUrl += folderName + name + suffixName + ",";// 返回文件名字
            }
            fileUrl = fileUrl.substring(0, fileUrl.length() - 1);
            return fileUrl;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("多图片上传失败:{}", e.getMessage());
            return null;
        }
    }

    /**
     * 多图片上传
     *
     * @param files 图片文件
     * @param filePath 图片存放的地址
     * @return 返回图片的名字+后缀
     */
    public static String uploads(@RequestParam("file") List<MultipartFile> files,
        @RequestParam("filePath") String filePath, Long maxSize) {
        try {
            String fileUrl = "";
            for (MultipartFile file : files) {
                if (file.isEmpty()) {
                    return null;
                }
                String fileName = file.getOriginalFilename();// 获取文件名
                String suffixName = fileName.substring(fileName.lastIndexOf("."));// 获取文件的后缀名
                if (!IMAGE_TYPE.contains(suffixName)) {// 判断图片后缀是否正确
                    throw new IllegalArgumentException("图片格式错误");
                }
                long size = file.getSize();// 获取文件大小
                Long fileMaxSize = IMAGE_MAX_SIZE;
                if (maxSize != null) {
                    fileMaxSize = maxSize;
                }
                if (size > fileMaxSize) {// 判断文件大小
                    throw new IllegalArgumentException("图片文件过大");
                }
                String name = UUID.randomUUID().toString() + System.currentTimeMillis();// 设置新的文件名
                String path = filePath + "/" + name + suffixName;// 拼接完整的存放路径地址
                log.info("上传图片路径:{}", path);
                File dest = new File(path);
                if (!dest.getParentFile().exists()) {// 检测是否存在目录
                    dest.getParentFile().mkdirs();// 新建文件夹
                }
                file.transferTo(dest);// 文件写入
                // 逗号分隔,并去掉最后一个逗号
                fileUrl += name + suffixName + ",";// 返回文件名字
            }
            fileUrl = fileUrl.substring(0, fileUrl.length() - 1);
            return fileUrl;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("多图片上传失败:{}", e.getMessage());
            return null;
        }
    }

    /**
     * 合约文件上传
     *
     * @param file 合约文件
     * @param filePath 合约文件存放的地址
     * @return 合约文件的名字+后缀
     */
    public static String uploadContract(@RequestParam("file") MultipartFile file,
        @RequestParam("filePath") String filePath) {
        try {
            if (file.isEmpty()) {
                return null;
            }
            String fileName = file.getOriginalFilename();// 获取文件名
            String path = filePath + "/" + fileName;// 拼接完整的存放路径地址
            log.debug("上传合约路径", path);
            File dest = new File(path);
            if (!dest.getParentFile().exists()) {// 检测是否存在目录
                dest.getParentFile().mkdirs();// 新建文件夹
            }
            file.transferTo(dest);// 文件写入
            return fileName;// 返回文件名字
        } catch (Exception e) {
            e.printStackTrace();
            log.error("文件上传失败:", e.getMessage());
            return null;
        }
    }

    /**
     * 资质文件上传
     *
     * @param file 文件
     * @param filePath 资质文件存放的地址
     * @param floderName 文件夹名称
     * @return 模板文件的名字+后缀
     */
    public static Map uploadFile(@RequestParam("file") MultipartFile file,
        @RequestParam("filePath") String filePath, @RequestParam("floderName") String floderName) {
        Map<String, String> resultMap = new HashMap<>();
        try {
            if (file.isEmpty()) {
                return null;
            }
            String fileName = file.getOriginalFilename();// 获取文件名
            log.info("资质文件名称:{}", fileName);
            // fileName = UUID.randomUUID().toString() + System.currentTimeMillis();//设置新的文件名
            String path = filePath + floderName + "/" + fileName;
            log.info("上传资质文件路径:{}", path);
            File dest = new File(path);
            if (!dest.getParentFile().exists()) {// 检测是否存在目录
                dest.getParentFile().mkdirs();// 新建文件夹
            }
            file.transferTo(dest);// 文件写入
            resultMap.put("fileName", fileName);
            resultMap.put("fileUrl", floderName + "/" + fileName);
            // 返回文件路径和名称
            return resultMap;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("资质文件上传异常:{}", e.getMessage());
            return null;
        }
    }

    /**
     * 上传多个文件
     *
     * @param files 文件
     * @param filePath 文件存放的地址
     * @param floderName 文件夹名称
     * @return 模板文件的名字+后缀
     */
    public static String uploadFiles(@RequestParam("file") List<MultipartFile> files,
        @RequestParam("filePath") String filePath, @RequestParam("floderName") String floderName) {
        try {
            String fileUrl = floderName + "/";
            for (MultipartFile file : files) {
                if (file.isEmpty()) {
                    return null;
                }
                String fileName = file.getOriginalFilename();// 获取文件名
                log.info("fileName:{}", fileName);
                String path = filePath + floderName + "/" + fileName;
                log.info("上传文件路径:{}", path);
                File dest = new File(path);
                if (!dest.getParentFile().exists()) {// 检测是否存在目录
                    dest.getParentFile().mkdirs();// 新建文件夹
                }
                file.transferTo(dest);// 文件写入
                // 逗号分隔,并去掉最后一个逗号
                fileUrl += fileName + ",";
                if (StrUtil.isNotBlank(fileUrl)) {
                    fileUrl = fileUrl.substring(0, fileUrl.length() - 1);
                }
                return fileUrl;// 返回文件名字
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
        return null;
    }

    /**
     * 下载文件
     *
     * @param path 文件的位置
     * @param fileName 自定义下载文件的名称
     * @param resp http响应
     * @param req http请求
     */
    public static void downloadFile(String path, String fileName, HttpServletResponse resp,
        HttpServletRequest req) {

        try {
            File file = new File(path);
            /**
             * 中文乱码解决
             */
            String type = req.getHeader("User-Agent").toLowerCase();
            if (type.indexOf("firefox") > 0 || type.indexOf("chrome") > 0) {
                /**
                 * 谷歌或火狐
                 */
                fileName = new String(fileName.getBytes(charsetCode), "iso8859-1");
            } else {
                /**
                 * IE
                 */
                fileName = URLEncoder.encode(fileName, charsetCode);
            }
            // 设置响应的头部信息
            resp.setHeader("content-disposition", "attachment;filename=" + fileName);
            // 设置响应内容的类型
            resp.setContentType(getFileContentType(fileName) + "; charset=" + charsetCode);
            // 设置响应内容的长度
            resp.setContentLength((int)file.length());
            // 输出
            outStream(new FileInputStream(file), resp.getOutputStream());
        } catch (Exception e) {
            log.error("执行downloadFile发生了异常:{}", e.getMessage());
        }
    }

    /**
     * 基础字节数组输出
     */
    private static void outStream(InputStream is, OutputStream os) {
        try {
            byte[] buffer = new byte[10240];
            int length = -1;
            while ((length = is.read(buffer)) != -1) {
                os.write(buffer, 0, length);
                os.flush();
            }
        } catch (Exception e) {
            System.out.println("执行 outStream 发生了异常：" + e.getMessage());
        } finally {
            try {
                os.close();
            } catch (IOException e) {
            }
            try {
                is.close();
            } catch (IOException e) {
            }
        }
    }

    /**
     * 文件的内容类型
     */
    private static String getFileContentType(String name) {
        String result = "";
        String fileType = name.toLowerCase();
        if (fileType.endsWith(".png")) {
            result = "image/png";
        } else if (fileType.endsWith(".gif")) {
            result = "image/gif";
        } else if (fileType.endsWith(".jpg") || fileType.endsWith(".jpeg")) {
            result = "image/jpeg";
        } else if (fileType.endsWith(".svg")) {
            result = "image/svg+xml";
        } else if (fileType.endsWith(".doc")) {
            result = "application/msword";
        } else if (fileType.endsWith(".xls")) {
            result = "application/x-excel";
        } else if (fileType.endsWith(".zip")) {
            result = "application/zip";
        } else if (fileType.endsWith(".pdf")) {
            result = "application/pdf";
        } else {
            result = "application/octet-stream";
        }
        return result;
    }

}
