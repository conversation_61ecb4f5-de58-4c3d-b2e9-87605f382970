package com.lj.auth.util;

import com.alibaba.fastjson.JSONObject;
import com.lj.auth.common.WxAuthParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.io.UnsupportedEncodingException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.*;


@Slf4j
public class WxPublicAccountUtil {

    static final String appid = "wxf2c99efd612b6318";
    static final String secret = "372c837b91efd3c0f2051dce1cdd5081";
    // access_token是公众号的全局唯一接口调用凭据，公众号调用各接口时都需使用access_token。开发者需要进行妥善保存。
    // access_token的存储至少要保留512个字符空间。access_token的有效期目前为2个小时，需定时刷新，重复获取将导致上次获取的access_token失效。

    public static void main(String[] args) {
        String jsapi_ticket = "jsapi_ticket";

        // 注意 URL 一定要动态获取，不能 hardcode
        String url = "http://example.com";
//        Map<String, String> ret = sign(jsapi_ticket, url);
//        for (Map.Entry entry : ret.entrySet()) {
//            System.out.println(entry.getKey() + ", " + entry.getValue());
//        }
    };


    public static JSONObject getAuthInfo(String url) {
        try {
            JSONObject wxAuthJSON = new JSONObject();
            RestTemplate restTemplate = new RestTemplate();
            // 获取微信accessToken
            final String wxTokenUrl = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=" + appid + "&secret=" + secret;
            JSONObject accessTokenJSON = restTemplate.getForObject(wxTokenUrl, JSONObject.class);
            System.out.println("获取accessToken:" + accessTokenJSON);

            if (accessTokenJSON != null) {
                String accessToken = accessTokenJSON.getString("access_token");
                if (accessToken != null) {
                    // 获取微信jsapi_ticket
                    final String wxTicketUrl = "https://api.weixin.qq.com/cgi-bin/ticket/getticket?access_token=" + accessToken + "&type=jsapi";
                    JSONObject wxTicketJSON = restTemplate.getForObject(wxTicketUrl, JSONObject.class);
                    System.out.println("获取wxTicket:" + wxTicketJSON);

                    if (wxTicketJSON != null) {
                        String errMsg = wxTicketJSON.getString("errmsg");
                        if ("ok".equals(errMsg)) {
                            String noncestr = create_nonce_str();
                            String timestamp = create_timestamp();
                            String jsapi_ticket = wxTicketJSON.getString("ticket");
                            // 注意 URL 一定要动态获取，不能 hardcode
                             String signature = sign(jsapi_ticket, noncestr, timestamp, url);
                            wxAuthJSON.put("jsapiTicket", jsapi_ticket);
                            wxAuthJSON.put("nonceStr", noncestr);
                            wxAuthJSON.put("timestamp", timestamp);
                            wxAuthJSON.put("url", url);
                            wxAuthJSON.put("signature", signature);
                            wxAuthJSON.put("appId", appid);
                        }
                    }
                }
            }
            return  wxAuthJSON;
        } catch (RestClientException e) {
            log.error("获取微信公共号认证信息失败:{}",e);
            return null;
        }
    }


    public static String sign(String jsapi_ticket,String nonce_str, String  timestamp,String url) {
        Map<String, String> ret = new HashMap<String, String>();
        String string1;
        String signature = "";

        //注意这里参数名必须全部小写，且必须有序
        string1 = "jsapi_ticket=" + jsapi_ticket +
                  "&noncestr=" + nonce_str +
                  "&timestamp=" + timestamp +
                  "&url=" + url;
        System.out.println(string1);

        try
        {
            MessageDigest crypt = MessageDigest.getInstance("SHA-1");
            crypt.reset();
            crypt.update(string1.getBytes("UTF-8"));
            signature = byteToHex(crypt.digest());
        }
        catch (NoSuchAlgorithmException e)
        {
            e.printStackTrace();
        }
        catch (UnsupportedEncodingException e)
        {
            e.printStackTrace();
        }
        return signature;
    }

    private static String byteToHex(final byte[] hash) {
        Formatter formatter = new Formatter();
        for (byte b : hash)
        {
            formatter.format("%02x", b);
        }
        String result = formatter.toString();
        formatter.close();
        return result;
    }

    private static String create_nonce_str() {
        return UUID.randomUUID().toString();
    }

    private static String create_timestamp() {
        return Long.toString(System.currentTimeMillis() / 1000);
    }

        public static String getNonce() {
            return "YL" + UUID.randomUUID().toString().replaceAll("-", "");
        }
}
