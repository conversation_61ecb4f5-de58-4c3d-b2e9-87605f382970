package com.lj.auth.util;

import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;


@Component
public class BaseConversionUtils {


    @Value("${readImagepath}")
    private static String readImagepath;//

    static {
        readImagepath=PropertiesRead.getYmlStringForActive("readImagepath");
    }


    /**
     * @Author: yinlu
     * @Description: 对象转map
     * @param  object
     * @return java.util.Map<java.lang.String,java.lang.Object>
     **/
    public static Map<String, Object> convert(Object object) throws Exception {
        Map<String, Object> map = new HashMap<>();
        Class<?> clazz = object.getClass();
        for (Field field : clazz.getDeclaredFields()) {
            field.setAccessible(true);
            // String value = field.get(object) != null ? field.get(object).toString() : "";
            Object value = field.get(object);
            map.put(field.getName(), value);
        }
        return map;
    }


    /**
     * @Author: yinlu
     * @Description: 转成百分比
     * @param  data
     * @return java.lang.String
     **/
    public static String parseRate(Double data){
        if(data != null){
            if(data == 0){
                return "0.0%";
            }
            BigDecimal bd = new BigDecimal(data*100);
            return bd.setScale(2, RoundingMode.HALF_UP).toPlainString()+"%";
        }
        return null;
    }


    public   static String strToTime(String time) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String format1 = sdf.format(new Date(Long.valueOf(time + "000")));
        return format1;
    }



    public static String parseImageUrl(String value) {
        if (StringUtils.isNotBlank(value)) {
            //如果检测到有自定义注解进行前缀处理
            String[] values = value.split(",");
            StringBuilder str = new StringBuilder();
            Arrays.stream(values).forEach(v -> {
                if (str.length() > 0) {
                    str.append(",");
                }
                if (!v.contains("http")) {
                    str.append(readImagepath.concat(v).replaceAll("///", "/"));
                } else {
                    str.append(v.replaceAll("///", "/"));
                }
            });
            return str.toString();
        } else {
            return value;
        }
    }

    public static String desensitizedName(String fullName){
        if (Strings.isNotBlank(fullName)) {
            String name = StringUtils.left(fullName, 1);
            return StringUtils.rightPad(name, StringUtils.length(fullName), "*");
        }
        return fullName;
    }

}
