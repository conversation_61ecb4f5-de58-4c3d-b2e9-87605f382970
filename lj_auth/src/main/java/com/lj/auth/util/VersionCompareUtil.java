package com.lj.auth.util;

/**
 * @author: wxm
 * @description: 版本比较工具类
 * @date: 2025/3/27 15:18
 */
public class VersionCompareUtil {


    /**
     * 比较版本号大小，前者大则返回true
     * @param version1 版本号1
     * @param version2 版本号2
     * @return
     */
    public static boolean compareVersion(String version1, String version2) {
        String[] v1 = version1.split("\\.");
        String[] v2 = version2.split("\\.");
        for (int i = 0; i < v1.length && i < v2.length; i++) {
            int num1 = Integer.parseInt(v1[i]);
            int num2 = Integer.parseInt(v2[i]);
            if (num1 > num2) {
                return true;
            } else if (num1 < num2) {
                return false;
            }
        }
        return false;
    }


    /**
     * 比较版本号大小，前者大于等于则返回true
     * @param newVersion
     * @param currentVersion
     * @return boolean
     * <AUTHOR>
     * @date 2025/04/08
     */
    public static boolean isVersionHigher(String newVersion, String currentVersion) {
        String[] newParts = newVersion.split("\\.");
        String[] currentParts = currentVersion.split("\\.");

        for (int i = 0; i < Math.min(newParts.length, currentParts.length); i++) {
            int newPart = Integer.parseInt(newParts[i]);
            int currentPart = Integer.parseInt(currentParts[i]);

            if (newPart > currentPart) {
                return true;
            } else if (newPart < currentPart) {
                return false;
            }
        }
        return newParts.length >= currentParts.length;
    }

    public static void main(String[] args) {
        boolean b = isVersionHigher("2.0.7","2.0.6");
        System.out.println(b);
    }


}
