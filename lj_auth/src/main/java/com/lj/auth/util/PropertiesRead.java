package com.lj.auth.util;

import cn.hutool.core.util.StrUtil;
import org.springframework.beans.factory.config.YamlPropertiesFactoryBean;
import org.springframework.core.io.ClassPathResource;

import java.util.Properties;

public class PropertiesRead {
    private static final String applicationName = "application.yml";

    public static void main(String[] args) {
//        System.out.println(getYmlStringForActive("readImagepath"));
        System.out.println(getYmlActive());
    }

    public static String getProperties(String key) {
        String value = "";
        try {
            Properties properties = new Properties();
            properties.load(PropertiesRead.class.getClassLoader().getResourceAsStream("application.yml"));
            value = properties.getProperty(key);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return value;
    }

    /**
     * 根据环境区分，读取对应环境配置文件配置（yml格式）
     * 
     * @return {@link String }
     * <AUTHOR>
     * @date 2024/04/02
     */
    public static String getYmlActive() {
        YamlPropertiesFactoryBean yamlMapFactoryBean = new YamlPropertiesFactoryBean();
        yamlMapFactoryBean.setResources(new ClassPathResource(applicationName));
        Properties properties = yamlMapFactoryBean.getObject();
        String active = properties.getProperty("spring.profiles.active");
        return active;
    }

    /**
     * @Description: 根据环境区分，读取对应环境配置文件配置（yml格式）
     * @Author: Niel
     * @params:
     * @param
     * @return: java.lang.String
     **/
    public static String getPort() {
        YamlPropertiesFactoryBean yamlMapFactoryBean = new YamlPropertiesFactoryBean();
        yamlMapFactoryBean.setResources(new ClassPathResource(applicationName));
        Properties properties = yamlMapFactoryBean.getObject();
        return properties.getProperty("server.port");
    }

    /**
     * @Description: 根据环境区分，读取对应环境配置文件配置（yml格式）
     * @Author: Niel
     * @Date: 2022/9/9 8:48 上午
     * @params:
     * @param propertyName 属性名称
     * @return: java.lang.String
     **/
    public static String getYmlStringForActive(String propertyName) {
        String active = getYmlActive();
        YamlPropertiesFactoryBean yamlMapFactoryBean = new YamlPropertiesFactoryBean();
        yamlMapFactoryBean.setResources(new ClassPathResource("application" + "-" + active + ".yml"));
        Properties properties = yamlMapFactoryBean.getObject();
        // 获取yml里的参数
        String param = properties.getProperty(propertyName);
        if (StrUtil.isBlank(param)) {
            return null;
        }
        return param;
    }

}
