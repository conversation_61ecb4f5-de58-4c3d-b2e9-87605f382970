package com.lj.auth.controller;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONObject;
import com.lj.auth.annotation.LogRecord;
import com.lj.auth.annotation.RequestLimit;
import com.lj.auth.annotation.ValidateSign;
import com.lj.auth.common.R;
import com.lj.auth.domain.Account;
import com.lj.auth.domain.resp.InviteCodeAccountResp;
import com.lj.auth.exception.ResponseEnum;
import com.lj.auth.service.AccountService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 描述： 个人信息相关操作 创建人: CFJ 创建时间: 2024/03/27
 */
@Slf4j
@Validated
@RestController
@RequestMapping(value = "/account")
public class AccountController {

    @Resource
    private AccountService accountService;

    /**
     * 请求短信验证码
     *
     * @param account 手机号码
     * @param state 1:注册 2:登录 3:不判断场景
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/27
     */
    @RequestMapping("/regCaptcha")
    @RequestLimit(second = 60, maxCount = 1)
    public R regCaptcha(@NotBlank(message = "账号不能为空") String account, @NotNull Integer state) {
        return accountService.sentCaptcha(account, state);
    }

    /**
     * 注册
     * 
     * @param request http请求
     * @param account 账户
     * @param varificationCode 验证码
     * @param password 密码
     * @param parentUUID 邀请人用户uuid
     * @param inviteCode 邀请码
     * @return
     */
    @LogRecord(methodName = "注册")
    @ValidateSign
    @RequestMapping("/registe")
    public R register(HttpServletRequest request, @NotBlank(message = "账号不能为空") String account,
        @NotBlank(message = "验证码不能为空") String varificationCode, @NotBlank(message = "密码不能为空") String password,
        String parentUUID, String inviteCode) {
        return accountService.register(request, account, varificationCode, password, parentUUID, inviteCode);
    }

    /**
     * 游客模式
     *
     * @param deviceId
     * @param type 1-APP 2-小程序
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/08/20
     */
    @RequestMapping("/tourist")
    @ValidateSign
    public R tourist(HttpServletRequest request, @NotBlank(message = "设备码不能为空") String deviceId, String appId,
        @RequestParam(defaultValue = "1") Integer type) {
        return accountService.touristService(deviceId, appId, type);
    }

    /**
     * 验证码登录
     *
     * @param account 手机号
     * @param varificationCode 验证码
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/27
     */
    @LogRecord(methodName = "验证码登录")
    @RequestMapping("/loginByCode")
    @ValidateSign
    @RequestLimit
    public R loginByCode(HttpServletRequest request, @NotBlank(message = "账号不能为空") String account,
        @NotBlank(message = "验证码不能为空") String varificationCode) {
        return accountService.loginByCode(request, account, varificationCode);
    }

    /**
     * 密码登录
     * 
     * @param request
     * @param account 账号
     * @param password 密码
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/08
     */
    @RequestLimit
    @ValidateSign
    @LogRecord(methodName = "密码登录")
    @RequestMapping("/loginByPassword")
    public R loginByPassword(HttpServletRequest request, @NotBlank(message = "账号不能为空") String account,
        @NotBlank(message = "密码不能为空") String password) {
        return accountService.loginByPassword(request, account, password);
    }

    /**
     * 仅限运营账号登录---不校验手机号
     * 
     * @param request
     * @param account
     * @param password
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/05/17
     */
    @RequestLimit
    @ValidateSign
    @LogRecord(methodName = "密码登录")
    @RequestMapping("/loginByPasswordV1")
    public R loginByPasswordV1(HttpServletRequest request, @NotBlank(message = "账号不能为空") String account,
        @NotBlank(message = "密码不能为空") String password) {
        return accountService.loginByPasswordV1(request, account, password);
    }

    /**
     * 运营账号和普通账号都能登录---不校验手机号
     *
     * @param request
     * @param account
     * @param password
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/05/17
     */
    @RequestLimit
    @ValidateSign
    @LogRecord(methodName = "密码登录")
    @RequestMapping("/loginByPasswordV2")
    public R loginByPasswordV2(HttpServletRequest request, @NotBlank(message = "账号不能为空") String account,
        @NotBlank(message = "密码不能为空") String password) {
        return accountService.loginByPasswordV2(request, account, password);
    }

    /**
     * 获取用户信息
     *
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/27
     */
    @RequestMapping("/getInfo")
    public R showCountInfo() {
        return R.okData(accountService.getAllUserInfo());
    }


    /**
     * 获取用户详细信息
     *
     * @param request
     * @param paramJson
     * @return {@link R }
     */
    @PostMapping("/getInfoDetail")
    public R showUserInfoDetail(HttpServletRequest request, @RequestBody JSONObject paramJson) {
      return  accountService.showUserInfoDetailService(paramJson);
    }


    /**
     * 编辑用户详细信息
     *
     * @param request
     * @param paramJson
     * @return {@link R }
     */
    @PostMapping("/userInfoDetailUpdate")
    public R userInfoDetailUpdate(HttpServletRequest request, @RequestBody JSONObject paramJson) {
        return  accountService.userInfoDetailUpdateService(paramJson);
    }

    /**
     * 获取地区信息
     * @param request
     * @param paramJson
     * @return {@link R }
     */
    @PostMapping("/serchRegion")
    public R serchRegion(HttpServletRequest request, @RequestBody JSONObject paramJson) {
        return  accountService.serchRegionService(paramJson);
    }

    /**
     * 标签列表
     * @param request
     * @param paramJson
     * @return {@link R }
     */
    @PostMapping("/tagList")
    public R tagList(HttpServletRequest request, @RequestBody JSONObject paramJson) {
        return  accountService.tagListService(paramJson);
    }

    /**
     * 标签类型列表
     * @return {@link R }
     */
    @PostMapping("/tagTypeList")
    public R tagTypeList() {
        return  accountService.tagTypeListService();
    }


    /**
     * 通过手机号获取用户信息
     *
     * @param phone 手机号
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/09
     */
    @RequestMapping("/getInfoByPhone")
    public R showCountInfo(@NotBlank(message = "手机号不能为空") String phone) {
        return accountService.getPhoneAllUserInfo(phone);
    }

    /**
     * 设置图像
     *
     * @param portrait 图片
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/27
     */
    @LogRecord(methodName = "设置用户图像")
    @ValidateSign
    @RequestMapping("/setPortrait")
    public R setPortrait(HttpServletRequest request, String portrait, Long nftId, Integer type) {
        R result = accountService.setPortrait(portrait, nftId, type);
        Integer code = (Integer)result.get("code");
        if (ObjectUtil.equals(200, code)) {
            // 异步同步im信息
            Account userInfo = accountService.getUserInfo();
            accountService.syncImInfo(userInfo);
        }

        return result;
    }

    /**
     * 设置支付密码
     *
     * @param account 手机号
     * @param varificationCode 验证码
     * @param payPassword 支付密码
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/27
     */
    @LogRecord(methodName = "设置支付密码")
    @ValidateSign
    @RequestMapping("/setPayPassword")
    public R setPayPassword(HttpServletRequest request, @NotBlank(message = "手机号不能为空") String account,
        @NotBlank(message = "验证码不能为空") String varificationCode,
        @NotBlank(message = "支付密码不能为空") String payPassword) {
        return accountService.setPayPassword(account, varificationCode, payPassword);
    }

    /**
     * 修改密码
     *
     * @param account 手机号
     * @param varificationCode 验证码
     * @param password 密码
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/28
     */
    @LogRecord(methodName = "修改密码")
    @ValidateSign
    @RequestMapping("/setPassword")
    public R setPassword(HttpServletRequest request, @NotBlank(message = "账号不能为空") String account,
        @NotBlank(message = "验证码不能为空") String varificationCode,
        @NotBlank(message = "密码不能为空") String password) {
        return accountService.setPassword(account, varificationCode, password);
    }

    /**
     * 修改手机号
     * 
     * @param account 手机号
     * @param varificationCode 验证码
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/09/05
     */
    @LogRecord(methodName = "修改手机号")
    @ValidateSign
    @RequestMapping("/setPhoneNumber")
    public R setPhoneNumber(HttpServletRequest request, @NotBlank(message = "账号不能为空") String account,
        @NotBlank(message = "验证码不能为空") String varificationCode) {
        return accountService.setPhoneNumberService(account, varificationCode);
    }

    /**
     * 验证码校验(旧手机号)
     *
     * @param account
     * @param varificationCode
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/11/20
     */
    @RequestMapping("/compareVerificationCodes")
    public R compareVerificationCodes(@NotBlank(message = "账号不能为空") String account,
        @NotBlank(message = "验证码不能为空") String varificationCode) {
        // 获取用户信息
        // 校验手机号是否是当前登录用户
        Account userInfo = accountService.getUserInfo();
        Assert.isTrue(ObjectUtil.equals(userInfo.getPhoneNumber(), account),
            ResponseEnum.PhoneNumberMismatch.getMsg());
        Assert.isTrue(accountService.verifyVerificationCode(account, varificationCode),
            ResponseEnum.VerificationCodeError.getMsg());
        return R.ok();
    }

    /**
     * 修改手机号(内部调用)
     *
     * @param uuid
     * @param account 手机号
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/09/05
     */
    @RequestMapping("/1/setPhoneNumber")
    public R setPhoneNumber1(@NotBlank(message = "uuid不能为空") String uuid,
        @NotBlank(message = "账号不能为空") String account) {
        return accountService.setPhoneNumberService1(uuid, account);
    }

    /**
     * 修改手机号(内部调用) 手机号账户绑定DID账户
     *
     * @param phone
     * @param didSymbol
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/09/05
     */
    @RequestMapping("/2/setPhoneNumber")
    public R setPhoneNumber2(@NotBlank(message = "手机号不能为空") String phone,
        @NotBlank(message = "did不能为空") String didSymbol) {
        return accountService.setPhoneNumberService2(phone, didSymbol);
    }

    /**
     * 忘记密码
     *
     * @param account 手机号
     * @param varificationCode 验证码
     * @param password 密码
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/28
     */
    @ValidateSign
    @RequestMapping("/forgetPassword")
    public R forgetPassword(HttpServletRequest request, @NotBlank(message = "账号不能为空") String account,
        @NotBlank(message = "验证码不能为空") String varificationCode,
        @NotBlank(message = "密码不能为空") String password) {
        return accountService.forgetPassword(account, varificationCode, password);
    }

    /**
     * 设置昵称
     *
     * @param nickName 昵称
     * @param domainNickName 域名昵称
     * @param type 1-昵称展示2-域名昵称展示
     * @return {@link R}
     */
    @ValidateSign
    @LogRecord(methodName = "设置昵称")
    @RequestMapping("/setName")
    public R setName(HttpServletRequest request, String nickName, String domainNickName, Integer type) {
        R result = accountService.setNickName(nickName, domainNickName, type);
        Integer code = (Integer)result.get("code");
        if (ObjectUtil.equals(200, code)) {
            Account userInfo = accountService.getUserInfo();
            // 异步同步im信息
            accountService.syncImInfo(userInfo);
        }
        return result;
    }

    /**
     * 注册协议
     * 
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/10
     */
    @RequestMapping("/getUserAgreement")
    public R getUserAgreement() {
        return accountService.getUserAgreementService();
    }

    /**
     * 我的推广
     *
     * @param page 页
     * @param pageSize 页码
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/10
     */
    @RequestMapping("/myPromotion")
    public R myPromotion(@RequestParam(defaultValue = "1") @Min(value = 1, message = "页码最小为1") int page,
        @RequestParam(defaultValue = "10") @Min(value = 1, message = "数量最小为1") int pageSize) {
        return accountService.myPromotionService(page, pageSize);
    }

    /**
     * 退出
     *
     * @return {@link R}
     */
    @RequestMapping("/logout")
    public R logout() {
        StpUtil.logout();
        return R.ok();
    }

    /**
     * 账号注销协议
     * 
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/06/14
     */
    @RequestMapping("/cancellationAgreement")
    public R cancellationAgreement() {
        return accountService.cancellationAgreementService();
    }

    /**
     * 注销账号
     * 
     * @param ids
     * @param reason
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/06/14
     */
    @ValidateSign
    @RequestMapping("/accountCancellation")
    public R accountCancellation(HttpServletRequest request, @RequestParam("ids") List<Integer> ids, String reason) {
        return accountService.accountCancellationService(ids, reason);
    }

    /**
     * 注销账号检测
     * 
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/06/19
     */
    @RequestMapping("/accountDetection")
    public R accountDetection() {
        return accountService.accountDetectionService();
    }

    /**
     * 忽略注销条件
     * 
     * @param id-条件id
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/06/19
     */
    @ValidateSign
    @RequestMapping("/ignore")
    public R conditionalOperation(HttpServletRequest request, @NotNull(message = "id不能为空") Integer id) {
        return accountService.conditionalOperationService(id);
    }

    /**
     * 获取渠道信息
     *
     * @param inviteCode 邀请码
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/03/12
     */
    @RequestMapping("/getOperateInfo")
    public R getOperateInfo(@NotBlank(message = "邀请码不能为空") String inviteCode) {
        return accountService.getOperateInfoService(inviteCode);
    }


    /**
     * 同步用户登录记录（内部登录信息同步使用）
     *
     * @param ip
     * @param application
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/04/09
     */
    @LogRecord(methodName = "同步用户登录信息")
    @RequestMapping("/logInMessageRecord")
    public R logInMessageRecord( @NotBlank(message = "ip地址不能为空") String ip,String application) {
        return R.okData(accountService.logInMessageRecordService(ip,application));
    }
    
    /**
     * 邀请码查询用户信息
     * @param inviteCode
     * @return
     */
    @RequestMapping("getByInviteCode")
    public R getByInviteCode(@NotBlank(message = "邀请码不能为空") String inviteCode) {
        log.info("邀请码查询用户信息--inviteCode:{}", inviteCode);
        InviteCodeAccountResp resp = accountService.getByInviteCode(inviteCode);
        log.info("邀请码查询用户信息--返回结果:{}", JSONObject.toJSONString(resp));
        return R.okData(resp);
    }

}
