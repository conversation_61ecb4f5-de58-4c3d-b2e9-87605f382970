package com.lj.auth.controller;

import com.alibaba.fastjson2.JSONObject;
import com.lj.auth.common.R;
import com.lj.auth.service.CouponService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * @author: wxm
 * @description: 优惠券
 * @date: 2024/11/5 9:29
 */
@Slf4j
@Validated
@RestController
@RequestMapping(value = "coupon")
public class CouponController {
    @Resource
    private CouponService couponService;

    /**
     * 券批次列表(可显示的券活动列表)
     *
     * @param request
     * @return
     */
    @RequestMapping("couponBatchList")
    public R couponBatchList(HttpServletRequest request, @RequestBody JSONObject paramJson) {
        return couponService.couponBatchList(paramJson);
    }

    /**
     * 券批次详情
     *
     * @param request
     * @return
     */
    @RequestMapping("couponBatchDetail")
    public R couponBatchDetail(HttpServletRequest request, @RequestBody JSONObject paramJson) {
        return couponService.couponBatchDetail(paramJson);
    }

    /**
     * 抢券
     *
     * @param request
     * @return
     */
//    @ValidateToken
    @RequestMapping("grabCoupon")
    public R grabCoupon(HttpServletRequest request, @RequestBody JSONObject paramJson) {
        return couponService.grabCoupon(paramJson);
    }

    /**
     * 历史优惠券分页
     *
     * @param request
     * @param paramJson
     * @return
     */
//    @ValidateToken
    @RequestMapping("historyCouponPage")
    public R historyCouponPage(HttpServletRequest request, @RequestBody JSONObject paramJson) {
        return couponService.historyCouponPage(paramJson);
    }

    /**
     * 优惠券列表(选择优惠券)
     *
     * @param request
     * @param paramJson
     * @return
     */
//    @ValidateToken
    @RequestMapping("couponList")
    public R couponList(HttpServletRequest request, @RequestBody JSONObject paramJson) {
        return couponService.couponList(paramJson);
    }

    /**
     * 抢券测试
     *
     * @param request
     * @return
     */
//    @RequestMapping("grabCouponTest")
    public R grabCouponTest(HttpServletRequest request, @RequestBody JSONObject paramJson) {
        return couponService.grabCouponTest(paramJson);
    }

}
