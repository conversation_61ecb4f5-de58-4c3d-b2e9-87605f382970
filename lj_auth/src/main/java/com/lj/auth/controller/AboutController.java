package com.lj.auth.controller;

import com.dtflys.forest.annotation.Body;
import com.dtflys.forest.annotation.Post;
import com.lj.auth.annotation.ValidateToken;
import com.lj.auth.common.R;
import com.lj.auth.service.AboutService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.net.InetSocketAddress;

/**
 * 描述：关于我们信息 创建人: CFJ 创建时间: 2024/03/27
 */
@Slf4j
@Validated
@RestController
@RequestMapping(value = "/about")
public class AboutController {
    @Resource
    private AboutService aboutService;

    /**
     * 关于我们
     *
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/27
     */
    @RequestMapping("/commonConfig")
    public R commonConfig() {
        return aboutService.commonConfig();
    }

    /**
     * 联系客服相关信息
     *
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/16
     */

    @ValidateToken
    @RequestMapping("/customerService")
    public R getCustomerService() {
        return aboutService.getCustomerService();
    }


    /**
     * 获取邀请海报
     *
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/18
     */
    @RequestMapping("/poster")
    public R getPoster() {
        return aboutService.getPosterService();
    }

    /**
     * 获取基础logo
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/25
     */
    @RequestMapping("/getLogo")
    public R getLogo() {
        return aboutService.getLogoService();
    }


    /**
     * 增加接口记录
     *
     * @param satoken
     * @param channel
     * @param edition
     * @param ip
     * @param path
     * @param requestParam
     */
    @RequestMapping("/addInterfaceRecord")
    public void addInterfaceRecord(@RequestParam("satoken") String satoken,
                                   @RequestParam("channel") String channel, @RequestParam("edition") String edition,
                                   @RequestParam("ip") String ip, @RequestParam("path") String path,
                                   @RequestParam("requestParam") String requestParam) {
        aboutService.addInterfaceRecordService(satoken, channel, edition, ip, path, requestParam);
    }

}
