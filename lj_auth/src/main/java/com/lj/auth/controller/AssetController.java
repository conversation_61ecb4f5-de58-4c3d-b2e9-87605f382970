package com.lj.auth.controller;

import java.math.BigDecimal;
import java.util.Date;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.*;

import com.lj.auth.annotation.AccessControl;
import com.lj.auth.annotation.LogRecord;
import com.lj.auth.annotation.ValidateSign;
import com.lj.auth.common.R;
import com.lj.auth.service.*;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import lombok.extern.slf4j.Slf4j;

/**
 * 描述： 描述：资产 创建人: CFJ 创建时间: 2024/04/03
 *
 * 创建人: CFJ 创建时间: 2024/04/15
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/asset")
public class AssetController {

    @Resource
    private RechargeAssetsService rechargeAssetsService;
    @Resource
    private TransactionTypeService transactionTypeService;
    @Resource
    private RechargeRecordService rechargeRecordService;
    @Resource
    private GlobalConfigService globalConfigService;
    @Resource
    private AggregatePayService aggregatePayService;

    /**
     * 获取个人资金账户
     *
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/16
     */
    @RequestMapping("/getUserAsset")
    public R getUserAsset() {
        return rechargeAssetsService.getUserAssetService();
    }

    /**
     * 获取交易类型
     *
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/03
     */
    @RequestMapping("/getTransactionType")
    public R getTransactionType() {
        return transactionTypeService.getTransactionTypeService();
    }

    /**
     * 获取资金明细
     *
     * @param page 页
     * @param pageSize 页码
     * @param type 0-全部类型 1-余额充值 2-余额提现 3-域名注册 4-我的返佣 5-服务订购
     * @param date
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/03
     */
    @RequestMapping("/queryFundList")
    public R queryFundList(@RequestParam(defaultValue = "1") @Min(value = 1, message = "页码最小为1") int page,
        @RequestParam(defaultValue = "10") @Min(value = 1, message = "数量最小为1") int pageSize, Integer type,
        @DateTimeFormat(pattern = "yyyy-MM") Date date) {
        return rechargeRecordService.queryFundListService(page, pageSize, type, date);
    }

    /**
     * 门户远程调用使用
     * 
     * @param uuid
     * @param page
     * @param pageSize
     * @param type
     * @param date
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/01/17
     */
    @RequestMapping("/queryFundListF")
    public R queryFundListF(@RequestParam("uuid") @NotBlank(message = "uuid不能为空") String uuid,
        @RequestParam(defaultValue = "1") @Min(value = 1, message = "页码最小为1") int page,
        @RequestParam(defaultValue = "10") @Min(value = 1, message = "数量最小为1") int pageSize, Integer type,
        @DateTimeFormat(pattern = "yyyy-MM") Date date) {
        return rechargeRecordService.queryFundListFService(uuid, page, pageSize, type, date);
    }
    

    /**
     * @param merOrderId
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/30
     */
    @RequestMapping("/queryOrders")
    public R queryOrders(@NotBlank(message = "订单id不能为空") String merOrderId) {
        return aggregatePayService.appQuery(merOrderId);
    }

    /**
     * 获取资金明细详情
     *
     * @param id id
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/03
     */
    @RequestMapping("/queryFundDetail")
    public R queryFundDetail(@NotNull Integer id) {
        return rechargeRecordService.queryFundDetailService(id);
    }

    @RequestMapping("/queryFundDetailV1")
    public R queryFundDetailV1(@NotNull Integer id) {
        return rechargeRecordService.queryFundDetailService1(id);
    }

    /**
     * 资金关联订单信息
     * 
     * @param orderId
     * @param typeId
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/06/05
     */
    @RequestMapping("/fundRelatedOrders")
    public R getFundRelatedOrders(@NotNull Integer orderId, @NotNull Integer typeId) {
        return rechargeRecordService.getFundRelatedOrdersService(orderId, typeId);
    }

    /**
     * 取消提现
     *
     * @param id
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/06/06
     */
    @ValidateSign
    @RequestMapping("/cancelReflection")
    public R getCancelReflection(HttpServletRequest request, @NotNull Integer id) {
        return rechargeRecordService.getCancelReflectionService(id);
    }

    /**
     * 获取提现协议
     *
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/03
     */
    @RequestMapping("/getWithdrawalProtocol")
    public R getWithdrawalProtocol() {
        return globalConfigService.getWithdrawalProtocolService();
    }

    /**
     * 添加提现记录
     *
     * @param amount 金额
     * @param cardId 银行卡ID
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/03
     */
    @ValidateSign
    @RequestMapping("/addWithdrawalRecord")
    @AccessControl(methodName = "asset::addWithdrawalRecord")
    @LogRecord(methodName = "余额提现")
    public R addWithdrawalRecord(HttpServletRequest request, @Digits(integer = 10, fraction = 2,
        message = "资金格式错误") @DecimalMin(value = "5", message = "资金最小值不能低于5元") BigDecimal amount,
        @NotNull Integer cardId) {
        return rechargeRecordService.addWithdrawalRecordService(amount, cardId);
    }

    /**
     * 获取提现服务费率
     *
     * @param withdrawAmount 提现金额
     * @return {@link R}
     */
    @RequestMapping("/withdrawServCharge")
    public R withdraw(@NotNull(message = "提现金额不能为空") @Digits(integer = 10, fraction = 2,
        message = "资金格式错误") @DecimalMin(value = "5", message = "资金最小值不能低于5元") BigDecimal withdrawAmount) {
        return R.okData(rechargeRecordService.withdrawServChargeService(withdrawAmount));
    }

}
