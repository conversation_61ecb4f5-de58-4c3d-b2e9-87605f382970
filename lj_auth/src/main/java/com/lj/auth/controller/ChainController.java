package com.lj.auth.controller;

import javax.annotation.Resource;

import com.alibaba.fastjson2.JSONObject;
import com.lj.auth.annotation.ServiceAuth;
import com.lj.auth.annotation.ValidateSign;
import com.lj.auth.common.R;
import com.lj.auth.domain.Chain;
import com.lj.auth.domain.Result.SpTransferResp;
import com.lj.auth.service.ChainService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Validated
@RestController
@RequestMapping(value = "/chain")
public class ChainController {

    @Resource
    private ChainService chainService;

    /**
     * 获取界外开放链信息
     *
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/10
     */
    @RequestMapping("/getLjChainInfo")
    public R getLjChainInfo() {
        Chain result = chainService.getLjChainInfoService();
        return R.okData(result);
    }

    /**
     * 获取所有链信息
     * 
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/16
     */
    @RequestMapping("/getChainInfo")
    public R getChainInfo() {
        List<Chain> result = chainService.getChainInfoService();
        return R.okData(result);
    }

    /**
     * 获取sp资产
     *
     * @return {@link R }
     */
    @ValidateSign
    @RequestMapping("/getBalance")
    public R getBalance(@RequestBody JSONObject jsonObject) {
        return R.okData(chainService.getBalance(jsonObject));
    }




    /**
     * 获取交易所需信息
     *
     * @return {@link R }
     */
    @RequestMapping("/queryTxRequireInfo")
    public R queryTxRequireInfo(@RequestBody JSONObject jsonObject) {
        return R.okData(chainService.queryTxRequireInfo(jsonObject));
    }

    /**
     * 获取签名
     *
     * @return {@link R }
     */
    @RequestMapping("/getSign")
    public R getSign(@RequestBody JSONObject jsonObject) {
        return R.okData(chainService.getSign(jsonObject));
    }


    /**
     * Sp转让
     *
     * @return {@link R }
     */
    @ValidateSign
    @RequestMapping("/transfer")
    public R transfer(@RequestBody JSONObject jsonObject) {
        SpTransferResp spTransferResp = chainService.transferSP(jsonObject);
        return R.okData(spTransferResp);
    }

    /**
     * Sp转让Feign
     *
     * @return {@link R }
     */
    @ServiceAuth(level= ServiceAuth.AuthLevel.SERVICE)
    @RequestMapping("/transferFeign")
    public R transferFeign(@RequestBody JSONObject jsonObject) {
        SpTransferResp spTransferResp = chainService.transferFeign(jsonObject);
        return R.okData(spTransferResp);
    }


    /**
     * 获取服务间的token
     *
     * @return {@link R }
     */
    @RequestMapping("/getServeToken")
    public R getServeToken(@RequestBody JSONObject jsonObject) {
        return R.okData(chainService.getServeToken(jsonObject));
    }

}
