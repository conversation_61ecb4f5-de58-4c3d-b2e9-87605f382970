package com.lj.auth.controller;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import com.lj.auth.annotation.LogRecord;
import com.lj.auth.common.R;
import com.lj.auth.service.ExploreAppCollectionService;
import com.lj.auth.service.ExploreAppRecentlyService;
import com.lj.auth.service.ExploreAppService;
import com.lj.auth.service.ExploreAppTypeService;
import org.hibernate.validator.constraints.Length;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import lombok.extern.slf4j.Slf4j;

/**
 * 描述：发现 创建人: CFJ 创建时间: 2024/04/01
 */

@Slf4j
@Validated
@RestController
@RequestMapping(value = "/explore")
public class ExploreController {
    @Resource
    private ExploreAppTypeService exploreAppTypeService;

    @Resource
    private ExploreAppService exploreAppService;
    @Resource
    private ExploreAppCollectionService exploreAppCollectionService;
    /**
     *
     */
    @Resource
    private ExploreAppRecentlyService exploreAppRecentlyService;

    /**
     * 探索应用类别
     *
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/01
     */
    @RequestMapping("/getAppTypeList")
    public R getAppTypeList() {
        return exploreAppTypeService.getAppTypeService();
    }

    /**
     * 获取应用列表
     *
     * @param page 页
     * @param pageSize 页大小
     * @param appType --0-获取全部 app类别id
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/01
     */
    @RequestMapping("/getAppList")
    public R getChainAccountListByUuid(
        @RequestParam(defaultValue = "1") @Min(value = 1, message = "页码最小为1") int page,
        @RequestParam(defaultValue = "10") @Min(value = 1, message = "数量最小为1") int pageSize,
        @RequestParam("appType") @NotNull(message = "类型不能为空") Integer appType) {
        return exploreAppService.getAppListService(page, pageSize, appType);
    }

    /**
     * 获取推荐应用列表
     *
     * @param page 页
     * @param pageSize 页大小
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/01
     */
    @RequestMapping("/getRecommendationList")
    public R getRecommendationList(
        @RequestParam(defaultValue = "1") @Min(value = 1, message = "页码最小为1") int page,
        @RequestParam(defaultValue = "10") @Min(value = 1, message = "数量最小为1") int pageSize) {
        return exploreAppService.getRecommendationListService(page, pageSize);
    }

    /**
     * 应用搜索
     * 
     * @param keyword 搜索关键字
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/12
     */
    @RequestMapping("/searchApp")
    public R searchApp(@RequestParam("keyWord") @Length(max = 50) String keyWord) {
        return exploreAppService.searchAppService(keyWord);
    }

    /**
     * 搜索历史记录
     *
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/01
     */
    @RequestMapping("/searchRecordList")
    public R searchRecordList() {
        return exploreAppService.searchRecordListService();
    }

    /**
     * 删除历史搜索记录
     *
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/01
     */
    @RequestMapping("/removeRecordList")
    public R removeRecordList() {
        return exploreAppService.removeRecordListService();
    }

    /**
     * 获取官方应用列表
     *
     * @param page 页
     * @param pageSize 页大小
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/01
     */
    @RequestMapping("/getOfficialList")
    public R getOfficialList(@RequestParam(defaultValue = "1") @Min(value = 1, message = "页码最小为1") int page,
        @RequestParam(defaultValue = "10") @Min(value = 1, message = "数量最小为1") int pageSize) {
        return exploreAppService.getOfficialListService(page, pageSize);
    }

    /**
     * 收藏
     *
     * @param appId 应用appId
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/01
     */
    @RequestMapping("/addCollection")
    @LogRecord(methodName = "收藏")
    public R addCollection(@NotNull(message = "应用ID不能为空") Integer appId) {
        return exploreAppCollectionService.addCollectionService(appId);
    }

    /**
     * 取消收藏
     *
     * @param id 应用id
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/01
     */
    @RequestMapping("/unAddCollection")
    @LogRecord(methodName = "取消收藏")
    public R unAddCollection(@NotNull(message = "应用ID不能为空") Integer id) {
        return exploreAppCollectionService.unAddCollectionService(id);
    }

    /**
     * 获取收藏列表
     *
     * @param page 页
     * @param pageSize 页大小
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/11
     */
    @RequestMapping("/getCollectionList")
    public R getCollectionList(@RequestParam(defaultValue = "1") @Min(value = 1, message = "页码最小为1") int page,
        @RequestParam(defaultValue = "10") @Min(value = 1, message = "数量最小为1") int pageSize) {
        return exploreAppCollectionService.getCollectionListService(page, pageSize);
    }

    /**
     * 获取最近访问记录
     *
     * @param page 页
     * @param pageSize 页大小
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/01
     */
    @RequestMapping("/getRecentlyList")
    public R getRecentlyList(@RequestParam(defaultValue = "1") @Min(value = 1, message = "页码最小为1") int page,
        @RequestParam(defaultValue = "10") @Min(value = 1, message = "数量最小为1") int pageSize) {
        return exploreAppRecentlyService.getRecentlyListService(page, pageSize);
    }

    /**
     * 删除访问记录
     * 
     * @param ids 纪录id集合
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/11
     */
    @RequestMapping("/unRecentlyList")
    public R unRecentlyList(@RequestParam("ids") @NotEmpty(message = "ids不能为空") List<Integer> ids) {
        return exploreAppRecentlyService.unRecentlyListService(ids);
    }

    /**
     * 获取探索背景图
     * 
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/15
     */
    @RequestMapping("/getBackgroundList")
    public R getBackgroundList() {
        return exploreAppRecentlyService.getBackgroundListService();
    }
}
