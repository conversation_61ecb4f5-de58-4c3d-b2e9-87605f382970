
package com.lj.auth.controller;

import com.lj.auth.annotation.RequestLimit;
import com.lj.auth.common.R;
import com.lj.auth.feginClient.IFileService;
import com.lj.auth.service.FileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotBlank;
import java.util.Arrays;
import java.util.List;

/**
 * 描述：文件上传 创建人: CFJ 创建时间: 2024/03/27
 */
@Slf4j
@Validated
@RestController
@RequestMapping(value = "/file")
public class FileController {
    /**
     * 指定图片类型
     */
    public static final List<String> POINT_IMAGE_TYPE = Arrays.asList(".jpg", ".jpeg", ".png");

    @Resource
    private FileService fileService;
    @Resource
    private IFileService iFileService;
    @Resource
    private HttpServletRequest request;

    /**
     * 单文件上传
     * 
     * @param picture
     * @return {@link R}
     * @Description: 上传图片
     */
    @RequestLimit
    @RequestMapping("/uploadPic")
    public R uploadPic(@RequestParam(value = "picture") MultipartFile picture, @NotBlank String folderName) {
        String fileName = picture.getOriginalFilename();// 获取文件名
        String suffixName = fileName.substring(fileName.lastIndexOf("."));// 获取文件的后缀名
        if (!POINT_IMAGE_TYPE.contains(suffixName)) {// 判断图片后缀是否正确
            throw new IllegalArgumentException("图片格式错误");
        }
        String s = iFileService.uploadPicV2(request, picture, folderName);
        // return fileService.upload(picture, folderName);
        return R.okData(s);
    }

    /**
     * 多文件上传
     * 
     * @param pictures
     * @return {@link R}
     * @Description: 上传多个图片
     */
    @RequestMapping("/multiUpload")
    @RequestLimit
    public R multiUpload(@RequestParam(value = "pictures") List<MultipartFile> pictures,
        @NotBlank String folderName) {
        String s = iFileService.multiUploadV2(pictures, folderName);
        // return fileService.multiUpload(pictures, folderName);
        return R.okData(s);
    }

}
