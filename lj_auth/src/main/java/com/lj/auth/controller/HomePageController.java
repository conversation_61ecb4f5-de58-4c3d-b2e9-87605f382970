package com.lj.auth.controller;

import com.alibaba.fastjson2.JSONObject;
import com.lj.auth.common.R;
import com.lj.auth.service.HomePageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * @author: wxm
 * @description:
 * @date: 2024/12/26 11:49
 */
@Slf4j
@Validated
@RestController
@RequestMapping(value = "homePage")
public class HomePageController {
    @Resource
    private HomePageService homePageService;

    /**
     * 获取第一部分内容(顶部导航栏、底部导航栏、固定金刚区)
     * @param request
     * @return
     */
    @RequestMapping("getPart1")
    public R getPart1(HttpServletRequest request) {
        String channel = request.getHeader("channel");
        if(StringUtils.isEmpty(channel)){
            channel = "JIEWAI";
        }
        return homePageService.getPart1(channel);
    }

    /**
     * 获取第二部分内容(轮播图、背景图)
     * @param request
     * @return
     */
    @RequestMapping("getPart2")
    public R getPart2(HttpServletRequest request) {
        String channel = request.getHeader("channel");
        if(StringUtils.isEmpty(channel)){
            channel = "JIEWAI";
        }
        return homePageService.getPart2(channel);
    }

    /**
     * 获取第三部分内容(功能金刚区、占位图、场景组件卡片图)
     * @param request
     * @return
     */
    @RequestMapping("getPart3")
    public R getPart3(HttpServletRequest request) {
        String channel = request.getHeader("channel");
        if(StringUtils.isEmpty(channel)){
            channel = "JIEWAI";
        }
        String version = request.getHeader("version");
        return homePageService.getPart3(channel,version);
    }


    /**
     * 获取话费功能区
     * @param request
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/01/15
     */
    @RequestMapping("getPhonePart")
    public R getPhonePart(HttpServletRequest request) {
        String channel = request.getHeader("channel");
        if(StringUtils.isEmpty(channel)){
            channel = "JIEWAI";
        }
        return homePageService.getPhonePart(channel);
    }

}
