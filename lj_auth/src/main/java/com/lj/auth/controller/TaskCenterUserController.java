package com.lj.auth.controller;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.lj.auth.annotation.AccessControl;
import com.lj.auth.annotation.ValidateSign;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.lj.auth.common.R;
import com.lj.auth.service.TaskCenterService;

/**
 * 描述：个人任务中心 创建人: CFJ 创建时间: 2024/05/28
 */
@Validated
@RestController
@RequestMapping("/taskCenterUser")
public class TaskCenterUserController {
    @Resource
    private TaskCenterService taskCenterService;

    /**
     * 统计任务更新任务完成情况
     *
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/05/28
     */
    @RequestMapping(value = "/count")
    public R statisticalTasks() {
        return taskCenterService.statisticalTasksService();
    }

    /**
     * 获取限时任务列表
     *
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/05/28
     */
    @RequestMapping(value = "/limitedTimeTask")
    public R getLimitedTimeTask() {
        return taskCenterService.getLimitedTimeTaskService();
    }

    /**
     * 获取任务类型
     *
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/05/28
     */
    @RequestMapping(value = "/centerType")
    public R getCenterType() {
        return taskCenterService.getCenterTypeService();
    }

    /**
     * 返回任务列表展示--日常任务-周期任务-新手任务
     *
     * @param request
     * @param taskTypeId
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/05/28
     */
    @RequestMapping(value = "/taskList")
    public R getTaskList(HttpServletRequest request,@NotNull(message = "任务类型id不能为空") Integer taskTypeId) {
        return taskCenterService.getTaskListService(request,taskTypeId);
    }

    /**
     * 邀请用户列表展示
     *
     * @param page
     * @param pageSize
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/05/29
     */
    @AccessControl(methodName = "taskCenterUser::userTask")
    @RequestMapping(value = "/userTask")
    public R getNewUserTaskList(@RequestParam(defaultValue = "1") @Min(1) Integer page,
        @RequestParam(defaultValue = "10") @Min(1) Integer pageSize) {
        return taskCenterService.getNewUserTaskListService(page, pageSize);
    }

    /**
     * 领取 sp
     *
     * @param id 任务id
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/05/30
     */
    @ValidateSign
    @RequestMapping(value = "/receive")
    public R getReceive(HttpServletRequest request, @NotNull(message = "任务id不能为空") Integer id,
        @NotBlank(message = "地址不能为空") String address) {
        return taskCenterService.getReceiceService(id, address);
    }

    /**
     * 一键领取sp
     *
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/05/30
     */
    @ValidateSign
    @RequestMapping(value = "/oneClickClaim")
    public R getOneClickClaim(HttpServletRequest request, @NotBlank(message = "地址不能为空") String address) {
        return taskCenterService.getOneClickClaimService(address);
    }

    /**
     * 统计获得总sp
     *
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/05/30
     */
    @RequestMapping(value = "/statisticalSp")
    public R getStatisticalSP() {
        return taskCenterService.getStatisticalSPService();
    }

    /**
     * 获取任务中心签到和任务统计
     * 
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/06/04
     */
    @RequestMapping(value = "/taskCenterStatistics")
    public R getTaskCenterStatistics() {
        return taskCenterService.getTaskCenterStatisticsService();
    }

    /**
     * 浏览社区热门动态30s
     *
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/05/30
     */
    @RequestMapping(value = "/dynamic30S")
    public R getDynamic30S() {
        return taskCenterService.getDynamic30SService();
    }

    /**
     * 浏览社区最新动态30s
     *
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/05/30
     */
    @RequestMapping(value = "/newDynamic30S")
    public R getNewDynamic30S() {
        return taskCenterService.getNewDynamic30SService();
    }

    /**
     * 奖励明细列表
     * 
     * @param page
     * @param pageSize
     * @param type 0-全部 1-签到 2-任务
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/06/03
     */
    @RequestMapping(value = "/detail")
    public R listSpDetail(@RequestParam(defaultValue = "1") @Min(1) Integer page,
        @RequestParam(defaultValue = "10") @Min(1) Integer pageSize,
        @RequestParam(defaultValue = "0") Integer type) {
        return taskCenterService.getListSpDetail(page, pageSize, type);
    }

    /**
     * 奖励详情
     * 
     * @param hash
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/06/03
     */
    @RequestMapping(value = "/detailRecord")
    public R listSpDetailRecord(@NotBlank(message = "hash不能为空") String hash) {
        return taskCenterService.getListSpDetailRecord(hash);
    }

    /**
     * 获取任务中心任务变动推送
     *
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/06/04
     */
    @AccessControl(methodName = "taskCenterUser::taskChanges")
    @RequestMapping(value = "/taskChanges")
    public R getTaskChanges() {
        return taskCenterService.getTaskChangesService();
    }

    /**
     * 任务领取状态
     * 
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/07/03
     */
    @RequestMapping(value = "/isReceiveState")
    public R isReceiveState() {
        return taskCenterService.isReceiveStateService();
    }

    /**
     * 首页红点数量
     *
     * @param request
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/03/13
     */
    @RequestMapping(value = "/numberOfRedDots")
    public R numberOfRedDots(HttpServletRequest request) {
        return taskCenterService.numberOfRedDotsService(request);
    }

    /**
     * 列表红点数量展示
     *
     * @param request
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/03/18
     */
    @RequestMapping(value = "/listRedDotsCount")
    public R listRedDotsCount(HttpServletRequest request) {
        return taskCenterService.listRedDotsCountService(request);
    }
    

}
