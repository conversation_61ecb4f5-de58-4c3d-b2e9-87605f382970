package com.lj.auth.controller;

import java.util.Date;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

import com.lj.auth.annotation.LogRecord;
import com.lj.auth.common.R;
import com.lj.auth.domain.Account;
import com.lj.auth.domain.InvoiceAccount;
import com.lj.auth.domain.vo.InvoiceAccountVo;
import com.lj.auth.service.AccountService;
import com.lj.auth.service.GlobalConfigService;
import com.lj.auth.service.InvoiceAccountService;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;

/**
 * 描述：用户发票 创建人: CFJ 创建时间: 2024/03/28
 */
@Validated
@RestController
@RequestMapping("/invoiceAccount")
public class InvoiceAccountController {
    @Resource
    private InvoiceAccountService invoiceAccountService;
    @Resource
    private GlobalConfigService globalConfigService;
    @Resource
    private AccountService accountService;

    /**
     * 开票配置信息
     *
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/28
     */
    @RequestMapping("/getConfig")
    public R getConfig() {
        return globalConfigService.getInvoiceConfig();
    }

    /**
     * 申请开票
     *
     * @param invoiceAccountVo
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/28
     */
    @RequestMapping("/add")
    @LogRecord(methodName = "申请开票")
    public R add(@Valid InvoiceAccountVo invoiceAccountVo) {
        InvoiceAccount invoiceAccount = new InvoiceAccount();
        BeanUtil.copyProperties(invoiceAccountVo, invoiceAccount);
        Account userInfo = accountService.getUserInfo();
        invoiceAccount.setAccountUuid(userInfo.getUuid());
        return invoiceAccountService.addService(invoiceAccount);
    }

    /**
     * 开票分页查询
     *
     * @param page 页
     * @param pageSize 页大小
     * @param ticketType 开票类型 1-增值税普通发票 2-增值税专用发票
     * @param examineState 审核状态 1-待审核 2-审核通过 3-审核驳回
     * @param time 时间
     * @param ticketUuid 申请单号
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/28
     */
    @RequestMapping("/getPage")
    public R getPage(@RequestParam(defaultValue = "1") @Min(1) Integer page,
        @RequestParam(defaultValue = "10") @Min(1) Integer pageSize, Integer ticketType, Integer examineState,
        @DateTimeFormat(pattern = "yyyy-MM") Date time, String ticketUuid) {
        Account userInfo = accountService.getUserInfo();
        String accountUUID = userInfo.getUuid();

        Date startTime = null;
        Date endTime = null;
        if (ObjectUtils.isNotEmpty(time)) {
            startTime = DateUtil.beginOfMonth(time);
            endTime = DateUtil.endOfMonth(time);
        }
        return invoiceAccountService.getPageService(page, pageSize, accountUUID, ticketType, examineState,
            startTime, endTime, ticketUuid);
    }

    /**
     * 发票详情
     *
     * @param id id
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/28
     */
    @RequestMapping("/getInfo")
    public R getInfo(@NotNull(message = "id非空") Long id) {
        Account userInfo = accountService.getUserInfo();
        String accountUUID = userInfo.getUuid();
        return invoiceAccountService.getInfoService(id, accountUUID);
    }
}
