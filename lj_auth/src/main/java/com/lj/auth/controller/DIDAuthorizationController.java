package com.lj.auth.controller;

import com.alibaba.fastjson.JSONObject;
import com.lj.auth.common.R;
import com.lj.auth.service.DidLoginApplicationRecordService;
import com.lj.auth.service.DidQrCodeService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;

/**
 * 描述：DID授权登录 创建人: CFJ 创建时间: 2024/07/01
 */
@Validated
@RestController
@RequestMapping(value = "/DID")
public class DIDAuthorizationController {
    @Resource
    private DidLoginApplicationRecordService didLoginApplicationRecordService;

    /**
     * 设置授权登录信息
     * 
     * @param data
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/07/01
     */
    @RequestMapping("/setSigDataSn")
    public R setSigDataSn(@RequestBody Map<String, Object> data) {
        return didLoginApplicationRecordService.setSigDataSnService(data);
    }

    /**
     * 扫码
     * 
     * @param data
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/07/01
     */
    @RequestMapping("/scanCode")
    public R scanCode(@RequestBody Map<String, Object> data) {
        return didLoginApplicationRecordService.scanCodeService(data);
    }

    /**
     * 扫码登录
     * 
     * @param data
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/07/01
     */
    @RequestMapping("/scanCodeLogin")
    public R scanCodeLogin(@RequestBody Map<String, Object> data) {
        return didLoginApplicationRecordService.scanCodeLoginService(data);
    }

    /**
     * DID扫码签到
     * 
     * @param code 码内容
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/07/31
     */
    @RequestMapping("/didSignIn")
    public R didSignIn(@NotBlank(message = "二维码信息不能为空") String code) {
        return didLoginApplicationRecordService.didSignInService(code);
    }
}
