package com.lj.auth.controller;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.lj.auth.annotation.LogRecord;
import com.lj.auth.common.R;
import com.lj.auth.domain.Account;
import com.lj.auth.domain.vo.DomainExperience;
import com.lj.auth.service.AccountService;
import com.lj.auth.service.SystemNoticeService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 描述：消息中心 创建人: CFJ 创建时间: 2024/03/29
 */
@Validated
@RestController
@RequestMapping("/message")
public class MessageCenterController {
    @Resource
    private SystemNoticeService systemNoticeService;
    @Resource
    private AccountService accountService;

    /**
     * 消息中心分页查询
     *
     * @param request
     * @param page     页
     * @param pageSize 页大小
     * @param source   息来源:0-系统消息 1-系统公告2-转账通知
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/29
     */
    @RequestMapping("/getPage")
    public R getPage(HttpServletRequest request,@RequestParam(defaultValue = "1") @Min(1) Integer page,
        @RequestParam(defaultValue = "10") @Min(1) Integer pageSize, @RequestParam("source") Integer source) {
        String edition = request.getHeader("edition");
        String channel = request.getHeader("channel");
        return systemNoticeService.getPage(page, pageSize, source,edition,channel);
    }

    /**
     * 全部已读
     *
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/29
     */
    @RequestMapping("/allRead")
    @LogRecord(methodName = "全部已读")
    public R allRead() {
        Account userInfo = accountService.getUserInfo();
        systemNoticeService.allRead(userInfo.getUuid());
        return R.ok();
    }

    /**
     * 已读
     *
     * @param id
     * @param state 1-今日已读
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/11/11
     */
    @RequestMapping("/isRead")
    @LogRecord(methodName = "已读")
    public R isRead(@NotNull Integer id, Integer state) {
        systemNoticeService.isReadService(id, state);
        return R.ok();
    }

    /**
     * 获取未读数量
     *
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/29
     */
    @RequestMapping("/unreadCount")
    public R getUnreadCount(HttpServletRequest request) {
        String edition = request.getHeader("edition");
        String channel = request.getHeader("channel");
        return systemNoticeService.getUreadCountService(edition,channel);
    }

    /**
     * 获取最新未读弹窗通知
     *
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/29
     */
    @RequestMapping("/latestNotice")
    public R getLatestNotice(HttpServletRequest request) {
        String edition = request.getHeader("edition");
        String channel = request.getHeader("channel");
        return systemNoticeService.getLatestNoticeService(edition,channel);
    }

    /**
     * 获取最新未读弹窗通知--返回多条
     *
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/29
     */
    @RequestMapping("/latestNoticeV2")
    public R getLatestNoticeV2(HttpServletRequest request) {
        String edition = request.getHeader("edition");
        String channel = request.getHeader("channel");
        return systemNoticeService.getLatestNoticeServiceV2(edition,channel);
    }

    /**
     * 添加授权登录通知
     * 
     * @param didSymbol-DID
     * @param title-标题
     * @param content-内容
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/06/27
     */
    @RequestMapping("/addloginNotification")
    public R addloginNotification(@NotBlank(message = "did标识不能为空") String didSymbol,
        @NotBlank(message = "标题不能为空") String title, @NotBlank(message = "内容不能为空") String content) {
        return systemNoticeService.addloginNotificationService(didSymbol, title, content);
    }


    /**
     * 获取域名体验弹窗
     *
     * @return {@link R }
     */
    @RequestMapping("/getDomainPopUp")
    public R getDomainPopUp() {
        return systemNoticeService.getDomainPopUpService();
    }

    /**
     * 域名体验弹窗今日已读
     *
     * @param id
     * @return {@link R }
     */
    @RequestMapping("/getDomainPopUpIsRead")
    public R getDomainPopUpIsRead(Integer id) {
        return systemNoticeService.getDomainPopUpIsReadService(id);
    }


    /**
     * 域名体验到期
     *
     * @param domainExperience
     * @return {@link R }
     */
    @PostMapping("/domainExperienceExpires")
    public R domainExperienceExpires( @RequestBody DomainExperience domainExperience) {
        return systemNoticeService.domainExperienceExpiresService(domainExperience);
    }



}
