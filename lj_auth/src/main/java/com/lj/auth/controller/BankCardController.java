package com.lj.auth.controller;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.lj.auth.annotation.LogRecord;
import com.lj.auth.common.R;
import com.lj.auth.service.AccountCardService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 描述：银行卡 创建人: CFJ 创建时间: 2024/04/01
 */
@RestController
@RequestMapping("/account_card")
@Validated
public class BankCardController {

    @Resource
    private AccountCardService accountCardService;

    /**
     * 添加银行卡
     *
     * @param name 姓名
     * @param carNumber 卡号
     * @param depositBank 开户行
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/01
     */
    @LogRecord(methodName = "添加银行卡")
    @RequestMapping("/add")
    public R addBankCard(@NotBlank String name, @NotBlank String carNumber, @NotBlank String depositBank) {

        return accountCardService.addBankCardService(name, carNumber, depositBank);
    }

    /**
     * 编辑银行卡
     * 
     * @param id id
     * @param name 姓名
     * @param carNumber 卡号
     * @param depositBank 开户行
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/01
     */
    @LogRecord(methodName = "编辑银行卡")
    @RequestMapping("/update")
    public R updateBankCard(@NotNull Integer id, @NotBlank String name, @NotBlank String carNumber,
        @NotBlank String depositBank) {
        return accountCardService.updateBankCardService(id, name, carNumber, depositBank);
    }

    /**
     * 获取银行卡列表
     *
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/01
     */
    @RequestMapping("/getList")
    public R getBankCardList() {
        return accountCardService.getBankCardListService();
    }

    /**
     * 移除银行卡
     *
     * @param id id
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/01
     */
    @LogRecord(methodName = "移除银行卡")
    @RequestMapping("/delete")
    public R deleteBandCardOfUser(@NotNull Integer id) {
        return accountCardService.deleteBandCardOfUserService(id);
    }

}
