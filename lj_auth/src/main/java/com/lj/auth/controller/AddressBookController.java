package com.lj.auth.controller;

import javax.annotation.Resource;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.lj.auth.annotation.LogRecord;
import com.lj.auth.common.R;
import com.lj.auth.exception.ResponseEnum;
import com.lj.auth.exception.ServiceException;
import com.lj.auth.service.AddressBookService;
import com.lj.auth.util.RegexUtil;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import lombok.extern.slf4j.Slf4j;

/**
 * 描述：通讯录 创建人: CFJ 创建时间: 2024/03/28
 */
@Slf4j
@Validated
@RestController
@RequestMapping(value = "/book")
public class AddressBookController {
    @Resource
    private AddressBookService addAddressBookService;

    /**
     * 新增联系人
     *
     * @param chainId 链ID
     * @param address 地址
     * @param describe 描述
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/28
     */
    @RequestMapping("/add")
    @LogRecord(methodName = "添加通讯录")
    public R addAddressBook(@NotNull Integer chainId, @NotBlank(message = "链地址不能为空") String address,
        String describe) {
        // 校验链地址
        if (!RegexUtil.checkEthAddress(address)) {
            throw new ServiceException(ResponseEnum.ChainAddressError);
        }
        return addAddressBookService.addAddressBookService(chainId, address, describe);
    }

    /**
     * 删除联系人
     *
     * @param id id
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/29
     */
    @RequestMapping("/delete")
    @LogRecord(methodName = "删除联系人")
    public R deleteAddressBook(@NotNull Long id) {
        return addAddressBookService.deleteAddressBookService(id);
    }

    /**
     * 修改联系人
     *
     * @param id id
     * @param chainId 链id
     * @param address 地址
     * @param describe 描述
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/29
     */
    @RequestMapping("/update")
    @LogRecord(methodName = "修改联系人")
    public R updateAddressBook(@NotNull Long id, @NotNull Integer chainId,
        @NotBlank(message = "链地址不能为空") String address, String describe) {
        return addAddressBookService.updateAddressBookService(id, chainId, address, describe);
    }

    /**
     * 查询联系人
     *
     * @param page     页
     * @param pageSize 页大小
     * @param chainId  链id
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/29
     */
    @RequestMapping("/queryPage")
    public R queryAddressBook(@RequestParam(defaultValue = "1") @Min(value = 1, message = "页码最小为1") int page,
        @RequestParam(defaultValue = "10") @Min(value = 1, message = "数量最小为1") int pageSize,
        Integer chainId) {
        return addAddressBookService.queryAddressBookService(page, pageSize, chainId);
    }
}
