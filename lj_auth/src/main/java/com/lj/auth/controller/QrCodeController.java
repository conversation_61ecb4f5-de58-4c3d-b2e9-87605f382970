package com.lj.auth.controller;

import com.dtflys.forest.annotation.Post;
import com.lj.auth.common.R;
import com.lj.auth.service.DidQrCodeService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotBlank;
import java.util.Map;

/**
 * 描述：DID授权登录 创建人: CFJ 创建时间: 2024/07/01
 */
@Validated
@RestController
@RequestMapping(value = "qr")
public class QrCodeController {
    @Resource
    private DidQrCodeService didQrCodeService;

    /**
     * 获取DID信息二维码
     * 
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/07/16
     */
    @PostMapping("/getQrCode")
    public R getQrCode(@NotBlank(message = "appId不能为空") String appId) {
        return didQrCodeService.getQrCodeService(appId);
    }

    /**
     * 通过二维码信息获取DID
     *
     * @param request
     * @param data
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/07/16
     */
    @PostMapping("/getDIDByQr")
    public R getDIDByQr(HttpServletRequest request, @RequestBody Map<String, Object> data) {
        return didQrCodeService.getDIDByQrService(request, data);
    }

}
