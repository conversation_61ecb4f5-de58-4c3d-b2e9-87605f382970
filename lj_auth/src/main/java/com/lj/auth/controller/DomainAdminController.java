package com.lj.auth.controller;

import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.lj.auth.annotation.LogRecord;
import com.lj.auth.common.R;
import com.lj.auth.domain.vo.TextVo;
import com.lj.auth.exception.ResponseEnum;
import com.lj.auth.exception.ServiceException;
import com.lj.auth.service.AccountDomainAddressService;
import com.lj.auth.service.DomainAdminRecordService;
import com.lj.auth.service.EnergyRechargeFlowService;
import com.lj.auth.util.IpUtil;
import com.lj.auth.util.RegexUtil;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;

import cn.hutool.core.date.DateUtil;

@Validated
@RestController
@RequestMapping(value = "/domain_admin")
public class DomainAdminController {

    @Resource
    private DomainAdminRecordService domainAdminRecordService;
    @Resource
    private AccountDomainAddressService accountDomainAddressService;
    @Resource
    private EnergyRechargeFlowService energyRechargeFlowService;

    /**
     * 设置域名所有者
     *
     * @param chainId 链ID
     * @param domain 域名
     * @param address 地址
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/07
     */
    @RequestMapping("/set_owner")
    @LogRecord(methodName = "设置域名所有者")
    public R setDomainOwner(@NotNull Integer chainId, @NotBlank(message = "域名不能为空") String domain,
        @NotBlank(message = "链地址不能为空") String address) {
        if (!RegexUtil.checkEthAddress(address)) {
            throw new ServiceException(ResponseEnum.BSNChainAddressError);
        }
        checkDomian(domain);
        return domainAdminRecordService.setDomainOwnerService(chainId, domain, address);
    }

    /**
     * 设置所有者回调通知
     *
     * @param request
     * @param response
     */
    @PostMapping("/set_owner/notify")
    public void setOwnerNotify(HttpServletRequest request, HttpServletResponse response) {
        String realIp = IpUtil.getRealIp(request);
        System.out.println("真实ip:" + realIp);
        domainAdminRecordService.setOwnerNotifyService(request);
    }

    /**
     * 设置所有者详情
     *
     * @param domain 域名
     * @param page 页
     * @param pageSize 页码
     */
    @RequestMapping("/set_owner/detail")
    public R setOwnerDetail(@NotBlank(message = "域名不能为空") String domain,
        @RequestParam(defaultValue = "1") @Min(value = 1, message = "页码最小为1") int page,
        @RequestParam(defaultValue = "10") @Min(value = 1, message = "数量最小为1") int pageSize) {
        return domainAdminRecordService.setOwnerDetailService(domain, page, pageSize);
    }

    /**
     * 设置域名管理者
     *
     * @param paramData
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/07
     */
    @RequestMapping("/set_admin")
    @LogRecord(methodName = "设置域名管理者")
    public R setDomainAdmin(@RequestBody Map<String, Object> paramData) {
        Object domain = paramData.get("domain");
        Object domainAdminInfo = paramData.get("domainAdminInfo");
        if (domain == null || domainAdminInfo == null) {
            throw new ServiceException(ResponseEnum.PARAM_ERROR);
        }
        checkDomian(domain.toString());
        return domainAdminRecordService.setDomainAdminService(domain, domainAdminInfo);
    }

    /**
     * 设置管理者回调通知
     *
     * @param request
     * @param response
     */
    @PostMapping("/set_admin/notify")
    public void setAdminNotify(HttpServletRequest request, HttpServletResponse response) {
        String realIp = IpUtil.getRealIp(request);
        System.out.println("真实ip:" + realIp);
        domainAdminRecordService.setAdminNotifyService(request);
    }

    /**
     * 获取域名管理者详情
     *
     * @param domain 域名
     * @param page 页
     * @param pageSize 页码
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/07
     */
    @RequestMapping("/set_admin/detail")
    public R setAdminDetail(@NotBlank(message = "域名不能为空") String domain,
        @RequestParam(defaultValue = "1") @Min(value = 1, message = "页码最小为1") int page,
        @RequestParam(defaultValue = "10") @Min(value = 1, message = "数量最小为1") int pageSize) {
        return domainAdminRecordService.setAdminDetailService(domain, page, pageSize);
    }

    /**
     * 设置域名解析记录
     *
     * @param paramData
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/07
     */
    @RequestMapping("/set_resolve")
    @LogRecord(methodName = "设置域名解析记录")
    public R setDomainResolveInfo(@RequestBody Map<String, Object> paramData) {
        Object domain = paramData.get("domain");
        Object resolveInfos = paramData.get("resolveInfos");
        if (domain == null || resolveInfos == null) {
            throw new ServiceException(ResponseEnum.PARAM_ERROR);
        }
        checkDomian(domain.toString());
        checkDomianResolve(resolveInfos);
        return domainAdminRecordService.setDomainResolveService(domain, resolveInfos);
    }

    /**
     * 设置解析记录回调通知
     *
     * @param request
     * @param response
     */
    @PostMapping("/set_resolve/notify")
    public void setResolveNotify(HttpServletRequest request, HttpServletResponse response) {
        String realIp = IpUtil.getRealIp(request);
        System.out.println("真实ip:" + realIp);
        domainAdminRecordService.setResolveNotifyService(request);
    }

    /**
     * 设置域名地址映射
     *
     * @param opbChainId 链框架Id
     * @param domain 域名
     * @param address 地址
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/07
     */
    @RequestMapping("/domainNameMapping")
    @LogRecord(methodName = "设置域名地址映射")
    public R accountDomianAddress(@NotNull Integer opbChainId, @NotBlank(message = "域名不能为空") String domain,
        @NotBlank(message = "链地址不能为空") String address) {
        // 校验链地址
        if (!RegexUtil.checkEthAddress(address)) {
            throw new ServiceException(ResponseEnum.ChainAddressError);
        }
        return accountDomainAddressService.domainNameMappingService(opbChainId, domain, address);
    }

    /**
     * 修改域名地址映射
     *
     * @param id id
     * @param opbChainId 链框架Id
     * @param address 地址
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/07
     */
    @RequestMapping("/update/domainNameMapping")
    @LogRecord(methodName = "修改域名地址映射")
    public R accountDomianAddressUpdate(@NotNull Integer id, @NotNull Integer opbChainId,
        @NotBlank(message = "链地址不能为空") String address) {
        return accountDomainAddressService.updateDomainNameMappingService(id, opbChainId, address);
    }

    /**
     * 查询域名映射列表
     *
     * @param domain 域名
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/07
     */
    @RequestMapping("/domainNameMappingList")
    public R accountDomianAddressList(@NotBlank(message = "域名不能为空") String domain) {
        return accountDomainAddressService.domainNameMappingListService(domain);
    }


    /**
     * 域名解析地址
     * @param domain
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/02/26
     */
    @RequestMapping("/domainResolveAddress")
    public R domainResolveAddress(@NotBlank(message = "域名不能为空") String domain) {
        return accountDomainAddressService.domainResolveAddressService(domain);
    }

    /**
     * 地址域名解析
     *
     * @param address
     * @return {@link R }
     */
    @RequestMapping("/addressDomainNameResolution")
    public R addressDomainNameResolution(@NotBlank(message = "地址不能为空") String address) {
        return accountDomainAddressService.addressDomainNameResolutionService(address);
    }



    /**
     * 移除域名地址映射
     *
     * @param id id
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/07
     */
    @LogRecord(methodName = "移除域名地址映射")
    @RequestMapping("/delete/domainNameMapping")
    public R accountDomianAddressDelete(@NotNull Integer id) {
        return accountDomainAddressService.domainNameMappingDeleteService(id);
    }


    /**
     * 手动移除域名链地址映射
     * @param id
     * @param uuid
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/02/26
     */
//    @LogRecord(methodName = "移除域名地址映射")
    @RequestMapping("/delete/domainNameMapping1")
    public R accountDomianAddressDelete(@NotNull Integer id,String uuid) {
        return accountDomainAddressService.domainNameMappingDeleteService1(id,uuid);
    }





    /**
     * TODO 调用其他服务 域名绑定DID
     *
     * @param domain 域名
     * @param didSymbol DID标识
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/07
     */
    @LogRecord(methodName = "域名绑定DID")
    @RequestMapping("/bind/domainDid")
    public R bindDomainDid(@NotBlank(message = "域名不能为空") String domain,
        @NotBlank(message = "did不能为空") String didSymbol) {
        // return accountDomainAddressService.bindDomainDidService(domain, didSymbol);
        return null;
    }

    /**
     * TODO 调用其他服务 解除域名绑定DID
     *
     * @param id
     * @return {@link R}
     */
    @LogRecord(methodName = "域名绑定DID")
    @PostMapping("/unbind/domainDid")
    public R unBindDomainDid(@NotBlank(message = "域名不能为空") String domain,
        @NotBlank(message = "did不能为空") String didSymbol) {
        // return accountDomainAddressService.unBindDomainDidService(domain, didSymbol);
        return null;
    }

    /**
     * TODO 调用其他服务 用户拥有域名列表
     * 
     * @param page
     * @param pageSize
     * @param parentDomain
     * @param domain
     * @return {@link R}
     */
    @PostMapping("/search")
    public R searchUserDomain(@RequestParam(defaultValue = "1") @Min(1) Integer page,
        @RequestParam(defaultValue = "10") @Min(1) Integer pageSize, String parentDomain, String domain) {
        // return accountDomainAddressService.searchUserDomainService(page, pageSize, parentDomain, domain);
        return null;
    }

    /**
     * TODO 调用其他服务 查询可以转让域名列表
     *
     * @param page
     * @param pageSize
     * @param parentDomain
     * @param domain
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/08
     */
    @PostMapping("/mayTransferDomainPage")
    public R mayTransferDomainPage(@RequestParam(defaultValue = "1") @Min(1) Integer page,
        @RequestParam(defaultValue = "10") @Min(1) Integer pageSize, String parentDomain, String domain) {
        // return accountDomainAddressService.mayTransferDomainPageService(page, pageSize, parentDomain, domain);
        return null;
    }

    /**
     * TODO 调用其他服务 查询域名转让状态
     * 
     * @param domain
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/08
     */
    @PostMapping("/domainTransferState")
    public R domainTransferState(String domain) {
        // return accountDomainAddressService.domainTransferStateService(domain);
        return null;
    }

    /**
     * 域名上链列表
     * 
     * @param domain 域名
     * @return {@link R}
     */
    @RequestMapping("/domainDetail")
    public R searchDomianDetail(@NotBlank(message = "域名不能为空") String domain) {
        return accountDomainAddressService.searchDomianDetailService(domain);
    }

    /**
     * 获取域名应用数量
     *
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/07
     */
    @RequestMapping("/queryApplication")
    public R queryDomainApplication() {
        return accountDomainAddressService.queryDomainApplicationService();
    }

    /**
     * 获取域名应用
     *
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/07
     */
    @RequestMapping("/queryApplicationManage")
    public R queryDomainApplicationManage() {
        return accountDomainAddressService.queryDomainApplicationManageService();
    }

    /**
     * 获取应用列表
     *
     * @param page 页
     * @param pageSize 页码
     * @param type 1-已经应用 2-未应用
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/07
     */
    @RequestMapping("/queryApplicationList")
    public R queryDomainApplicationList(@RequestParam(defaultValue = "1") @Min(1) Integer page,
        @RequestParam(defaultValue = "10") @Min(1) Integer pageSize, Integer type) {
        return accountDomainAddressService.queryDomainApplicationListService(page, pageSize, type);
    }

    /**
     * 获取域名应用详情
     *
     * @param domain 域名
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/07
     */
    @RequestMapping("/queryApplicationDetail")
    public R queryDomainApplicationDetail(@NotBlank(message = "域名不能为空") String domain) {
        return accountDomainAddressService.queryDomainApplicationDetailService(domain);
    }

    /**
     * TODO 调用其他服务 转让域名记录
     * 
     * @param recipientPhone
     * @param domain
     * @return {@link R}
     */
    @PostMapping("/transferDomainRecord")
    public R transferDomainRecord(@NotBlank(message = "接收用户手机号不能为空") String recipientPhone,
        @NotBlank(message = "domain不能为空") String domain) {
        // return accountDomainAddressService.transferDomainRecordService(recipientPhone, domain);
        return null;
    }

    /**
     * TODO 调用其他接口 域名转让前端完成主币扣费
     * 
     * @param domain
     * @param accountSymbol
     * @param accountName
     * @param hash
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/15
     */
    @PostMapping("/transferDomain")
    public R transferDomain(@NotBlank(message = "domain不能为空") String domain,
        @NotBlank(message = "接收人域名或DID不能为空") String accountSymbol,
        @NotBlank(message = "接收人实名姓名不能为空") String accountName,
        @NotBlank(message = "hash交易哈希不能为空") String hash) {
        // return accountDomainAddressService.transferDomainService2(domain, accountSymbol, accountName, hash);
        return null;
    }

    /**
     * TODO 调用其他服务 获取接收方姓名
     *
     * @param recipientPhone
     * @param id
     * @return {@link R}
     */
    @PostMapping("/getReceiveName")
    public R getReceiveName(@NotBlank(message = "域名或DID标识不能为空") String accountSymbol) {
        // return accountDomainAddressService.getReceiveNameService(accountSymbol);
        return null;
    }

    /**
     * 获取域名转让服务费
     *
     * @return {@link R}
     */
    @RequestMapping("/getServiceCharge")
    public R getServiceCharge() {
        return energyRechargeFlowService.getServiceChargeService();
    }

    /**
     * TODO 获取域名转让记录
     *
     * @param page
     * @param pageSize
     * @param time
     * @return {@link R}
     */
    @PostMapping("/queryTransferRecordList")
    public R queryTransferRecordList(@RequestParam(defaultValue = "1") @Min(1) Integer page,
        @RequestParam(defaultValue = "10") @Min(1) Integer pageSize,
        @DateTimeFormat(pattern = "yyyy-MM") Date time) {
        Date startTime = null;
        Date endTime = null;
        if (ObjectUtils.isNotEmpty(time)) {
            startTime = DateUtil.beginOfMonth(time);
            endTime = DateUtil.endOfMonth(time);
        }
        return null;
        // return accountDomainAddressService.queryTransferRecordListService(page, pageSize, startTime, endTime);
    }

    /**
     * TODO 调用其他服务 获取所有全详情
     * 
     * @param domain
     * @return {@link R}
     */
    @RequestMapping("/getDomainOwnerDetail")
    public R getDomainOwnerDetail(@NotBlank(message = "域名不能为空") String domain) {
        // return accountDomainAddressService.getDomainOwnerDetailService(domain);
        return null;
    }

    /**
     * 获取域名解析记录详情
     *
     * @param domain 域名
     * @param page 页
     * @param pageSize 页码
     * @return {@link R}
     */
    @RequestMapping("/set_resolve/detail")
    public R setResolveDetail(@NotBlank(message = "域名不能为空") String domain,
        @RequestParam(defaultValue = "1") @Min(value = 1, message = "页码最小为1") int page,
        @RequestParam(defaultValue = "10") @Min(value = 1, message = "数量最小为1") int pageSize) {
        return domainAdminRecordService.setReolveDetailService(domain, page, pageSize);
    }

    /**
     * 获取域名绑定状态
     * 
     * @param domain
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/19
     */
    @RequestMapping("/domainBindState")
    public R getDomainBindState(@NotBlank(message = "域名不能为空") String domain) {
        return domainAdminRecordService.getDomainBindStateService(domain);
    }

    /**
     * 一键解除域名绑定
     *
     * @param domain 域名
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/19
     */
    @RequestMapping("/domainClear")
    public R getDomainClear(@NotBlank(message = "域名不能为空") String domain) {
        return domainAdminRecordService.getDomainClearService(domain);
    }

    /**
     * 一键解除域名绑定(内部服务调用)
     *
     * @param domain 域名
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/19
     */
    @RequestMapping("/domainClearV2")
    public boolean getDomainClearV2(@NotBlank(message = "域名不能为空") String domain,
        @NotBlank(message = "用户uuid不能为空") String uuid) {
        return domainAdminRecordService.getDomainClearService(domain, uuid);
    }

    /**
     * 校验一级域名
     * 
     * @param domain
     */
    public void checkDomian(String domain) {
        if (domain.contains(".")) {
            // 后缀只能是 .web3 .bsn .com
            String suffix = domain.substring(domain.indexOf("."));
            // 主体只能是小写字母和数字组成
            String subject = domain.substring(0, domain.indexOf("."));
            if (suffix.equals(".web3") || suffix.equals(".bsn") || suffix.equals(".com")) {
                if (!RegexUtil.validDomianString(subject)) {
                    throw new ServiceException("域名格式错误");
                }
            } else {
                throw new ServiceException("域名后缀格式错误");
            }
        } else {
            throw new ServiceException("域名格式错误");
        }
    }

    public void checkDomianResolve(Object resolveInfos) {
        Map map = JSONObject.parseObject(JSON.toJSONString(resolveInfos), Map.class);
        Object text = map.get("text");
        if (text != null) {
            List<TextVo> textVos = JSON.parseArray(JSON.toJSONString(text), TextVo.class);
            for (TextVo textVo : textVos) {
                if (textVo.getKey().equals("YLDID")) {
                    throw new ServiceException(ResponseEnum.SpecialCharacters);
                }
            }
        }

    }
}
