package com.lj.auth.controller;

import com.lj.auth.annotation.LogRecord;
import com.lj.auth.common.R;
import com.lj.auth.service.AddresseeMessageService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 描述：发票收件信息 创建人: CFJ 创建时间: 2024/03/27
 */
@Validated
@RestController
@RequestMapping("/addresseeMessage")
public class AddresseeMessageController {

    @Resource
    private AddresseeMessageService addresseeMessageService;

    /**
     * 收件信息分页查询
     *
     * @param page
     * @param pageSize
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/27
     */
    @RequestMapping("/getPage")
    public R getPage(@RequestParam(defaultValue = "1") @Min(1) Integer page,
        @RequestParam(defaultValue = "10") @Min(1) Integer pageSize) {
        return addresseeMessageService.getPage(page, pageSize);
    }

    /**
     * 新增收件信息
     *
     * @param name 姓名
     * @param phone 手机号
     * @param email 邮箱
     * @param address 地址
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/27
     */
    @RequestMapping("/add")
     @LogRecord(methodName = "收件信息新增")
    public R add(String name, String phone, String email, String address) {
        return addresseeMessageService.add(name, phone, email, address);
    }

    /**
     * 修改收件信息
     *
     * @param id id
     * @param name 姓名
     * @param phone 手机号
     * @param email 邮箱
     * @param address 地址
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/27
     */
    @RequestMapping("/update")
     @LogRecord(methodName = "收件信息修改")
    public R update(@NotNull(message = "id非空") Long id, String name, String phone, String email,
        String address) {
        return addresseeMessageService.updateService(id, name, phone, email, address);
    }

    /**
     * 删除收件信息
     *
     * @param id id
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/27
     */
    @RequestMapping("/del")
     @LogRecord(methodName = "收件信息删除")
    public R del(@NotNull(message = "id非空") Long id) {
        return addresseeMessageService.del(id);
    }

    /**
     * 设置默认收件信息
     *
     * @param id id
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/27
     */
    @RequestMapping("/setDefault")
     @LogRecord(methodName = "收件信息设置默认")
    public R setDefault(@NotNull(message = "id非空") Long id) {
        return addresseeMessageService.setDefault(id);
    }
}
