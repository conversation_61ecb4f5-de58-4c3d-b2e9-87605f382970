package com.lj.auth.controller;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.*;

import com.lj.auth.annotation.LogRecord;
import com.lj.auth.annotation.ValidateSign;
import com.lj.auth.common.R;
import com.lj.auth.domain.vo.OrderNotifyRequest;
import com.lj.auth.domain.vo.PayNotifyVo;
import com.lj.auth.service.AggregatePayService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;

/**
 * 描述：
 * 创建人: cfj
 * 创建时间: 2024/12/05
 */

@Slf4j
@Validated
@RestController
@RequestMapping("/aggregatePay")
public class AggregatePayController {
    @Resource
    private AggregatePayService aggregatePayService;

    /**
     * 获取支付方式配置
     *
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/03
     */
    @RequestMapping("/getPayConfig")
    public R getPayConfig() {
        return aggregatePayService.getPayConfigService();
    }

    /**
     * app下单支付
     *
     * @param merOrderId 订单id
     * @param jsCode 临时登录凭证
     * @param returnUrl 订单展示页面
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/10
     */
    @ValidateSign
    @RequestMapping("/appPay")
    @LogRecord(methodName = "充值")
    public R appPay(HttpServletRequest request, @NotBlank(message = "订单merOrderId不能为空") String merOrderId, String jsCode,
        String returnUrl) {
        return aggregatePayService.appPay(merOrderId, jsCode, returnUrl);
    }

    /**
     * 获取微信小程序支付跳转链接并下单
     *
     * @param amount 金额
     * @param payType 支付方式
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/10
     */
    @RequestMapping("/getWeiXinLink")
    public R getWeiXinLink(
        @Digits(integer = 10, fraction = 2, message = "资金格式错误") @DecimalMin(value = "0.01",
            message = "资金最小值不能低于0.01元") BigDecimal amount,
        @Min(message = "最小值为1", value = 1) @Max(message = "最大值为2", value = 2) int payType, String remark) {
        return aggregatePayService.getWeiXinLinkService(amount, payType,remark);
    }

    /**
     * app关闭未支付订单
     * 
     * @param merOrderId 商户订单号
     * @return
     */
    @ValidateSign
    @RequestMapping("/appClose")
    public R appClose(HttpServletRequest request, String merOrderId) {
        return aggregatePayService.appClose(merOrderId);
    }

    /**
     * app订单交易查询
     *
     * @param merOrderId 商户订单号
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/03
     */
    @RequestMapping("/appQuery")
    public R appQuery(@NotBlank(message = "账单号不能为空") String merOrderId) {
        return aggregatePayService.appQuery(merOrderId);
    }

    /**
     * app支付回调
     * 
     * @param data
     * @return
     * @throws UnsupportedEncodingException
     */
    @PostMapping("/appNotify")
    public R appNotify(PayNotifyVo data) throws UnsupportedEncodingException {
        log.info("支付回调开始================");
        log.info("支付回调开始================");
        log.info("支付回调开始================");
        log.info("appNotifyMsg:{}", JSON.toJSONString(data));
        return aggregatePayService.appNotify(data);
    }
    //充值修改-----------------------------
    //充值修改-----------------------------
    //充值修改-----------------------------
    //1、创建订单

    /**
     * 创建订单
     *
     * @param request
     * @param amount  金额
     * @param remark
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/12/03
     */

    @ValidateSign
    @RequestMapping("/createOrder")
    public R createOrder(HttpServletRequest request,@Digits(integer = 10, fraction = 2, message = "资金格式错误") @DecimalMin(value = "0.01",
        message = "资金最小值不能低于0.01元") BigDecimal amount, String remark) {
        return aggregatePayService.createOrderService(request,amount, remark);
    }


    /**
     * @param data
     * @return {@link R }<AUTHOR>
     * @date 2024/12/05
     */
    @PostMapping("/appNotify1")
    public R appNotify1(@RequestBody  OrderNotifyRequest data) {
        log.info("支付回调开始================");
        log.info("支付回调开始================");
        log.info("支付回调开始================");
        log.info("appNotifyMsg:{}", JSON.toJSONString(data));
        return aggregatePayService.appNotify1(data);
    }

}
