package com.lj.auth.controller;

import com.alibaba.fastjson.JSON;
import com.lj.auth.annotation.AccessControl;
import com.lj.auth.annotation.LogRecord;
import com.lj.auth.common.R;
import com.lj.auth.domain.vo.OrderNotifyRequest;
import com.lj.auth.domain.vo.PayNotifyVo;
import com.lj.auth.service.ContributionRecordService;

import com.lj.auth.service.GlobalConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.*;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import static com.lj.auth.common.CommonConstant.*;

/**
 * 描述：贡献排行榜 创建人: CFJ 创建时间: 2024/07/02
 */
@Validated
@Slf4j
@RestController
@RequestMapping(value = "/contribution")
public class ContributionController {
    /**
     * 服务对象
     */
    @Resource
    private ContributionRecordService contributionRecordService;
    @Resource
    private GlobalConfigService globalConfigService;

    /**
     * 获取支付配置
     * 
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/07/02
     */
    @PostMapping("/getPayConfig")
    public R getPayConfig() {
        return contributionRecordService.getPayConfigService();
    }

    /**
     * 贡献记录
     * 
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/07/03
     */
    @PostMapping("/contributionRecords")
    public R contributionRecords() {
        return contributionRecordService.contributionRecordsService();
    }

    /**
     * 排行榜
     * 
     * @param page 页码
     * @param pageSize 数量
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/07/02
     */
    @PostMapping("/rankingList")
    public R rankingList(@RequestParam(defaultValue = "1") @Min(value = 1, message = "页码最小为1") int page,
        @RequestParam(defaultValue = "10") @Min(value = 1, message = "数量最小为1") int pageSize) {
        return contributionRecordService.rankingListService(page, pageSize);
    }

    /**
     * 我的贡献列表
     * 
     * @param page 页
     * @param pageSize 页码
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/07/04
     */
    @PostMapping("/myContribution")
    public R myContribution(@RequestParam(defaultValue = "1") @Min(value = 1, message = "页码最小为1") int page,
        @RequestParam(defaultValue = "10") @Min(value = 1, message = "数量最小为1") int pageSize) {
        return contributionRecordService.myContributionService(page, pageSize);
    }

    /**
     * 我的贡献排行
     *
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/07/08
     */
    @PostMapping("/myRanking")
    public R myRanking() {
        return contributionRecordService.myRankingService();
    }

    /**
     * 获取微信小程序支付跳转链接并下单
     *
     * @param amount 金额
     * @param payType 支付方式1-微信2-支付宝
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/10
     */
    @RequestMapping("/getWeiXinLink")
    public R getWeiXinLink(
        @Digits(integer = 10, fraction = 2, message = "资金格式错误") @DecimalMin(value = "0.01",
            message = "资金最小值不能低于0.01元") BigDecimal amount,
        @Min(message = "最小值为1", value = 1) @Max(message = "最大值为2", value = 2) int payType) {
        return contributionRecordService.getGXWeiXinLinkService(amount, payType);
    }

    /**
     * app下单支付
     *
     * @param merOrderId 订单id
     * @param jsCode 临时登录凭证
     * @param returnUrl 订单展示页面
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/10
     */
    @RequestMapping("/appPay")
    @LogRecord(methodName = "贡献充值")
    public R appPay(@NotBlank(message = "订单merOrderId不能为空") String merOrderId, String jsCode,
        String returnUrl) {
        return contributionRecordService.appPay(merOrderId, jsCode, returnUrl);
    }

    /**
     * app支付回调
     *
     * @param data
     * @return
     * @throws UnsupportedEncodingException
     */
    @PostMapping("/appNotify")
    public R appNotify(PayNotifyVo data) throws UnsupportedEncodingException {
        log.info("支付回调开始================");
        log.info("支付回调开始================");
        log.info("支付回调开始================");
        log.info("appNotifyMsg:{}", JSON.toJSONString(data));
        return contributionRecordService.appNotify(data);
    }

    /**
     * app订单交易查询
     *
     * @param merOrderId 商户订单号
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/03
     */
    @RequestMapping("/appQuery")
    public R appQuery(@NotBlank(message = "账单号不能为空") String merOrderId) {
        return contributionRecordService.appQuery(merOrderId);
    }

    /**
     * 获取贡献模块开关
     * 
     * <AUTHOR>
     * @date 2024/07/11
     */

    @RequestMapping(value = "/switch")
    public R getSwitch() {
        String globalConfig = globalConfigService.getGlobalConfig(CONTRIBUTION_SWITCH);
        return R.okData(globalConfig);
    }


    /**
     * 创建订单
     *
     * @param request
     * @param amount  金额
     * @param remark
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/12/03
     */
    @RequestMapping("/createOrder")
    public R createOrder(HttpServletRequest request, @Digits(integer = 10, fraction = 2, message = "资金格式错误") @DecimalMin(value = "0.01",
        message = "资金最小值不能低于0.01元") BigDecimal amount, String remark) {
        return contributionRecordService.createOrderService(request,amount, remark);
    }

    @PostMapping("/appNotify1")
    public R appNotify1(@RequestBody OrderNotifyRequest data)  {
        log.info("支付回调开始================");
        log.info("支付回调开始================");
        log.info("支付回调开始================");
        log.info("appNotifyMsg:{}", JSON.toJSONString(data));
        return contributionRecordService.appNotify1(data);
    }

}
