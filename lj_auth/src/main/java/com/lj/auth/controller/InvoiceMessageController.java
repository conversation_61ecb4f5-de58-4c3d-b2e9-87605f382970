package com.lj.auth.controller;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

import com.lj.auth.annotation.LogRecord;
import com.lj.auth.common.R;
import com.lj.auth.domain.Account;
import com.lj.auth.domain.InvoiceMessage;
import com.lj.auth.domain.vo.InvoiceMessageUpVo;
import com.lj.auth.domain.vo.InvoiceMessageVo;
import com.lj.auth.service.AccountService;
import com.lj.auth.service.InvoiceMessageService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import cn.hutool.core.bean.BeanUtil;

/**
 * 描述： 开票信息 创建人: CFJ 创建时间: 2024/03/28
 */
@Validated
@RestController
@RequestMapping("/invoiceMessage")
public class InvoiceMessageController {
    @Resource
    private InvoiceMessageService invoiceMessageService;
    @Resource
    private AccountService accountService;

    /**
     * 查询开票信息
     *
     * @param page 页
     * @param pageSize 页大小
     * @param type 主体类型 1-个人 2-企业 3-组织
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/28
     */
    @RequestMapping("/getPage")
    public R getPage(@RequestParam(defaultValue = "1") @Min(1) Integer page,
        @RequestParam(defaultValue = "10") @Min(1) Integer pageSize, Integer type) {
        return invoiceMessageService.getPage(type, page, pageSize);
    }

    /**
     * 新增开票信息
     *
     * @param invoiceMessageVo
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/28
     */
    @RequestMapping("/add")
    @LogRecord(methodName = "新增开票信息")
    public R add(@Valid InvoiceMessageVo invoiceMessageVo) {
        InvoiceMessage invoiceMessage = new InvoiceMessage();
        BeanUtil.copyProperties(invoiceMessageVo, invoiceMessage);
        Account userInfo = accountService.getUserInfo();
        invoiceMessage.setState(2);
        invoiceMessage.setAccountUuid(userInfo.getUuid());
        return invoiceMessageService.addService(invoiceMessage);
    }

    /**
     * 修改开票信息
     *
     * @param invoiceMessageUpVo
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/28
     */
    @RequestMapping("/update")
    @LogRecord(methodName = "修改开票信息")
    public R update(@Valid InvoiceMessageUpVo invoiceMessageUpVo) {
        Account userInfo = accountService.getUserInfo();
        InvoiceMessage invoiceMessage = new InvoiceMessage();
        BeanUtil.copyProperties(invoiceMessageUpVo, invoiceMessage);
        invoiceMessage.setAccountUuid(userInfo.getUuid());
        return invoiceMessageService.updateService(invoiceMessage);
    }

    /**
     * 删除开票信息
     */
    @RequestMapping("/del")
    @LogRecord(methodName = "删除开票信息")
    public R del(@NotNull(message = "id非空") Long id) {
        return invoiceMessageService.del(id);
    }

    /**
     * 设置默认开票信息
     */
    @RequestMapping("/setDefault")
    @LogRecord(methodName = "设置开票信息默认")
    public R setDefault(@NotNull(message = "id非空") Long id) {
        return invoiceMessageService.setDefault(id);
    }

}
