package com.lj.auth.controller;

import java.math.BigDecimal;

import javax.annotation.Resource;
import javax.validation.constraints.*;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONArray;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lj.auth.annotation.RequestLimit;
import com.lj.auth.common.R;
import com.lj.auth.domain.Account;
import com.lj.auth.domain.FusuiWithdrawal;
import com.lj.auth.domain.RechargeWithdrawRecord;
import com.lj.auth.event.PlanStatusEvent;
import com.lj.auth.mapper.AccountMapper;
import com.lj.auth.mapper.FusuiWithdrawalMapper;
import com.lj.auth.mapper.RechargeWithdrawRecordMapper;
import com.lj.auth.service.AccountService;
import com.lj.auth.util.FusuiUtil;
import com.lj.auth.util.HeadUtil;
import com.lj.auth.util.RegexUtil;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson2.JSONObject;

import cn.dev33.satoken.stp.StpUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 描述：福穗平台 创建人: CFJ 创建时间: 2024/04/11
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/fusui")
public class FusuiController {
    @Resource
    private AccountService accountService;
    @Resource
    private FusuiUtil fusuiUtil;
    @Resource
    private ApplicationEventPublisher applicationEventPublisher;
    @Resource
    private AccountMapper accountMapper;
    @Resource
    private RechargeWithdrawRecordMapper rechargeWithdrawRecordMapper;
    @Resource
    private FusuiWithdrawalMapper fusuiWithdrawalMapper;

    /**
     * 福穗签约
     *
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/11
     */
    @RequestMapping("/uploadPeople")
    @RequestLimit(second = 60, maxCount = 1)
    public R uploadPeople() {
        return accountService.uploadPeopleService();
    }

    /**
     * 福穗签约（身份证号）
     * 
     * @param idCard
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/05/23
     */
    @RequestMapping("/uploadPeopleIdCard")
    @RequestLimit(second = 60, maxCount = 1)
    public R uploadPeople(@NotBlank(message = "姓名不能为空") String name,
        @NotBlank(message = "身份证号不能为空") String idCard) {
        Assert.isTrue(RegexUtil.isIDCard18(idCard), "请输入合法的身份证号");
        return accountService.uploadPeopleService(name, idCard);
    }


    /**
     * 福穗签约（身份证号）
     *
     * @param name
     * @param idCard
     * @param phone
     * @return {@link R }
     */
    @RequestMapping("/uploadPeopleIdCardV2")
    @RequestLimit(second = 60, maxCount = 1)
    public R uploadPeopleIdCardV2(@NotBlank(message = "姓名不能为空") String name,
                          @NotBlank(message = "身份证号不能为空") String idCard,
                                  @NotBlank(message = "手机号不能为空") String phone) {
        Assert.isTrue(RegexUtil.isIDCard18(idCard), "请输入合法的身份证号");
        Assert.isTrue(RegexUtil.isMobileSimple(phone), "请输入合法的手机号");
        return accountService.uploadPeopleIdCardV2Service(name, idCard,phone);
    }


    /**
     * 判断用户是否实名(修改成判断福穗有没有签约需不需要填身份证号或者弹窗)
     * 等于3前端不会调用这个接口
     *
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/05/23
     */
    @RequestMapping("/isRealName")
    public R isRealName() {
        Account userInfo = accountService.getUserInfo();
        // 刷新用户实际的签约状态、主动拉取
        String peopleId = userInfo.getPeopleId();
        Integer signStatus = userInfo.getSignStatus();
        try {
            if (StrUtil.isNotBlank(peopleId)) {
                if (ObjectUtil.equals(signStatus, 2) || ObjectUtil.equals(signStatus, 1)) {
                    // 查询一下签约状态
                    JSONObject jsonObject = fusuiUtil.queryPeople(peopleId);
                    System.out.println("查询签约返回信息： " + jsonObject);
                    Integer code = jsonObject.getInteger("code");
                    if (code == 0) {
                        JSONObject data = jsonObject.getJSONObject("data");
                        Integer signStatusNew = data.getInteger("signStatus");
                        if (ObjectUtil.equals(signStatusNew, 3)) {
                            // 修改用户签约状态
                            accountMapper.update(null,
                                Wrappers.<Account>lambdaUpdate().eq(Account::getId, userInfo.getId())
                                    .set(Account::getSignStatus, signStatusNew));
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("查询用户签约状态异常", e);
        }
        // Integer isRealName = userInfo.getIsRealName();
        if (ObjectUtil.equals(accountService.getUserInfo().getSignStatus(), 2)) {
            return R.okData(true);
        }
        return R.okData(false);
    }

    /**
     * 签约状态查询
     *
     * @param peopleId 签约人id
     * @return
     */
    @RequestMapping("/queryPeople")
    public R queryPeople(@NotBlank(message = "签约人id不能为空") String peopleId) {
        JSONObject jsonObject = fusuiUtil.queryPeople(peopleId);
        Integer code = jsonObject.getInteger("code");
        if (code == 0) {
            JSONObject data = jsonObject.getJSONObject("data");
            return R.okData(data);
        } else {
            String msg = jsonObject.getString("msg");
            return R.error(msg);
        }
    }

    /**
     * 签约状态列表查询
     *
     * @param userName
     * @param idCard
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/07/09
     */
    @RequestMapping("/queryPeopleList")
    public R queryPeopleList(@NotBlank(message = "用户名称不能为空") String userName,
        @NotBlank(message = "身份证号不能为空") String idCard) {
        JSONObject jsonObject = fusuiUtil.queryPeopleList(userName, idCard);
        Integer code = jsonObject.getInteger("code");
        if (code == 0) {
            JSONArray data = jsonObject.getJSONArray("data");
            return R.okData(data);
        } else {
            String msg = jsonObject.getString("msg");
            return R.error(msg);
        }
    }

    /**
     * 查询公司账户余额
     *
     * @return
     */
    @RequestMapping("/queryAmount")
    public R queryAmount() {
        JSONObject jsonObject = fusuiUtil.queryAmount();
        Integer code = jsonObject.getInteger("code");
        if (code == 0) {
            JSONObject data = jsonObject.getJSONObject("data");
            return R.okData(data);
        } else {
            String msg = jsonObject.getString("msg");
            return R.error(msg);
        }
    }

    /**
     * 查询支持的提现类型
     *
     * @return
     */
    @RequestMapping("/queryPayMethod")
    public R queryPayMethod() {
        JSONObject jsonObject = fusuiUtil.queryPayMethod();
        Integer code = jsonObject.getInteger("code");
        if (code == 0) {
            JSONObject data = jsonObject.getJSONObject("data");
            return R.okData(data);
        } else {
            String msg = jsonObject.getString("msg");
            return R.error(msg);
        }
    }

    /**
     * 提现申请
     *
     * @param cardNumber 银行卡号
     * @param amount 提现金额
     * @param idCard 身份证号
     * @param mobile 手机号
     * @param outBizNo 流水号
     * @param userName 用户姓名
     * @return
     */
    @RequestMapping("/uploadPlan")
    public R uploadPlan(@NotBlank(message = "银行卡号不能为空") String cardNumber,
        @NotNull(message = "提现金额不能为空") @DecimalMin(value = "0.01", message = "提现金额格式不符合要求") @Digits(
            integer = 12, fraction = 2, message = "提现金额格式不正确") BigDecimal amount,
        @NotBlank(message = "身份证号不能为空") String idCard, @NotBlank(message = "手机号不能为空") String mobile,
        @NotBlank(message = "流水号不能为空") String outBizNo, @NotBlank(message = "用户姓名不能为空") String userName) {
        JSONObject jsonObject =
            fusuiUtil.uploadPlan(cardNumber, amount, "银行卡", idCard, mobile, outBizNo, userName);
        System.out.println("提现审核参数：卡号： " + cardNumber + " 金额： " + amount + "  类型：" + "银行卡" + "身份证号： " + idCard
            + " 手机号：" + mobile + " 流水号： " + outBizNo + " 姓名： " + userName);
        System.out.println("提现审核通过： " + jsonObject);
        Integer code = jsonObject.getInteger("code");
        if (code == 0) {
            JSONObject data = jsonObject.getJSONObject("data");
            return R.okData(data);
        } else {
            String msg = jsonObject.getString("msg");
            return R.error(msg);
        }
    }

    /**
     * 提现申请V2
     *
     * @param id
     * @return
     */
    @RequestMapping("/uploadPlanV2")
    public R uploadPlanV2(@NotNull Long id) {
        // 获取提现信息
        RechargeWithdrawRecord rechargeWithdrawRecord = rechargeWithdrawRecordMapper.selectById(id);
        String cardNumber = rechargeWithdrawRecord.getCardNumber();
        BigDecimal amount = rechargeWithdrawRecord.getAmount();
        String outBizNo = rechargeWithdrawRecord.getTranNumber();
        String userName = rechargeWithdrawRecord.getCardName();
        String mobile = null;
        String idCard = null;
        // 获取个人身份证号
        Account account = accountMapper.selectOne(
            Wrappers.<Account>lambdaQuery().eq(Account::getUuid, rechargeWithdrawRecord.getAccountUuid()));
        // 获取签约记录信息
        FusuiWithdrawal fusuiWithdrawal = fusuiWithdrawalMapper.selectOne(
            Wrappers.<FusuiWithdrawal>lambdaQuery().eq(FusuiWithdrawal::getUuid, account.getUuid()));
        if (fusuiWithdrawal != null) {
            mobile = fusuiWithdrawal.getPhoneNumber();
            idCard = fusuiWithdrawal.getIdCard();
        } else {
            mobile = account.getPhoneNumber();
            idCard = account.getIdCard();
        }
        if (StrUtil.isBlank(mobile)) {
            return R.error("手机号不能为空");
        }
        if (StrUtil.isBlank(idCard)) {
            return R.error("身份证号不能为空");
        }
        JSONObject jsonObject =
            fusuiUtil.uploadPlan(cardNumber, amount, "银行卡", idCard, mobile, outBizNo, userName);
        System.out.println("提现审核参数：卡号： " + cardNumber + " 金额： " + amount + "  类型：" + "银行卡" + "身份证号： " + idCard
            + " 手机号：" + mobile + " 流水号： " + outBizNo + " 姓名： " + userName);
        System.out.println("提现审核通过： " + jsonObject);
        Integer code = jsonObject.getInteger("code");
        if (code == 0) {
            JSONObject data = jsonObject.getJSONObject("data");
            return R.okData(data);
        } else {
            String msg = jsonObject.getString("msg");
            return R.error(msg);
        }
    }



    /**
     * 提现结果查询
     * 
     * @param outBizNo 外部流水号
     * @return
     */
    @RequestMapping("/queryPlan")
    public R queryPlan(@NotBlank(message = "流水号不能为空") String outBizNo) {
        JSONObject jsonObject = fusuiUtil.queryPlan(outBizNo);
        Integer code = jsonObject.getInteger("code");
        if (code == 0) {
            JSONObject data = jsonObject.getJSONObject("data");
            return R.okData(data);
        } else {
            String msg = jsonObject.getString("msg");
            return R.error(msg);
        }
    }

    /**
     * 结算发放结果分页查询
     * 
     * @param page 当前页数
     * @param limit 分页大小 最大1000
     * @param outBizNo 外部流水号 非必填
     * @param auditStatus 审核状态 0-待审核 1-审核通过 2,3-人工审核拒绝 非必填
     * @param grantStatus 发放(推送)状态 1-发放通过 2-发放拒绝 非必填
     * @param payStatus 支付状态 0-未处理 1-待打款 2-打款中 3-打款成功 4-打款失败 5-退票退款 非必填
     * @param startTime 开始时间 格式'yyyy-MM-dd HH:mm:ss'
     * @param endTime 结束时间 格式'yyyy-MM-dd HH:mm:ss'
     * @return 调用结果
     */
    @RequestMapping("/queryPlanPage")
    public R queryPlanPage(@RequestParam(defaultValue = "1") @Min(value = 1, message = "页码最小为1") Integer page,
        @RequestParam(defaultValue = "10") @Min(value = 1, message = "数量最小为1") Integer limit, String outBizNo,
        Integer auditStatus, Integer grantStatus, Integer payStatus,
        @NotBlank(message = "开始时间不能为空") String startTime, @NotBlank(message = "结束时间不能为空") String endTime) {
        JSONObject jsonObject = fusuiUtil.queryPlanPage(page, limit, outBizNo, auditStatus, grantStatus,
            payStatus, startTime, endTime);
        Integer code = jsonObject.getInteger("code");
        if (code == 0) {
            JSONObject data = jsonObject.getJSONObject("data");
            return R.okData(data);
        } else {
            String msg = jsonObject.getString("msg");
            return R.error(msg);
        }
    }

    @RequestMapping("/updatePlanData")
    public R updatePlanData(@NotBlank(message = "流水号不能为空") String bizOutNo,
        @NotNull(message = "审核状态不能为空") Integer auditStatus,
        @NotNull(message = "发放状态不能为空") Integer grantStatus,
        @NotNull(message = "支付状态不能为空") Integer payStatus) {
        log.info("-----------接收到打款定时任务发来的信息，bizOutNo:{}----------", bizOutNo);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("bizOutNo", bizOutNo);
        jsonObject.put("auditStatus", auditStatus);
        jsonObject.put("grantStatus", grantStatus);
        jsonObject.put("payStatus", payStatus);
        applicationEventPublisher.publishEvent(new PlanStatusEvent(jsonObject));
        return R.ok();
    }
}
