package com.lj.auth.controller;

import java.math.BigDecimal;
import java.util.Date;

import javax.annotation.Resource;
import javax.validation.constraints.*;

import com.lj.auth.annotation.LogRecord;
import com.lj.auth.annotation.RequestLimit;
import com.lj.auth.common.R;
import com.lj.auth.exception.ResponseEnum;
import com.lj.auth.exception.ServiceException;
import com.lj.auth.service.EnergyRechargeFlowService;
import com.lj.auth.util.RegexUtil;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 描述：能量充值 创建人: CFJ 创建时间: 2024/04/02
 */
@Slf4j
@Validated
@RestController
@RequestMapping(value = "/energy")
public class EnergyController {
    @Resource
    private EnergyRechargeFlowService energyRechargeFlowService;

    /**
     * 自研链能量充值
     *
     * @param amount 金额
     * @param payPassword 支付密码
     * @param address 链地址
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/02
     */
    @RequestMapping("/addEnergy")
    @LogRecord(methodName = "能量充值")
    public R addAddressEnergy(
        @Digits(integer = 10, fraction = 2, message = "总金额格式错误") @DecimalMin(value = "0.01",
            message = "总金额最小值不能低于0.01元") BigDecimal amount,
        @NotBlank(message = "密码不能为空") String payPassword, @NotBlank(message = "地址不能为空") String address) {
        return R.error("暂未开放");
        // if (!RegexUtil.checkEthAddress(address)) {
        // throw new ServiceException(ResponseEnum.ChainAddressError);
        // }
        // return energyRechargeFlowService.addAddressEnergyService(amount, payPassword, address);
    }

    /**
     * 获取云链能量定价
     *
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/02
     */
    @RequestMapping("/getYLGas")
    public R getYlGasQuantity() {
        return energyRechargeFlowService.getYlGasQuantityService();
    }

    /**
     * 获取个人能量
     *
     * @param address
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/02
     */
    @RequestMapping("/getPersonalEnergy")
    public R getPersonalEnergy(@NotBlank(message = "地址不能为空") String address) {
        if (!RegexUtil.checkEthAddress(address)) {
            throw new ServiceException(ResponseEnum.ChainAddressError);
        }
        return energyRechargeFlowService.getPersonalEnergyService(address);
    }

    /**
     * 能量明细（充值）
     *
     * @param page 页
     * @param pageSize 页大小
     * @param type 交易类型：1=充值2=消耗
     * @param date 日期
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/02
     */
    @RequestMapping("/queryPage")
    public R queryEnergyDetail(@RequestParam(defaultValue = "1") @Min(value = 1, message = "页码最小为1") int page,
        @RequestParam(defaultValue = "10") @Min(value = 1, message = "数量最小为1") int pageSize, Integer type,
        @DateTimeFormat(pattern = "yyyy-MM") Date date) {
        if (date == null) {
            date = DateUtil.beginOfMonth(new Date());
        }
        return energyRechargeFlowService.queryEnergyDetailService(page, pageSize, type, date);
    }

    /**
     * 更具hash获取交易详情
     *
     * @param hash 哈希
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/02
     */
    @RequestMapping("/queryHashDetail")
    public R queryHashDetail(@NotBlank(message = "交易哈希不能为空") String hash) {
        return energyRechargeFlowService.queryHashDetailService(hash);
    }

    /**
     * 域名解析地址
     *
     * @param domain 域名
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/02
     */
    @RequestMapping("/addressResolution")
    public R addressResolution(@NotBlank(message = "地址不能为空") String domain) {
        return energyRechargeFlowService.addressResolutionService(domain);
    }

    /**
     * 能量二维码背景
     *
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/02
     */
    @RequestMapping("/energyQRCodeBackground")
    public R energyQRCodeBackground() {
        return energyRechargeFlowService.energyQRCodeBackgroundService();
    }

    /**
     * 能量明细（链上交易）
     *
     * @param page 页
     * @param pageSize 页大小
     * @param address 地址
     * @param type 0-全部 -1转入 2-转出
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/02
     */
    @RequestMapping("/getAddressAllOfPage")
    public R getAddressAllOfPage(
        @RequestParam(defaultValue = "1") @Min(value = 1, message = "页码最小为1") int page,
        @RequestParam(defaultValue = "10") @Min(value = 1, message = "数量最小为1") int pageSize,
        @NotBlank(message = "地址不能为空") String address, @RequestParam("type") int type) {
        return energyRechargeFlowService.getWalletAddressAllOfPageService(page, pageSize, address, type);
    }

    /**
     * 获取备注字符串hex
     *
     * @param message
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/02
     */
    @RequestMapping("/getHexMessage")
    public R getHexMessage(@NotBlank(message = "message不能为空") String message) {
        try {
            StringBuilder hex = new StringBuilder();
            byte[] bytes = message.getBytes("UTF-8");
            for (byte b : bytes) {
                hex.append(String.format("%02x", b));
            }
            return R.okData(hex.toString());
        } catch (Exception e) {
        }
        return R.okData(message);
    }

    /**
     * 源力概览
     * 
     * @param address -链地址
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/06/04
     */
    @RequestMapping("/spOverviewV1")
    public R getSpOverview(String address) {
        return energyRechargeFlowService.getSpOverviewService(address);
    }

    @RequestMapping("/spOverview")
    public R getSpOverviewV1(String address) {
        return energyRechargeFlowService.getSpOverviewService1(address);
    }

    /**
     * SP单价趋势
     * 
     * @param type 1-一个月 2-近三个月
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/06/04
     */
    @RequestMapping("/spUnitPriceTrend")
    public R getSpUnitPriceTrend(@RequestParam(defaultValue = "1") int type) {
        return energyRechargeFlowService.getSpUnitPriceTrend(type);
    }

    /**
     * SP单价趋势
     *
     * @param type 1-日 2-周 3-月
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/06/04
     */
    @RequestMapping("/spUnitPriceKLine")
    public R spUnitPriceKLine(@RequestParam(defaultValue = "1") int type,
        @RequestParam(defaultValue = "1") @Min(value = 1, message = "页码最小为1") int page,
        @RequestParam(defaultValue = "10") @Min(value = 1, message = "数量最小为1") int pageSize) {
        return energyRechargeFlowService.spUnitPriceKLineService(type, page, pageSize);
    }

}
