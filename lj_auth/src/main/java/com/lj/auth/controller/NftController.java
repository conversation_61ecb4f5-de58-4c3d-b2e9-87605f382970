package com.lj.auth.controller;

import com.lj.auth.common.R;
import com.lj.auth.service.NftGetRecordService;
import com.lj.auth.service.NftOnChainService;
import com.lj.auth.service.NftService;
import com.lj.auth.service.NftTransferRecordService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.web3j.protocol.core.methods.response.TransactionReceipt;

import javax.annotation.Resource;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author: zjd
 * @Description:
 * @Date: 2023/11/21 9:12
 */
@Validated
@RestController
@RequestMapping("/nft")
public class NftController {

    @Resource
    private NftService nftService;
    @Resource
    private NftTransferRecordService nftTransferRecordService;
    @Resource
    private NftGetRecordService nftGetRecordService;
    @Resource
    private NftOnChainService nftOnChainService;

    /**
     * 获取NFT列表
     * 
     * @param opbChainId
     * @param chainAccount
     * @param nftName
     * @return
     */
    @RequestMapping("/getNfts")
    public R getNfts(@NotNull(message = "链框架id不能为空") Integer opbChainId,
        @NotBlank(message = "链账户地址不能为空") String chainAccount, String nftName) {
        return nftService.getNfts(opbChainId, chainAccount, nftName);
    }

    /**
     * 获取NFT列表
     * 
     * @param pageNum 页号
     * @param pageSize 页码
     * @param opbChainId 链id
     * @param chainAccount 链账户
     * @param queryType 查询类型 1：可用 2：不可用
     * @param nftName NFT名称
     * @return
     */
    @RequestMapping("/getNftsV2")
    public R getNfts2(@RequestParam(defaultValue = "1") @Min(value = 1, message = "页码最小为1") Integer pageNum,
        @RequestParam(defaultValue = "10") @Min(value = 1, message = "数量最小为1") Integer pageSize,
        @NotNull(message = "链框架id不能为空") Integer opbChainId,
        @NotBlank(message = "链账户地址不能为空") String chainAccount,
        @RequestParam(defaultValue = "1") Integer queryType, String nftName) {
        return nftService.getNftsV2(pageNum, pageSize, opbChainId, chainAccount, queryType, nftName);
    }

    /**
     * 获取NFT详情
     * 
     * @param id
     * @return
     */
    @RequestMapping("/getNftDetails")
    public R getNftDetails(@NotNull(message = "id不能为空") Long id) {
        return nftService.getNftDetails(id);
    }

    /**
     * 获取NFT详情的NFT列表
     * 
     * @param pageNum 页码
     * @param pageSize 数量
     * @param nftid nftid
     * @return
     */
    @RequestMapping("/getNftDetailsNFTList")
    public R getNftDetailsNFTList(
        @RequestParam(defaultValue = "1") @Min(value = 1, message = "页码最小为1") Integer pageNum,
        @RequestParam(defaultValue = "10") @Min(value = 1, message = "数量最小为1") Integer pageSize,
        @NotNull(message = "NFTId不能为空") Long nftid) {
        return nftService.getNftDetailsNFTList(pageNum, pageSize, nftid);
    }

    /**
     * 获取NFT详情v2
     * 
     * @param id
     * @return
     */
    @RequestMapping("/getNftDetailsv2")
    public R getNftDetailsv2(@NotNull(message = "id不能为空") Long id) {
        return nftService.getNftDetailsv2(id);
    }

    /**
     * 获取NFT列表
     * 
     * @param id
     * @param nftName
     * @return
     */
    @RequestMapping("/getNftList")
    public R getNftList(@NotNull(message = "id不能为空") Long id, String nftName) {
        return nftService.getNftList(id, nftName);
    }

    /**
     * 获取NFT列表
     * 
     * @param id
     * @param nftName
     * @return
     */
    @RequestMapping("/getNftListV2")
    public R getNftListV2(
        @RequestParam(defaultValue = "1") @Min(value = 1, message = "页码最小为1") Integer pageNum,
        @RequestParam(defaultValue = "10") @Min(value = 1, message = "数量最小为1") Integer pageSize,
        @NotNull(message = "id不能为空") Long id, String nftName) {
        return nftService.getNftListV2(pageNum, pageSize, id, nftName);
    }

    /**
     * 获取交易详情
     * 
     * @param id
     * @return
     */
    @RequestMapping("/getTransactionDetail")
    public R getTransactionDetail(@NotNull(message = "id不能为空") Long id) {
        return nftService.getTransactionDetail(id);
    }

    /**
     * 查询nft的交易记录
     * 
     * @param opbChainId
     * @param contractId
     * @param tokenId
     * @return
     */
    @RequestMapping("/getNftTransactions")
    public R getNftTransactions(@NotNull(message = "链框架id不能为空") Integer opbChainId,
        @NotNull(message = "合约id不能为空") Integer contractId, @NotNull(message = "tokenId不能为空") Integer tokenId,
        @NotBlank(message = "持有人链地址不能为空") String holderAddress, @NotNull(message = "类型不能为空") @Min(value = 0,
            message = "类型取值最小为0") @Max(value = 2, message = "类型取值最大为2") Integer type) {
        return nftTransferRecordService.getNftTransactions(opbChainId, contractId, tokenId, holderAddress,
            type);
    }

    /**
     * 查询链账户下所有nft的交易记录
     * 
     * @param chainAccountAddress
     * @param type
     * @param time
     * @return
     */
    @RequestMapping("/getAllNftTransactions")
    public R getAllNftTransactions(@NotNull(message = "链框架id不能为空") Integer opbChainId,
        @NotBlank(message = "链账户地址不能为空") String chainAccountAddress,
        @NotNull(message = "类型不能为空") @Min(value = 0, message = "类型取值最小为0") @Max(value = 2,
            message = "类型取值最大为2") Integer type,
        String time) {
        return nftTransferRecordService.getAllNftTransactions(opbChainId, chainAccountAddress, type, time);
    }

    /**
     * 自动批量生成nft
     * 
     * @param chainAccountAddress
     * @param nftCount
     * @return
     */
    @RequestMapping("/autoBatchCreateNft")
    public R autoBatchCreateNft(@NotBlank(message = "链账户地址不能为空") String chainAccountAddress,
        @NotNull(message = "nft数量不能为空") Integer nftCount) {
        return nftService.autoBatchCreateNftV2(chainAccountAddress, nftCount);
    }

    /**
     * 设置nft描述
     * 
     * @param id
     * @param describe
     * @return
     */
    @RequestMapping("/setNftDescribe")
    public R setNftDescribe(@NotNull(message = "id不能为空") Long id, String describe) {
        return nftService.setNftDescribe(id, describe);
    }

    /**
     * 预估交易NFT的能量花费
     * 
     * @param opbChainId
     * @param contractAddress
     * @param fromAddress
     * @param toAddress
     * @param tokenId
     * @return
     */
    @RequestMapping("/getTransferEstimateGas")
    public R getEthEstimateGas(@NotNull(message = "链框架id不能为空") Integer opbChainId,
        @NotBlank(message = "合约地址不能为空") String contractAddress,
        @NotBlank(message = "发送地址不能为空") String fromAddress, @NotBlank(message = "接收地址不能为空") String toAddress,
        @NotNull(message = "tokenId不能为空") Integer tokenId) {
        return nftService.getTransferEstimateGas(opbChainId, contractAddress, fromAddress, toAddress,
            tokenId);
    }

    /**
     * 获取NFT绑定状态
     * 
     * @param tokenId
     * @return
     */
    @RequestMapping("/getNFTBindStatus")
    public R getNFTBindStatus(@NotNull(message = "tokenId不能为空") Long tokenId) {
        return nftService.getNFTBindStatus(tokenId);
    }

    /**
     * 获取NFT绑定状态
     * 
     * @param tokenId
     * @return
     */
    @RequestMapping("/NFTUnbind")
    public R NFTUnbind(@NotNull(message = "tokenId不能为空") Long tokenId,
        @NotNull(message = "链账户不能为空") String chainAccountAddress) {
        return nftService.NFTUnbind(tokenId, chainAccountAddress);
    }

    /**
     * 保存交易NFT记录 同步灵戒AppNFT 转移信息
     * 
     * @param opbChainId
     * @param transactionHash
     * @return
     */
    @RequestMapping("/saveTransferRecord")
    public R saveTransferRecord(@NotNull(message = "链框架id不能为空") Integer opbChainId,
        @NotBlank(message = "交易hash不能为空") String transactionHash) {
        return nftService.saveTransferRecord(opbChainId, transactionHash);
    }

    /**
     * 保存销毁NFT记录
     * 
     * @param opbChainId
     * @param transactionHash
     * @return
     */
    @RequestMapping("/saveBurnRecord")
    public R saveBurnRecord(@NotNull(message = "链框架id不能为空") Integer opbChainId,
        @NotBlank(message = "交易hash不能为空") String transactionHash) {
        return nftService.saveBurnRecord(opbChainId, transactionHash);
    }

    /**
     * 查询用户领取nft的信息
     * 
     * @return
     */
    @RequestMapping("/queryGetNftInfo")
    public R queryGetNftInfo() {
        return nftGetRecordService.queryGetNftInfo();
    }

    /**
     * 查询用户领取nft 数量的信息
     * 
     * @return
     */
    @RequestMapping("/queryGetNftInfoV2")
    public R queryGetNftInfoV2() {
        return R.okData(nftGetRecordService.queryGetNftInfoV2());
    }

    /**
     * 查询nft记录
     * 
     * @return
     */
    @RequestMapping("/queryGetNftRecord")
    public R queryGetNftRecord() {
        return nftGetRecordService.queryGetNftRecord();
    }

    /**
     * 查询nft记录
     * 
     * @return
     */
    @RequestMapping("/queryGetNftRecordV2")
    public R queryGetNftRecordV2(
        @RequestParam(defaultValue = "1") @Min(value = 1, message = "页码最小为1") Integer pageNum,
        @RequestParam(defaultValue = "10") @Min(value = 1, message = "数量最小为1") Integer pageSize) {
        return nftGetRecordService.queryGetNftRecordV2(pageNum, pageSize);
    }

    /**
     * 获取链账户总资产
     * 
     * @param chainAccountAddress
     * @return
     */
    @RequestMapping("/getChainAccountAsset")
    public R getChainAccountAsset(String chainAccountAddress) {
        return nftService.getChainAccountAsset(chainAccountAddress);
    }

    /**
     * 同步NFT上链信息
     * 
     * @return
     */
    @RequestMapping("/syncNFTUpchainInfo")
    public R syncNFTUpchainInfo() {
        return nftService.syncNFTUpchainInfo();
    }

    /**
     * 从NFT getRecord 补偿
     * 
     * @param nftManualDisposeId nft手动补发id
     * @param plaftAddress 平台地址
     * @return
     */
    @RequestMapping("/compensationFromNftManualDisposeId")
    public R compensationFromNFTGetRecord(@NotNull Long nftManualDisposeId, @NotBlank String plaftAddress) {
        return nftService.compensationFromNFTGetRecord(nftManualDisposeId, plaftAddress);
    }

    /**
     * 单个NFT 校验同步 approve 信息
     * 
     * @param nftIds NFTId
     * @param transactionHash 交易hash
     * @return
     */
    @RequestMapping("/validApprove")
    public R approve(@RequestParam List<Integer> nftIds, @NotBlank String transactionHash) {
        return nftService.validApprove(nftIds, transactionHash);
    }

    /**
     * @return
     */
    @RequestMapping("/getTransactionReceipt")
    public R getTransactionReceipt(@NotBlank String transactionHash) {
        // 校验交易是否成功
        TransactionReceipt transactionReceipt = nftService.getTransactionReceipt(transactionHash);
        return R.okData(transactionReceipt);
    }

    /**
     * NFT续费流程后半段的NFT交易给平台地址
     *
     * @param domainRenewalNFTIds 域名续费NFT表id
     * @return
     */
    @RequestMapping("/transferToPlatformAddress")
    public R transferToPlatformAddress(
        @RequestParam(value = "domainRenewalNFTIds") List<Long> domainRenewalNFTIds) {
        return nftService.transferToPlatformAddress(domainRenewalNFTIds);
    }

    /**
     * NFT续费流程后半段退款，回收地址将NFT返还给用户
     *
     * @param domainRenewalNFTRefundIds 域名续费NFT退款表id
     * @return
     */
    @RequestMapping("/transferToUserAddress")
    public R transferToUserAddress(
        @RequestParam(value = "domainRenewalNFTRefundIds") List<Long> domainRenewalNFTRefundIds) {
        return nftService.transferToUserAddress(domainRenewalNFTRefundIds);
    }

    /**
     * 回收NFT
     * 
     * @param platformAccountAddress
     * @param contractAddress
     * @param NFTOwnerAddress
     * @param NFTRecycleAddress
     * @param tokenId
     * @return
     */
    @RequestMapping("/transferNFT")
    public R transferNFT(@NotBlank String platformAccountAddress, @NotBlank String contractAddress,
        @NotBlank String NFTOwnerAddress, @NotBlank String NFTRecycleAddress, @NotNull Integer tokenId) {
        TransactionReceipt transactionReceipt = nftOnChainService.transferNFT(null, platformAccountAddress,
            contractAddress, NFTOwnerAddress, NFTRecycleAddress, tokenId);
        return R.okData(transactionReceipt);
    }

    /**
     * 查询交易回执
     * 
     * @param transactionHash
     * @return
     */
    @RequestMapping("/queryTransactionReceipt")
    public R queryTransactionReceipt(@NotBlank String transactionHash) {
        return nftOnChainService.queryTransactionReceipt(transactionHash);
    }

    /**
     * 授权当前地址
     * 
     * @param privateKey 初始地址私钥
     * @param contractAddress 合约地址
     * @param approvalAddress 授权地址
     * @param approved true 授权 false撤销授权
     * @return
     */
    @RequestMapping("/setApprovalForAll")
    public R setApprovalForAll(@NotBlank String privateKey, @NotNull String contractAddress,
        @NotBlank String approvalAddress, @RequestParam(defaultValue = "true") Boolean approved) {
        TransactionReceipt transactionReceipt =
            nftOnChainService.setApprovalForAll(privateKey, contractAddress, approvalAddress, approved);
        return R.okData(transactionReceipt);
    }

    /**
     * 是否授权所有
     * 
     * @param ownerAddress 合约拥有者地址
     * @param contractAddress 合约地址
     * @param approvedAddress 授权地址
     * @return
     */
    @RequestMapping("/isApprovedForAll")
    public R isApprovedForAll(@NotBlank String ownerAddress, @NotBlank String contractAddress,
        @NotBlank String approvedAddress) {
        Boolean isApprovedForAll =
            nftOnChainService.isApprovedForAll(ownerAddress, contractAddress, approvedAddress);
        return R.okData(isApprovedForAll);
    }

    // ------ 链浏览器调用接口(浏览器服务内容调用)-----------
    // ------ 链浏览器调用接口(浏览器服务内容调用)-----------
    // ------ 链浏览器调用接口(浏览器服务内容调用)-----------

    /**
     * 查询链地址概览
     * 
     * @param address
     * @return {@link R }
     */
    @RequestMapping("/addressOverview")
    public R getAddressOverview(@NotBlank String address) {
        return nftService.getAddressOverviewService(address);
    }

    /**
     * 合约列表
     *
     * @param page
     * @param pageSize
     * @param address
     * @return {@link R }
     */
    @RequestMapping("/contractList")
    public R contractList(@RequestParam(defaultValue = "1") @Min(value = 1, message = "页码最小为1") Integer page,
        @RequestParam(defaultValue = "10") @Min(value = 1, message = "数量最小为1") Integer pageSize) {
        return nftService.getContractListService(page, pageSize);
    }

    /**
     * 合约详情
     *
     * @param id
     * @return {@link R }
     */
    @RequestMapping("/contractDetail")
    public R contractDetail(@NotNull(message = "合约id不能为空")Integer id) {
        return nftService.contractDetailService(id);
    }


    /**
     * NFT交易记录列表
     *
     * @param page
     * @param pageSize
     * @param address
     * @return {@link R }
     */
    @RequestMapping("/nftTrRecord")
    public R nftTrRecord(@RequestParam(defaultValue = "1") @Min(value = 1, message = "页码最小为1") Integer page,
        @RequestParam(defaultValue = "10") @Min(value = 1, message = "数量最小为1") Integer pageSize,
        String address, Integer tokenId) {
        return nftService.nftTrRecordService(page, pageSize, address,tokenId);
    }

    /**
     * nft详情
     *
     * @param tokenId
     * @return {@link R }
     */
    @RequestMapping("/nftDetail")
    public R nftDetail(@NotNull(message = "tokenId不能为空")Integer tokenId) {
        return nftService.nftDetailService(tokenId);
    }


    /**
     * NFT资产
     *
     * @param page
     * @param pageSize
     * @param address
     * @return {@link R }
     */
    @RequestMapping("/nftProperty")
    public R nftProperty(@RequestParam(defaultValue = "1") @Min(value = 1, message = "页码最小为1") Integer page,
        @RequestParam(defaultValue = "10") @Min(value = 1, message = "数量最小为1") Integer pageSize,String address) {
        return nftService.nftPropertyService(page, pageSize,address);
    }


    /**
     * nft地址持有列表
     *
     * @param page
     * @param pageSize
     * @return {@link R }
     */
    @RequestMapping("/nftHoldRecord")
    public R nftHoldRecord(@RequestParam(defaultValue = "1") @Min(value = 1, message = "页码最小为1") Integer page,
        @RequestParam(defaultValue = "10") @Min(value = 1, message = "数量最小为1") Integer pageSize) {
        return nftService.nftHoldRecordService(page, pageSize);
    }

}
