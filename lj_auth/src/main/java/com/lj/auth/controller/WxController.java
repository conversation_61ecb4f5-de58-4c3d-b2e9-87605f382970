package com.lj.auth.controller;

/**
 * <AUTHOR>
 * @describe
 */

import com.alibaba.fastjson.JSONObject;
import com.lj.auth.common.R;
import com.lj.auth.common.WxAuthParam;
import com.lj.auth.util.RedisUtils;
import com.lj.auth.util.WxPublicAccountUtil;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;
import javax.validation.Valid;


@Validated
@RestController
@RequestMapping("/wx")
public class WxController {

    // 避免频繁调用微信token，此处作为调试临时缓存一下
    static JSONObject wxAuthVoSession = null;

    @Resource
    private RedisUtils redisUtils;

    /**
     * 发送公众号url验证信息
     * @param wxAuthParam
     * @return
     */
    @RequestMapping("/getWxPublicAccountAuth")
    public R getWxAuth(@Valid @RequestBody WxAuthParam wxAuthParam) {
        String url = wxAuthParam.getUrl();
        String key = "getWxPublicAccountAuth:" + url;
        JSONObject wxAuthJSON = (JSONObject) redisUtils.get(key);
        if (wxAuthJSON == null) {
            wxAuthJSON = WxPublicAccountUtil.getAuthInfo(url);
            if (wxAuthJSON != null) {
                redisUtils.set(key, wxAuthJSON, 60 * 60);
            }else {
                wxAuthJSON=new JSONObject();
            }
        }
        return R.okData(wxAuthJSON);
    }


}


