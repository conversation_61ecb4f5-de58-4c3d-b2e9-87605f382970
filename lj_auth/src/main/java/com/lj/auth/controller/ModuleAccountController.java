package com.lj.auth.controller;

import cn.hutool.core.util.ObjectUtil;
import com.lj.auth.annotation.LogRecord;
import com.lj.auth.annotation.RequestLimit;
import com.lj.auth.common.R;
import com.lj.auth.domain.Account;
import com.lj.auth.service.AccountService;
import com.lj.auth.service.ModuleAccountService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotBlank;

/**
 * @describe：内部调用接口（用于依赖Account逻辑处理）
 * @author: cfj
 * @date: 2025/04/10
 */

@Slf4j
@Validated
@RestController
@RequestMapping(value = "/moduleAccount")
public class ModuleAccountController {

    @Resource
    private ModuleAccountService moduleAccountService;

    @Resource
    private AccountService accountService;


    /**
     * 验证码登录
     *
     * @param account 手机号
     * @param varificationCode 验证码
     * @return {@link R }
     */
    @LogRecord(methodName = "验证码登录")
    @RequestMapping("/loginByCode")
    @RequestLimit
    public R loginByCode(HttpServletRequest request, @NotBlank(message = "账号不能为空") String account,
                         @NotBlank(message = "验证码不能为空") String varificationCode) {
        return moduleAccountService.loginByCodeWithModule(request, account, varificationCode);
    }


    /**
     * 密码登录
     *
     * @param request
     * @param account 账号
     * @param password 密码
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/08
     */
    @RequestLimit
    @LogRecord(methodName = "密码登录")
    @RequestMapping("/loginByPassword")
    public R loginByPassword(HttpServletRequest request, @NotBlank(message = "账号不能为空") String account,
                             @NotBlank(message = "密码不能为空") String password) {
        return moduleAccountService.loginByPasswordWithModule(request, account, password);
    }


    /**
     * 获取用户信息内部调用
     * @return
     */
    @RequestMapping("/getInfoByUUID")
    public R getInfoVoByUUID(@NotBlank String accountUuid) {
        return R.okData(moduleAccountService.getInfoVoByUUIDWithModule(accountUuid));
    }


    /**
     * 通过did查询用户信息
     * @param didSymbol
     * @return
     */
    @RequestMapping("/queryByDid")
    public R queryByDid(@NotBlank(message = "did不能为空") String didSymbol) {
        return R.okData(moduleAccountService.queryByDidWithModule(didSymbol));
    }


    /**
     * 通过uuid查询用户信息
     * @param accountUuid
     * @return
     */
    @RequestMapping("/queryByUUID")
    public R queryByUUID(@NotBlank(message = "uuid不能为空") String accountUuid) {
        return R.okData(moduleAccountService.queryByUUIDWithModule(accountUuid));
    }

    /**
     * 通过手机号查询用户信息
     * @param phoneNumber
     * @return
     */
    @RequestMapping("/queryByPhone")
    public R queryByPhone(@NotBlank(message = "手机号不能为空") String phoneNumber) {
        return R.okData(moduleAccountService.queryByPhoneWithModule(phoneNumber));
    }


    /**
     * 设置图像
     *
     * @param portrait 图片
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/27
     */
    @LogRecord(methodName = "设置用户图像")
    @RequestMapping("/setPortrait")
    public R setPortrait(@NotBlank String accountUuid,String portrait, Long nftId, Integer type) {
        R result = moduleAccountService.setPortraitWithModule(accountUuid,portrait, nftId, type);
        Integer code = (Integer)result.get("code");
        if (ObjectUtil.equals(200, code)) {
            // 异步同步im信息
            Account userInfo = moduleAccountService.queryByUUIDWithModule(accountUuid);
            accountService.syncImInfo(userInfo);
        }

        return result;
    }


    /**
     * 设置昵称
     * @param accountUuid
     * @param nickName
     * @param domainNickName
     * @param type
     * @return
     */
    @RequestMapping("/setName")
    public R setNickName( @NotBlank String accountUuid, String nickName, String domainNickName, Integer type) {
        R result = moduleAccountService.setNickNameWithModule(accountUuid, nickName, domainNickName, type);
        Integer code = (Integer)result.get("code");
        if (ObjectUtil.equals(200, code)) {
            Account userInfo = accountService.getUserInfo();
            // 异步同步im信息
            accountService.syncImInfo(userInfo);
        }
        return result;
    }


    /**
     * 修改密码
     *
     * @param account 手机号
     * @param varificationCode 验证码
     * @param password 密码
     * @return {@link R }
     */
    @LogRecord(methodName = "修改密码")
    @RequestMapping("/setPassword")
    public R setPassword(@NotBlank(message = "uuid不能为空") String accountUuid,
                         @NotBlank(message = "账号不能为空") String account,
                         @NotBlank(message = "验证码不能为空") String varificationCode,
                         @NotBlank(message = "密码不能为空") String password) {
        return moduleAccountService.setPasswordWithModule(accountUuid,account, varificationCode, password);
    }


    /**
     * 修改手机号
     *
     * @param account 手机号
     * @param varificationCode 验证码
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/09/05
     */
    @LogRecord(methodName = "修改手机号")
    @RequestMapping("/setPhoneNumber")
    public R setPhoneNumber(@NotBlank(message = "uuid不能为空") String accountUuid,
                            @NotBlank(message = "账号不能为空") String account,
                            @NotBlank(message = "验证码不能为空") String varificationCode) {
        return moduleAccountService.setPhoneNumberWithModule(accountUuid,account, varificationCode);
    }


    /**
     * 设置支付密码
     *
     * @param account 手机号
     * @param varificationCode 验证码
     * @param payPassword 支付密码
     * @return {@link R }
     */
    @LogRecord(methodName = "设置支付密码")
    @RequestMapping("/setPayPassword")
    public R setPayPassword(@NotBlank(message = "uuid不能为空") String accountUuid,
                            @NotBlank(message = "手机号不能为空") String account,
                            @NotBlank(message = "验证码不能为空") String varificationCode,
                            @NotBlank(message = "支付密码不能为空") String payPassword) {
        return moduleAccountService.setPayPasswordWithModule(accountUuid,account, varificationCode, payPassword);
    }


    /**
     * 验证码校验(旧手机号)
     *
     * @param account
     * @param varificationCode
     * @return {@link R }
     */
    @RequestMapping("/compareVerificationCodes")
    public R compareVerificationCodes(@NotBlank(message = "uuid不能为空") String accountUuid,
                                      @NotBlank(message = "账号不能为空") String account,
                                      @NotBlank(message = "验证码不能为空") String varificationCode) {
         moduleAccountService.compareVerificationCodesWithModule(accountUuid,account, varificationCode);
        return R.ok();
    }


    /**
     * 同步用户登录记录（内部登录信息同步使用）
     *
     * @param accountUuid
     * @param ip
     * @param application
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/04/09
     */
    @LogRecord(methodName = "同步用户登录信息")
    @RequestMapping("/logInMessageRecord")
    public R logInMessageRecord(@NotBlank(message = "uuid不能为空") String accountUuid,
                                @NotBlank(message = "ip地址不能为空") String ip,String application) {
        return R.okData(moduleAccountService.logInMessageRecordService(accountUuid, ip,application));
    }


}
