package com.lj.auth.controller;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.lj.auth.common.R;
import com.lj.auth.domain.DownloadPage;
import com.lj.auth.service.DownloadPageService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;



import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Description
 * @date 2024/1/22 17:57
 */
@Slf4j
@Validated
@RestController
@RequestMapping("downloadPage")
public class DownloadPageController {
    @Resource
    DownloadPageService downloadPageService;

    /**
     * 获取下载页
     *
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/25
     */
    @PostMapping("get")
    public R get() {
        DownloadPage downloadPage = downloadPageService.get();
        return <PERSON><PERSON>okData(downloadPage);
    }
}
