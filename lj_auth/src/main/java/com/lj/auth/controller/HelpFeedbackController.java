package com.lj.auth.controller;

import javax.annotation.Resource;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;

import com.lj.auth.annotation.LogRecord;
import com.lj.auth.common.R;
import com.lj.auth.service.FeedbackService;
import com.lj.auth.service.HelpService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import lombok.extern.slf4j.Slf4j;

/**
 * 描述： 帮助与反馈
 * <p>
 * <p>
 * 创建人: CFJ 创建时间: 2024/03/29
 */
@Slf4j
@Validated
@RestController
@RequestMapping(value = "/help")
public class HelpFeedbackController {
    @Resource
    private HelpService helpService;
    @Resource
    private FeedbackService feedbackService;

    /**
     * 获取帮助中心列表
     *
     * @param page     页
     * @param pageSize 页大小
     * @param type
     * @param keyword  关键字
     * @return {@link R}<AUTHOR>
     * @date 2025/01/21
     */
    @RequestMapping("/search")
    public R getHelpList(@RequestParam(defaultValue = "1",name = "page") @Min(value = 1, message = "页码最小为1") int page,
        @RequestParam(defaultValue = "10",name = "pageSize") @Min(value = 1, message = "数量最小为1") int pageSize,
        @RequestParam(defaultValue = "1") Integer type, @RequestParam("keyword") String keyword) {
        return helpService.getHelpListService(page, pageSize, keyword,type);
    }

    /**
     * 新增反馈
     * 
     * @param title 标题
     * @param content 内容
     * @param images 图片
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/29
     */
    @RequestMapping("/add")
    @LogRecord(methodName = "新增反馈")
    public R addUserFeedback(@NotBlank(message = "反馈标题不能为空") String title,
        @NotBlank(message = "反馈内容不能为空") String content, String images) {
        return feedbackService.addUserFeedbackService(title, content, images);
    }

    /**
     * 获取反馈内容列表
     *
     * @param page 页
     * @param pageSize 页码
     * @param type 1-已回复 0-未回复 2全-部
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/29
     */
    @RequestMapping("/feedBackSearch")
    public R getUserFeedbackList(
        @RequestParam(defaultValue = "1") @Min(value = 1, message = "页码最小为1") int page,
        @RequestParam(defaultValue = "10") @Min(value = 1, message = "数量最小为1") int pageSize, Integer type) {
        return feedbackService.getUserFeedbackListService(page, pageSize, type);
    }

}
