package com.lj.auth.controller;

import java.math.BigDecimal;
import java.util.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;

//import com.lj.eventServer.event.MyTestEventPublisher;
import com.alibaba.fastjson2.JSON;
import com.lj.auth.mapper.AccountMergeAssetsMapper;
import org.apache.commons.collections4.ListUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.lj.auth.common.R;
import com.lj.auth.domain.vo.order.OrderButton;
import com.lj.auth.domain.vo.order.OrderViewJson;
import com.lj.auth.domain.vo.orderDetail.*;
import com.lj.auth.exception.ResponseEnum;
import com.lj.auth.exception.ServiceException;
import com.lj.auth.task.CheckTransactionState;
import com.lj.auth.util.IpfsUtil;
import com.lj.auth.util.JsonUtils;

import cn.dev33.satoken.stp.StpUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 描述：测试接口 创建人: CFJ 创建时间: 2024/03/26
 */
@RestController
@Validated
@RequestMapping(value = "/test")
@Slf4j
public class TestController {
    @Resource
    private CheckTransactionState checkTransactionState;
//    @Resource
//    private MyTestEventPublisher myTestEventPublisher;


    @Resource
    private AccountMergeAssetsMapper accountMergeAssetsMapper;

    @RequestMapping(value = "/a")
    public String aa() {
        return "成功了";
    }

    @RequestMapping(value = "/b")
    public R bb() {
        return R.error(ResponseEnum.OK);
    }

    @RequestMapping(value = "/c")
    public R cc() {
        throw new ServiceException(ResponseEnum.ERROR);
    }

    @RequestMapping(value = "/d")
    public R dd(int a, @NotBlank(message = "姓名不能为空") String name) {
        return R.okData(name + "===" + a);
    }

    @PostMapping(value = "/e")
    public R ee(Integer a, @NotBlank(message = "姓名不能为空") String name) {
        return R.okData(name + "===" + a);
    }

    @PostMapping(value = "/f")
    public R ff(@RequestBody JSONObject data) {
        log.error(data.getString("phoneNumber"));
        log.error(data.getString("nickName"));
        log.error(data.getString("did"));
        return R.ok();
    }

    @PostMapping(value = "/pathCid")
    public R pathCid( @NotBlank String contractAddress) {
        String pathCid = IpfsUtil.getJsonPathCid(contractAddress);
        return  R.okData(pathCid);
    }

    @PostMapping(value = "/token")
    public R pathCid11(  String uuid) {
        StpUtil.login(uuid);
        return  R.okData(StpUtil.getTokenValue());
    }

    @PostMapping(value = "/sp")
    public R pathCid111(String time) {
        checkTransactionState.checkSignState();
        return R.ok();
    }

    @PostMapping(value = "/event")
    public R event(String time) {
//        myTestEventPublisher.pushListener("中秋节前期测试");
//        myTestEventPublisher.pushListener01("中秋节前期测试111111");
        return R.ok();
    }


//    /**
//     * 修改合并账号的operate_uuid
//     * @return {@link R }
//     * <AUTHOR>
//     * @date 2025/02/24
//     */
//    @PostMapping(value = "/merge")
//    public R merge(String foperateUUID, String toUUID) {
//        Long  startTime =System.currentTimeMillis();
//        final List<String> YMOperatetableNames = List.of("ym_voucher_accredit", "ym_record",
//            "ym_recharge_withdraw_record", "ym_recharge_supplement", "ym_recharge_flow", "ym_recharge_assets",
//            "ym_order_domain", "ym_order_bsn_info", "ym_order", "ym_operate_apply_order", "ym_operate_apply",
//            "ym_invoice_message", "ym_invoice_account", "ym_domain_intention", "ym_domain_account",
//            "ym_addressee_message", "ym_account_info_voucher", "ym_account_did_history",
//            "ym_account_did_domain", "ym_account_did_device", "ym_account_did", "ym_account_card",
//            "ym_account_accredit", "mix_order", "lj_transfer_fix_domain_order", "lj_phone_recharge_record");
//        // 修改所有DID uuid相关联的operate_uuid 为手机号的operate_uuid
//        for (String tableName : YMOperatetableNames) {
//            accountMergeAssetsMapper.updateAccountUuidOperateUuid(tableName, foperateUUID, toUUID);
//        }
//        Long  endTime = System.currentTimeMillis();
//        System.out.println("耗时："+(endTime-startTime));
//        System.out.println(foperateUUID);
//        System.out.println(toUUID);
//        return R.ok();
//    }




    public static void main(String[] args) {
//        for (int i = 0; i < 10; i++) {
//            String s = generateRandomChinese(5);
//            System.out.println(s);
//        }
        String aa="https://dynamics-source-**********.cos.ap-guangzhou.myqcloud.com/images/YLaadbc9e4fa-android-*************-image_1737104716367.jpg";
        System.out.println(aa.length());
    }

    /**
     * 根据指定长度生成随机汉字字符串
     *
     * @param length 需要生成的汉字数量
     * @return 返回由随机汉字组成的字符串
     */
    public static String generateRandomChinese(int length) {
        // 预定义的一些优美好听的汉字，可以根据需要进行扩展
      String[] prettyChineseCharacters = new String[]{
                "风", "云", "雪", "月", "星", "梦", "幻", "影", "夜", "光",
                "天", "地", "海", "山", "水", "花", "木", "琴", "书", "画",
                "泊", "安", "蝉", "玢", "辰", "澄", "柒", "朗", "漫", "宓",
                "诺", "谦", "冉", "荣", "瑞", "芮", "润", "娴", "芯", "溪",
                "郁", "渊", "岳", "越", "昀", "泽", "昭", "芷", "灿", "慈",
                "纹", "婕", "正", "智", "橦", "恩", "嵩", "琳", "灵", "宝",
                "同", "淳", "柏", "萧", "凌", "新", "悦", "子", "渝", "妗",
                "森", "天", "格", "仑", "玄", "宣", "钰", "裕", "毓", "勇",
                "凡", "彧", "姗", "添", "仪", "唯", "瞳", "俊", "昕", "汝",
        };
        Random random = new Random();
        StringBuilder usernameBuilder = new StringBuilder();
        for (int i = 0; i < 3; i++) {
            // 随机选取一个汉字并添加到用户名构建器中
            int index = random.nextInt(prettyChineseCharacters.length);
            usernameBuilder.append(prettyChineseCharacters[index]);
        }
        String generatedUsername = usernameBuilder.toString();
        return generatedUsername;
    }

}
