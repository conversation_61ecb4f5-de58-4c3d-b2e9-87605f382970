package com.lj.auth.controller;

import com.lj.auth.common.R;
import com.lj.auth.service.VoucherAccreditService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @date 2024/4/26 16:00
 */
@Validated
@RestController
@RequestMapping("accountInfoVoucher")
public class VoucherAccreditController {
    @Resource
    VoucherAccreditService voucherAccreditService;

    /**
     * 凭证扫码签到 2024华中Web3.0大会-暨界外科技新品发布会 武汉界外科技有限公司
     * 
     * @param vcId 凭证id
     */
    @PostMapping("voucherSignInLj")
    public R voucherSignInLj(@NotBlank(message = "凭证标识非空") String vcId) {
        voucherAccreditService.voucherSignIn(vcId);
        return R.ok();
    }

    /**
     * 扫码总次数 2024华中Web3.0大会-暨界外科技新品发布会
     * 
     * @return
     */
    @PostMapping("getCountLj")
    public R getCountLj() {
        Integer count = voucherAccreditService.getCountLj();
        return R.okData(count);
    }

    /**
     * 重庆签到-2024.5.22 标题：2024全网分布式域名及实名DID大会 单位：重庆市永川区派棠商业管理有限公司
     * 
     * @param vcId
     * @return
     */
    @PostMapping("voucherSignInCq")
    public R voucherSignInCq(@NotBlank(message = "凭证标识非空") String vcId) {
        voucherAccreditService.voucherSignInCq(vcId);
        return R.ok();
    }

    @PostMapping("getCountCq")
    public R getCountCq() {
        Integer count = voucherAccreditService.getCountCq();
        return R.okData(count);
    }

    /**
     * 重庆开州签到-2024.5.24 标题：全网分布式域名及实名DID大会(重庆·开州) 单位：重庆林帮利科技有限公司
     * 
     * @param vcId
     * @return
     */
    @PostMapping("voucherSignInCqkz")
    public R voucherSignInCqkz(@NotBlank(message = "凭证标识非空") String vcId) {
        voucherAccreditService.voucherSignInCqkz(vcId);
        return R.ok();
    }

    @PostMapping("getCountCqkz")
    public R getCountCqkz() {
        Integer count = voucherAccreditService.getCountCqkz();
        return R.okData(count);
    }

    /**
     * 四川-2024.6.19 标题：BSN全网分布式域名及可信数字身份DID四川启动大会 单位：晟世派商（四川）科技有限公司
     * 
     * @param vcId
     * @return
     */
    @PostMapping("voucherSignInSsps")
    public R voucherSignInSsps(@NotBlank(message = "凭证标识非空") String vcId) {
        voucherAccreditService.voucherSignInSsps(vcId);
        return R.ok();
    }

    @PostMapping("getCountSsps")
    public R getCountSsps() {
        Integer count = voucherAccreditService.getCountSsps();
        return R.okData(count);
    }

    /**
     * 添加凭证授权记录
     *
     * @param didSymbol did
     * @param logo log
     * @param title 标题
     * @param orgName 组织
     * @param type 类型
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/06/27
     */
    @PostMapping("addAuthorization")
    public R addAuthorization(@RequestParam("didSymbol") @NotBlank(message = "did不能为空") String didSymbol,
        @RequestParam("logo") String logo, @RequestParam("title") String title,
        @RequestParam("orgName") String orgName, @RequestParam("type") Integer type,
        @RequestParam("code") String code, @RequestParam("failReason") String failReason) {
        return voucherAccreditService.addAuthorizationService(didSymbol, logo, title, orgName, type, code,
            failReason);
    }

    /**
     * 授权记录
     * 
     * @param type 0-全部 1-DID 2-个人身份信息凭证
     * @param page 页码
     * @param pageSize 数量
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/07/03
     */
    @PostMapping("authorizationRecords")
    public R authorizationRecords(@NotNull(message = "type不能为空") Integer type,
        @RequestParam(defaultValue = "1") @Min(value = 1, message = "页码最小为1") int page,
        @RequestParam(defaultValue = "10") @Min(value = 1, message = "数量最小为1") int pageSize) {
        return voucherAccreditService.authorizationRecordsService(type, page, pageSize);
    }

    /**
     * 移除授权记录
     * 
     * @param ids id集合
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/07/03
     */
    @PostMapping("isDelete")
    public R isDelete(@RequestParam("ids") @Size(min = 1, message = "至少要有一个元素") List<Integer> ids) {
        return voucherAccreditService.isDeleteService(ids);
    }

    /**
     * DID信息补全（内部服务使用）
     *
     * @param didSymbol
     * @param message
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/03/25
     */
    @PostMapping("informationCompletion")
    public R informationCompletion(@RequestParam("didSymbol") @NotBlank(message = "did不能为空") String didSymbol,
        @RequestParam("message") String message) {
        return voucherAccreditService.informationCompletionService(didSymbol, message);
    }

}
