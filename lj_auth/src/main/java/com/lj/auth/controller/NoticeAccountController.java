package com.lj.auth.controller;

import com.lj.auth.common.R;
import com.lj.auth.service.NoticeAccountService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description 消息通知
 * @date 2025/3/19 16:09
 */
@Validated
@RestController
@RequestMapping("/noticeAccount")
public class NoticeAccountController {
    @Resource
    private NoticeAccountService noticeAccountService;
    
    /**
     * DID信息补全通知
     * @param accountUuid
     * @param versionId 版本id
     * @return
     */
    @PostMapping("didCompletionNotify")
    public R didCompletionNotify(String accountUuid, Integer versionId) {
        noticeAccountService.noticeAccountService(accountUuid, versionId);
        return R.ok();
    }
}
