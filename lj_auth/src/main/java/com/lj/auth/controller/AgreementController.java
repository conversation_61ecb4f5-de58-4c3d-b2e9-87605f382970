package com.lj.auth.controller;

import com.lj.auth.common.R;
import com.lj.auth.domain.Param.*;
import com.lj.auth.service.AgreementService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;

/**
 */
@Slf4j
@Validated
@RestController
@RequestMapping(value = "/agreement")
public class AgreementController {

    @Resource
    private AgreementService agreementNotice;


    /**
     * 校验协议通知
     * @param request
     * @return
     */
    @RequestMapping("/validVersion")
    public R validVersion( HttpServletRequest request) {
        return R.okData(agreementNotice.validVersion(request));
    }


    /**
     * 协议已读确认
     * @param agreementConfirmParam
     * @return
     */
    @RequestMapping("/confirm")
    public R agreementConfirm( @RequestBody @Valid AgreementConfirmParam agreementConfirmParam) {
        return agreementNotice.agreementConfirm(agreementConfirmParam);
    }


    /**
     * 查询用户的协议已读记录
     * @param accountAgreementReadedRecordParam
     * @return
     */
    @RequestMapping("/accountVersionRecord")
    public R accountVersionRecord(@RequestBody @Valid  AccountAgreementReadedRecordParam accountAgreementReadedRecordParam) {
        return R.okData(agreementNotice.accountVersionRecord(accountAgreementReadedRecordParam));
    }


    /**
     * 分页查询协议信息
     * @param accountAgreementReadedRecordParam
     * @return
     */
    @RequestMapping("/versionList")
    public R versionList(@RequestBody @Valid AgreementVersionListParam accountAgreementReadedRecordParam) {
        return R.okData(agreementNotice.versionList(accountAgreementReadedRecordParam));
    }


    /**
     * 后台添加或修改协议版本信息
     * @param editAgreementParam
     * @return
     */
    @RequestMapping("/editAgreement")
    public R editAgreement(@RequestBody @Valid EditAgreementParam editAgreementParam) {
        return agreementNotice.editAgreement(editAgreementParam);
    }



    /**
     * 同步版本阅读记录
     * @param accountUUID
     * @return
     */
    @RequestMapping("/syncRecord")
    public R syncRecord(String accountUUID) {
         agreementNotice.syncRecord(accountUUID);
        return R.ok();
    }

    /**
     * 同步版本阅读记录
     * @param accountUUID
     * @return
     */
    @RequestMapping("/syncAccountAgreementRecord")
    public R syncRecordAccount(@NotBlank String accountUUID) {
         agreementNotice.syncAccountAgreementRecord(accountUUID);
        return R.ok();
    }




}
