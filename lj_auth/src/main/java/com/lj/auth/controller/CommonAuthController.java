package com.lj.auth.controller;

import com.lj.auth.annotation.RequestLimit;
import com.lj.auth.common.R;
import com.lj.auth.service.CommonAuthService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Slf4j
@Validated
@RestController
@RequestMapping(value = "/common")
public class CommonAuthController {
    @Resource
    private CommonAuthService commonAuthService;

    /**
     * 获取用户基础信息
     *
     * @param didSymbol
     * @return {@link R }
     */
    @PostMapping("/getBaseUserInfo")
    public R getBaseUserInfo(@NotBlank(message = "did不能为空") String didSymbol) {
        return commonAuthService.getBaseUserInfoService(didSymbol);
    }

    /**
     * 批量获取用户基础信息
     *
     * @param didSymbols
     * @return {@link R }
     */
    @PostMapping("/getBatchBaseUserInfoAll")
    public R getBatchBaseUserInfoAll(
        @RequestParam("didSymbols") @NotEmpty(message = "didSymbols集合不能为空") List<String> didSymbols) {
        return commonAuthService.getBatchBaseUserInfoAllService(didSymbols);
    }

    /**
     * 获取用户基础信息
     *
     * @param didSymbols
     * @param type 1-昵称 2-头像
     * @return {@link R }
     */
    @PostMapping("/getBatchBaseUserInfo")
    public R getBatchBaseUserInfo(
        @RequestParam("didSymbols") @NotEmpty(message = "didSymbols集合不能为空") List<String> didSymbols,
        @NotNull(message = "类型不能为空") Integer type) {
        return commonAuthService.getBatchBaseUserInfoService(didSymbols, type);
    }

    /**
     * 请求短信验证码
     *
     * @param account          手机号码
     * @param verificationCode 验证码
     * @return {@link R }
     */
    @RequestMapping("/regCaptcha")
    @RequestLimit(second = 60, maxCount = 1)
    public R regCaptcha(@NotBlank(message = "账号不能为空") String account,
        @NotBlank(message = "验证码不能为空") String verificationCode) {
        return commonAuthService.sentCaptcha(account, verificationCode);
    }
}
