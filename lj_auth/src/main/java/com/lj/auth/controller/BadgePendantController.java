package com.lj.auth.controller;

import com.alibaba.fastjson2.JSONObject;
import com.lj.auth.common.R;
import com.lj.auth.service.BadgeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 徽章挂件相关接口
 *
 * <AUTHOR>
 * @date 2025/05/15
 */
@Slf4j
@Validated
@RestController
@RequestMapping(value = "/badge")
public class BadgePendantController {
    @Resource
    private BadgeService badgeService;

    /**
     * 获取个人挂件列表
     *
     * @return {@link String }
     */
    @RequestMapping("/pendantList")
    public R getBadgePendant(HttpServletRequest request, @RequestBody JSONObject paramJson) {
        return badgeService.getBadgePendantService();
    }

    /**
     * 设置个人挂件
     *
     * @param request
     * @param paramJson
     * @return {@link R }
     */
    @RequestMapping("/setPendant")
    public R setPendant(HttpServletRequest request, @RequestBody JSONObject paramJson) {
        return badgeService.setPendantService(request,paramJson);
    }

    /**
     * 不佩戴挂件
     *
     * @param request
     * @param paramJson
     * @return {@link R }
     */
    @RequestMapping("/cancelPendant")
    public R cancelPendant(HttpServletRequest request, @RequestBody JSONObject paramJson) {
        return badgeService.cancelPendantService(request,paramJson);
    }

    /**
     * 获取个人最新徽章（最多三个）
     *
     * @param request
     * @param paramJson
     * @return {@link R }
     */
    @RequestMapping("/latestBadge")
    public R latestBadge(HttpServletRequest request, @RequestBody JSONObject paramJson) {
        return badgeService.latestBadgeService(request,paramJson);
    }


    /**
     * 获取徽章列表
     *
     * @param request
     * @param paramJson
     * @return {@link R }
     */
    @RequestMapping("/list")
    public R list(HttpServletRequest request, @RequestBody JSONObject paramJson) {
        return badgeService.listService(request,paramJson);
    }

    /**
     * 设置徽章
     *
     * @param request
     * @param paramJson
     * @return {@link R }
     */
    @RequestMapping("/set")
    public R set(HttpServletRequest request, @RequestBody JSONObject paramJson) {
        return badgeService.setService(request,paramJson);
    }

    /**
     * 取消佩戴徽章
     *
     * @param request
     * @param paramJson
     * @return {@link R }
     */
    @RequestMapping("/cancel")
    public R cancel(HttpServletRequest request, @RequestBody JSONObject paramJson) {
        return badgeService.cancelService(request,paramJson);
    }


    /**
     * 刷新挂件
     *
     * @param request
     * @param paramJson
     * @return {@link R }
     */
    @RequestMapping("/refreshPendant")
    public R refreshPendant(HttpServletRequest request, @RequestBody JSONObject paramJson) {
        return badgeService.refreshPendantService(request,paramJson);
    }

}
