package com.lj.auth.controller;
import com.lj.auth.common.R;
import com.lj.auth.domain.NicknameLibrary;
import com.lj.auth.mapper.NicknameLibraryMapper;
import lombok.extern.slf4j.Slf4j;
import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
import net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;

@RestController
@Validated
@RequestMapping(value = "/nickName")
@Slf4j
public class NickName {
    // 常用汉字列表
    public static String[] commonHanzi =
            {
                    "风", "云", "雪", "月", "星", "梦", "幻", "影", "夜", "光",
                    "天", "地", "海", "山", "水", "花", "木", "琴", "书", "画",
                    "泊", "安", "蝉", "玢", "辰", "澄", "柒", "朗", "漫", "宓",
                    "诺", "谦", "冉", "荣", "瑞", "芮", "润", "娴", "芯", "溪",
                    "郁", "渊", "岳", "越", "昀", "泽", "昭", "芷", "灿", "慈",
                    "纹", "婕", "正", "智", "橦", "恩", "嵩", "琳", "灵", "宝",
                    "同", "淳", "柏", "萧", "凌", "新", "悦", "子", "渝", "妗",
                    "森", "天", "格", "仑", "玄", "宣", "钰", "裕", "毓", "勇",
                    "凡", "彧", "姗", "添", "仪", "唯", "瞳", "俊", "昕", "汝",
            };

    @Resource
    private NicknameLibraryMapper nicknameLibraryMapper;

    @RequestMapping("/save")
    public R batchGenerateNicknames(int count, int length) {
        saveNickname(count, length);
        return R.ok();
    }

    public void saveNickname(int count, int length) {
        for (int i = 0; i < count; i++) {
            // 生成指定长度的网名
            String name = generateChineseName(length); // Example: generate names of length 3
            NicknameLibrary nicknameLibrary = new NicknameLibrary();
            nicknameLibrary.setNickName(name);
            try {
                nicknameLibraryMapper.insert(nicknameLibrary);
            } catch (Exception e) {
                System.out.println("昵称重复:" + name);
            }
        }
    }

    public static Map<String, List<String>> classifyHanziByTone(String[] hanziArray) {
        Map<String, List<String>> hanziDict = new HashMap<>();
        hanziDict.put("平", new ArrayList<>());
        hanziDict.put("仄", new ArrayList<>());
        HanyuPinyinOutputFormat format = new HanyuPinyinOutputFormat();
        format.setToneType(HanyuPinyinToneType.WITH_TONE_NUMBER);
        for (String hanzi : hanziArray) {
            try {
                String[] pinyinArray = PinyinHelper.toHanyuPinyinStringArray(hanzi.charAt(0), format);
                if (pinyinArray != null && pinyinArray.length > 0) {
                    String pinyin = pinyinArray[0];
                    char toneChar = pinyin.charAt(pinyin.length() - 1);

                    if (toneChar == '1' || toneChar == '2') {
                        hanziDict.get("平").add(hanzi);
                    } else if (toneChar == '3' || toneChar == '4') {
                        hanziDict.get("仄").add(hanzi);
                    }
                }
            } catch (BadHanyuPinyinOutputFormatCombination e) {
                e.printStackTrace();
            }
        }
        return hanziDict;
    }

    public static String generateChineseName(int length) {

        // 获取平仄字库
        Map<String, List<String>> hanziDict = classifyHanziByTone(commonHanzi);

        Random random = new Random();
        String[][] patterns = {{"平", "仄", "平"}, {"平", "平", "仄"}, {"仄", "平", "平"}, {"平", "平", "仄", "仄"},
            {"平", "仄", "平", "平"}, {"平", "仄", "仄", "平"}, {"仄", "仄", "平", "平"}, {"平", "仄", "平", "平"},
            {"平", "平", "平", "仄", "仄"}, {"仄", "仄", "平", "平", "仄"}, {"平", "平", "仄", "仄", "平"},
            {"仄", "仄", "平", "平", "仄"}, {"仄", "仄", "平", "平", "仄", "仄"}, {"平", "平", "仄", "仄", "平", "平"},
            {"平", "仄", "平", "平", "仄", "仄"}, {"仄", "仄", "平", "平", "仄", "平"}};
        for (String[] pattern : patterns) {
            if (pattern.length != length) {
                continue; // Skip patterns that do not match the desired length
            }
            StringBuilder name = new StringBuilder();
            for (String tone : pattern) {
                List<String> hanziList = hanziDict.get(tone);
                int index = random.nextInt(hanziList.size());
                name.append(hanziList.get(index));
            }
            return name.toString();
        }
        return "无法生成符合要求的网名";
    }
}