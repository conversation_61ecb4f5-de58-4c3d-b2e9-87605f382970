package com.lj.auth.controller;

import com.lj.auth.common.R;
import com.lj.auth.domain.Version;
import com.lj.auth.service.VersionService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 描述： APP版本信息 创建人: CFJ 创建时间: 2024/03/27
 */
@Validated
@RestController
@RequestMapping("appVersion")
public class AppVersionController {

    @Resource
    private VersionService versionService;

    /**
     * 获取APP版本信息
     *
     * @param platform
     * @param version    版本
     * @param systemType 系统类型 1-安卓 2-IOS
     * @param build      构建次数
     * @param isShop   1-商店0-非商店（手动）
     * @return {@link R }
     */
    @RequestMapping("latestVersion")
    public R latestVersion(String platform,@NotBlank(message = "版本不能为空") String version,
        @Min(value = 1, message = "最小值为1") @Max(value = 2,
            message = "最大值为2") @NotNull(message = "版本不能为空") Integer systemType,
        @NotBlank(message = "构建次数不能为空") String build,Integer isShop) {
        Version appVersion = versionService.getLatestVersionService(version, systemType, build,platform,isShop);
        return R.okData(appVersion);
    }
}
