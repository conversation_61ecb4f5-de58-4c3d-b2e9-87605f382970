
package com.lj.auth.controller;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.lj.auth.annotation.AccessControl;
import com.lj.auth.annotation.LogRecord;
import com.lj.auth.annotation.ValidateSign;
import com.lj.auth.common.R;
import com.lj.auth.exception.ResponseEnum;
import com.lj.auth.exception.ServiceException;
import com.lj.auth.service.SignInService;
import com.lj.auth.util.RegexUtil;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 描述： 签到 创建人: CFJ 创建时间: 2024/04/02
 */
@Validated
@RestController
@RequestMapping("/signIn")
public class SignInController {

    @Resource
    private SignInService signInService;

    /**
     * 查询签到记录
     *
     * @param page 页
     * @param pageSize 页大小
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/02
     */
    @RequestMapping(value = "/detail")
    public R listSignInDetail(@RequestParam(defaultValue = "1") @Min(1) Integer page,
        @RequestParam(defaultValue = "10") @Min(1) Integer pageSize) {
        return signInService.signInDetailService(page, pageSize);
    }

    /**
     * 签到记录详情
     * 
     * @param id id
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/10
     */
    @RequestMapping(value = "/detailRecord")
    public R listSignInDetailRecord(@NotNull(message = "id不能为空") Integer id) {
        return signInService.signInDetailRecordService(id);
    }

    /**
     * 查询签到周期记录
     *
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/02
     */
    @RequestMapping(value = "/weeklyCheckInRecord")
    public R weeklyCheckInRecord() {
        return signInService.weeklyCheckInRecordService();
    }

    /**
     * 签到
     *
     * @param address 链地址
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/02
     */
    @ValidateSign
    @RequestMapping(value = "/in")
    @AccessControl(methodName = "signIn::in")
    @LogRecord(methodName = "签到")
    public R postSignIn(HttpServletRequest request, @NotBlank(message = "地址不能为空") String address) {
        if (!RegexUtil.checkEthAddress(address)) {
            throw new ServiceException(ResponseEnum.ChainAddressError);
        }
        return signInService.signInService(address);
    }

    /**
     * 签到规则
     *
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/02
     */
    @RequestMapping(value = "/signInRules")
    public R signInRules() {
        return signInService.signInRulesService();
    }

}
