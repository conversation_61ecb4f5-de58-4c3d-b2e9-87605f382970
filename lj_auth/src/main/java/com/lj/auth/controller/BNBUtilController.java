package com.lj.auth.controller;

import static com.lj.auth.common.CommonConstant.YL_ENERGY_RECHARGE;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.Optional;

import javax.annotation.Resource;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Digits;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.lj.auth.util.RegexUtil;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.web3j.protocol.core.methods.response.TransactionReceipt;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lj.auth.client.YlBesuChainClientCall;
import com.lj.auth.common.R;
import com.lj.auth.domain.Account;
import com.lj.auth.domain.SpRecord;
import com.lj.auth.exception.ServiceException;
import com.lj.auth.mapper.AccountMapper;
import com.lj.auth.mapper.SpRecordMapper;
import com.lj.auth.service.EnergyRechargeFlowService;
import com.lj.auth.util.BNBUtil;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 描述： 创建人: CFJ 创建时间: 2024/04/26
 */
@Slf4j
@Validated
@RestController
@RequestMapping(value = "/util")
public class BNBUtilController {
    @Resource
    private EnergyRechargeFlowService energyRechargeFlowService;
    @Resource
    private YlBesuChainClientCall ylBesuChainClientCall;
    @Resource
    private SpRecordMapper spRecordMapper;
    @Resource
    private AccountMapper accountMapper;

    /**
     * 发送主币
     *
     * @param address
     * @param gas
     * @param -任务类别1-日常任务2-周期任务3-新手任务4-限时任务5-长期任务6-签到7-直播奖励
     * @param remark
     * @return {@link String }
     * <AUTHOR>
     * @date 2024/11/15
     */
    @RequestMapping("/tr")
    public String transactionMain(@NotNull(message = "地址不能为空") String address,
        @Digits(integer = 10, fraction = 2, message = "资金格式错误") @DecimalMin(value = "0.01",
            message = "资金最小值不能低于0.01元") BigDecimal gas,
        Integer type, String accountUuid, String remark) throws Exception {

        String newHash = energyRechargeFlowService.transactionMainCurrency1(YL_ENERGY_RECHARGE, address, gas,
            4, remarkTOhex(remark));

        TransactionReceipt transactionReceipt = getTransactionReceipt(newHash);

        boolean statusOK = transactionReceipt == null ? false : transactionReceipt.isStatusOK();
        // 交易成功更改状态
        if (statusOK) {
            // 校验用户uuid 是否存在
            if (StrUtil.isBlank(accountUuid)) {
                throw new ServiceException("accountUuid不能为空");
            }
            Account account =
                accountMapper.selectOne(Wrappers.<Account>lambdaQuery().eq(Account::getUuid, accountUuid));
            if (account == null) {
                throw new ServiceException("用户不存在");
            }
            // 直播奖励
            if (ObjectUtil.equals(type, 7)) {
                SpRecord spRecord = new SpRecord();
                spRecord.setAccountUuid(accountUuid);
                spRecord.setAddress(address);
                spRecord.setType(3);
                spRecord.setTaskType(7);
                spRecord.setTaskTypeName("直播中奖");
                spRecord.setTaskName("直播中奖");
                spRecord.setReward(gas);
                spRecord.setHash(newHash);
                spRecordMapper.insert(spRecord);
                // 粉丝活动奖励
            } else if (ObjectUtil.equals(type, 8)) {
                SpRecord spRecord = new SpRecord();
                spRecord.setAccountUuid(accountUuid);
                spRecord.setAddress(address);
                spRecord.setType(3);
                spRecord.setTaskType(8);
                spRecord.setTaskTypeName("粉丝活动奖励");
                spRecord.setTaskName("粉丝活动奖励");
                spRecord.setReward(gas);
                spRecord.setHash(newHash);
                spRecordMapper.insert(spRecord);
            }
        }
        return newHash;
    }

    /**
     * 发送主币
     *
     * @param address
     * @param gas
     * @param typeName
     * @param accountUuid
     * @param remark
     * @param operator
     * @return {@link String }
     * @throws Exception
     */
    @RequestMapping("/trV2")
    public String transactionMainV2(@NotBlank(message = "地址不能为空") String address,
        @Digits(integer = 10, fraction = 2, message = "资金格式错误") @DecimalMin(value = "0.01",
            message = "资金最小值不能低于0.01元") BigDecimal gas,
        @NotNull(message = "任务名称不能为空") String typeName, @NotBlank(message = "uuid不能为空") String accountUuid,
        @NotBlank(message = "打币备注不能为空") String remark, @NotBlank(message = "操作人不能为空") String operator)
        throws Exception {
        //校验打币地址
        if (!RegexUtil.checkEthAddress(address)) {
            throw new ServiceException("地址格式错误");
        }
        String newHash = energyRechargeFlowService.transactionMainCurrency1(YL_ENERGY_RECHARGE, address, gas,
            4, remarkTOhex(remark));
        TransactionReceipt transactionReceipt = getTransactionReceipt(newHash);
        boolean statusOK = transactionReceipt == null ? false : transactionReceipt.isStatusOK();
        // 交易成功更改状态
        if (statusOK) {
            // 校验用户uuid 是否存在
            if (StrUtil.isBlank(accountUuid)) {
                throw new ServiceException("accountUuid不能为空");
            }
            Account account =
                accountMapper.selectOne(Wrappers.<Account>lambdaQuery().eq(Account::getUuid, accountUuid));
            if (account == null) {
                throw new ServiceException("用户不存在");
            }
            SpRecord spRecord = new SpRecord();
            spRecord.setAccountUuid(accountUuid);
            spRecord.setAddress(address);
            spRecord.setType(3);
            spRecord.setTaskType(7);
            spRecord.setTaskTypeName("线下活动奖励");
            spRecord.setTaskName(typeName);
            spRecord.setReward(gas);
            spRecord.setHash(newHash);
            spRecord.setRemark(operator);
            spRecordMapper.insert(spRecord);
        }
        return newHash;
    }


    /**
     * 更具hash查询交易详情
     * 
     * @param hash
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/26
     */
    @RequestMapping("/getHash")
    public R getHashDetail(@NotNull(message = "hash") String hash) {
        return ylBesuChainClientCall.getHashDetail(hash);
    }

    @RequestMapping("/getTrState")
    public R getTrState(@NotNull(message = "hash") String hash) throws Exception {
        TransactionReceipt transactionReceipt = getTransactionReceipt(hash);
        boolean statusOK = transactionReceipt == null ? false : transactionReceipt.isStatusOK();
        return R.okData(statusOK);
    }



    public String remarkTOhex(String message) {
        try {
            StringBuilder hex = new StringBuilder();
            byte[] bytes = message.getBytes("UTF-8");
            for (byte b : bytes) {
                hex.append(String.format("%02x", b));
            }
            return hex.toString();
        } catch (Exception e) {
        }
        return message;
    }

    public TransactionReceipt getTransactionReceipt(String transactionHash) throws Exception {
        int num = 0;
        while (true) {
            Optional<TransactionReceipt> transactionReceipt =
                BNBUtil.ethGetTransactionReceipt(transactionHash).getTransactionReceipt();
            if (transactionReceipt.isPresent()) {
                return transactionReceipt.get();
            }
            Thread.sleep(1000);
            num++;
            if (num > 10) {
                return null;
            }
        }
    }
}
