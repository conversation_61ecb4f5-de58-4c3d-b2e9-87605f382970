package com.lj.auth.controller;

import com.lj.auth.annotation.ValidateToken;
import com.lj.auth.common.R;
import com.lj.auth.domain.ModuleSwitch;
import com.lj.auth.exception.ServiceException;
import com.lj.auth.service.GlobalConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import static com.lj.auth.common.CommonConstant.DEFAULT_COORDINATE;

/**
 * <AUTHOR>
 * @Description
 * @date 2025/1/9 18:04
 */
@Slf4j
@Validated
@RestController
@RequestMapping(value = "globalConfig")
public class GlobalConfigController {
    @Resource
    private GlobalConfigService globalConfigService;

    /**
     * 默认坐标
     * 
     * @return
     */
    @RequestMapping(value = "defaultCoordinate")
    public R defaultCoordinate() {
        String globalConfig = globalConfigService.getGlobalConfig(DEFAULT_COORDINATE);
        return R.okData(globalConfig);
    }

    /**
     * 获取模块开关
     * 
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/04/07
     */
    @ValidateToken
    @RequestMapping(value = "getModuleSwitch")
    public R getModuleSwitch(HttpServletRequest request) {
        String channel = request.getHeader("channel");
        if (StringUtils.isEmpty(channel)) {
            channel = "JIEWAI";
        }
        String version = request.getHeader("version");
        if (StringUtils.isEmpty(version)) {
            throw new ServiceException("version is null");
        }
        return globalConfigService.getModuleSwitchService(channel, version);
    }

    /**
     * 新增模块开关数据
     * 
     * @param function
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/04/07
     */
    @ValidateToken
    @RequestMapping(value = "add")
    public R addSwitch(@RequestBody ModuleSwitch function) {
        return globalConfigService.addSwitchService(function);
    }

}
