package com.lj.auth.annotation;

import com.lj.auth.util.ServiceAuthUtil;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @describe
 */
@Aspect
@Component
@Order(1) // 确保在其他切面之前执行
public class ServiceAuthAspect {

    @Autowired
    private ServiceAuthUtil serviceAuthUtil;

    @Autowired
    private SignatureValidator signatureValidator;
//    @Pointcut("@annotation(com.lj.auth.annotation.ServiceAuth)")
//    public void pointCut() {
//    }

//    @Around("@annotation(com.lj.auth.annotation.ServiceAuth)|| @within(serviceAuth)")
    @Around("@annotation(serviceAuth) || @within(serviceAuth)")
    public Object handleServiceAuth(ProceedingJoinPoint point,ServiceAuth serviceAuth) throws Throwable {

        HttpServletRequest request = getCurrentRequest();
        if (request == null) {
            throw new IllegalArgumentException("无法获取HTTP请求上下文");
        }

        // 获取实际的ServiceAuth注解（这是关键修改）
        ServiceAuth actualServiceAuth = getServiceAuthAnnotation(point, serviceAuth);

        // 检查是否跳过验证
        if (hasSkipAuthAnnotation(point, actualServiceAuth)) {
            return point.proceed();
        }

        ServiceAuth.AuthLevel authLevel = actualServiceAuth != null ?
                actualServiceAuth.level() : ServiceAuth.AuthLevel.SERVICE;

        // 根据验证级别进行相应验证
        switch (authLevel) {
            case NONE:
                return point.proceed();

            case SERVICE:
                validateServiceAuth(request, actualServiceAuth);
                break;

            case SIGNATURE:
                validateSignature(request);
                break;

            case BOTH:
                // 优先检查服务间认证，如果通过则跳过签名验证
                if (!tryServiceAuth(request, actualServiceAuth)) {
                    validateSignature(request);
                }
                break;
        }

        return point.proceed();
    }

    // 关键方法：获取实际的ServiceAuth注解
    private ServiceAuth getServiceAuthAnnotation(ProceedingJoinPoint point, ServiceAuth paramServiceAuth) {
        // 1. 如果参数中的serviceAuth不为null，优先使用（方法级别注解）
        if (paramServiceAuth != null) {
            return paramServiceAuth;
        }

        // 2. 手动从方法上获取注解
        MethodSignature signature = (MethodSignature) point.getSignature();
        Method method = signature.getMethod();
        ServiceAuth methodAuth = method.getAnnotation(ServiceAuth.class);
        if (methodAuth != null) {
            return methodAuth;
        }

        // 3. 从类上获取注解
        Class<?> targetClass = point.getTarget().getClass();
        ServiceAuth classAuth = targetClass.getAnnotation(ServiceAuth.class);
        if (classAuth != null) {
            return classAuth;
        }

        // 4. 从接口上获取注解（如果目标类实现了接口）
        Class<?>[] interfaces = targetClass.getInterfaces();
        for (Class<?> interfaceClass : interfaces) {
            ServiceAuth interfaceAuth = interfaceClass.getAnnotation(ServiceAuth.class);
            if (interfaceAuth != null) {
                return interfaceAuth;
            }
        }

        return null;
    }

    /**
     * 检查方法是否有SkipAuth注解
     */
    private boolean hasSkipAuthAnnotation(ProceedingJoinPoint point, ServiceAuth serviceAuth) {
        // 使用传入的serviceAuth参数
        if (serviceAuth != null && serviceAuth.level() == ServiceAuth.AuthLevel.NONE) {
            return true;
        }

        return false;
    }

    /**
     * 服务间认证验证
     */
    private void validateServiceAuth(HttpServletRequest request, ServiceAuth serviceAuth) {
        String serviceToken = request.getHeader("ServiceToken");

        if (serviceToken == null) {
            throw new IllegalArgumentException("缺少服务认证Token");
        }

        if (!serviceAuthUtil.validateServiceToken(serviceToken)) {
            throw new IllegalArgumentException("服务认证Token无效");
        }

        // 检查允许的服务列表
        if (serviceAuth != null && serviceAuth.allowedServices().length > 0) {
            String serviceName = serviceAuthUtil.getServiceName(serviceToken);
            boolean allowed = Arrays.asList(serviceAuth.allowedServices()).contains(serviceName);

            if (!allowed) {
                throw new IllegalArgumentException("服务 " + serviceName + " 无权限访问此接口");
            }
        }
    }

    /**
     * 签名验证
     */
    private void validateSignature(HttpServletRequest request) {
        if (!signatureValidator.validate(request)) {
            throw new IllegalArgumentException("签名验证失败");
        }
    }

    /**
     * 尝试服务间认证（不抛异常）
     */
    private boolean tryServiceAuth(HttpServletRequest request, ServiceAuth serviceAuth) {
        try {
            validateServiceAuth(request, serviceAuth);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    private HttpServletRequest getCurrentRequest() {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (requestAttributes instanceof ServletRequestAttributes) {
            return ((ServletRequestAttributes) requestAttributes).getRequest();
        }
        return null;
    }
}
