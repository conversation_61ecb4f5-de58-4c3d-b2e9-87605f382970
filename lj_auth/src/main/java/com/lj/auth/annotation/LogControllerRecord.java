package com.lj.auth.annotation;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.lj.auth.domain.Account;
import com.lj.auth.service.AccountService;
import com.lj.auth.util.IpUtil;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Author: chenfangjun
 * @Date: 2022/8/8
 * @date 2023/10/26
 */

@Slf4j
@Aspect
@Component
public class LogControllerRecord {
    @Resource
    private AccountService accountService;

    @Around(value = "@annotation(logRecord)")
    public Object processRst(ProceedingJoinPoint point, LogRecord logRecord) throws Throwable {
        // 获得请求信息
        ServletRequestAttributes sra = (ServletRequestAttributes)RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = sra.getRequest();
        Object returnValue = null;
        final List<Object> params = new ArrayList<>();
        if (sra == null) {
            return point.proceed();
        }
        Object[] args = point.getArgs();
        // 过滤出HttpServlet
        if (args != null && args.length > 0) {
            for (int i = 0; i < args.length; i++) {
                Object object = args[i];
                if (object instanceof HttpServletResponse) {
                    continue;
                }
                if (object instanceof HttpServletRequest) {
                    continue;
                }
                params.add(object);
            }
        }
        log.info("--------------------请求开始---------------------------");
        log.info("请求方法 rest contexPath:——>" + request.getContextPath());
        log.info("请求方法 rest method:——>" + request.getMethod());
        log.info("请求路径 rest route:——>" + point.getSignature().getDeclaringTypeName() + "."
            + point.getSignature().getName());
        log.info("请求URL:" + request.getRequestURL().toString());
        log.info("请求IP:" + request.getRemoteAddr());
        // String cloneParams = JSONObject.toJSONString(params, SerializerFeature.IgnoreNonFieldGetter);
        String cloneParams = JSONObject.toJSONString(params, SerializerFeature.IgnoreNonFieldGetter);
        log.info("请求参数 rest param:——>" + cloneParams);
        returnValue = point.proceed(point.getArgs());
        Boolean boolean1 = true;
        if (returnValue != null) {
            Map map = JSON.parseObject(JSONObject.toJSONString(returnValue), Map.class);
            log.info("响应数据 rest Data:——>" + JSONObject.toJSONString(returnValue));
            String state = map.get("code").toString();
            String account = "";
            if (logRecord.methodName().contains("登录")||logRecord.methodName().contains("注册")) {
                account = (String)params.get(0);
            } else {
                Account userInfo = accountService.getUserInfo();
                account = userInfo.getPhoneNumber();
            }
            accountService.addAccountLoginInfo(account, IpUtil.getRealIp(request),
                state.equals("200") ? 1 : 0, cloneParams, logRecord.methodName());
            // -----日志记录
            log.info("-------------------请求结束---------------------------");
        }
        return returnValue;
    }

}