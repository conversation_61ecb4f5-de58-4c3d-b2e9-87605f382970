package com.lj.auth.annotation;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Hex;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import javax.servlet.http.HttpServletRequest;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.TreeMap;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @describe
 */
@Slf4j
@Component
public class SignatureValidator {


    @Value("${serviceAuth.apiSecret}")
    private String apiSecret;

    @Value("${serviceAuth.timeout:300}")
    private long timeoutSeconds; // 签名有效期，默认5分钟

    /**
     * 验证HTTP请求签名
     */
    public boolean validate(HttpServletRequest request) {
        try {
            // 获取签名相关头部信息
            String signature = request.getHeader("X-Signature");
            String timestamp = request.getHeader("X-Timestamp");
            String nonce = request.getHeader("X-Nonce");
            String appId = request.getHeader("X-App-Id");

            // 基础参数检查
            if (StringUtils.isEmpty(signature) || StringUtils.isEmpty(timestamp)) {
                log.warn("缺少必要的签名参数");
                return false;
            }

            // 时间戳验证
            if (!validateTimestamp(timestamp)) {
                log.warn("时间戳验证失败: {}", timestamp);
                return false;
            }

            // 防重放攻击检查
            if (!StringUtils.isEmpty(nonce) && !validateNonce(nonce, timestamp)) {
                log.warn("Nonce验证失败: {}", nonce);
                return false;
            }

            // 生成预期签名
            String expectedSignature = generateSignature(request, timestamp, nonce, appId);

            // 签名比对
            boolean valid = signature.equals(expectedSignature);

            if (!valid) {
                log.warn("签名验证失败 - 预期: {}, 实际: {}", expectedSignature, signature);
            }

            return valid;

        } catch (Exception e) {
            log.error("签名验证异常", e);
            return false;
        }
    }

    /**
     * 生成签名
     * 签名算法: HMAC-SHA256(appId + method + uri + queryString + body + timestamp + nonce + secret)
     */
    private String generateSignature(HttpServletRequest request, String timestamp,
                                     String nonce, String appId) throws Exception {

        StringBuilder signString = new StringBuilder();

        // 1. App ID
        if (!StringUtils.isEmpty(appId)) {
            signString.append(appId);
        }

        // 2. HTTP方法
        signString.append(request.getMethod().toUpperCase());

        // 3. 请求路径
        signString.append(request.getRequestURI());

        // 4. 查询参数 (按参数名排序)
        String queryString = getSortedQueryString(request);
        if (!StringUtils.isEmpty(queryString)) {
            signString.append("?").append(queryString);
        }

        // 5. 请求体
        String body = getRequestBody(request);
        if (!StringUtils.isEmpty(body)) {
            signString.append(body);
        }

        // 6. 时间戳
        signString.append(timestamp);

        // 7. 随机数
        if (!StringUtils.isEmpty(nonce)) {
            signString.append(nonce);
        }

        // 8. 密钥
        signString.append(apiSecret);

        // 生成HMAC-SHA256签名
        String signature = hmacSHA256(signString.toString());

        log.debug("签名字符串: {}", signString.toString());
        log.debug("生成签名: {}", signature);

        return signature;
    }

    /**
     * 验证时间戳
     */
    private boolean validateTimestamp(String timestamp) {
        try {
            long requestTime = Long.parseLong(timestamp);
            long currentTime = System.currentTimeMillis() / 1000;
            long timeDiff = Math.abs(currentTime - requestTime);

            return timeDiff <= timeoutSeconds;

        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 验证Nonce，防重放攻击
     */
    private boolean validateNonce(String nonce, String timestamp) {
        // 这里可以使用Redis或内存缓存存储已使用的nonce
        String cacheKey = "api_nonce:" + nonce + ":" + timestamp;

        // 检查nonce是否已存在
        if (existsInCache(cacheKey)) {
            return false; // 重复请求
        }

        // 缓存nonce，过期时间为签名有效期
        cacheNonce(cacheKey, timeoutSeconds);

        return true;
    }

    /**
     * 获取排序后的查询参数字符串
     */
    private String getSortedQueryString(HttpServletRequest request) {
        Map<String, String[]> paramMap = request.getParameterMap();
        if (paramMap.isEmpty()) {
            return "";
        }

        TreeMap<String, String> sortedParams = new TreeMap<>();

        for (Map.Entry<String, String[]> entry : paramMap.entrySet()) {
            String key = entry.getKey();
            String[] values = entry.getValue();

            if (values.length > 0) {
                // 多个值用逗号连接
                sortedParams.put(key, String.join(",", values));
            }
        }

        return sortedParams.entrySet().stream()
                .map(entry -> entry.getKey() + "=" + entry.getValue())
                .collect(Collectors.joining("&"));
    }

    /**
     * 获取请求体内容
     */
    private String getRequestBody(HttpServletRequest request) {
        // 使用包装器获取请求体，避免流只能读取一次的问题
        if (request instanceof ContentCachingRequestWrapper) {
            ContentCachingRequestWrapper wrapper = (ContentCachingRequestWrapper) request;
            return new String(wrapper.getContentAsByteArray(), StandardCharsets.UTF_8);
        }

        // 如果不是包装器，尝试从属性中获取（需要在Filter中预先缓存）
        Object cachedBody = request.getAttribute("CACHED_REQUEST_BODY");
        if (cachedBody != null) {
            return cachedBody.toString();
        }

        return "";
    }

    /**
     * HMAC-SHA256加密
     */
    private String hmacSHA256(String data) throws Exception {
        Mac mac = Mac.getInstance("HmacSHA256");
        SecretKeySpec secretKeySpec = new SecretKeySpec(
                apiSecret.getBytes(StandardCharsets.UTF_8), "HmacSHA256"
        );
        mac.init(secretKeySpec);

        byte[] hash = mac.doFinal(data.getBytes(StandardCharsets.UTF_8));
        return Hex.encodeHexString(hash); // 或使用Base64编码
    }

    // 缓存相关方法 - 可以使用Redis实现
    private boolean existsInCache(String key) {
        // 示例实现，实际应该使用Redis
        return nonceCache.containsKey(key);
    }

    private void cacheNonce(String key, long expireSeconds) {
        // 示例实现，实际应该使用Redis设置过期时间
        nonceCache.put(key, System.currentTimeMillis() + expireSeconds * 1000);
    }

    // 简单的内存缓存示例（生产环境建议使用Redis）
    private final Map<String, Long> nonceCache = new ConcurrentHashMap<>();

    // 定期清理过期的nonce
    @Scheduled(fixedRate = 60000) // 每分钟执行一次
    public void cleanExpiredNonce() {
        long currentTime = System.currentTimeMillis();
        nonceCache.entrySet().removeIf(entry -> entry.getValue() < currentTime);
    }
}


