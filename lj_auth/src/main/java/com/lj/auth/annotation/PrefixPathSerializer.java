package com.lj.auth.annotation;

import java.io.IOException;
import java.util.Arrays;

import cn.hutool.core.util.StrUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.ObjectUtils;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.BeanProperty;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.ContextualSerializer;

public class PrefixPathSerializer extends JsonSerializer<String> implements ContextualSerializer {

    @Value("${readImagepath}")
    private String readImagepath;// 图片访问路径,存到数据库

    private boolean addPrefix;

    @Override
    public JsonSerializer<?> createContextual(SerializerProvider prov, BeanProperty property)
        throws JsonMappingException {
        PrefixPath addUrlPrefix = property.getAnnotation(PrefixPath.class);
        if (!ObjectUtils.isEmpty(addUrlPrefix)) {
            this.addPrefix = true;
        }
        return this;
    }

    @Override
    public void serialize(String value, JsonGenerator gen, SerializerProvider serializers)
        throws IOException {
        if (addPrefix && StrUtil.isNotBlank(value)) {
            // 如果检测到有自定义注解进行前缀处理
            String[] values = value.split(",");
            StringBuilder str = new StringBuilder();
            Arrays.stream(values).forEach(v -> {
                if (str.length() > 0) {
                    str.append(",");
                }
                if (!v.contains("http")) {
                    str.append(readImagepath.concat(v).replaceAll("///", "/"));
                } else {
                    str.append(v.replaceAll("///", "/"));
                }
            });
            gen.writeString(str.toString());
        } else {
            gen.writeString(value);
        }
    }

}
