package com.lj.auth.annotation;

/**
 * <AUTHOR>
 * @describe
 */

import javax.servlet.ReadListener;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import java.io.*;
import java.nio.charset.StandardCharsets;

/**
 * 请求体缓存包装器 - 解决请求体只能读取一次的问题
 */
public class ContentCachingRequestWrapper extends HttpServletRequestWrapper {

    private final byte[] cachedContent;

    public ContentCachingRequestWrapper(HttpServletRequest request) throws IOException {
        super(request);

        InputStream inputStream = request.getInputStream();
        this.cachedContent = inputStream.readAllBytes();
    }

    @Override
    public ServletInputStream getInputStream() {
        return new CachedBodyServletInputStream(cachedContent);
    }

    @Override
    public BufferedReader getReader() {
        ByteArrayInputStream inputStream = new ByteArrayInputStream(cachedContent);
        return new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));
    }

    public byte[] getContentAsByteArray() {
        return cachedContent.clone();
    }

    private static class CachedBodyServletInputStream extends ServletInputStream {

        private final ByteArrayInputStream inputStream;

        public CachedBodyServletInputStream(byte[] cachedContent) {
            this.inputStream = new ByteArrayInputStream(cachedContent);
        }

        @Override
        public boolean isFinished() {
            return inputStream.available() == 0;
        }

        @Override
        public boolean isReady() {
            return true;
        }

        @Override
        public void setReadListener(ReadListener listener) {
            throw new UnsupportedOperationException();
        }

        @Override
        public int read() {
            return inputStream.read();
        }
    }
}
