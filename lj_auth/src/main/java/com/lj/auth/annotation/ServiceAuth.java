package com.lj.auth.annotation;

import java.lang.annotation.*;

@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface ServiceAuth {

    /**
     * 是否需要服务间认证
     * @return true需要验证，false跳过验证
     */
    boolean required() default true;

    /**
     * 允许的调用服务列表，空表示允许所有已认证服务
     * @return 服务名数组
     */
    String[] allowedServices() default {};

    /**
     * 验证级别
     * @return 验证级别
     */
    AuthLevel level() default AuthLevel.SERVICE;

    enum AuthLevel {
        NONE,       // 无需验证
        SERVICE,    // 仅服务间验证
        SIGNATURE,  // 外部签名验证
        BOTH        // 两种验证都需要
    }
}
