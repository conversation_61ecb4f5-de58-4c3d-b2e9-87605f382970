package com.lj.auth.annotation;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import com.lj.auth.exception.ServiceException;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;


import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * @Author: zjd
 * @Description:
 * @Date: 2023/7/26 16:34
 */
@Aspect
@Component
@Slf4j
@EnableAspectJAutoProxy
public class ValidateTokenAspect {

    @Pointcut("@annotation(ValidateToken)")
    public void pointCut() {}

    @Resource
    private StringRedisTemplate stringRedisTemplate;


    @Around("pointCut()")
    public Object validateToken(ProceedingJoinPoint joinPoint) throws Throwable {
        // 获取参数
        Object[] args = joinPoint.getArgs();
        ServletRequestAttributes sra = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = sra.getRequest();
        for (int i = 0; i < args.length; i++) {
            if (args[i] instanceof HttpServletRequest) {
                request = (HttpServletRequest) args[i];
                break;
            }
        }
        // 从请求头中获取token
        String satoken = request.getHeader("satoken");
        log.info("token值：{}", satoken);
        if (StrUtil.isEmpty(satoken)) {
            throw new ServiceException("参数中缺少token或token已过期",401);
        }
        // 根据token去redis查询
        String key = "satoken:login:token:" + satoken;
        String str = stringRedisTemplate.opsForValue().get(key);
        if (StrUtil.isEmpty(str)) {
            throw new ServiceException("token已过期",401);
        }
        return joinPoint.proceed(args);
    }
}
