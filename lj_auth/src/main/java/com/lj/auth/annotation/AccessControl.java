package com.lj.auth.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 描述：接口访问开关 创建人: CFJ 创建时间: 2024/05/10
 */
@Target({ElementType.METHOD, ElementType.TYPE}) // 可用于方法或类上
@Retention(RetentionPolicy.RUNTIME) // 运行时保留，这样可以在运行时通过反射获取到
public @interface AccessControl {
       String methodName();
}