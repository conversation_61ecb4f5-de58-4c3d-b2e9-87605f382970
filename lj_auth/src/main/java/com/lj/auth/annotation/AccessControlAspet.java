package com.lj.auth.annotation;

import com.lj.auth.common.R;
import com.lj.auth.service.GlobalConfigService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 描述：接口开关配置 创建人: CFJ 创建时间: 2024/05/10
 */
@Aspect // 标记为切面类
@Component // 让Spring管理这个切面
@Slf4j
public class AccessControlAspet {
    @Resource
    private GlobalConfigService globalConfigService;

    @Around("@annotation(accessControl)") // 拦截所有标记了@AccessControl的方法
    public Object checkAccess(ProceedingJoinPoint joinPoint, AccessControl accessControl) throws Throwable {
        String name = accessControl.methodName();

        String accessControlData = globalConfigService.getGlobalConfig(name);

        if (accessControlData.equals("true")) {
            // 在这里可以根据实际需求添加更复杂的访问控制逻辑
            log.info("可以方法问接口：{}", name);
            return joinPoint.proceed(); // 继续执行被拦截的方法
        } else {
            log.error("暂时未开放接口：{}", name);
            // 这里可以抛出自定义异常或者返回特定的结果表示访问被拒绝
            return R.error("暂未开放敬请期待");
        }
    }

}
