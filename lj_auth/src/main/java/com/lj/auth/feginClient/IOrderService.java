package com.lj.auth.feginClient;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import com.alibaba.fastjson2.JSONObject;
import com.lj.auth.common.R;

/**
 * IOrder服务
 *
 * <AUTHOR>
 * @date 2024/11/22
 */
@FeignClient(contextId = "lj-order", name = "lj-order")
public interface IOrderService {

    /** 创建订单
     * @param paramJson
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/11/22
     */
    @PostMapping("/lj-order/mixOrder/createOrder")
    public R createOrder(@RequestBody JSONObject paramJson);

    /**
     * 支付订单
     *
     * @param paramJson
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/11/22
     */
    @RequestMapping("/lj-order/mixOrder/payOrder")
    public R payOrder(@RequestBody JSONObject paramJson);

}
