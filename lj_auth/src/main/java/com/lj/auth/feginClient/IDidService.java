package com.lj.auth.feginClient;

import com.lj.auth.common.ResultInfo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * ichain服务
 *
 * <AUTHOR>
 * @date 2022/09/30
 */
@FeignClient(contextId = "lj-did", name = "lj-did")
public interface IDidService {

    @PostMapping("/lj-did/accountDid/unbindDidDomainV2")
    public boolean unbindDidDomainV2(@RequestParam("accountUuid") String accountUuid,
        @RequestParam("domain") String domain);
}
