package com.lj.auth.feginClient;

import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.lj.auth.common.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

/**
 * ichain服务
 *
 * <AUTHOR>
 * @date 2022/09/30
 */
@FeignClient(contextId = "lj-transfer", name = "lj-transfer")
public interface ITransferService {

    @PostMapping("/lj-transfer/issue/deactivateDomains")
    public boolean deactivateDomains(@RequestParam("accountUUID") @NotBlank String accountUUID,
        @RequestParam(value = "domains", required = false) List<String> domains);
}
