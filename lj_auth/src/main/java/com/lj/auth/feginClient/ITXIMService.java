package com.lj.auth.feginClient;

import com.lj.auth.common.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;


/**
 * IM 服务
 *
 * <AUTHOR>
 * @date 2022/09/30
 */
@FeignClient(contextId = "lj-tx-im", name = "lj-tx-im")
//@FeignClient(name = "lj-tx-im", url = "http://106.13.113.251:9998")
public interface ITXIMService {

    /**
     * 导入账户信息到im
     * @param userId
     * @return
     */
    @PostMapping("lj-tx-im/account/register")
     R register(@RequestParam("userId")  String userId);


    /**
     * 同步修改im信息
     * @param fromAccount
     * @param nickName
     * @param profileUrl
     * @return
     */
    @PostMapping("lj-tx-im/account/portraitSet")
     R portraitSet(@RequestParam("fromAccount")  String fromAccount,
                   @RequestParam("nickName")  String nickName,
                   @RequestParam("profileUrl")  String profileUrl);


    /**
     * 好友关系校验
     * @param fromAccount
     * @param toAccountIdList
     * @return
     */
    @PostMapping("lj-tx-im/account/friendCheck")
    R friendCheck(@RequestParam("fromAccount")  String fromAccount,
                  @RequestParam("toAccountIdList") List<String> toAccountIdList);
}
