package com.lj.auth.feginClient;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * ichain服务
 *
 * <AUTHOR>
 * @date 2022/09/30
 */
@FeignClient(contextId = "lj-file", name = "lj-file")
public interface IFileService {
    @RequestMapping(value = "/lj-file/file/uploadPicV2", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public String uploadPicV2(HttpServletRequest request,
        @RequestPart(value = "picture") MultipartFile picture,
        @RequestParam("folderName") @NotBlank String folderName);

    @RequestMapping(value = "/lj-file/file/multiUploadV2", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public String multiUploadV2(@RequestPart(value = "pictures") List<MultipartFile> pictures,
        @RequestParam("folderName") @NotBlank String folderName);
}
