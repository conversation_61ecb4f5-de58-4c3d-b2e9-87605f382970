package com.lj.auth.feginClient;

import com.lj.auth.common.R;
import com.lj.auth.common.ResultInfo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;

/**
 * ichain服务
 *
 * <AUTHOR>
 * @date 2022/09/30
 */
@FeignClient(contextId = "lj-domain", name = "lj-domain")
// @FeignClient(name = "lj-domain", url = "http://106.13.113.251:11110/lj-domain")
public interface IDomainService {
    @PostMapping("/lj-domain/domainAccount/transferDomainV2")
    public boolean transferDomainV2(@RequestParam("domain") @NotBlank String domain,
        @RequestParam("fromAccountUuid") @NotBlank String fromAccountUuid,
        @RequestParam("toAccountUuid") @NotBlank String toAccountUuid);

    @RequestMapping(method = RequestMethod.POST,
        value = "/lj-domain/domainAccount/addHistoricalHoldDomainInfo")
    R addHistoricalHoldDomainInfo(@RequestParam(value = "domain") String domain,
        @RequestParam(value = "fromAccountUUID") String fromAccountUUID,
        @RequestParam(value = "toAccountUUID") String toAccountUUID,
        @RequestParam(value = "transType") Integer transType);

    @PostMapping("/lj-domain/orderDomainRenewalActivity/domainRenewalActivityList")
    R domainRenewalActivityList();

    @PostMapping("/lj-domain/ymV2/getAccountExperienceState")
     ResultInfo getAccountExperienceState(
        @RequestParam("accountUuid") @NotBlank(message = "账户不能为空") String accountUuid);
}
