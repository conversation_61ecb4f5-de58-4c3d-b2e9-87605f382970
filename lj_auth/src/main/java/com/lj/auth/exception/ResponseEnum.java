package com.lj.auth.exception;

/**
 * <AUTHOR>
 * @date 2020/7/9
 */
public enum ResponseEnum {

    /**
     * ok
     */
    OK(200, "ok"),

    ERROR(500, "网络开小差了"),

    PARAM_ERROR(500, "参数错误"),

    // ====短信=====================
    SMSTypeError(500, "经销商UUID或短信类型不能为空"),

    SMSConfigurationDoesNotExist(500, "短信配置信息不存在"),

    // ============ 通用配置 ================
    CommonConfigurationMissing(500, "通用配置缺失，请联系客服"),

    // ======登录注册 个人中心 =================

    PhoneNumberFormatError(500, "账号格式错误"),

    InvalidTOKEN(500, "无效token"),

    CancellationLessThanOneYearOld(500, "注销未满一年"),


    LoginOnlyForOperationalAccounts(500, "仅限运营账号登录"),


    OperationAccountSMSError(500, "运营账号不支持发送验证码"),

    CommunityOperationAccountError(500, "社区运营账号不支持发送验证码"),

    PhoneNumberFormatErrorBinding(500, "手机号格式错误,请前去绑定"),

    TouristAccountError(500, "游客账号不支持发送验证码"),

    TheAccountHasBeenCancelled(500, "账号已被注销"),

    AccountCannotLogInNormally(500, "该账号无法登录"),

    VerificationCodeError(603, "验证码错误"),

    PhoneNumberAlreadyExists(500, "该手机号已注册，请更换其他号码"),

    PhoneNumberDoesNotExist(500, "当前手机号未绑定实名DID，无法登录使用"),

    PhoneNumberMismatch(500, "与原绑定手机号不符请重新输入"),

    PayPasswordRuleError(500, "密码为6位纯数字"),

    PasswordRuleError(500, "请输入正确的密码格式:长度在8~20之间，密码含有数字和字母"),

    EnterTheCorrectPhoneNumber(500, "请输入正确的登录账号"),

    NicknameFormatError(500, "最大长度10位且不能包含特殊字符"),

    NicknameDuplication(500, "昵称已存在"),

    UnrecognizedName(500, "未实名请前往实名"),

    NotDIDCertified(500, "未DID认证"),
    NotDIDCertifiedPlease(620, "请先DID认证"),

    FailedToUpdateUserInformation(500, "更新用户信息失败"),

    InvalidInvitationCode(500, "邀请码无效，请输入正确的邀请码"),

    DealerDoesNotExist(500, "经销商不存在"),

    PleaseDoNotRegisterAgain(500, "手机号已存在,请勿重复注册"),

    MissingPrefix(500, "经销商前缀缺失请联系管理员"),

    UserDoesNotExist(500, "用户不存在"),

    DealerUuidDoesNotExist(500, "经销商uuid不存在"),

    DealerSerialNumberCannotBeEmpty(500, "经销商序号不能为空"),

    ThisAccountHasBeenLockedFor5Minutes(500, "该账号已被锁定5分钟"),

    SetPassword(500, "请先设置登录密码"),

    AccountOrPasswordError(500, "账号或密码错误"),

    // =============发票================
    InvoiceTypeMismatch(500, "主体类型和开票类型不匹配"),

    MismatchedInvoicingMedia(500, "开票类型和开票介质不匹配"),

    InvoiceParameterAbnormality(500, "非法参数,无法获取发票详情"),
    // ========充值===订单====
    OrderParameterAbnormality(500, "订单参数异常"),

    PaymentPasswordError(500, "支付密码错误"),

    InsufficientAccountBalance(500, "账户余额不足"),

    PaymentOrderFailed(500, "生成支付订单失败"),

    RechargeOrderDoesNotExist(500, "充值订单不存在"),

    QueryFailed(500, "查询失败"),

    // =========银行卡==========
    NameIsIllegal(500, "请输入合法的姓名"),

    SameCardNumber(500, "已有相同卡号,请勿重复添加"),

    BankCardInformationDoesNotExist(500, "银行卡信息不存在"),

    // ==========签到=================
    ChainAddressError(500, "请输入正确的链地址"),

    // =========域名管理==================
    ParsingFailed(500, "解析失败"),

    BSNChainAddressError(500, "请输入正确的链地址"),

    EnterYourOwnChainAddress(500, "该地址不是自己的,请输入自己的链地址"),

    AddressOccupation(500, "签到失败，链地址存在占用情况，请更换链地址后重新签到"),

    AddressAlreadyBound(500, "该域名已经绑定了地址"),

    DomainNameUnbound(500, "该域名没有绑定地址，请前去绑定"),

    DomainNameAddressMappingError(500, "域名地址映射修改错误"),

    IllegalRemoval(500, "非法的移除"),

    SpecialCharacters(500, "特殊key:YLDID,不能作为域名解析"),
    //=====注销
    NonOrdinaryUsers(500, "非普通用户不能注销"),

    AllCancellationConditions(500, "未满足全部注销条件"),

    ;

    private final Integer code;

    private final String msg;

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    ResponseEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    @Override
    public String toString() {
        return "ResponseEnum{" + "code='" + code + '\'' + ", msg='" + msg + '\'' + "} " + super.toString();
    }

}
