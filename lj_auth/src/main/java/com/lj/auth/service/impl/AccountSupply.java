package com.lj.auth.service.impl;

import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.lj.auth.exception.ResponseEnum;
import com.lj.auth.feginClient.IDomainService;
import com.lj.auth.feginClient.ITXIMService;
import com.lj.auth.mapper.*;
import com.lj.auth.notify.sms.NotifyService;
import com.lj.auth.pay.AggregatePay.MiniPay.MiniPayUtil;
import com.lj.auth.service.GlobalConfigService;
import com.lj.auth.task.CheckLogout;
import com.lj.auth.util.DomainMappingUtilParam;
import com.lj.auth.util.FusuiUtil;
import com.lj.auth.util.RedisUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.ServletContext;

import java.util.Date;

import static com.lj.auth.common.CommonConstant.YL_UUID;

/**
 * <AUTHOR>
 * @describe
 */
@Component
public class AccountSupply {

    @Resource
    private RedisUtils redisUtils;

    @Resource
    private AccountMapper accountMapper;


    public boolean verifyVerificationCode(String account, String verificationCode) {
        String operateUUID = YL_UUID;
        // 验证码redis的key
        String verificationCodeKey = "lj:verificationCodeKey:" + operateUUID + ":" + account;
        // 从缓存中获取验证码
        String cacheVerificationCode = (String)redisUtils.get(verificationCodeKey);
        // 校验验证码
        if (cacheVerificationCode != null && cacheVerificationCode.equals(verificationCode)) {
            // 清除 缓存验证码
            redisUtils.del(verificationCodeKey);
            return true;
        }
        return false;
    }

    /**
     * 校验用户登录失败次数
     *
     * @param servletContext
     * @param operateuuidAndPhone
     * @return
     */
    public boolean checkLock(ServletContext servletContext, String operateuuidAndPhone) {
        Object obj = servletContext.getAttribute(operateuuidAndPhone);
        if (obj == null) {
            return true;
        }
        JSONObject json = JSON.parseObject(JSON.toJSONString(obj));
        Integer num = json.getInteger("num");
        Date date = json.getDate("lastDate");
        long timeDifference = ((new Date().getTime() - date.getTime()) / 60 / 1000);
        return num < 3 || timeDifference >= 5;
    }



    /**
     * 登录判断手机号是否存在
     *
     * @param account
     * @return boolean
     * <AUTHOR>
     * @date 2024/03/27
     */
    public boolean handleLoginScenario(String account) {
        Boolean phoneNumberIsExists = accountMapper.phoneNumberIsExists(account);
        Assert.isTrue(phoneNumberIsExists, ResponseEnum.PhoneNumberDoesNotExist.getMsg());
        return true;
    }

    /**
     * 新增用户登录失败次数
     *
     * @param servletContext
     * @param operateuuidAndPhone
     */
    public void addFailNum(ServletContext servletContext, String operateuuidAndPhone) {
        Object obj = servletContext.getAttribute(operateuuidAndPhone);
        JSONObject json;
        int num = 0;
        if (obj == null) {
            json = new JSONObject();
        } else {
            json = JSON.parseObject(JSON.toJSONString(obj));
            num = json.getInteger("num");
            Date date = json.getDate("lastDate");
            long timeDifference = ((new Date().getTime() - date.getTime()) / 60 / 1000);
            if (timeDifference >= 5) {
                num = 0;
            }
        }
        json.put("num", num + 1);
        json.put("lastDate", new Date());
        servletContext.setAttribute(operateuuidAndPhone, json);
    }



    /**
     * 清理用户登录失败的记录
     *
     * @param servletContext
     * @param operateuuidAndPhone
     */
    public void cleanFailNum(ServletContext servletContext, String operateuuidAndPhone) {
        servletContext.removeAttribute(operateuuidAndPhone);
    }
}
