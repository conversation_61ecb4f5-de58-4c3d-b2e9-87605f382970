package com.lj.auth.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lj.auth.domain.VersionRecord;
import com.lj.auth.mapper.VersionRecordMapper;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Objects;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lj.auth.domain.Version;
import com.lj.auth.mapper.VersionMapper;
import com.lj.auth.service.VersionService;

import javax.annotation.Resource;

@Service
public class VersionServiceImpl extends ServiceImpl<VersionMapper, Version> implements VersionService {
    @Resource
    private VersionMapper versionMapper;
    @Resource
    private VersionRecordMapper versionRecordMapper;

    /**
     * 获取APP版本信息
     *
     * @param version    版本
     * @param systemType 系统类型 1-安卓 2-IOS
     * @param build      构建次数
     * @param platform
     * @param isShop
     * @return {@link Version }
     */
    @Override
    public Version getLatestVersionService(String version, Integer systemType, String build, String platform,
        Integer isShop) {
        Version appVersion = versionMapper.getLatestVersion(systemType, platform);
        if (StrUtil.isBlank(platform)) {
            appVersion = versionMapper.getLatestVersion(systemType, "JIEWAI");
            appVersion.setPlatform(null);
        }

        if (appVersion == null) {
            return null;
        }

        // 如果版本号相同但构建号不同，或者新版本号更高，则返回更新信息
        if (Objects.equals(appVersion.getVersion(), version)
            && !Objects.equals(appVersion.getBuild(), build)) {
            return appVersion;
        } else if (isVersionHigher(appVersion.getVersion(), version)) {
            // 判断新版本是否是强更
            if (!ObjectUtil.equals(appVersion.getStatus(), 2)) {
                // 判断 落后的版本到最新版本 中间是否存在强更
                List<VersionRecord> versions = versionRecordMapper.selectList(Wrappers
                    .<VersionRecord>lambdaQuery().eq(VersionRecord::getSystemType, appVersion.getSystemType())
                    .eq(VersionRecord::getPlatform, appVersion.getPlatform())
                    .eq(VersionRecord::getStatus, 2));
                if (CollectionUtil.isNotEmpty(versions)) {
                    for (VersionRecord versionRecord : versions) {
                        if (isVersionHigher(versionRecord.getVersion(), version)) {
                            appVersion.setStatus(2);
                            break;
                        }
                    }

                }
            }
            return appVersion;
        }
        // 版本也相同构建号也相同，但是是手动更新，是安卓平台默认更新到JIEWAI
        if (Objects.equals(appVersion.getVersion(), version) && Objects.equals(appVersion.getBuild(), build)
            && !Objects.equals(platform, "JIEWAI") && Objects.equals(systemType, 1)
            && Objects.equals(isShop, 0)) {
            // 返回界外版本最新版本
            appVersion = versionMapper.getLatestVersion(1, "JIEWAI");
            appVersion.setStatus(2);
            return appVersion;
        }

        return null;
    }

    private boolean isVersionHigher(String newVersion, String currentVersion) {
        String[] newParts = newVersion.split("\\.");
        String[] currentParts = currentVersion.split("\\.");

        for (int i = 0; i < Math.min(newParts.length, currentParts.length); i++) {
            int newPart = Integer.parseInt(newParts[i]);
            int currentPart = Integer.parseInt(currentParts[i]);

            if (newPart > currentPart) {
                return true;
            } else if (newPart < currentPart) {
                return false;
            }
        }
        return newParts.length > currentParts.length;
    }

}
