package com.lj.auth.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lj.auth.common.CommonConstant;
import com.lj.auth.domain.PlatformAccount;
import com.lj.auth.domain.Result.SpBalanceResp;
import com.lj.auth.domain.Result.SpTransferResp;
import com.lj.auth.domain.Result.TransferReq;
import com.lj.auth.domain.Result.TransferRequireInfo;
import com.lj.auth.entity.SpTransactionRecords;
import com.lj.auth.exception.ServiceException;
import com.lj.auth.mapper.PlatformAccountMapper;
import com.lj.auth.mapper.SpAddressBlacklistMapper;
import com.lj.auth.mapper.SpTransactionRecordsMapper;
import com.lj.auth.util.BNBUtil;
import com.lj.auth.util.HmacSHA256Util;
import com.lj.auth.util.LockUtil;
import com.lj.auth.util.ServiceAuthUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lj.auth.mapper.ChainMapper;
import com.lj.auth.domain.Chain;
import com.lj.auth.service.ChainService;
import org.web3j.protocol.core.methods.response.*;
import org.web3j.utils.Convert;
import org.web3j.utils.Numeric;

import javax.annotation.Resource;

@Slf4j
@Service
public class ChainServiceImpl extends ServiceImpl<ChainMapper, Chain> implements ChainService {
    @Value("${ylChainId}")
    private Integer chainId;

    @Resource
    private SpAddressBlacklistMapper spAddressBlacklistMapper;

    @Resource
    private LockUtil lockUtil;

    @Resource
    private ServiceAuthUtil serviceAuthUtil;

    @Resource
    private PlatformAccountMapper platformAccountMapper;
    @Resource
    private SpTransactionRecordsMapper spTransactionRecordsMapper;

    @Override
    public Chain getLjChainInfoService() {
        Chain chain = this.getOne(Wrappers.<Chain>lambdaQuery().eq(Chain::getChainId, chainId));
        return chain;
    }

    /**
     * 获取所有链信息
     * @return {@link List }<{@link Chain }>
     * <AUTHOR>
     * @date 2024/04/16
     */
    @Override
    public List<Chain> getChainInfoService() {
        List<Chain> list = this.list(Wrappers.<Chain>lambdaQuery().eq(Chain::getState, true));
        return list;
    }

    public static void main(String[] args) {
        String paramSecretKey = "lj_server_wish_2099";
        Map params = new HashMap();
        params.put("address", "0x3214d9F2E6c6EBE54B5fFe005646E52DcFB241aB");
        String json =JSONObject.toJSONString(params);
        JSONObject jsonObject = JSONObject.parseObject(json);
        for (String key : jsonObject.keySet()) {
            if ("sign".equals(key) || "nonce".equals(key) || "timestamp".equals(key)) {
                continue;
            }
            Object value = jsonObject.get(key);
            if (value != null && !"".equals(value.toString())) {
                 params.put(key, value.toString());
//                String jsonString = JSONObject.toJSONString(value, JSONWriter.Feature.WriteMapNullValue);
//                jsonString = jsonString.replace("\"","");
//                params.put(key, jsonString);
            }
        }
        String signContent = HmacSHA256Util.buildSignContent(params);
        System.out.println("signContent：" + signContent);
        String sign = HmacSHA256Util.sign(signContent, paramSecretKey);

        System.out.println("sign：" + sign);
        System.out.println("timestamp：" + System.currentTimeMillis());
    }


    /**
     * 获取sp余额信息
     * @param jsonObject
     * @return
     */
    @Override
    public SpBalanceResp getBalance(JSONObject jsonObject) {
        String address = jsonObject.getString("address");
        Assert.notBlank(address, "address is null");
        SpBalanceResp spBalanceResp =new SpBalanceResp();
        spBalanceResp.setAddress(address);
        BigDecimal balance = getBalance(address);
        spBalanceResp.setBalance(balance);
        return spBalanceResp;
    }

    private BigDecimal getBalance(String address) {
        BigInteger balance = BNBUtil.getBalance(address);
        BigDecimal ylAsset = new BigDecimal(balance).divide(BigDecimal.TEN.pow(18));
        //向下取整6为位小数
        ylAsset = ylAsset.setScale(6, BigDecimal.ROUND_DOWN);
        return ylAsset;
    }



    /**
     * SP转账
     * @param jsonObject 包含转账信息的JSON对象
     * @return SpTransferResp 转账结果
     */
    @Override
    public SpTransferResp transferSP(JSONObject jsonObject) {
        log.info("开始处理SP转账请求: {}", jsonObject);

        SpTransferResp spTransferResp = new SpTransferResp();

        try {
            // 参数提取和验证
            String signedData = jsonObject.getString("signedData");
            String fromAddress = jsonObject.getString("fromAddress");
            String toAddress = jsonObject.getString("toAddress");
            BigDecimal amount = jsonObject.getBigDecimal("amount");

            // 严格的参数验证
            validateTransferParams(signedData, fromAddress, toAddress, amount);

            // 校验地址是否在黑名单
            validateAressddBlackList(fromAddress);

            BigInteger GAS_LIMIT = new BigInteger("3000000");

            // 使用签名数据哈希作为锁键，确保相同签名数据不会重复执行
            String lockKey = "SP_TRANSFER:" + org.apache.commons.codec.digest.DigestUtils.md5Hex(signedData);

            log.info("开始执行SP转账，从地址: {}, 到地址: {}, 金额: {}, 锁键: {}", fromAddress, toAddress, amount, lockKey);

            // 使用分布式锁防止重复转账，将余额检查也放在锁内
            TransferReq transferReq = lockUtil.executeWithBlockingLock(lockKey, () -> {
                // 在锁内重新检查余额，避免竞态条件
                validateBalance(fromAddress, amount);

                // 检查是否已经处理过相同的签名数据
                if (isTransactionAlreadyProcessed(signedData)) {
                    throw new ServiceException("该交易已经处理过，请勿重复提交");
                }

                return executeSignedDataOnChain(signedData, fromAddress, toAddress, amount, GAS_LIMIT, spTransferResp);
            });

            spTransferResp.setIsStatusOK(transferReq.getIsStatusOK());

            if (transferReq.getIsStatusOK()) {
                log.info("SP转账成功，交易哈希: {}", spTransferResp.getTransactionHash());
            } else {
                log.warn("SP转账失败，交易哈希: {}", spTransferResp.getTransactionHash());
            }

            return spTransferResp;

        } catch (ServiceException e) {
            log.error("SP转账业务异常: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("SP转账发生未知错误", e);
            throw new ServiceException("转账失败: " + e.getMessage());
        }
    }


    /**
     * 执行签名数据
     * @param jsonObject
     * @return
     */
    @Override
    public SpTransferResp transferFeign(JSONObject jsonObject) {
        return transferSP(jsonObject);
    }

    @Override
    public Map getServeToken(JSONObject jsonObject) {
        Map<String, String> map = new HashMap<>();
        String serviceName = jsonObject.getString("serviceName");
        String generateServiceToken = serviceAuthUtil.generateServiceToken(serviceName);
        map.put("ServiceToken", generateServiceToken);
        return map;
    }

    /**
     * 验证转账参数
     */
    private void validateTransferParams(String signedData, String fromAddress, String toAddress, BigDecimal amount) {
        if (StringUtils.isBlank(signedData)) {
            throw new ServiceException("签名数据不能为空");
        }
        if (StringUtils.isBlank(fromAddress)) {
            throw new ServiceException("发送方地址不能为空");
        }
        if (StringUtils.isBlank(toAddress)) {
            throw new ServiceException("接收方地址不能为空");
        }
        if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new ServiceException("转账金额必须大于0");
        }

        // 验证地址格式（简单验证）
        if (!fromAddress.matches("^0x[a-fA-F0-9]{40}$")) {
            throw new ServiceException("发送方地址格式不正确");
        }
        if (!toAddress.matches("^0x[a-fA-F0-9]{40}$")) {
            throw new ServiceException("接收方地址格式不正确");
        }

    }

    /**
     * 验证余额是否充足
     */
    private void validateBalance(String fromAddress, BigDecimal amount) {
        try {
            BigDecimal balance = getBalance(fromAddress);
            if (balance.compareTo(amount) < 0) {
                throw new ServiceException("余额不足，当前余额: " + balance + " SP，转账金额: " + amount + " SP");
            }
            log.info("余额验证通过，地址: {}, 余额: {}, 转账金额: {}", fromAddress, balance, amount);
        } catch (Exception e) {
            if (e instanceof ServiceException) {
                throw e;
            }
            log.error("获取余额失败，地址: {}", fromAddress, e);
            throw new ServiceException("获取余额失败，请稍后重试");
        }
    }

    /**
     * 检查交易是否已经处理过（防止重复提交）
     */
    private boolean isTransactionAlreadyProcessed(String signedData) {
        try {
            // 从签名数据中解析交易信息
            String signedDataHash = org.apache.commons.codec.digest.DigestUtils.md5Hex(signedData);

            // 检查数据库中是否已存在相同的签名数据哈希
            // 这里可以查询交易记录表，看是否有相同的签名数据
            // 为了简化，这里先返回 false，实际项目中应该查询数据库

            log.debug("检查交易重复性，签名数据哈希: {}", signedDataHash);
            return false;
        } catch (Exception e) {
            log.warn("检查交易重复性时发生异常", e);
            return false;
        }
    }

    /**
     * 执行链上签名交易
     */
    private TransferReq executeSignedDataOnChain(String signedData, String fromAddress, String toAddress,
                                                BigDecimal amount, BigInteger gasLimit, SpTransferResp spTransferResp) {
        try {
            log.info("开始执行链上交易，从地址: {}, 到地址: {}, 金额: {}", fromAddress, toAddress, amount);

            String transactionHash = BNBUtil.sendTransaction(signedData, fromAddress, toAddress, amount, gasLimit);
            spTransferResp.setTransactionHash(transactionHash);

            log.info("交易已提交到区块链，交易哈希: {}", transactionHash);

            // 记录交易提交信息（可选）
            recordTransactionSubmission(signedData, transactionHash, fromAddress, toAddress, amount);

            TransferReq transferReq = queryTxInfo(transactionHash);
            return transferReq;
        } catch (Exception e) {
            log.error("执行链上交易失败，从地址: {}, 到地址: {}, 金额: {}", fromAddress, toAddress, amount, e);
            throw new ServiceException("交易执行失败: " + e.getMessage());
        }
    }

    /**
     * 记录交易提交信息
     */
    private void recordTransactionSubmission(String signedData, String transactionHash,
                                           String fromAddress, String toAddress, BigDecimal amount) {
        try {
            // 这里可以记录交易提交的详细信息到数据库
            // 包括签名数据哈希、交易哈希、提交时间等
            String signedDataHash = org.apache.commons.codec.digest.DigestUtils.md5Hex(signedData);
            log.info("记录交易提交信息 - 哈希: {}, 签名哈希: {}, 从: {}, 到: {}, 金额: {}",
                    transactionHash, signedDataHash, fromAddress, toAddress, amount);
        } catch (Exception e) {
            log.warn("记录交易提交信息失败", e);
        }
    }


    @Override
    public String getSign(JSONObject jsonObject) {
        String privateKey = jsonObject.getString("privateKey");
        String toAddress = jsonObject.getString("toAddress");
        String amount = jsonObject.getString("amount");
        String address = BNBUtil.getAddressByPrivateKey(privateKey);
        log.info("fromAddress:{}",address);
        TransferRequireInfo transferRequireInfo = queryTxRequireInfo(address);
        BigInteger nonce = transferRequireInfo.getNonce();
        BigInteger gasPrice = transferRequireInfo.getGasPrice();
        BigInteger gasLimit = transferRequireInfo.getGasLimit();
        BigInteger value = Convert.toWei(amount, Convert.Unit.ETHER).toBigInteger();
        //16进制
        String transSpData=  CommonConstant.ENERGY_SEND_SP;
        String signedData="";
        try {
            signedData = BNBUtil.signTransaction(nonce, gasPrice, gasLimit, toAddress.toLowerCase(), value, transSpData,
                    chainId, privateKey);
            System.out.println("signedData:" + signedData);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return signedData;
    }




    /**
     * 获取交易所需信息
     * @param jsonObject
     * @return
     */
    @Override
    public TransferRequireInfo queryTxRequireInfo(JSONObject jsonObject) {
        String fromAddress = jsonObject.getString("fromAddress");
        String toAddress = jsonObject.getString("toAddress");
        // 1.SP转账 2.SP充值
        Integer type = jsonObject.getInteger("type");
        if (fromAddress == null) {
            throw new RuntimeException("address is null");
        }
        //SP充值
        if (ObjectUtil.equals(type,2)){
            //获取平台链账户地址
            PlatformAccount ylPlatformAccount=platformAccountMapper.queryByTypeAndStatus(PlatformAccount.TYPE_SP_RECHARGE_POINT, PlatformAccount.STATUS_SHOW);
            Assert.notNull(ylPlatformAccount,"平台数据异常，请联系管理员");
            toAddress=ylPlatformAccount.getAddress();
        }


        //校验地址是否黑名单
        validateAressddBlackList(fromAddress);
        TransferRequireInfo transferRequireInfo = queryTxRequireInfo(fromAddress);

        if(StringUtils.isNotBlank(toAddress)){
            BigDecimal   estimateTransactionFeeInEth = BNBUtil.getEstimateTransactionFeeInEth(fromAddress, toAddress,  CommonConstant.ENERGY_SEND_SP);
            transferRequireInfo.setEstimateTransactionFeeInEth(estimateTransactionFeeInEth.toPlainString());
            transferRequireInfo.setToAddress(toAddress);
        }
        return transferRequireInfo;
    }





    private void validateAressddBlackList(String fromAddress){
        Boolean blacklist = spAddressBlacklistMapper.isBlacklist(fromAddress);
        if (blacklist) {
            throw new ServiceException("检测到该地址存在异常操作，为确保信息安全，系统已锁定该地址");
        }
    }


    private TransferRequireInfo queryTxRequireInfo(String fromAddress) {
        //校验地址是否黑名单
        validateAressddBlackList(fromAddress);
        TransferRequireInfo transferRequireInfo=new TransferRequireInfo();
        transferRequireInfo.setFromAddress(fromAddress);
        //获取余额
        BigDecimal balance = getBalance(fromAddress);
        transferRequireInfo.setBalance(balance);
        //获取nonce
        BigInteger nonce = BNBUtil.getTransactionNonce(fromAddress);
        transferRequireInfo.setNonce(nonce);

        //获取gasLimit
        BigInteger GAS_LIMIT = new BigInteger("3000000");
        transferRequireInfo.setGasLimit(GAS_LIMIT);
        try {
            BigInteger gasPrice = BNBUtil.ethGasPrice().getGasPrice();
            transferRequireInfo.setGasPrice(gasPrice);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return transferRequireInfo;
    }


    /**
     * 查询交易信息
     * @param jsonObject
     * @return
     */
    @Override
    public TransferReq queryTxInfo(JSONObject jsonObject) {
            return null;
    }


    /**
     * 通过交易hash查询 交易信息
     * @param transactionHash
     * @return
     */
    @Override
    public TransferReq queryTxInfo(String transactionHash) {
        TransferReq transferReq=new TransferReq();
        transferReq.setTransactionHash(transactionHash);
        try {
            TransactionReceipt transactionReceipt = getTransactionReceipt(transactionHash);
            if (!transactionReceipt.isStatusOK()) {
                transferReq.setIsStatusOK(false);
            }else {
                transferReq.setIsStatusOK(true);
                // 交易成功时保存交易信息到数据库
                try {
                    saveTransactionInfo(transactionReceipt);
                } catch (Exception saveException) {
                    // 保存失败不影响查询结果，只记录日志
                    log.error("保存交易信息失败，交易哈希: {}", transactionHash, saveException);
                }
            }
        } catch (Exception e) {
            log.error("查询交易信息失败，交易哈希: {}", transactionHash, e);
            throw new ServiceException("查询交易信息遇到网络错误");
        }
        return transferReq;
    }





    public TransactionReceipt getTransactionReceipt(String transactionHash) {
        try {
            EthGetTransactionReceipt ethGetTransactionReceipt =
                    BNBUtil.ethGetTransactionReceipt(transactionHash);
            Optional<TransactionReceipt> optional = ethGetTransactionReceipt.getTransactionReceipt();
            int count = 0;
            if (!optional.isPresent()) {
                while (count < 20) {
                    count++;
                    // 等待1秒
                    log.info("等待" + count + "秒");
                    TimeUnit.SECONDS.sleep(1);
                    ethGetTransactionReceipt = BNBUtil.ethGetTransactionReceipt(transactionHash);
                    optional = ethGetTransactionReceipt.getTransactionReceipt();
                    if (optional.isPresent()) {
                        return optional.get();
                    }
                }
                throw new RuntimeException(transactionHash + " 你太慢了");
            }
            return optional.get();
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException("获取交易回执失败");
        }
    }


    private void saveTransactionInfo(TransactionReceipt transactionReceipt) {
        if (transactionReceipt == null) {
            log.warn("TransactionReceipt is null, cannot save transaction info");
            return;
        }

        SpTransactionRecords spTransactionRecords = new SpTransactionRecords();

        try {
            String transactionHash = transactionReceipt.getTransactionHash();
            log.info("开始保存交易信息，交易哈希: {}", transactionHash);

            // 交易哈希
            spTransactionRecords.setTxHash(transactionHash);

            // 区块号
            if (transactionReceipt.getBlockNumber() != null) {
                spTransactionRecords.setBlockNumber(transactionReceipt.getBlockNumber().longValue());
            }

            // 交易状态（status 为 "0x1" 表示成功，"0x0" 失败）
            spTransactionRecords.setStatus(
                    "0x1".equals(transactionReceipt.getStatus()) ? 1 : 0
            );

            // ============ 关键点 ============
            // TransactionReceipt 没有 from、to、value、gasPrice，需要再查一次原始交易
            EthTransaction ethTransaction;
            try {
                ethTransaction = BNBUtil.ethGetTransactionByHash(transactionHash);
            } catch (IOException e) {
                log.error("获取交易信息失败，交易哈希: {}", transactionHash, e);
                throw new ServiceException("获取交易信息失败");
            }

            Optional<Transaction> transaction = ethTransaction.getTransaction();
            if (!transaction.isPresent()) {
                log.error("交易不存在，交易哈希: {}", transactionHash);
                throw new ServiceException("交易不存在");
            }

            Transaction transactionInfo = transaction.get();

            // Gas 费用计算 = gasUsed * gasPrice
            BigInteger gasUsed = transactionReceipt.getGasUsed();
            BigInteger gasPrice = transactionInfo.getGasPrice();
            if (gasUsed != null && gasPrice != null) {
                // 转换为 BigDecimal，保持精度
                BigDecimal gasFee = new BigDecimal(gasUsed.multiply(gasPrice)).divide(new BigDecimal("1000000000000000000"), 18, RoundingMode.HALF_UP);
                spTransactionRecords.setGasFee(gasFee);
            }

            // 设置发送方和接收方地址
            spTransactionRecords.setFromAddress(transactionInfo.getFrom());
            spTransactionRecords.setToAddress(transactionInfo.getTo());

            // 转账金额（从 wei 转换为 ether）
            if (transactionInfo.getValue() != null) {
                BigDecimal amount = new BigDecimal(transactionInfo.getValue())
                        .divide(new BigDecimal("1000000000000000000"), 18, RoundingMode.HALF_UP);
                spTransactionRecords.setAmount(amount);
            }

            // 设置链ID
            spTransactionRecords.setChainId(Long.valueOf(chainId));

            // 主币交易 vs 代币交易判断
            if (transactionInfo.getTo() != null && transactionInfo.getInput() != null
                    && transactionInfo.getInput().length() > 2) {
                // 简单判断是否代币转账：input 非空
                spTransactionRecords.setTxType(2);
            } else {
                spTransactionRecords.setTxType(1);
            }

            // 获取交易时间
            if (transactionInfo.getBlockNumber() != null) {
                try {
                    EthBlock ethBlock = BNBUtil.ethGetBlockByNumber(transactionInfo.getBlockNumber(), false);
                    if (ethBlock != null && ethBlock.getBlock() != null) {
                        BigInteger timestamp = ethBlock.getBlock().getTimestamp();
                        String strTime = DateUtil.date(timestamp.multiply(new BigInteger("1000")).longValue())
                                .toString("yyyy-MM-dd HH:mm:ss");
                        spTransactionRecords.setTransactionTime(strTime);
                    }
                } catch (IOException e) {
                    log.warn("获取区块时间失败，使用当前时间，交易哈希: {}", transactionHash, e);
                }
            }

            // 符号和合约地址（根据业务需求设置）
            spTransactionRecords.setSymbol("SP");
            spTransactionRecords.setContractAddress(null);

            // 创建和更新时间
            Date nowDate = new Date();
            spTransactionRecords.setCreateTime(nowDate);
            spTransactionRecords.setUpdateTime(nowDate);

            // 保存到数据库
            int result = spTransactionRecordsMapper.insert(spTransactionRecords);
            if (result > 0) {
                log.info("交易信息保存成功，交易哈希: {}", transactionHash);
            } else {
                log.error("交易信息保存失败，交易哈希: {}", transactionHash);
                throw new ServiceException("交易信息保存失败");
            }

        } catch (ServiceException e) {
            // 重新抛出业务异常
            throw e;
        } catch (Exception e) {
            log.error("保存交易信息时发生未知错误", e);
            throw new ServiceException("保存交易信息失败: " + e.getMessage());
        }
    }

}
