package com.lj.auth.service;

import com.lj.auth.common.R;
import com.lj.auth.domain.InvoiceMessage;
import com.baomidou.mybatisplus.extension.service.IService;

public interface InvoiceMessageService extends IService<InvoiceMessage> {
    /**
     * 查询开票信息
     *
     * @param page 页
     * @param pageSize 页大小
     * @param type 主体类型 1-个人 2-企业 3-组织
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/28
     */
    R getPage(Integer type, Integer page, Integer pageSize);

    /**
     * 新增开票信息
     * 
     * @param invoiceMessage
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/28
     */
    R addService(InvoiceMessage invoiceMessage);

    /**
     * 修改开票信息
     * 
     * @param invoiceMessage
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/28
     */
    R updateService(InvoiceMessage invoiceMessage);

    /**
     * 删除开票信息
     * 
     * @param id id
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/28
     */
    R del(Long id);

    /**
     * 设置默认开票信息
     * 
     * @param id
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/28
     */
    R setDefault(Long id);
}
