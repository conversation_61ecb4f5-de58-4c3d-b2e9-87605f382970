package com.lj.auth.service;

import com.lj.auth.common.R;
import com.lj.auth.domain.RechargeAssets;
import com.baomidou.mybatisplus.extension.service.IService;

import java.math.BigDecimal;

public interface RechargeAssetsService extends IService<RechargeAssets> {

    /**
     * 校验资产余额
     *
     * @param amount 消耗金额
     * @return {@link RechargeAssets }
     * <AUTHOR>
     * @date 2024/04/02
     */
    RechargeAssets checkAssets(BigDecimal amount);

    /**
     * 扣除资产
     *
     * @param accountUuid 账户uuid
     * @param amount 金额
     * <AUTHOR>
     * @date 2024/04/02
     */
    void updateAssets(String accountUuid, BigDecimal amount);

    /**
     * 获取个人资金账户
     *
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/17
     */
    R getUserAssetService();

    /**
     * 增加冻结扣除余额
     *
     * @param amount 金额
     * @param accountUuid 用户uuid
     * <AUTHOR>
     * @date 2024/04/03
     */
    void addFreeze(BigDecimal amount, String accountUuid);
}
