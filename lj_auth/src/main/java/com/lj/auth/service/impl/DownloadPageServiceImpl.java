package com.lj.auth.service.impl;

import javax.annotation.Resource;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lj.auth.domain.About;
import com.lj.auth.domain.DownloadPage;
import com.lj.auth.mapper.AboutMapper;
import com.lj.auth.mapper.DownloadPageMapper;
import com.lj.auth.service.DownloadPageService;
import com.lj.auth.service.GlobalConfigService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import static com.lj.auth.common.CommonConstant.YL_UUID;

/**
 * <AUTHOR>
 * @Description
 * @date 2024/1/22 17:57
 */
@Service
public class DownloadPageServiceImpl extends ServiceImpl<DownloadPageMapper, DownloadPage>
    implements DownloadPageService {
    @Resource
    DownloadPageMapper downloadPageMapper;
    @Resource
    private GlobalConfigService globalConfigService;
    @Resource
    AboutMapper aboutMapper;
    @Value("${readImagepath}")
    private String readImagepath;

    @Override
    public DownloadPage get() {
        String operateUUID = globalConfigService.getGlobalConfig(YL_UUID);
        DownloadPage downloadPage = downloadPageMapper
            .selectOne(new LambdaQueryWrapper<DownloadPage>().eq(DownloadPage::getApplyType, 2));
        if (null == downloadPage) {
            return null;
        }
        About about =
            aboutMapper.selectOne(new LambdaQueryWrapper<About>().eq(About::getOperateUuid, operateUUID));
        if (null != about) {
            downloadPage.setBrowserIcon(readImagepath + about.getBrowserIcon());
        }
        return downloadPage;
    }
}
