package com.lj.auth.service.impl;

import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lj.auth.common.R;
import com.lj.auth.domain.Account;
import com.lj.auth.exception.ResponseEnum;
import com.lj.auth.service.AccountService;
import com.lj.auth.util.RegexUtil;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lj.auth.mapper.AccountCardMapper;
import com.lj.auth.domain.AccountCard;
import com.lj.auth.service.AccountCardService;

import javax.annotation.Resource;

@Service
public class AccountCardServiceImpl extends ServiceImpl<AccountCardMapper, AccountCard>
    implements AccountCardService {
    @Resource
    private AccountService accountService;

    /**
     * 添加银行卡
     * 
     * @param name 姓名
     * @param carNumber 卡号
     * @param depositBank 开户行
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/01
     */
    @Override
    public R addBankCardService(String name, String carNumber, String depositBank) {
        Account userInfo = accountService.getUserInfo();
        // 校验姓名
        Assert.isTrue(RegexUtil.isValidUsername(name), ResponseEnum.NameIsIllegal.getMsg());

        Integer carCount =
            this.count(Wrappers.<AccountCard>lambdaQuery().eq(AccountCard::getAccountUuid, userInfo.getUuid())
                .eq(AccountCard::getCardNumber, carNumber).eq(AccountCard::getState, true));

        Assert.equals(carCount, 0, ResponseEnum.SameCardNumber.getMsg());

        AccountCard accountCard = new AccountCard();
        accountCard.setAccountUuid(userInfo.getUuid());
        accountCard.setCardNumber(carNumber);
        accountCard.setName(name);
        accountCard.setBank(depositBank);
        this.save(accountCard);
        return R.ok();
    }

    /**
     * 编辑银行卡
     * 
     * @param id id
     * @param name 姓名
     * @param carNumber 卡号
     * @param depositBank 银行卡
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/01
     */
    @Override
    public R updateBankCardService(Integer id, String name, String carNumber, String depositBank) {
        Account userInfo = accountService.getUserInfo();
        AccountCard accountCard = this.getOne(
            Wrappers.<AccountCard>lambdaQuery().eq(AccountCard::getId, id).eq(AccountCard::getState, true));

        if (accountCard.getCardNumber().equals(carNumber)) {
            this.update(null, Wrappers.<AccountCard>lambdaUpdate().eq(AccountCard::getId, id)
                .set(AccountCard::getName, name).set(AccountCard::getBank, depositBank));
        } else {
            Integer carCount = this
                .count(Wrappers.<AccountCard>lambdaQuery().eq(AccountCard::getAccountUuid, userInfo.getUuid())
                    .eq(AccountCard::getCardNumber, carNumber).eq(AccountCard::getState, true));
            Assert.equals(carCount, 0, ResponseEnum.SameCardNumber.getMsg());

            this.update(null,
                Wrappers.<AccountCard>lambdaUpdate().eq(AccountCard::getId, id)
                    .set(AccountCard::getName, name).set(AccountCard::getCardNumber, carNumber)
                    .set(AccountCard::getBank, depositBank));
        }
        return R.ok();
    }

    /**
     * 获取银行卡列表
     * 
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/01
     */
    @Override
    public R getBankCardListService() {
        Account userInfo = accountService.getUserInfo();
        return R.okData(this.list(Wrappers.<AccountCard>lambdaQuery()
            .eq(AccountCard::getAccountUuid, userInfo.getUuid()).eq(AccountCard::getState, true)));
    }

    /**
     * 移除银行卡
     * 
     * @param id id
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/01
     */
    @Override
    public R deleteBandCardOfUserService(Integer id) {
        Account userInfo = accountService.getUserInfo();
        AccountCard accountCard = this
            .getOne(Wrappers.<AccountCard>lambdaQuery().eq(AccountCard::getAccountUuid, userInfo.getUuid())
                .eq(AccountCard::getId, id).eq(AccountCard::getState, true));

        Assert.notNull(accountCard, ResponseEnum.BankCardInformationDoesNotExist.getMsg());

        this.update(null, Wrappers.<AccountCard>lambdaUpdate().eq(AccountCard::getId, id)
            .set(AccountCard::getState, false));

        return R.ok();
    }

    /**
     * 获取银行卡详情
     * 
     * @param cardId
     * @return {@link AccountCard }
     * <AUTHOR>
     * @date 2024/04/03
     */
    @Override
    public AccountCard getCardDetail(Integer cardId) {
        return this.getById(cardId);
    }
}
