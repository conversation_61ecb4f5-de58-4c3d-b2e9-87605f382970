package com.lj.auth.service;

import com.lj.auth.common.R;
import com.lj.auth.domain.InvoiceAccount;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Date;

public interface InvoiceAccountService extends IService<InvoiceAccount> {

    /**
     * 申请开票
     * 
     * @param invoiceAccount
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/28
     */
    R addService(InvoiceAccount invoiceAccount);

    /**
     * 获取开票列表
     *
     * @param page 页
     * @param pageSize 页大小
     * @param accountUUID 用户uuid
     * @param ticketType 开票类型 1-增值税普通发票 2-增值税专用发票
     * @param examineState 审核状态 1-待审核 2-审核通过 3-审核驳回
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param ticketUuid 申请单号
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/28
     */
    R getPageService(Integer page, Integer pageSize, String accountUUID, Integer ticketType,
        Integer examineState, Date startTime, Date endTime, String ticketUuid);

    /**
     * 发票详情
     * 
     * @param id id
     * @param accountUUID 用户UUID
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/28
     */
    R getInfoService(Long id, String accountUUID);
}
