package com.lj.auth.service.impl;

import com.lj.auth.common.R;
import com.lj.auth.service.FileService;
import com.lj.auth.util.RegexUtil;
import com.lj.auth.util.UploadUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * @ClassName FileServiceImpl
 * @Description
 * @Authod yinlu
 * @Version 1.0
 **/
@Slf4j
@Service
public class FileServiceImpl implements FileService {

    @Value("${imagepath}")
    private String imagepath;// 图片上传路径

    /**
     * @param picture 图片
     * <AUTHOR>
     * @Description: 上传单张图片
     * @Return com.ylzh.common.entity.About
     **/
    @Override
    public R upload(MultipartFile picture, String folderName) {
        // 校验图片格式
        if (!RegexUtil.isImage(picture)) {
            return R.error("文件格式错误");
        }
        String picUrl = UploadUtils.upload(picture, imagepath, folderName);
        return R.okData(picUrl);
    }

    /**
     * @param pictures 图片
     * <AUTHOR>
     * @Description: 上传多张图片
     * @Return com.ylzh.common.entity.About
     **/
    @Override
    public R multiUpload(List<MultipartFile> pictures, String folderName) {
        for (MultipartFile picture : pictures) {
            // 校验图片格式
            if (!RegexUtil.isImage(picture)) {
                return R.error("文件格式错误");
            }
        }
        String picUrl = UploadUtils.uploads(pictures, imagepath, folderName);
        return R.okData(picUrl);
    }

}
