package com.lj.auth.service;

import com.lj.auth.common.R;
import com.lj.auth.domain.AccountCard;
import com.baomidou.mybatisplus.extension.service.IService;

public interface AccountCardService extends IService<AccountCard> {

    /**
     * 添加银行卡
     * 
     * @param name 姓名
     * @param carNumber 卡号
     * @param depositBank 开户行
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/01
     */
    R addBankCardService(String name, String carNumber, String depositBank);

    /**
     * 编辑银行卡
     * 
     * @param id id
     * @param name 姓名
     * @param carNumber 卡号
     * @param depositBank 开户行
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/01
     */
    R updateBankCardService(Integer id, String name, String carNumber, String depositBank);

    /**
     * 获取银行卡列表
     * 
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/01
     */
    R getBankCardListService();

    /**
     * 移除银行卡
     * 
     * @param id id
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/01
     */
    R deleteBandCardOfUserService(Integer id);

    /**
     * 获取银行卡详情
     * 
     * @param cardId 银行卡id
     * @return {@link AccountCard }
     * <AUTHOR>
     * @date 2024/04/03
     */
    AccountCard getCardDetail(Integer cardId);
}
