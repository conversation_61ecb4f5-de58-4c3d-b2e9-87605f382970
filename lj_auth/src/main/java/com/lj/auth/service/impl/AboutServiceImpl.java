package com.lj.auth.service.impl;
import java.util.Date;

import static com.lj.auth.common.CommonConstant.*;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.StrUtil;
import com.lj.auth.domain.*;
import com.lj.auth.mapper.*;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lj.auth.common.R;
import com.lj.auth.domain.vo.BaseImageVo;
import com.lj.auth.domain.vo.PosterVo;
import com.lj.auth.exception.ResponseEnum;
import com.lj.auth.service.AboutService;
import com.lj.auth.service.AccountService;
import com.lj.auth.service.GlobalConfigService;
import com.lj.auth.util.RedisUtils;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Assert;

@Service
public class AboutServiceImpl extends ServiceImpl<LjAboutMapper, LjAbout> implements AboutService {
    @Resource
    private RedisUtils redisUtils;
    @Resource
    private LjAboutMapper ljAboutMapper;
    @Resource
    private CustomerserviceMapper customerserviceMapper;
    @Resource
    private PosterMapper posterMapper;
    @Resource
    private AccountService accountService;
    @Resource
    private GlobalConfigService globalConfigService;

    @Resource
    private AgreementVersionMapper agreementVersionMapper;

    @Resource
    private InterfaceRecordMapper  interfaceRecordMapper;

    @Override
    public R commonConfig() {
        String commonConfigKey = "lj-commonConfig";
        LjAbout aboutInfo = redisUtils.get(commonConfigKey, LjAbout.class);
        if (aboutInfo == null) {
            List<LjAbout> aboutInfo1 = ljAboutMapper.selectList(null);
            aboutInfo = aboutInfo1.get(0);
            Assert.notNull(aboutInfo, ResponseEnum.CommonConfigurationMissing.getMsg());
            // 添加支付协议信息
            aboutInfo.setPayAgreement(getPaymentAgreement());
            redisUtils.set(commonConfigKey, aboutInfo, 20L);
        }
        return R.okData(aboutInfo);
    }

    /**
     * 获取最新的支付协议信息
     * 
     * @return
     */
    private String getPaymentAgreement() {
        String paymentAgreement = "";
        // 最新的版本信息
        AgreementVersion latestAgreementVersion = agreementVersionMapper.queryLatestAgreement(4);
        if (latestAgreementVersion != null) {
            paymentAgreement = latestAgreementVersion.getAgreementContent();
        }
        return paymentAgreement;
    }

    @Override
    public R getCustomerService() {
        return R.okData(customerserviceMapper.selectById(1));
    }

    /**
     * 获取邀请海报
     * 
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/18
     */
    @Override
    public R getPosterService() {
        Account userInfo = accountService.getUserInfo();
        // 邀请码
        String inviteCode = userInfo.getInviteCode();
        PosterVo posterVo = new PosterVo();
        List<Poster> posters = posterMapper.selectList(null);
        Poster poster = posters.get(0);
        BeanUtil.copyProperties(poster, posterVo);
        posterVo.setInviteCode(inviteCode);
        return R.okData(posterVo);
    }

    /**
     * 获取基础logo
     * 
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/25
     */
    @Override
    public R getLogoService() {
        List<BaseImageVo> baseImageVos = new ArrayList<>();

        String ljLogo = globalConfigService.getGlobalConfig(LJ_LOGO);
        String ljyl = globalConfigService.getGlobalConfig(LJ_Y_L);
        String ljchainLogo = globalConfigService.getGlobalConfig(LJ_Chain_logo);

        BaseImageVo imageVo = new BaseImageVo();
        imageVo.setUrl(ljLogo);
        imageVo.setName(LJ_LOGO);

        BaseImageVo imageVo1 = new BaseImageVo();
        imageVo1.setUrl(ljyl);
        imageVo1.setName(LJ_Y_L);
        BaseImageVo imageVo2 = new BaseImageVo();
        imageVo2.setUrl(ljchainLogo);
        imageVo2.setName(LJ_Chain_logo);

        baseImageVos.add(imageVo);
        baseImageVos.add(imageVo1);
        baseImageVos.add(imageVo2);

        return R.okData(baseImageVos);
    }

    /**
     * 增加接口记录
     * 
     * @param satoken
     * @param channel
     * @param edition
     * @param ip
     * @param path
     * @param requestParam
     */
    @Override
    public void addInterfaceRecordService(String satoken, String channel, String edition, String ip,
        String path, String requestParam) {
        InterfaceRecord interfaceRecord = new InterfaceRecord();
        interfaceRecord.setAccount(
            StrUtil.isNotBlank(satoken) ? StpUtil.getLoginIdByToken(satoken).toString() : satoken);
        interfaceRecord.setRequestIp(ip);
        interfaceRecord.setCreateTime(new Date());
        interfaceRecord.setPath(path);
        interfaceRecord.setRequestParam(requestParam);
        interfaceRecord.setChannel(channel);
        interfaceRecord.setEdition(edition);
        interfaceRecordMapper.insert(interfaceRecord);
    }
}
