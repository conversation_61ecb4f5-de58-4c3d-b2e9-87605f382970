package com.lj.auth.service;

import com.lj.auth.common.R;
import com.lj.auth.domain.SystemNotice;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lj.auth.domain.vo.DomainExperience;

public interface SystemNoticeService extends IService<SystemNotice> {

    /**
     * 消息中心分页查询
     *
     * @param page 页
     * @param pageSize 页大小
     * @param source 息来源:0-系统消息 1-系统公告2-转账通知
     * @param edition
     * @param channel
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/29
     */
    R getPage(Integer page, Integer pageSize, Integer source, String edition, String channel);

    /**
     * 全部已读
     * 
     * @param accountUuid 用户UUID
     * <AUTHOR>
     * @date 2024/03/29
     */
    void allRead(String accountUuid);

    /**
     * 已读
     *
     * @param id
     * @param state
     * <AUTHOR>
     * @date 2024/11/11
     */
    void isReadService(Integer id, Integer state);

    /**
     * 获取未读数量
     * 
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/29
     */
    R getUreadCountService(String edition, String channel);

    /**
     * 未读弹窗通知
     * 
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/29
     */
    R getLatestNoticeService(String edition, String channel);

    /**
     * 添加授权登录通知
     *
     * @param didSymbol-DID
     * @param title-标题
     * @param content-内容
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/06/27
     */
    R addloginNotificationService(String didSymbol, String title, String content);

    /**
     * 返回多条弹窗
     * 
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/11/12
     */
    R getLatestNoticeServiceV2(String edition, String channel);

    /**
     * 获取域名体验弹窗
     * 
     * @return {@link R }
     */
    R getDomainPopUpService();

    /**
     * 域名弹窗今日已读
     *
     * @param id
     * @return {@link R }
     */
    R getDomainPopUpIsReadService(Integer id);

    /**
     * 域名体验到期
     *
     * @param domainExperience
     * @return {@link R }
     */
    R domainExperienceExpiresService(DomainExperience domainExperience);
}
