package com.lj.auth.service.impl;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lj.auth.mapper.AccountInfoVoucherMapper;
import com.lj.auth.domain.AccountInfoVoucher;
import com.lj.auth.service.AccountInfoVoucherService;
/**
* <AUTHOR>
* @Description 
* @date 2024/4/26 16:04
*/
@Service
public class AccountInfoVoucherServiceImpl extends ServiceImpl<AccountInfoVoucherMapper, AccountInfoVoucher> implements AccountInfoVoucherService{

}
