package com.lj.auth.service.impl;
import java.util.Date;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSONArray;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lj.auth.common.CommonConstant;
import com.lj.auth.common.R;
import com.lj.auth.domain.*;
import com.lj.auth.domain.Result.FriendCheckItemResult;
import com.lj.auth.domain.resp.InviteCodeAccountResp;
import com.lj.auth.domain.vo.AccountVo;
import com.lj.auth.domain.vo.BadgeVo;
import com.lj.auth.domain.vo.RegexResult;
import com.lj.auth.domain.vo.RegionVo;
import com.lj.auth.exception.ResponseEnum;
import com.lj.auth.exception.ServiceException;
import com.lj.auth.feginClient.IDomainService;
import com.lj.auth.feginClient.ITXIMService;
import com.lj.auth.mapper.*;
import com.lj.auth.notify.sms.NotifyService;
import com.lj.auth.pay.AggregatePay.MiniPay.MiniPayUtil;
import com.lj.auth.service.AccountService;
import com.lj.auth.service.CommonAuthService;
import com.lj.auth.service.GlobalConfigService;
import com.lj.auth.task.CheckLogout;
import com.lj.auth.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

import static com.lj.auth.common.CommonConstant.*;

@Service
@Slf4j
public class AccountServiceImpl extends ServiceImpl<AccountMapper, Account> implements AccountService {
    private static final long TIMEOUT_DURATION = 10; // 设定超时时间，单位为秒

    @Value("${SMSOpen}")
    private Boolean SMSOpen;

    @Resource
    private RedisUtils redisUtils;

    @Resource
    private AccountMapper accountMapper;
    @Resource
    private NotifyService notifyService;
    @Resource
    private OperationRecordsMapper operationRecordsMapper;
    @Resource
    private OperateMapper operateMapper;
    @Resource
    private AboutMapper aboutMapper;
    @Resource
    private GlobalConfigService globalConfigService;
    @Resource
    private RechargeAssetsMapper rechargeAssetsMapper;
    @Resource
    private FusuiUtil fusuiUtil;
    @Resource
    private NftMapper nftMapper;
    @Resource
    private DomainMappingUtilParam domainMappingUtilParam;
    @Resource
    private AccountLogoutReasonMapper accountLogoutReasonMapper;
    @Resource
    private AccountLogoutMapper accountLogoutMapper;
    @Resource
    private TouristDevicePhoneMapper touristDevicePhoneMapper;
    @Resource
    private BlacklistMapper blacklistMapper;
    @Resource
    private AccountLogoutConditionRecordMapper accountLogoutConditionRecordMapper;
    @Resource
    private AccountLogoutConditionMapper accountLogoutConditionMapper;
    @Resource
    private OrderBsnInfoMapper orderBsnInfoMapper;
    @Resource
    private RechargeWithdrawRecordMapper rechargeWithdrawRecordMapper;
    @Resource
    private MovieOrderMapper movieOrderMapper;
    @Resource
    private FixDomainMapper fixDomainMapper;
    @Resource
    private AuctionDomainMapper auctionDomainMapper;
    @Resource
    private DomainAccountMapper domainAccountMapper;
    @Resource
    private AccountDidMapper accountDidMapper;
    @Resource
    private MiniPayUtil miniPayUtil;
    @Resource
    private FusuiWithdrawalMapper fusuiWithdrawalMapper;
    @Resource
    private IDomainService iDomainService;
    @Resource
    private DomainApplicationRecordMapper domainApplicationRecordMapper;
    @Resource
    private RechargeFlowMapper rechargeFlowMapper;
    @Resource
    @Lazy
    private CheckLogout checkLogout;

    @Resource
    private ITXIMService itximService;
    @Resource
    private NicknameLibraryMapper nicknameLibraryMapper;
    @Resource
    private AccountMergeMapper accountMergeMapper;
    @Resource
    private AccountMergeAssetsMapper accountMergeAssetsMapper;
    @Resource
    private FollowMapper followMapper;
    @Resource
    private SkySplitUserMapper  skySplitterMapper;
    @Resource
    private UserBadgeMapper userBadgeMapper;
    @Resource
    private BadgeMapper badgeMapper;
    @Resource
    private BadgeAndPendantHelper badgeAndPendantHelper;
    @Resource
    private CommonAuthService  commonAuthService;
    @Resource
    private AccountExpandMessageMapper accountExpandMessageMapper;
    @Resource
    private AccountTagSelectedMapper accountTagSelectedMapper;
    @Resource
    private RegionMapper regionMapper;
    @Resource
    private AccountTagTypeMapper accountTagTypeMapper;
    @Resource
    private AccountTagMapper accountTagMapper;

    /**
     * 请求短信验证码
     *
     * @param account 手机号码
     * @param state 1:注册 2:登录 3:不判断场景
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/27
     */
    @Override
    public R sentCaptcha(String account, Integer state) {
        // 判断是否是运营账号
        Account userInfo =
            accountMapper.selectOne(Wrappers.<Account>lambdaQuery().eq(Account::getPhoneNumber, account));
        if (userInfo != null) {
            Integer registrationSource = userInfo.getRegistrationSource();
            Integer blacklist = userInfo.getBlacklist();
            String phoneNumber = userInfo.getPhoneNumber();
            boolean mobileSimple = RegexUtil.isMobileSimple(phoneNumber);
            if (registrationSource == 0) {
                return R.error(ResponseEnum.OperationAccountSMSError);
            } else if (registrationSource == 4) {
                return R.error(ResponseEnum.CommunityOperationAccountError);
            }else  if (registrationSource == 3||(!mobileSimple)){
                return R.error(ResponseEnum.TouristAccountError);
            }
            if (blacklist == 3) {
                return R.error(ResponseEnum.TheAccountHasBeenCancelled);
            }
        }

        Assert.isTrue(RegexUtil.isMobileSimple(account), ResponseEnum.PhoneNumberFormatError.getMsg());

        String code = "123456";
        boolean validateResult = true;

        switch (state) {
            case 1: // 注册场景下判断
                // 判断注销账号是否满一年
                checkUserLogoutime(account);
                validateResult = handleRegistrationScenario(account);
                break;

            case 2: // 登录场景下判断
                validateResult = handleLoginScenario(account);
                break;

            case 3: // 不做处理
                break;

            default:
                return R.error(ResponseEnum.PARAM_ERROR);
        }

        if (SMSOpen && validateResult) {
            code = CharUtil.getRandomNum(6);
            validateResult = notifyService.sendSms(SMS_TYPE_VERIFICATION_CODE, account, code);
        }

        if (validateResult) {
            setVerificationCode(account, code);
        }

        return R.ok();
    }

    /**
     * 获取详细用户信息
     *
     * @return {@link AccountVo }
     * <AUTHOR>
     * @date 2024/04/08
     */
    @Override
    public AccountVo getAllUserInfo() {
        String accountUUID = StpUtil.getLoginId().toString();
        Account userInfo =
            accountMapper.selectOne(Wrappers.<Account>lambdaQuery().eq(Account::getUuid, accountUUID));
        AccountVo accountVo = new AccountVo();
        // 获取经销商信息
        Operate operate = operateMapper.selectOne(
            Wrappers.<Operate>lambdaQuery().eq(Operate::getAccountUuid, userInfo.getOperateUuid()));
        // 获取经销商信息
        About about = aboutMapper
            .selectOne(Wrappers.<About>lambdaQuery().eq(About::getOperateUuid, userInfo.getOperateUuid()));
        String browserPortalTitle = about.getBrowserPortalTitle();
        BeanUtils.copyProperties(userInfo, accountVo);
        accountVo.setOperate(operate);
        accountVo.setBrowserPortalTitle(browserPortalTitle);
        // 封装用户昵称和头像
        Integer showType = accountVo.getShowType();
        Integer headPortraitType = accountVo.getHeadPortraitType();
        if (ObjectUtil.equals(showType, 2)) {
            accountVo.setNickName(accountVo.getDomainNickName());
        }
        if (ObjectUtil.equals(headPortraitType, 2)) {
            // 获取Nft图像
            Long headPortraitNftId = accountVo.getHeadPortraitNftId();
            if (headPortraitNftId != null) {
                // 查询NFT图像
                Nft nft = nftMapper.selectOne(Wrappers.<Nft>lambdaQuery().eq(Nft::getId, headPortraitNftId)
                    .eq(Nft::getStatus, 1).eq(Nft::getIsDelete, false));
                if (nft != null) {
                    accountVo.setNft(nft);
                    accountVo.setHeadPortrait(nft.getNftImage());
                } else {
                    log.error("该用户NFT图像异常，用户：" + accountVo.getUuid());
                }

            } else {
                log.error("该用户NFT图像异常，用户：" + accountVo.getUuid());
            }
        }
        // 针对游客手机号至空
        if (accountVo.getPhoneNumber().contains("yk")) {
            accountVo.setPhoneNumber("");
        }
        accountVo.setDomainNickNameSignImage(globalConfigService.getGlobalConfig(DOMAINNICKNAMESIGNIMAGE));
        accountVo
            .setExitAmbassadorLevelPicture(globalConfigService.getGlobalConfig(EXITAMBASSADORLEVELPICTURE));
        // 获取徽章列表
        List<BadgeVo> badgeVos = userBadgeMapper.getUserBadge(userInfo.getUuid());
        accountVo.setBadgeVos(badgeVos);
        // 异步缓存用户信息
        try {
            commonAuthService.cacheUserInformation(userInfo);
        } catch (Exception e) {
            log.error("缓存用户信息异常：" + e.getMessage());
        }
        return accountVo;
    }


    /**
     * 内部使用
     *
     * @param uuid
     * @return {@link AccountVo }
     */
    @Override
    public AccountVo getAllUserInfo(String uuid) {
        Account userInfo =
                accountMapper.selectOne(Wrappers.<Account>lambdaQuery().eq(Account::getUuid, uuid));
        AccountVo accountVo = new AccountVo();
        // 获取经销商信息
        Operate operate = operateMapper.selectOne(
                Wrappers.<Operate>lambdaQuery().eq(Operate::getAccountUuid, userInfo.getOperateUuid()));
        // 获取经销商信息
        About about = aboutMapper
                .selectOne(Wrappers.<About>lambdaQuery().eq(About::getOperateUuid, userInfo.getOperateUuid()));
        String browserPortalTitle = about.getBrowserPortalTitle();
        BeanUtils.copyProperties(userInfo, accountVo);
        accountVo.setOperate(operate);
        accountVo.setBrowserPortalTitle(browserPortalTitle);
        // 封装用户昵称和头像
        Integer showType = accountVo.getShowType();
        Integer headPortraitType = accountVo.getHeadPortraitType();
        if (ObjectUtil.equals(showType, 2)) {
            accountVo.setNickName(accountVo.getDomainNickName());
        }
        if (ObjectUtil.equals(headPortraitType, 2)) {
            // 获取Nft图像
            Long headPortraitNftId = accountVo.getHeadPortraitNftId();
            if (headPortraitNftId != null) {
                // 查询NFT图像
                Nft nft = nftMapper.selectOne(Wrappers.<Nft>lambdaQuery().eq(Nft::getId, headPortraitNftId)
                        .eq(Nft::getStatus, 1).eq(Nft::getIsDelete, false));
                if (nft != null) {
                    accountVo.setHeadPortrait(nft.getNftImage());
                } else {
                    log.error("该用户NFT图像异常，用户：" + accountVo.getUuid());
                }

            } else {
                log.error("该用户NFT图像异常，用户：" + accountVo.getUuid());
            }
        }
        // 针对游客手机号至空
        if (accountVo.getPhoneNumber().contains("yk")) {
            accountVo.setPhoneNumber("");
        }
        accountVo.setDomainNickNameSignImage(globalConfigService.getGlobalConfig(DOMAINNICKNAMESIGNIMAGE));
        accountVo
                .setExitAmbassadorLevelPicture(globalConfigService.getGlobalConfig(EXITAMBASSADORLEVELPICTURE));
        // 获取徽章列表
        List<BadgeVo> badgeVos = new ArrayList<>();
        List<UserBadge> userBadges = userBadgeMapper
                .selectList(Wrappers.<UserBadge>lambdaQuery().eq(UserBadge::getAccountUuid, userInfo.getUuid())
                        .orderByDesc(UserBadge::getCreateTime).last("limit 3"));
        if (CollectionUtil.isNotEmpty(userBadges)) {
            // 获取徽章信息
            List<Badge> badges = badgeMapper
                    .selectBatchIds(userBadges.stream().map(UserBadge::getBadgeId).collect(Collectors.toList()));
            for (Badge badge : badges) {
                BadgeVo badgeVo = new BadgeVo();
                badgeVo.setBadgeId(badge.getId());
                badgeVo.setBadgeName(badge.getName());
                badgeVo.setBadgeType(badge.getType());
                badgeVo.setDefaultImage(badge.getDefaultImage());
                badgeVos.add(badgeVo);
            }
        }
        accountVo.setBadgeVos(badgeVos);
        return accountVo;
    }

    /**
     * 内部使用获取用户信息
     * 
     * @return {@link Account }
     * <AUTHOR>
     * @date 2024/04/08
     */
    @Override
    public Account getUserInfo() {
        String accountUUID = StpUtil.getLoginId().toString();
        Account userInfo =
            accountMapper.selectOne(Wrappers.<Account>lambdaQuery().eq(Account::getUuid, accountUUID));
        return userInfo;
    }

    /**
     * 验证码登录
     *
     * @param account 手机号
     * @param varificationCode 验证码
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/27
     */
    @Override
    @Transactional
    public R loginByCode(HttpServletRequest request, String account, String varificationCode) {
        String source = request.getHeader("platformSource");
        String itemSource = request.getHeader("itemSource");
        if(StrUtil.isBlank(source)){
            source="APP";
            itemSource="DID";
        }
        if (RegexUtil.isMobileSimple(account)) {
            handleLoginScenario(account);
        } else {
            return R.error(ResponseEnum.PhoneNumberFormatError);
        }
        // 查询用户信息
        Account userInfo =
            accountMapper.selectOne(Wrappers.<Account>lambdaQuery().eq(Account::getPhoneNumber, account));

        // 验证用户权限
        Integer blacklist = userInfo.getBlacklist();
        if (!blacklist.equals(CommonConstant.ACCOUNT_STATE_NORMAL)) {
            return R.error(ResponseEnum.AccountCannotLogInNormally);
        }

        // 判断验证码是否相等
        if (!verifyVerificationCode(account, varificationCode)) {
            return R.error(ResponseEnum.VerificationCodeError);
        }
        // 更新用户最近的登录信息
        userInfo.setRecentLoginIp(IpUtil.getRealIp(request));
        userInfo.setRecentLoginTime(new Date());
        if (!updateById(userInfo)) {
            return R.error(ResponseEnum.FailedToUpdateUserInformation);
        }
        StpUtil.login(userInfo.getUuid(),itemSource+source);
        redisUtils.set(StpUtil.getTokenValue(), userInfo.getDidSymbol(),StpUtil.getTokenTimeout());
        return R.okData(StpUtil.getTokenValue());
    }

    @Override
    public boolean verifyVerificationCode(String account, String verificationCode) {
        String operateUUID = YL_UUID;
        // 验证码redis的key
        String verificationCodeKey = "lj:verificationCodeKey:" + operateUUID + ":" + account;
        // 从缓存中获取验证码
        String cacheVerificationCode = (String)redisUtils.get(verificationCodeKey);
        // 校验验证码
        if (cacheVerificationCode != null && cacheVerificationCode.equals(verificationCode)) {
            // 清除 缓存验证码
            redisUtils.del(verificationCodeKey);
            return true;
        }
        return false;
    }

    /**
     * 设置图像
     *
     * @param portrait 图像
     * @param nftId nftId
     * @param type 图像类型 1-普通图像 2-nft图像
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/17
     */
    @Override
    @Transactional
    public R setPortrait(String portrait, Long nftId, Integer type) {

        Account userInfo = getUserInfo();
        // 图像类型 1-普通图像 2-nft图像
        Integer showType = userInfo.getHeadPortraitType();
        // 只修改类型
        if (type != null && !showType.equals(type)) {
            accountMapper.update(null, Wrappers.<Account>lambdaUpdate()
                .eq(Account::getUuid, userInfo.getUuid()).set(Account::getHeadPortraitType, type));
        }
        // 设置普通图像
        if (StrUtil.isNotBlank(portrait)) {
            String headPortrait = userInfo.getHeadPortrait();
            if (!portrait.equals(headPortrait)) {
                accountMapper.update(null, Wrappers.<Account>lambdaUpdate()
                    .eq(Account::getUuid, userInfo.getUuid()).set(Account::getHeadPortrait, portrait));
            }
        }
        // 设置NFT图像
        if (nftId != null) {
            if (userInfo.getHeadPortraitNftId() != null) {
                Long headPortraitNftId = userInfo.getHeadPortraitNftId();
                if (!headPortraitNftId.equals(nftId)) {
                    accountMapper.update(null, Wrappers.<Account>lambdaUpdate()
                        .eq(Account::getUuid, userInfo.getUuid()).set(Account::getHeadPortraitNftId, nftId));
                }
            } else {
                accountMapper.update(null, Wrappers.<Account>lambdaUpdate()
                    .eq(Account::getUuid, userInfo.getUuid()).set(Account::getHeadPortraitNftId, nftId));
            }
        }
        return R.ok();
    }

    @Async
    @Override
    public void syncImInfo(Account userInfo) {
        try {
            String syncImInfoSwitchStr = globalConfigService.getGlobalConfig(SYNC_IM_INFO_SWITCH);
            boolean syncImInfoSwitch = BooleanUtil.toBoolean(syncImInfoSwitchStr);
            if(!syncImInfoSwitch){
                log.info("同步开关关闭直接返回");
                return;
            }
            log.info("开始同步用户im信息:{}",userInfo.getUuid());
            String portraitUrl = "";
            String nickName = "";
            String accountUuid = userInfo.getUuid();
            String didSymbol = userInfo.getDidSymbol();
            if (StringUtils.isBlank(didSymbol)) {
                return;
            } else {
                //im状态 0:未同步 1:已同步
                Integer imStatus = userInfo.getImStatus();
                if (imStatus == 0) {
                    //此次会获取最新的用户信息导入到im
                    log.info("账户还未注册到im，直接同步到im:{}",accountUuid);
                    itximService.register(accountUuid);
                    return;
                }
            }
            Integer headPortraitType = userInfo.getHeadPortraitType();
            if (headPortraitType == 1) {
                portraitUrl = userInfo.getHeadPortrait();
            } else {
                Long headPortraitNftId = userInfo.getHeadPortraitNftId();
                if (headPortraitNftId != null) {
                    Nft nft = nftMapper.selectById(headPortraitNftId);
                    portraitUrl = nft.getNftImage();
                }
            }
            portraitUrl = BaseConversionUtils.parseImageUrl(portraitUrl);
            // 1-昵称展示 2-域名昵称展示
            Integer showType = userInfo.getShowType();
            if (showType == 1) {
                nickName = userInfo.getNickName();
            } else {
                nickName = userInfo.getDomainNickName();
            }
            log.info("修改账户信息到im:accountUuid:{},nickName:{},portraitUrl:{}",accountUuid,nickName,portraitUrl);
            itximService.portraitSet(accountUuid, nickName, portraitUrl);
        } catch (Exception e) {
            log.error("同步im信息失败:{}", e);
        }
    }


    /**
     * 设置支付密码
     *
     * @param account 手机号
     * @param varificationCode 验证码
     * @param payPassword 支付密码
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/27
     */
    @Override
    public R setPayPassword(String account, String varificationCode, String payPassword) {
        Account userInfo = getUserInfo();
        Assert.equals(userInfo.getPhoneNumber(), account, ResponseEnum.PhoneNumberMismatch.getMsg());

        Assert.isTrue(RegexUtil.isNumber(payPassword), ResponseEnum.PayPasswordRuleError.getMsg());

        Assert.isTrue(verifyVerificationCode(account, varificationCode),
            ResponseEnum.VerificationCodeError.getMsg());

        BCryptPasswordEncoder encoder = new BCryptPasswordEncoder();

        String encodePayPassword = encoder.encode(payPassword);
        accountMapper.update(null, Wrappers.<Account>lambdaUpdate().eq(Account::getUuid, userInfo.getUuid())
            .set(Account::getPayPassword, encodePayPassword));
        return R.ok();
    }

    /**
     * 修改密码
     * 
     * @param account 手机号
     * @param varificationCode 验证码
     * @param password 密码
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/28
     */
    @Override
    public R setPassword(String account, String varificationCode, String password) {
        // 校验手机号
        Account userInfo = getUserInfo();

        Assert.isTrue(verifyVerificationCode(account, varificationCode),
            ResponseEnum.VerificationCodeError.getMsg());

        Assert.equals(userInfo.getPhoneNumber(), account, ResponseEnum.PhoneNumberMismatch.getMsg());

        // 校验密码格式
        Assert.isTrue(RegexUtil.isPasswordValidWithAlphanumeric(password), ResponseEnum.PasswordRuleError.getMsg());

        BCryptPasswordEncoder encoder = new BCryptPasswordEncoder();
        // 修改密码
        accountMapper.update(null, Wrappers.<Account>lambdaUpdate().eq(Account::getPhoneNumber, account)
            .set(Account::getPassword, encoder.encode(password)));
        return R.ok();
    }

    /**
     * 忘记密码
     * 
     * @param account 手机号
     * @param varificationCode 验证码
     * @param password 密码
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/28
     */
    @Override
    public R forgetPassword(String account, String varificationCode, String password) {
        Assert.isTrue(verifyVerificationCode(account, varificationCode),
            ResponseEnum.VerificationCodeError.getMsg());

        // 校验密码格式
        Assert.isTrue(RegexUtil.isPasswordValidWithAlphanumeric(password), ResponseEnum.PasswordRuleError.getMsg());

        Account userInfo =
            accountMapper.selectOne(Wrappers.<Account>lambdaQuery().eq(Account::getPhoneNumber, account));

        Assert.notNull(userInfo, ResponseEnum.EnterTheCorrectPhoneNumber.getMsg());

        // 修改密码
        BCryptPasswordEncoder encoder = new BCryptPasswordEncoder();
        // 修改密码
        accountMapper.update(null, Wrappers.<Account>lambdaUpdate().eq(Account::getPhoneNumber, account)
            .set(Account::getPassword, encoder.encode(password)));
        return R.ok();
    }

    /**
     * 设置昵称
     * 
     * @param nickName 昵称
     * @param domainNickName 域名昵称
     * @param type 1-昵称展示2-域名昵称展示
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/28
     */
    @Override
    @Transactional
    public R setNickName(String nickName, String domainNickName, Integer type) {
        Account userInfo = getUserInfo();
        // 1-昵称展示 2-域名昵称展示
        Integer showType = userInfo.getShowType();
        // 只修改类型
        if (type != null && !showType.equals(type)) {
            accountMapper.update(null, Wrappers.<Account>lambdaUpdate()
                .eq(Account::getUuid, userInfo.getUuid()).set(Account::getShowType, type));
        }

        // 设置普通昵称
        if (StrUtil.isNotBlank(nickName)) {
            // 校验昵称的合法性 1-10长度 不能有特殊字符 暂不校验
            // boolean rightNickname = RegexUtil.isRightNickname(nickName);
            int length = nickName.length();
            if (length > 10) {
                return R.error(ResponseEnum.NicknameFormatError);
            }
            String name = userInfo.getNickName();
            if (!nickName.equals(name)) {
                // 校验普通昵称唯一性
                List<Account> accounts = accountMapper
                    .selectList(Wrappers.<Account>lambdaQuery().eq(Account::getNickName, nickName));
                if (accounts.size() > 0) {
                    return R.error(ResponseEnum.NicknameDuplication);
                }
                accountMapper.update(null, Wrappers.<Account>lambdaUpdate()
                    .eq(Account::getUuid, userInfo.getUuid()).set(Account::getNickName, nickName));
            }
        }
        // 设置域名昵称
        if (StrUtil.isNotBlank(domainNickName)) {
            if (userInfo.getDomainNickName() != null) {
                String name = userInfo.getDomainNickName();
                if (!domainNickName.equals(name)) {
                    // 解除原先域名绑定
                    boolean b1 = domainMappingUtilParam.clearBinad(name);
                    // 进行专属昵称绑定 成功之后再进行 更改
                    boolean b = domainMappingUtilParam.setTextNicekName(domainNickName, userInfo.getUuid());
                    if (b) {
                        accountMapper.update(null,
                            Wrappers.<Account>lambdaUpdate().eq(Account::getUuid, userInfo.getUuid())
                                .set(Account::getDomainNickName, domainNickName));
                        // 异步刷新挂件是否获取
                        try {
                            userInfo.setDomainNickName(domainNickName);
                            badgeAndPendantHelper.asyncRefreshBadgeAcquisition(userInfo);
                        } catch (Exception e) {
                            log.error("异步刷新徽章异常", e);
                        } ;
                    }
                }
            } else {
                // 进行专属昵称绑定 成功之后再进行 更改
                boolean b = domainMappingUtilParam.setTextNicekName(domainNickName, userInfo.getUuid());
                if (b) {
                    accountMapper.update(null,
                        Wrappers.<Account>lambdaUpdate().eq(Account::getUuid, userInfo.getUuid())
                            .set(Account::getDomainNickName, domainNickName));
                    // 异步刷新挂件是否获取
                    try {
                        userInfo.setDomainNickName(domainNickName);
                        badgeAndPendantHelper.asyncRefreshBadgeAcquisition(userInfo);
                    } catch (Exception e) {
                        log.error("异步刷新徽章异常", e);
                    } ;

                }
            }
        }

        return R.ok();
    }

    /**
     * 接口操作记录
     *
     * @param account 基础账号
     * @param realIp 最近登录IP
     * @param state 操作状态 0-失败 1-成功
     * @param requestParam 请求参数
     * @param requestInterface 请求接口
     * <AUTHOR>
     * @date 2024/03/28
     */
    @Override
    public void addAccountLoginInfo(String account, String realIp, int state, String requestParam,
        String requestInterface) {
        Date nowDate = new Date();
        OperationRecords operationRecords = new OperationRecords();
        operationRecords.setAccount(account);
        operationRecords.setLoginIp(realIp);
        operationRecords.setLoginTime(nowDate);
        operationRecords.setState(state);
        operationRecords.setRequestParam(requestParam);
        operationRecords.setRequestInterface(requestInterface);
        operationRecordsMapper.insert(operationRecords);
    }

    /**
     * 注册
     * 
     * @param request http请求
     * @param account 账户
     * @param varificationCode 验证码
     * @param password 密码
     * @param parentUUID 邀请人用户uuid
     * @param inviteCode 邀请码
     * @return
     */
    @Override
    @Transactional
    public R register(HttpServletRequest request, String account, String varificationCode, String password,
        String parentUUID, String inviteCode) {

        // 校验账号格式是否正确
        Assert.isTrue(RegexUtil.isMobileSimple(account), ResponseEnum.PhoneNumberFormatError.getMsg());

        // 校验密码格式
        RegexResult passwordValid = RegexUtil.isPasswordValid(password);
        Assert.isTrue(passwordValid.isResult(), passwordValid.getMsg());

        Account accountEntity = new Account();
        // 设置并校验邀请码信息
        String operateUUID = setInviteInfo(inviteCode, accountEntity);
        // 校验验证码
        Assert.isTrue(verifyVerificationCode(account, varificationCode),
            ResponseEnum.VerificationCodeError.getMsg());

        // 校验经销商信息
        Operate operateInfo = operateMapper
            .selectOne(Wrappers.<Operate>lambdaQuery().eq(Operate::getOpearteNumber, operateUUID));
        Assert.notNull(operateInfo, ResponseEnum.DealerDoesNotExist.getMsg());
        // 设置经销商uuid
        accountEntity.setOperateUuid(operateUUID);

        // 判断注册手机号信息
        validateRegistePhone(account, ResponseEnum.PleaseDoNotRegisterAgain.getMsg());

        // 设置手机号
        accountEntity.setPhoneNumber(account);
        // 设置注册来源
        accountEntity.setRegistrationSource(2);

        // 设置密码
        BCryptPasswordEncoder encoder = new BCryptPasswordEncoder();
        accountEntity.setPassword(encoder.encode(password));

        // 记录Ip地址和注册时间
        // 获取访问的ip
        String ipAddr = IpUtil.getRealIp(request);
        accountEntity.setRegisterIp(ipAddr);
        accountEntity.setRecentLoginIp(ipAddr);

        Date date = new Date();
        accountEntity.setRegisterTime(date);
        accountEntity.setRecentLoginTime(date);

        // 设置uuid
        setUUID(operateUUID, account, accountEntity);

        // 设置默认头像
        String defaultPortrait = globalConfigService.getGlobalConfig(DEFAULT_PORTRAIT);

        accountEntity.setHeadPortrait(defaultPortrait);
        // 设置默认昵称
        String defaultNickname = getDefaultNickname();
        accountEntity.setNickName(defaultNickname);

        // 生成邀请码
        // addInviteCode(accountEntity);

        // 保存账户信息
        accountMapper.insert(accountEntity);

        // 生成邀请码
        addInviteCode1(accountEntity);

        // 添加用户资产信息
        addRechargeAssetsInfo(accountEntity);
        // 添加登录信息
        // addAccountLoginInfo(account,ipAddr,1,"","注册");
        // 新用户--赠送优惠券
        // giftCoupon(accountEntity.getOperateUuid(),accountEntity.getUuid());

        return R.ok();
    }

    /**
     * 密码登录
     * 
     * @param request 请求
     * @param account 手机号
     * @param password 密码
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/08
     */
    @Override
    public R loginByPassword(HttpServletRequest request, String account, String password) {
        String source = request.getHeader("platformSource");
        String itemSource = request.getHeader("itemSource");
        if(StrUtil.isBlank(source)){
            source="APP";
            itemSource="DID";
        }
        Account accountEntity = null;
        ServletContext servletContext = request.getServletContext();
        String operateuuidAndPhone = null;
        if (RegexUtil.isMobileSimple(account)) {
            accountEntity =
                accountMapper.selectOne(Wrappers.<Account>lambdaQuery().eq(Account::getPhoneNumber, account));
            if (accountEntity == null) {
                return R.error(ResponseEnum.PhoneNumberDoesNotExist);
            }
            operateuuidAndPhone = accountEntity.getOperateUuid() + account;
            // 效验账号是否锁定
            if (!checkLock(servletContext, operateuuidAndPhone)) {
                return R.error(ResponseEnum.ThisAccountHasBeenLockedFor5Minutes);
            }
        } else {
            return R.error(ResponseEnum.PhoneNumberFormatError);
        }
        // 判断用户是否存在
        if (accountEntity == null) {
            return R.error(ResponseEnum.PhoneNumberDoesNotExist);
        } else {
            // 验证用户权限
            Integer blacklist = accountEntity.getBlacklist();
            if (!blacklist.equals(CommonConstant.ACCOUNT_STATE_NORMAL)) {
                return R.error(ResponseEnum.AccountCannotLogInNormally);
            }
        }
        String accountPassword = accountEntity.getPassword();
        Assert.notBlank(accountPassword, ResponseEnum.SetPassword.getMsg());
        // 加密的密码
        BCryptPasswordEncoder encoder = new BCryptPasswordEncoder();
        // 校验密码 password
        if (!encoder.matches(password, accountPassword)) {
            // 插入错误记录
            addFailNum(servletContext, operateuuidAndPhone);
            return R.error(ResponseEnum.AccountOrPasswordError);
        }
        // 更新用户最近的登录信息
        accountEntity.setRecentLoginIp(IpUtil.getRealIp(request));
        accountEntity.setRecentLoginTime(new Date());
        updateById(accountEntity);

        StpUtil.login(accountEntity.getUuid(),itemSource+source);
        String tokenValue = StpUtil.getTokenValue();
        // 登录成功,清空登录失败记录
        cleanFailNum(servletContext, operateuuidAndPhone);
        redisUtils.set(StpUtil.getTokenValue(), accountEntity.getDidSymbol(),StpUtil.getTokenTimeout());
        // 添加登录信息
        return R.okData(tokenValue);
    }

    /**
     * 仅限运营账号登录
     *
     * @param request
     * @param account
     * @param password
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/05/17
     */
    @Override
    public R loginByPasswordV1(HttpServletRequest request, String account, String password) {
        ServletContext servletContext = request.getServletContext();
        String operateuuidAndPhone = null;

        Account accountEntity =
            accountMapper.selectOne(Wrappers.<Account>lambdaQuery().eq(Account::getPhoneNumber, account));

        if (accountEntity == null) {
            return R.error(ResponseEnum.PhoneNumberDoesNotExist);
        } else {
            // 验证用户权限
            Integer blacklist = accountEntity.getBlacklist();
            if (!blacklist.equals(CommonConstant.ACCOUNT_STATE_NORMAL)) {
                return R.error(ResponseEnum.AccountCannotLogInNormally);
            }
        }
        // 判断用户是否是运营账号
        Integer registrationSource = accountEntity.getRegistrationSource();
        // 判断是否是运营账号
        if (registrationSource != 0) {
            return R.error(ResponseEnum.LoginOnlyForOperationalAccounts);
        }

        operateuuidAndPhone = accountEntity.getOperateUuid() + account;
        // 效验账号是否锁定
        if (!checkLock(servletContext, operateuuidAndPhone)) {
            return R.error(ResponseEnum.ThisAccountHasBeenLockedFor5Minutes);
        }

        String accountPassword = accountEntity.getPassword();
        Assert.notBlank(accountPassword, ResponseEnum.SetPassword.getMsg());
        // 加密的密码
        BCryptPasswordEncoder encoder = new BCryptPasswordEncoder();
        // 校验密码 password
        if (!encoder.matches(password, accountPassword)) {
            // 插入错误记录
            addFailNum(servletContext, operateuuidAndPhone);
            return R.error(ResponseEnum.AccountOrPasswordError);
        }
        // 更新用户最近的登录信息
        accountEntity.setRecentLoginIp(IpUtil.getRealIp(request));
        accountEntity.setRecentLoginTime(new Date());
        updateById(accountEntity);

        StpUtil.login(accountEntity.getUuid());
        String tokenValue = StpUtil.getTokenValue();
        // 登录成功,清空登录失败记录
        cleanFailNum(servletContext, operateuuidAndPhone);
        redisUtils.set(StpUtil.getTokenValue(), accountEntity.getDidSymbol(),StpUtil.getTokenTimeout());
        // 添加登录信息
        return R.okData(tokenValue);
    }

    /**
     * 运营账号和普通账号都能登录
     *
     * @param request
     * @param account
     * @param password
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/05/17
     */
    @Override
    public R loginByPasswordV2(HttpServletRequest request, String account, String password) {

        ServletContext servletContext = request.getServletContext();
        String operateuuidAndPhone = null;

        Account accountEntity =
            accountMapper.selectOne(Wrappers.<Account>lambdaQuery().eq(Account::getPhoneNumber, account));

        if (accountEntity == null) {
            return R.error(ResponseEnum.PhoneNumberDoesNotExist);
        } else {
            // 验证用户权限
            Integer blacklist = accountEntity.getBlacklist();
            if (!blacklist.equals(CommonConstant.ACCOUNT_STATE_NORMAL)) {
                return R.error(ResponseEnum.AccountCannotLogInNormally);
            }
        }

        operateuuidAndPhone = accountEntity.getOperateUuid() + account;
        // 效验账号是否锁定
        if (!checkLock(servletContext, operateuuidAndPhone)) {
            return R.error(ResponseEnum.ThisAccountHasBeenLockedFor5Minutes);
        }

        String accountPassword = accountEntity.getPassword();
        Assert.notBlank(accountPassword, ResponseEnum.SetPassword.getMsg());
        // 加密的密码
        BCryptPasswordEncoder encoder = new BCryptPasswordEncoder();
        // 校验密码 password
        if (!encoder.matches(password, accountPassword)) {
            // 插入错误记录
            addFailNum(servletContext, operateuuidAndPhone);
            return R.error(ResponseEnum.AccountOrPasswordError);
        }
        // 更新用户最近的登录信息
        accountEntity.setRecentLoginIp(IpUtil.getRealIp(request));
        accountEntity.setRecentLoginTime(new Date());
        updateById(accountEntity);

        StpUtil.login(accountEntity.getUuid());
        String tokenValue = StpUtil.getTokenValue();
        // 登录成功,清空登录失败记录
        cleanFailNum(servletContext, operateuuidAndPhone);
        redisUtils.set(StpUtil.getTokenValue(), accountEntity.getDidSymbol(),StpUtil.getTokenTimeout());
        // 添加登录信息
        return R.okData(tokenValue);
    }

    @Override
    public R getPhoneAllUserInfo(String phone) {
        String accountUUID = StpUtil.getLoginId().toString();

        Account userInfo =
            accountMapper.selectOne(Wrappers.<Account>lambdaQuery().eq(Account::getPhoneNumber, phone));

        AccountVo accountVo = new AccountVo();

        // 获取经销商信息
        About about = aboutMapper
            .selectOne(Wrappers.<About>lambdaQuery().eq(About::getOperateUuid, userInfo.getOperateUuid()));
        String browserPortalTitle = about.getBrowserPortalTitle();
        // 获取经销商信息
        Operate operate = operateMapper.selectOne(
            Wrappers.<Operate>lambdaQuery().eq(Operate::getAccountUuid, userInfo.getOperateUuid()));

        BeanUtils.copyProperties(userInfo, accountVo);
        accountVo.setOperate(operate);
        accountVo.setBrowserPortalTitle(browserPortalTitle);
        return R.okData(accountVo);
    }

    /**
     * 注册协议
     *
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/10
     */
    @Override
    public R getUserAgreementService() {
        String registration = globalConfigService.getGlobalConfig(REGISTRATION_AGREEMENT);
        String userAgrement = globalConfigService.getGlobalConfig(USER_AGREEMENT);
        String privateAgrement = globalConfigService.getGlobalConfig(PRIVACY_AGREEMENT);
        String didLogin = globalConfigService.getGlobalConfig(DID_LOGIN_INSTRUCTIONS);
        Map<String, String> result = new HashMap<>();
        result.put("registration", registration);
        result.put("userAgrement", userAgrement);
        result.put("privateAgrement", privateAgrement);
        result.put("didLogin", didLogin);
        return R.okData(result);
    }

@Override
public R myPromotionService(int page, int pageSize) {
    Map<String, Object> result = new HashMap<>();
    Account userInfo = getUserInfo();
    String inviteCode = userInfo.getInviteCode();
    String accountUuid = userInfo.getUuid();
    Date userSkyTime = null;
    Integer skySplitLevel = userInfo.getSkySplitLevel();
    if (skySplitLevel != null && skySplitLevel > 0) {
        // 用户是裂空者 查询成为裂空者时间
        SkySplitUser skySplitUser = skySplitterMapper
            .selectOne(Wrappers.<SkySplitUser>lambdaQuery().eq(SkySplitUser::getAccountUuid, accountUuid));
        if (skySplitUser == null) {
            log.error("该账户裂空者信息异常");
            return R.okData(result);
        } else {
            userSkyTime = skySplitUser.getCreateTime();
        }
    }
    Page<AccountVo> accountPage = accountMapper.pageQueryParentUUID(new Page<>(page, pageSize), accountUuid);
    long total = accountPage.getTotal();
    // 查询DID用户和裂空者用户
    Integer didCount = accountMapper.selectDidCount(accountUuid, "1", null);
    Integer skyCount = accountMapper.selectDidCount(accountUuid, null, "1");

    // 查询关注我的好友集合
    List<Follow> follows = followMapper.selectList(
        Wrappers.<Follow>lambdaQuery().eq(Follow::getFollowUuid, accountUuid).eq(Follow::getRemoveFlag, 0));
    // 获取关注用户的uuid集合
    List<String> followUuidList = follows.stream().map(Follow::getAccountUuid).collect(Collectors.toList());

    // 校验im好友绑定关系
    try {
        if (total > 0) {
            Map<String, FriendCheckItemResult> friendCheckMap = new HashMap<>();
            List<AccountVo> records = accountPage.getRecords();
            List<String> toAccountIdList = new ArrayList<>();
            for (AccountVo account : records) {
                // 封装关注关系排除自己
                if (followUuidList.contains(account.getUuid())
                        && !accountUuid.equals(account.getUuid())) {
                    account.setIsFollow(1);
                }
                // 封装用户昵称和头像
                Integer showType = account.getShowType();
                Integer headPortraitType = account.getHeadPortraitType();
                if (ObjectUtil.equals(showType, 2)) {
                    account.setNickName(account.getDomainNickName());
                }
                if (ObjectUtil.equals(headPortraitType, 2)) {
                    // 获取Nft图像
                    Long headPortraitNftId = account.getHeadPortraitNftId();
                    if (headPortraitNftId != null) {
                        // 查询NFT图像
                        Nft nft =
                            nftMapper.selectOne(Wrappers.<Nft>lambdaQuery().eq(Nft::getId, headPortraitNftId)
                                .eq(Nft::getStatus, 1).eq(Nft::getIsDelete, false));
                        if (nft != null) {
                            account.setHeadPortrait(nft.getNftImage());
                        } else {
                            log.error("该用户NFT图像异常，用户：" + account.getUuid());
                        }

                    } else {
                        log.error("该用户NFT图像异常，用户：" + account.getUuid());
                    }
                }
                Integer tgSkySplitLevel = account.getSkySplitLevel();
                String didSymbol = account.getDidSymbol();
                // DID不为空 即是 DID用户
                if (!StringUtils.isEmpty(didSymbol)) {
                    toAccountIdList.add(account.getUuid());
                    // 封装有DID标识图片
                    account.setTgDidSignImage(globalConfigService.getGlobalConfig(TG_DID_SIGN_IMAGE));
                } else {
                    // 封装无DID标识图片
                    account.setTgDidSignImage(globalConfigService.getGlobalConfig(TG_DID_SIGN_IMAGE_H));
                }
                // 判断 用户是否是裂空者
                if (skySplitLevel != null && skySplitLevel > 0) {
                    // 用户是裂空者
                    // 判断推广用户 是否是裂空者
                    if (tgSkySplitLevel != null && tgSkySplitLevel > 0) {
                        // 推广用户是裂空者
                        // 获取推广用户成为裂空者时间
                        SkySplitUser skySplitUser = skySplitterMapper.selectOne(Wrappers
                            .<SkySplitUser>lambdaQuery().eq(SkySplitUser::getAccountUuid, account.getUuid()));
                        if (skySplitUser != null) {
                            Date tgSkyTime = skySplitUser.getCreateTime();
                            // 判断成为裂空者的时间和用户成为裂空者的时间
                            if (DateUtil.compare(tgSkyTime, userSkyTime) > 0) {
                                // 推广用户成为裂空者时间大于用户成为裂空者时间--有效裂空者
                                account.setTgLkzSignImage(
                                    globalConfigService.getGlobalConfig(TG_LKZ_SIGN_IMAGE));
                            } else if (DateUtil.compare(tgSkyTime, userSkyTime) < 0) {
                                // 推广用户成为裂空者时间小于用户成为裂空者时间--无效裂空者
                                account.setLkzImageState(0);
                                account.setLkzCreateTime(tgSkyTime);
                                account.setLkzPrompt(globalConfigService.getGlobalConfig(TG_LKZ_PROMPT));
                                account.setTgLkzSignImage(
                                    globalConfigService.getGlobalConfig(TG_LKZ_SIGN_IMAGE_W));
                            }
                        } else {
                            log.error("推广用户裂空者信息异常，账号：" + account.getUuid());
                        }
                    }
                } else {
                    // 用户不用裂空者
                    // 判断推广用户是否是裂空者
                    if (tgSkySplitLevel != null && tgSkySplitLevel > 0) {
                        // 推广用户是裂空者
                        // 获取推广用户成为裂空者时间
                        Account account1 = accountMapper.selectOne(
                                Wrappers.<Account>lambdaQuery().eq(Account::getUuid, account.getUuid()));
                        if (account1 != null) {
                            Date tgSkyTime = account1.getCreateTime();
                            account.setLkzImageState(0);
                            account.setLkzCreateTime(tgSkyTime);
                            account.setLkzPrompt(globalConfigService.getGlobalConfig(TG_LKZ_PROMPT));
                            account.setTgLkzSignImage(globalConfigService.getGlobalConfig(TG_LKZ_SIGN_IMAGE_W));
                        }else {
                            log.error("推广用户裂空者信息异常，账号：" + account.getUuid());
                        }

                    }
                }
            }
            if (toAccountIdList.size() > 0) {
                R friendCheckResult = itximService.friendCheck(accountUuid, toAccountIdList);
                if ((Integer)friendCheckResult.get("code") == 200) {
                    String friendCheckResultStr = (String)friendCheckResult.get("data");
                    JSONObject accountCheckResultJSON = JSONObject.parseObject(friendCheckResultStr);
                    log.info("校验好友信息返回结果:{}", accountCheckResultJSON);
                    com.alibaba.fastjson.JSONArray infoItem = accountCheckResultJSON.getJSONArray("InfoItem");
                    List<FriendCheckItemResult> friendCheckItemResultList =
                        infoItem.toJavaList(FriendCheckItemResult.class);
                    friendCheckMap = friendCheckItemResultList.stream().collect(
                        Collectors.toMap(FriendCheckItemResult::getTo_Account, friend -> friend, (a, b) -> {
                            throw new IllegalStateException("Duplicate to_account found");
                        }));
                    for (AccountVo account : records) {
                        String promotionAccountUUID = converAccountUUID2IMAccountId(account.getUuid());
                        if (friendCheckMap.containsKey(promotionAccountUUID)) {
                            FriendCheckItemResult friendCheckItemResult =
                                friendCheckMap.get(promotionAccountUUID);
                            String resultInfo = friendCheckItemResult.getResultInfo();
                            String relation = friendCheckItemResult.getRelation();
                            if (ObjectUtil.equals("CheckResult_Type_NoRelation", relation)) {
                                if (ObjectUtil.equals("Err_SNS_Invalid_To_Account", resultInfo)) {
                                    account.setAvaliableAddFriend(2);
                                } else {
                                    account.setAvaliableAddFriend(1);
                                }
                            } else {
                                account.setAvaliableAddFriend(0);
                            }
                        }
                    }
                }
            }
        }
    } catch (Exception e) {
        log.error("获取im好友关系异常 error", e);
    }
    result.put("count", total);
    result.put("didCount", didCount);
    result.put("skyCount", skyCount);
    result.put("invite_code", inviteCode);
    result.put("list", accountPage);
    return R.okData(result);
}



    /**
     * 将账户UUID转换为IM的账号ID
     * @param paramAccountUUID
     * @return
     */
    public String converAccountUUID2IMAccountId(String paramAccountUUID){
        String accountUUID=paramAccountUUID;
        String ymlActive = PropertiesRead.getYmlActive();
        if(!"prod".equals(ymlActive)){
            if(!paramAccountUUID.startsWith("test_")){
                accountUUID="test_"+paramAccountUUID;
            }
        }
        return accountUUID;
    }

    /**
     * 福穗签约
     * 
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/11
     */
    @Override
    public R uploadPeopleService() {

        Account account = getUserInfo();
        // Integer isRealName = account.getIsRealName();
        // // String didSymbol = account.getDidSymbol();
        // if (isRealName != 2) {
        // // 未实名认证
        // return R.error(ResponseEnum.UnrecognizedName);
        // }
        Integer signStatus1 = account.getSignStatus();
        com.alibaba.fastjson2.JSONObject jsonObject =
            fusuiUtil.uploadPeople(account.getRealName(), account.getPhoneNumber(), account.getIdCard());
        System.out.println("签约返回信息： " + jsonObject);
        Integer code = jsonObject.getInteger("code");
        if (code == 0) {
            com.alibaba.fastjson2.JSONObject data = jsonObject.getJSONObject("data");
            Integer signStatus = data.getInteger("signStatus");
            if (signStatus == 2 || signStatus == 3) {
                account.setPeopleId(data.getString("peopleId"));
                account.setSignUrl(data.getString("signUrl"));
            }
            if(!ObjectUtil.equals(signStatus1,3)){
                account.setSignStatus(signStatus);
            }
            accountMapper.updateById(account);
            return R.okData(data);
        } else {
            // 通过身份证号查询用户是否已签约
            com.alibaba.fastjson2.JSONObject jsonObject1 =
                fusuiUtil.queryPeopleByNameAndIdCardAndTaxID(account.getRealName(), account.getIdCard());
            System.out.println("签约身份信息确认： " + jsonObject1);
            Integer code1 = jsonObject1.getInteger("code");
            if (code1 == 0) {
                // com.alibaba.fastjson2.JSONObject data1 = jsonObject1.getJSONObject("data");
                com.alibaba.fastjson2.JSONObject data1 = jsonObject1.getJSONArray("data").getJSONObject(0);
                Integer signStatus = data1.getInteger("signStatus");
                if (signStatus == 2 || signStatus == 3) {
                    account.setPeopleId(data1.getString("peopleId"));
                    account.setSignUrl(data1.getString("signUrl"));
                }
                if(!ObjectUtil.equals(signStatus1,3)){
                    account.setSignStatus(signStatus);
                }
                accountMapper.updateById(account);
                return R.okData(data1);
            }
            String msg = jsonObject1.getString("msg");
            return R.error(msg);
        }
    }

    /**
     * 福穗签约
     *
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/11
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R uploadPeopleService(String name,String idCard) {
        // 实名身份信息
        // Boolean idcardCheckResult = IdcardcheckUtils.idcardcheck2(idCardCheckAPPId, name, idCard);
        // if (!idcardCheckResult) {
        // return R.error("请填写真实有效的身份信息");
        // }
        Account account = getUserInfo();
        Integer signStatus1 = account.getSignStatus();
        // Integer isRealName = account.getIsRealName();
        //校验用户手机号是否合法
        if (!RegexUtil.isMobileSimple(account.getPhoneNumber())) {
            return R.error(ResponseEnum.PhoneNumberFormatErrorBinding);
        }

        com.alibaba.fastjson2.JSONObject jsonObject =
            fusuiUtil.uploadPeople(name, account.getPhoneNumber(), idCard);
        System.out.println("签约返回信息： " + jsonObject);
        Integer code = jsonObject.getInteger("code");
        if (code == 0) {
            com.alibaba.fastjson2.JSONObject data = jsonObject.getJSONObject("data");
            Integer signStatus = data.getInteger("signStatus");
            if (signStatus == 2 || signStatus == 3) {
                account.setPeopleId(data.getString("peopleId"));
                account.setSignUrl(data.getString("signUrl"));
            }
            if(!ObjectUtil.equals(signStatus1,3)){
                account.setSignStatus(signStatus);
            }
            accountMapper.updateById(account);
            //插入签约用户信息
            fusuiWithdrawalMapper.insert(FusuiWithdrawal.builder()
                .uuid(account.getUuid())
                .phoneNumber(account.getPhoneNumber())
                .idCard(idCard)
                .realName(name)
                .build());
            return R.okData(data);
        } else {
            // 通过身份证号查询用户是否已签约
            com.alibaba.fastjson2.JSONObject jsonObject1 =
                fusuiUtil.queryPeopleByNameAndIdCardAndTaxID(name, idCard);
            System.out.println("签约身份信息确认： " + jsonObject1);
            Integer code1 = jsonObject1.getInteger("code");
            if (code1 == 0) {
                //判断 data 是否为空
                JSONArray data = jsonObject1.getJSONArray("data");
                if (data.isEmpty()) {
                    String msg = jsonObject.getString("msg");
                    return R.error(msg);
                }
                // com.alibaba.fastjson2.JSONObject data1 = jsonObject1.getJSONObject("data");
                com.alibaba.fastjson2.JSONObject data1 = jsonObject1.getJSONArray("data").getJSONObject(0);
                Integer signStatus = data1.getInteger("signStatus");
                if (signStatus == 2 || signStatus == 3) {
                    account.setPeopleId(data1.getString("peopleId"));
                    account.setSignUrl(data1.getString("signUrl"));
                }
                if(!ObjectUtil.equals(signStatus1,3)){
                    account.setSignStatus(signStatus);
                }
                accountMapper.updateById(account);
                return R.okData(data1);
            }
            String msg = jsonObject1.getString("msg");
            return R.error(msg);
        }
    }

    /**
     * 游客模式注册并登录
     *
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/06/12
     */
    @Override
    @Transactional
    public R touristService(String deviceId,  String appId,Integer type) {
        String token ="";
        // 小程序查询openId做为机器码
        if (type == 2) {
            Map map = JSON.parseObject(miniPayUtil.getOpenIdTourist(appId,deviceId).toString(), Map.class);
            deviceId = map.get(("openid")).toString();
            log.info("微信openId:{}", deviceId);
        }
        // 查询机器码是否生成过手机号
        TouristDevicePhone touristDevicePhone = touristDevicePhoneMapper
            .selectOne(Wrappers.<TouristDevicePhone>lambdaQuery().eq(TouristDevicePhone::getMeid, deviceId));
        if (touristDevicePhone == null) {
            // 不存在 生成 新的手机号 建立时间戳当手机号
            long timeStamp = (new Date()).getTime();
            String phoneNumber = "yk" + String.valueOf(timeStamp);
            TouristDevicePhone newTourist = new TouristDevicePhone();
            newTourist.setMeid(deviceId).setPhoneNumber(phoneNumber);
            newTourist.setSource(type);
            touristDevicePhoneMapper.insert(newTourist);
            // 注册新账号
            token = registerAndLogin(HeadUtil.getRequest(), phoneNumber, null);
        } else {
            // 不为空 登录
            token = loginByPasswordAndtourist(HeadUtil.getRequest(), touristDevicePhone.getPhoneNumber());
        }
        return R.okData(token);
    }

    /**
     * 账号注销协议
     *
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/06/14
     */
    @Override
    public R cancellationAgreementService() {
        Map<String, Object> result = new HashMap<>();
        // 账号注销协议
        String accountCancellationAgreement =
            globalConfigService.getGlobalConfig(ACCOUNT_CANCELLATION_AGREEMENT);
        // 账号注销条件
        String accountCancellationConditions =
            globalConfigService.getGlobalConfig(ACCOUNT_CANCELLATION_CONDITIONS);
        // 注销风险
        String accountCancellationRisk = globalConfigService.getGlobalConfig(ACCOUNT_CANCELLATION_RISK);
        // 注销数据提示
        String unregisterDataProcessingPrompt =
            globalConfigService.getGlobalConfig(unregister_DataProcessing_Prompt);

        List<AccountLogoutReason> accountLogoutReasons = accountLogoutReasonMapper.selectList(
            Wrappers.<AccountLogoutReason>lambdaQuery().orderByDesc(AccountLogoutReason::getNumber));
        result.put("accountCancellationAgreement", accountCancellationAgreement);
        result.put("accountCancellationConditions", accountCancellationConditions);
        result.put("accountCancellationRisk", accountCancellationRisk);
        result.put("unregisterDataProcessingPrompt", unregisterDataProcessingPrompt);
        result.put("accountLogoutReasons", accountLogoutReasons);
        return R.okData(result);
    }

    /**
     * 注销账号
     *
     * @param ids
     * @param reason
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/06/14
     */
    @Transactional
    @Override
    public R accountCancellationService(List<Integer> ids, String reason) {
        AccountLogout accountLogout = new AccountLogout();
        Account account = getUserInfo();

        // 如果是经销商也不允许注销
        Integer identity = account.getIdentity();
        if (identity != 1) {
            throw new ServiceException(ResponseEnum.NonOrdinaryUsers);
        }

        // 检查各项列表
        checkCancellationConditions(account.getUuid());

        // 如果存在未处理的不允许注销
        // List<AccountLogoutConditionRecord> accountLogoutConditionRecords =
        // accountLogoutConditionRecordMapper.selectList(Wrappers.<AccountLogoutConditionRecord>lambdaQuery()
        // .eq(AccountLogoutConditionRecord::getAccountUuid, account.getUuid())
        // .eq(AccountLogoutConditionRecord::getIsIgnore, 0));
        // if (CollectionUtil.isNotEmpty(accountLogoutConditionRecords)) {
        // throw new ServiceException(ResponseEnum.AllCancellationConditions);
        // }
        // 注销账号统一把注销条件改为已忽略
        accountLogoutConditionRecordMapper.update(null,
            Wrappers.<AccountLogoutConditionRecord>lambdaUpdate()
                .eq(AccountLogoutConditionRecord::getAccountUuid, accountLogout.getUuid())
                .set(AccountLogoutConditionRecord::getIsIgnore, 1));

        // 获取注销图像
        String defaultPortrait = globalConfigService.getGlobalConfig(UNREGISTER_IMAGE);
        // 账户表注销该用户==>修改图像为默认图像或者注销图像==>昵称设为注销昵称
        accountMapper.update(null,
            Wrappers.<Account>lambdaUpdate().eq(Account::getId, account.getId())
                .set(Account::getHeadPortrait, defaultPortrait).set(Account::getHeadPortraitType, 1)
                .set(Account::getNickName, getDefaultNicknameV2()).set(Account::getShowType, 1)
                .set(Account::getBlacklist, 3));
        // 设置DID不能登录
        AccountDid accountDid = accountDidMapper
            .selectOne(Wrappers.<AccountDid>lambdaQuery().eq(AccountDid::getAccountUuid, account.getUuid()));
        if (accountDid != null) {
            accountDid.setDidLoginState(2);
            accountDid.setDomainLoginState(2);
            accountDidMapper.updateById(accountDid);
        }

        String idsStr = ids.stream().map(Object::toString) // 将Integer转换为String
            .collect(Collectors.joining(",")); // 使用逗号连接字符串
        BeanUtils.copyProperties(account, accountLogout);
        accountLogout.setReasonId(idsStr);
        accountLogout.setReasonSelf(reason);
        accountLogout.setLogoutTime(new Date());
        accountLogoutMapper.insert(accountLogout);
        // 注销同时推出该账号
        StpUtil.logout();
        return R.ok();
    }

    /**
     * 注册判断手机号是否注册
     *
     * @param account -手机号
     * @return boolean
     * <AUTHOR>
     * @date 2024/03/27
     */
    @Override
    public boolean handleRegistrationScenario(String account) {
        Boolean phoneNumberIsExists = accountMapper.phoneNumberIsExists(account);
        Assert.isFalse(phoneNumberIsExists, ResponseEnum.PhoneNumberAlreadyExists.getMsg());
        return true;
    }

    @Override
    @Transactional
    public R accountDetectionService() {
        Account account = getUserInfo();
        // 检查各项注销条件状态
        checkCancellationConditions(account.getUuid());
        // 返回列表
        List<AccountLogoutCondition> result =
            accountLogoutConditionMapper.seleLogoutConditionRecords(account.getUuid());
        return R.okData(result);
    }

    /**
     * 注销提醒条件操作
     *
     * @param id-条件id
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/06/19
     */
    @Override
    public R conditionalOperationService(Integer id) {
        Account account = getUserInfo();
        AccountLogoutConditionRecord accountLogoutConditionRecord =
            accountLogoutConditionRecordMapper.selectOne(Wrappers.<AccountLogoutConditionRecord>lambdaQuery()
                .eq(AccountLogoutConditionRecord::getAccountUuid, account.getUuid())
                .eq(AccountLogoutConditionRecord::getConditionId, id));
        Integer isIgnore = accountLogoutConditionRecord.getIsIgnore();
        if (isIgnore == 0 && id != 1) {
            accountLogoutConditionRecord.setIsIgnore(1);
            accountLogoutConditionRecordMapper.updateById(accountLogoutConditionRecord);
        }
        return R.ok();
    }

    /**
     * 修改手机号
     *
     * @param account
     * @param varificationCode
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/09/05
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R setPhoneNumberService(String account, String varificationCode) {
        // 校验手机号
        Account userInfo = getUserInfo();
        Assert.isTrue(verifyVerificationCode(account, varificationCode),
            ResponseEnum.VerificationCodeError.getMsg());
        if (ObjectUtil.equals(account, userInfo.getPhoneNumber())) {
            return R.ok();
        }
        // 判断手机号是否是游客手机号
        String phoneNumber = userInfo.getPhoneNumber();

        boolean mobileSimple = RegexUtil.isMobileSimple(phoneNumber);
        // 校验手机号是否重复

        Boolean b = accountMapper.phoneNumberIsExists(account);

         //不是游客账号
        if (mobileSimple) {
            //手机号不存在
            if (!b) {
                // 修改手机号
                accountMapper.update(null, Wrappers.<Account>lambdaUpdate()
                    .eq(Account::getId, userInfo.getId()).set(Account::getPhoneNumber, account));
            } else {
                Assert.isFalse(b, ResponseEnum.PhoneNumberAlreadyExists.getMsg());
            }

        } else {
            //移除游客设备机器码
            touristDevicePhoneMapper.delete(Wrappers.<TouristDevicePhone>lambdaQuery()
                    .eq(TouristDevicePhone::getPhoneNumber,userInfo.getPhoneNumber()));
            // 不是正常手机号代表是游客 --判断修改的手机号不存在正常修改补全手机号
            if (!b) {
                // 修改手机号
                accountMapper.update(null, Wrappers.<Account>lambdaUpdate()
                    .eq(Account::getId, userInfo.getId()).set(Account::getPhoneNumber, account));
            } else {
                Assert.isFalse(b, ResponseEnum.PhoneNumberAlreadyExists.getMsg());
//                // 判断修改手机号是否有DID
//                Account account1 = accountMapper
//                    .selectOne(Wrappers.<Account>lambdaQuery().eq(Account::getPhoneNumber, account));
//                // 手机号已存在且是游客账号
//                // 判断游客账号是否有DID
//                String didSymbol = userInfo.getDidSymbol();
//                if (StrUtil.isBlank(didSymbol)) {
//                    accountMapper.update(null, Wrappers.<Account>lambdaUpdate()
//                        .eq(Account::getId, userInfo.getId()).set(Account::getBlacklist, 2));
//                    //  判断用户有没有资产 合并用户资产
//                    checkLogout.mergeAssets(userInfo, account1);
//                } else {
//                    String didSymbol1 = account1.getDidSymbol();
//                    if (StrUtil.isBlank(didSymbol1)) {
//                        // 查询用户DID关联表
//                        AccountDid accountDid = accountDidMapper.selectOne(Wrappers.<AccountDid>lambdaQuery()
//                            .eq(AccountDid::getAccountUuid, userInfo.getUuid()));
//                        // 修改游客用户DID关联表为修改用户的
//                        accountDidMapper.update(null,
//                            Wrappers.<AccountDid>lambdaUpdate().eq(AccountDid::getId, accountDid.getId())
//                                .set(AccountDid::getAccountUuid, account1.getUuid())
//                                .set(AccountDid::getOperateUuid, account1.getOperateUuid())
//                        );
//                        // 游客账号有DID且修改手机号已存在
//                        // 禁用游客账号并修改游客账号DID
//                        accountMapper.update(null,
//                            Wrappers.<Account>lambdaUpdate().eq(Account::getId, userInfo.getId())
//                                .set(Account::getBlacklist, 2).set(Account::getDidSymbol, "yk" + didSymbol));
//                        accountMapper.update(null, Wrappers.<Account>lambdaUpdate()
//                            .eq(Account::getPhoneNumber, account).set(Account::getDidSymbol, didSymbol));
//                        // TODO 判断用户有没有资产合并资产
//                        // TODO 判断用户有没有域名资产
//                        // todo 判断用户有没有现金资产
//                        checkLogout.mergeAssets(userInfo, account1);
//                    } else {
//                        return R.error("绑定手机号已经实名");
//                    }
//                }
            }
        }
        return R.ok();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R setPhoneNumberService1(String uuid, String account) {
        // 校验手机号
        Account userInfo =
            accountMapper.selectOne(Wrappers.<Account>lambdaQuery().eq(Account::getUuid, uuid));
        if (ObjectUtil.equals(account, userInfo.getPhoneNumber())) {
            return R.ok();
        }
        // 判断手机号是否是游客手机号
        String phoneNumber = userInfo.getPhoneNumber();

        boolean mobileSimple = RegexUtil.isMobileSimple(phoneNumber);
        // 校验手机号是否重复

        Boolean b = accountMapper.phoneNumberIsExists(account);

        if (mobileSimple) {
            if (!b) {
                // 修改手机号
                accountMapper.update(null, Wrappers.<Account>lambdaUpdate()
                    .eq(Account::getId, userInfo.getId()).set(Account::getPhoneNumber, account));
            } else {
                Assert.isFalse(b, ResponseEnum.PhoneNumberAlreadyExists.getMsg());
            }

        } else {
            //移除游客设备机器码
            touristDevicePhoneMapper.delete(Wrappers.<TouristDevicePhone>lambdaQuery()
                    .eq(TouristDevicePhone::getPhoneNumber,userInfo.getPhoneNumber()));
            // 不是正常手机号代表是游客 --判断修改的手机号不存在正常修改补全手机号
            if (!b) {
                // 修改手机号
                accountMapper.update(null, Wrappers.<Account>lambdaUpdate()
                    .eq(Account::getId, userInfo.getId()).set(Account::getPhoneNumber, account));
            } else {
                // 判断修改手机号是否有DID
                Account account1 = accountMapper
                    .selectOne(Wrappers.<Account>lambdaQuery().eq(Account::getPhoneNumber, account));
                // 手机号已存在且是游客账号
                // 判断游客账号是否有DID
                String didSymbol = userInfo.getDidSymbol();
                if (StrUtil.isBlank(didSymbol)) {
                    accountMapper.update(null, Wrappers.<Account>lambdaUpdate()
                        .eq(Account::getId, userInfo.getId()).set(Account::getBlacklist, 2));
                    // TODO 判断用户有没有资产 合并用户资产
                    checkLogout.mergeAssets(userInfo, account1);
                } else {
                    String didSymbol1 = account1.getDidSymbol();
                    if (StrUtil.isBlank(didSymbol1)) {
                        // 查询用户DID关联表
                        AccountDid accountDid = accountDidMapper.selectOne(Wrappers.<AccountDid>lambdaQuery()
                            .eq(AccountDid::getAccountUuid, userInfo.getUuid()));
                        // 修改游客用户DID关联表为修改用户的
                        accountDidMapper.update(null,
                            Wrappers.<AccountDid>lambdaUpdate().eq(AccountDid::getId, accountDid.getId())
                                .set(AccountDid::getAccountUuid, account1.getUuid()));
                        // 游客账号有DID且修改手机号已存在
                        // 禁用游客账号并修改游客账号DID
                        accountMapper.update(null,
                            Wrappers.<Account>lambdaUpdate().eq(Account::getId, userInfo.getId())
                                .set(Account::getBlacklist, 2).set(Account::getDidSymbol, "yk" + didSymbol));

                        accountMapper.update(null, Wrappers.<Account>lambdaUpdate()
                            .eq(Account::getPhoneNumber, account).set(Account::getDidSymbol, didSymbol));
                        // TODO 判断用户有没有资产合并资产
                        // TODO 判断用户有没有域名资产
                        // todo 判断用户有没有现金资产
                        checkLogout.mergeAssets(userInfo, account1);
                    }
                }
            }
        }
        return R.ok();
    }

    /**
     * 内部调用
     * 手机号账户绑定DID账户
     *
     * @param phone
     * @param didsymbol
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/02/06
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R setPhoneNumberService2(String phone, String didsymbol) {
        System.out.println("请求参数：" + phone + " " + didsymbol);
        // 查询 did账号
        Account accountDid =
            accountMapper.selectOne(Wrappers.<Account>lambdaQuery().eq(Account::getDidSymbol, didsymbol));
        // 查询 手机号账户
        Account account =
            accountMapper.selectOne(Wrappers.<Account>lambdaQuery().eq(Account::getPhoneNumber, phone));
        String phoneDid = accountDid.getPhoneNumber();
        String parentUuidDid = accountDid.getParentUuid();
        String operateUuidDid = accountDid.getOperateUuid();
        String phoneAccount = account.getPhoneNumber();
        String parentUuid = account.getParentUuid();
        String operateUuid = account.getOperateUuid();
        if (ObjectUtil.equals(phoneDid, phoneAccount)) {
            return R.error("合并手机号相同");
        }
        // 判断手机号账户是没有DID did账户是有DID的
        if (ObjectUtil.isNull(account.getDidSymbol()) && ObjectUtil.isNotNull(accountDid.getDidSymbol())) {
            // 保存合并两个账号的记录
            AccountMerge accountMerge = new AccountMerge();
            AccountMerge accountMergeDid = new AccountMerge();
            BeanUtil.copyProperties(account, accountMerge);
            BeanUtil.copyProperties(accountDid, accountMergeDid);
            accountMergeMapper.insert(accountMerge);
            accountMergeMapper.insert(accountMergeDid);
            // 互换两个账号的手机号以及parent_uuid以及渠道UUID
            accountMapper.update(null, Wrappers.<Account>lambdaUpdate().eq(Account::getId, account.getId())
                .set(Account::getPhoneNumber, "hb"+phoneAccount)
                    .set(Account::getOperateUuid, operateUuidDid)
                    .set(Account::getParentUuid, parentUuidDid)
                    .set(Account::getBlacklist, 2)
            );

            accountMapper.update(null, Wrappers.<Account>lambdaUpdate().eq(Account::getId, accountDid.getId())
                .set(Account::getPhoneNumber, phoneAccount)
                    .set(Account::getOperateUuid, operateUuid)
                    .set(Account::getParentUuid, parentUuid));
            // 生成合并资产记录
            AccountMergeAssets accountMergeAssets = new AccountMergeAssets();
            accountMergeAssets.setFOperateUuid(account.getOperateUuid());
            accountMergeAssets.setTOperateUuid(accountDid.getOperateUuid());
            accountMergeAssets.setFromUuid(account.getUuid());
            accountMergeAssets.setToUuid(accountDid.getUuid());
            accountMergeAssetsMapper.insert(accountMergeAssets);
            return R.ok();
        }
        return R.error("该账号已存在DID");
    }

    /**
     * 获取渠道信息
     * @param inviteCode
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/03/12
     */
    @Override
    public R getOperateInfoService(String inviteCode) {
        Boolean aBoolean = checkRefreshCount(inviteCode);
        if (!aBoolean) {
            return R.ok();
        }
        // 获取用户信息
        Account userInfo =
            accountMapper.selectOne(Wrappers.<Account>lambdaQuery().eq(Account::getInviteCode, inviteCode));
        if(userInfo!=null){
            //获取渠道信息
            // 获取经销商信息
            About about = aboutMapper
                    .selectOne(Wrappers.<About>lambdaQuery().eq(About::getOperateUuid, userInfo.getOperateUuid()));
            return R.okData(about);
        }
        return R.ok();
    }


    /**
     * 同步用户信息
     * @param ip
     * @param application
     * @return
     */
    @Override
    public Object logInMessageRecordService(String ip, String application) {
        // 获取用户信息
        String accountUuid = StpUtil.getLoginId().toString();
        Account userInfo = accountMapper.queryByUUID(accountUuid);
        if (userInfo != null) {
            this.update(null, Wrappers.<Account>lambdaUpdate().eq(Account::getUuid, accountUuid)
                    .set(Account::getRecentLoginIp, ip).set(Account::getRecentLoginTime, new Date()));
        }
        return R.ok();
    }
    
    /**
     * 邀请码查询用户信息
     *
     * @param inviteCode
     * @return
     */
    @Override
    public InviteCodeAccountResp getByInviteCode(String inviteCode) {
        String accountUuid = StpUtil.getLoginIdAsString();
        Account account = accountMapper.queryByUUID(accountUuid);
        if (null != account) {
            String parentUuid = account.getParentUuid();
            if (StringUtils.isNotBlank(parentUuid)) {
                log.error("邀请码查询用户信息--当前账户[{}]已绑定上级,不允许填写邀请码", accountUuid);
                throw new ServiceException("当前账户已绑定上级,不允许填写邀请码");
            }
        }
        InviteCodeAccountResp resp = accountMapper.getByInviteCode(inviteCode);
        if (null == resp) {
            throw new ServiceException("未检测到该邀请码，请仔细核对是否有误");
        }
        // 睡眠5秒
        // try {
        //     Thread.sleep(10000);
        // } catch (InterruptedException e) {
        //     throw new RuntimeException(e);
        // }
        return resp;
    }

    /**
     * 福穗用户签约
     *
     * @param name
     * @param idCard
     * @param phone
     * @return {@link R }
     */
    @Override
    public R uploadPeopleIdCardV2Service(String name, String idCard, String phone) {
        Account account = getUserInfo();
        Integer signStatus1 = account.getSignStatus();
        com.alibaba.fastjson2.JSONObject jsonObject =
                fusuiUtil.uploadPeople(name,phone, idCard);
        System.out.println("签约返回信息： " + jsonObject);
        Integer code = jsonObject.getInteger("code");
        if (code == 0) {
            com.alibaba.fastjson2.JSONObject data = jsonObject.getJSONObject("data");
            Integer signStatus = data.getInteger("signStatus");
            if (signStatus == 2 || signStatus == 3) {
                account.setPeopleId(data.getString("peopleId"));
                account.setSignUrl(data.getString("signUrl"));
            }
            if(!ObjectUtil.equals(signStatus1,3)){
                account.setSignStatus(signStatus);
            }
            accountMapper.updateById(account);
            //插入签约用户信息
            fusuiWithdrawalMapper.insert(FusuiWithdrawal.builder()
                    .uuid(account.getUuid())
                    .phoneNumber(phone)
                    .idCard(idCard)
                    .realName(name)
                    .build());
            return R.okData(data);
        } else {
            // 通过身份证号查询用户是否已签约
            com.alibaba.fastjson2.JSONObject jsonObject1 =
                    fusuiUtil.queryPeopleByNameAndIdCardAndTaxID(name, idCard);
            System.out.println("签约身份信息确认： " + jsonObject1);
            Integer code1 = jsonObject1.getInteger("code");
            if (code1 == 0) {
                //判断 data 是否为空
                JSONArray data = jsonObject1.getJSONArray("data");
                if (data.isEmpty()) {
                    String msg = jsonObject.getString("msg");
                    return R.error(msg);
                }
                // com.alibaba.fastjson2.JSONObject data1 = jsonObject1.getJSONObject("data");
                com.alibaba.fastjson2.JSONObject data1 = jsonObject1.getJSONArray("data").getJSONObject(0);
                Integer signStatus = data1.getInteger("signStatus");
                if (signStatus == 2 || signStatus == 3) {
                    account.setPeopleId(data1.getString("peopleId"));
                    account.setSignUrl(data1.getString("signUrl"));
                }
                if(!ObjectUtil.equals(signStatus1,3)){
                    account.setSignStatus(signStatus);
                }
                accountMapper.updateById(account);
                return R.okData(data1);
            }
            String msg = jsonObject1.getString("msg");
            return R.error(msg);
        }
    }

    /**获取用户详细信息
     * @param paramJson
     * @return {@link R }
     */
    @Override
    public R showUserInfoDetailService(com.alibaba.fastjson2.JSONObject paramJson) {
        Map<String, Object> result = new HashMap<>();
        String accountUUID = StpUtil.getLoginId().toString();
        Account userInfo =
            accountMapper.selectOne(Wrappers.<Account>lambdaQuery().eq(Account::getUuid, accountUUID));
        AccountExpandMessage accountExpandMessage = accountExpandMessageMapper.selectOne(
            Wrappers.<AccountExpandMessage>lambdaQuery().eq(AccountExpandMessage::getUuid, accountUUID));
        if (accountExpandMessage != null) {
            result.put("accountExpandMessage", accountExpandMessage);
            Integer tagDisplayFlag = accountExpandMessage.getTagDisplayFlag();
            if (tagDisplayFlag == 1) {
                // 获取用户兴趣标签集合
                List<AccountTagSelected> userTagList =
                    accountTagSelectedMapper.selectUserTagList(accountUUID);
                result.put("accountTagList", userTagList);
            }
        }
        return R.okData(result);
    }

    /**
     * 编辑用户详细信息
     * 
     * @param paramJson
     * @return {@link R }
     */
    @Transactional
    @Override
    public R userInfoDetailUpdateService(com.alibaba.fastjson2.JSONObject paramJson) {
        Integer gender = paramJson.getInteger("gender");
        String birthday = paramJson.getString("birthday");
        Integer birDisplayFlag = paramJson.getInteger("birDisplayFlag");
        String constellation = paramJson.getString("constellation");
        Integer consDisplayFlag = paramJson.getInteger("consDisplayFlag");
        String regionName = paramJson.getString("regionName");
        String center = paramJson.getString("center");
        Integer regDisplayFlag = paramJson.getInteger("regDisplayFlag");
        String introduction = paramJson.getString("introduction");
        Integer tagDisplayFlag = paramJson.getInteger("tagDisplayFlag");
        // 获取用户标签集合
        List<AccountTagSelected> tagIdList = paramJson.getList("accountTags", AccountTagSelected.class);
        String accountUUID = StpUtil.getLoginId().toString();
        Account userInfo =
            accountMapper.selectOne(Wrappers.<Account>lambdaQuery().eq(Account::getUuid, accountUUID));
        AccountExpandMessage accountExpandMessage = accountExpandMessageMapper.selectOne(
            Wrappers.<AccountExpandMessage>lambdaQuery().eq(AccountExpandMessage::getUuid, accountUUID));
        if (accountExpandMessage == null) {
            // 新增用户详细信息
            AccountExpandMessage accountExpandMessageNew = new AccountExpandMessage();
            accountExpandMessageNew.setAccountId(userInfo.getId());
            accountExpandMessageNew.setPhoneNumber(userInfo.getPhoneNumber());
            accountExpandMessageNew.setUuid(accountUUID);
            if (gender != null) {
                accountExpandMessageNew.setGender(gender);
            }
            if (birDisplayFlag != null) {
                accountExpandMessageNew.setBirthday(birthday);
                accountExpandMessageNew.setBirDisplayFlag(birDisplayFlag);
            }
            if (consDisplayFlag != null) {
                accountExpandMessageNew.setConstellation(constellation);
                accountExpandMessageNew.setConsDisplayFlag(consDisplayFlag);
            }
            if (regDisplayFlag != null) {
                accountExpandMessageNew.setRegionName(regionName);
                accountExpandMessageNew.setCenter(center);
                accountExpandMessageNew.setRegDisplayFlag(regDisplayFlag);
            }
            if (tagDisplayFlag != null) {
                accountExpandMessageNew.setIntroduction(introduction);
                accountExpandMessageNew.setTagDisplayFlag(tagDisplayFlag);
            }

            // 兴趣标签不为空
            if (CollectionUtil.isNotEmpty(tagIdList)) {
                // 删除旧的兴趣标签
                accountTagSelectedMapper.delete(Wrappers.<AccountTagSelected>lambdaUpdate()
                    .eq(AccountTagSelected::getAccountUuid, accountUUID));
                // 新增兴趣标签
                for (AccountTagSelected accountTagSelected : tagIdList) {
                    accountTagSelected.setAccountUuid(accountUUID);
                    accountTagSelected.setCreateTime(new Date());
                    accountTagSelected.setUpdateTime(new Date());
                }
                accountTagSelectedMapper.batchInsert(tagIdList);
            }
            accountExpandMessageMapper.insert(accountExpandMessageNew);

        } else {
            accountExpandMessageMapper.update(null,
                Wrappers.<AccountExpandMessage>lambdaUpdate().eq(AccountExpandMessage::getUuid, accountUUID)
                    .set(AccountExpandMessage::getGender, gender)
                    .set(AccountExpandMessage::getBirthday, birthday)
                    .set(AccountExpandMessage::getBirDisplayFlag, birDisplayFlag)
                    .set(AccountExpandMessage::getConstellation, constellation)
                    .set(AccountExpandMessage::getConsDisplayFlag, consDisplayFlag)
                    .set(AccountExpandMessage::getRegionName, regionName)
                    .set(AccountExpandMessage::getCenter, center)
                    .set(AccountExpandMessage::getRegDisplayFlag, regDisplayFlag)
                    .set(AccountExpandMessage::getIntroduction, introduction)
                    .set(AccountExpandMessage::getTagDisplayFlag, tagDisplayFlag));
            // 删除旧的兴趣标签
            accountTagSelectedMapper.delete(Wrappers.<AccountTagSelected>lambdaUpdate()
                .eq(AccountTagSelected::getAccountUuid, accountUUID));

            // 查询所有标签
            List<AccountTag> accountTagList = accountTagMapper.selectList(null);
            // 获取id
            List<Integer> tagIdsList =
                accountTagList.stream().map(AccountTag::getId).collect(Collectors.toList());
            // 兴趣标签不为空
            if (CollectionUtil.isNotEmpty(tagIdList)) {
                List<AccountTagSelected> tagIdListNew = new ArrayList<>();
                // 新增兴趣标签
                for (AccountTagSelected accountTagSelected : tagIdList) {
                    if (tagIdsList.contains(accountTagSelected.getAccountTagId())) {
                        AccountTagSelected accountTagSelectedNew = new AccountTagSelected();
                        accountTagSelectedNew.setCreateTime(new Date());
                        accountTagSelectedNew.setUpdateTime(new Date());
                        accountTagSelectedNew.setSort(0);
                        accountTagSelectedNew.setAccountTagId(accountTagSelected.getAccountTagId());
                        accountTagSelectedNew.setAccountUuid(accountUUID);
                        tagIdListNew.add(accountTagSelectedNew);
                    }
                }
                accountTagSelectedMapper.batchInsert(tagIdListNew);
            }
        }
        return R.ok();
    }

    /**
     * 获取地区信息表
     * @param paramJson
     * @return {@link R }
     */
    @Override
    public R serchRegionService(com.alibaba.fastjson2.JSONObject paramJson) {
        List<Region> regions = regionMapper
            .selectList(Wrappers.<Region>lambdaQuery().eq(Region::getGrade, 0).orderByAsc(Region::getSpell));
        // 更具拼音首字母分组,并按照a-z排序 封装成List<RegionVo>
        Map<String, List<Region>> regionMap = regions.stream().collect(Collectors.groupingBy(region -> {
            String spell = region.getSpell();
            if (spell != null && !spell.isEmpty()) {
                return String.valueOf(spell.charAt(0)).toUpperCase();
            }
            return "#";
        }));

        List<RegionVo> regionList = new ArrayList<>();
        regionMap.entrySet().stream().sorted(Map.Entry.comparingByKey()).forEach(entry -> {
            RegionVo regionVo = new RegionVo();
            regionVo.setLetter(entry.getKey());
            regionVo.setRegionList(entry.getValue());
            regionList.add(regionVo);
        });

        return R.okData(regionList);
    }

    /**
     * 标签列表
     * @param paramJson
     * @return {@link R }
     */
    @Override
    public R tagListService(com.alibaba.fastjson2.JSONObject paramJson) {
        Map<String, Object> result = new HashMap<>();
        String accountUUID = StpUtil.getLoginId().toString();
        Account userInfo =
            accountMapper.selectOne(Wrappers.<Account>lambdaQuery().eq(Account::getUuid, accountUUID));
        Integer tagTypeId = paramJson.getInteger("tagTypeId");
        if (tagTypeId == null) {
            return R.ok();
        }
        List<AccountTag> accountTags = accountTagMapper.selectList(Wrappers.<AccountTag>lambdaQuery()
            .eq(AccountTag::getAccountTagTypeId, tagTypeId).orderByAsc(AccountTag::getSort));
        // 获取用户选择的标签列表
        List<AccountTagSelected> userTagList =
                accountTagSelectedMapper.selectUserTagList(accountUUID);
        if (CollectionUtil.isNotEmpty(accountTags)) {
            for (AccountTag accountTag : accountTags) {
                Integer id = accountTag.getId();
                if (CollectionUtil.isNotEmpty(userTagList)) {
                    for (AccountTagSelected accountTagSelected : userTagList) {
                        Integer accountTagId = accountTagSelected.getAccountTagId();
                        if (id.equals(accountTagId)) {
                            accountTag.setIsSelected(true);
                        }
                    }
                }
            }
        }
        result.put("tags", accountTags);
        result.put("userTags", userTagList);
        return R.okData(result);
    }

    /**
     * 标签类型列表
     * @return {@link R }
     */
    @Override
    public R tagTypeListService() {
        Map<String, Object> result = new HashMap<>();
        List<AccountTagType> accountTagTypes = accountTagTypeMapper
            .selectList(Wrappers.<AccountTagType>lambdaQuery().orderByAsc(AccountTagType::getSort));
        result.put("tagTypeList", accountTagTypes);
        return R.okData(result);
    }


    // ===================工具方法====================
    private Boolean checkRefreshCount(String  inviteCode) {
        int seconds = 5;
        int maxCount = 1;
        String key = "accessChannels:" + inviteCode;
        Optional<Object> optionalValue = Optional.ofNullable(redisUtils.get(key));
        int count =
                optionalValue.filter(value -> value instanceof Integer).map(value -> (Integer)value).orElse(0); // 如果 key 不存在或值不是 Integer，则默认值为 0
        log.debug("检测已经访问的次数{}", count);
        if (count <= 0) {
            redisUtils.set(key, 1, seconds);
            return true;
        }
        if (count < maxCount) {
            redisUtils.increment(key);
            return true;
        }
        log.warn("请求过于频繁请稍后再试");
        return false;
    }



    public void checkCancellationConditions(String accountUUID) {
        // 检查各项列表
        List<AccountLogoutCondition> accountLogoutConditions = accountLogoutConditionMapper.selectList(null);
        for (AccountLogoutCondition accountLogoutCondition : accountLogoutConditions) {
            updateAccountLogoutConditionRecord(accountLogoutCondition.getId(), accountUUID);
        }
    }

    public void updateAccountLogoutConditionRecord(int id, String accountUUID) {
        // 查询注销记录表里面是否有记录
        AccountLogoutConditionRecord accountLogoutConditionRecord =
            accountLogoutConditionRecordMapper.selectOne(Wrappers.<AccountLogoutConditionRecord>lambdaQuery()
                .eq(AccountLogoutConditionRecord::getAccountUuid, accountUUID)
                .eq(AccountLogoutConditionRecord::getConditionId, id));

        AccountLogoutConditionRecord accountLogoutConditionRecord1 = new AccountLogoutConditionRecord();

        switch (id) {
            case 1:
                // 检查账号广场是否存在封禁状态
                Blacklist blacklist = blacklistMapper
                    .selectOne(Wrappers.<Blacklist>lambdaQuery().eq(Blacklist::getAccountUuid, accountUUID));

                if (accountLogoutConditionRecord == null) {
                    accountLogoutConditionRecord1.setConditionId(id).setAccountUuid(accountUUID);
                    if (blacklist == null) {
                        accountLogoutConditionRecord1.setIsIgnore(2);
                    } else {
                        accountLogoutConditionRecord1.setIsIgnore(0);
                    }
                    accountLogoutConditionRecordMapper.insert(accountLogoutConditionRecord1);
                } else {
                    Integer isIgnore = accountLogoutConditionRecord.getIsIgnore();

                    if (blacklist == null && isIgnore == 0) {
                        accountLogoutConditionRecord.setIsIgnore(2);
                        accountLogoutConditionRecordMapper.updateById(accountLogoutConditionRecord);
                    }

                }
                break;
            case 2:
                // 查询账号是否有余额和冻结
                RechargeAssets one = rechargeAssetsMapper.selectOne(
                    Wrappers.<RechargeAssets>lambdaQuery().eq(RechargeAssets::getAccountUuid, accountUUID));
                if (accountLogoutConditionRecord == null) {
                    accountLogoutConditionRecord1.setConditionId(id).setAccountUuid(accountUUID);
                    if (one == null) {
                        accountLogoutConditionRecord1.setIsIgnore(2);
                    } else {
                        BigDecimal balance = one.getBalance();
                        BigDecimal freeze = one.getFreeze();
                        if (balance.add(freeze).compareTo(new BigDecimal("0")) > 0) {
                            accountLogoutConditionRecord1.setIsIgnore(0);
                        } else {
                            accountLogoutConditionRecord1.setIsIgnore(2);
                        }
                    }
                    accountLogoutConditionRecordMapper.insert(accountLogoutConditionRecord1);
                } else {
                    Integer isIgnore = accountLogoutConditionRecord.getIsIgnore();
                    if (isIgnore == 0) {
                        if (one == null) {
                            accountLogoutConditionRecord.setIsIgnore(2);
                        } else {
                            BigDecimal balance = one.getBalance();
                            BigDecimal freeze = one.getFreeze();
                            if (balance.add(freeze).compareTo(new BigDecimal("0")) > 0) {
                                accountLogoutConditionRecord.setIsIgnore(0);
                            } else {
                                accountLogoutConditionRecord.setIsIgnore(2);
                            }
                        }
                        accountLogoutConditionRecordMapper.updateById(accountLogoutConditionRecord);
                    }
                }
                break;
            case 3:
                boolean flag = true;
                // 查询账号是否有未完成订单==交易市场竞拍和上架中==电影票==提现=域名购买
                List<OrderBsnInfo> orderBsnInfos = orderBsnInfoMapper.selectList(
                    Wrappers.<OrderBsnInfo>lambdaQuery().eq(OrderBsnInfo::getAccountUuid, accountUUID)
                        .in(OrderBsnInfo::getStatus, Arrays.asList(1, 4)));

                // 提现
                List<RechargeWithdrawRecord> rechargeWithdrawRecords =
                    rechargeWithdrawRecordMapper.selectList(Wrappers.<RechargeWithdrawRecord>lambdaQuery()
                        .eq(RechargeWithdrawRecord::getAccountUuid, accountUUID)
                        .eq(RechargeWithdrawRecord::getType, 2).eq(RechargeWithdrawRecord::getState, 1));
                // 电影票
                List<MovieOrder> movieOrders = movieOrderMapper
                    .selectList(Wrappers.<MovieOrder>lambdaQuery().eq(MovieOrder::getAccountUuid, accountUUID)
                        .in(MovieOrder::getOrderStatus, Arrays.asList(2, 3)));

                // 域名上架
                // 一口价
                List<FixDomain> fixDomains = fixDomainMapper.selectList(Wrappers.<FixDomain>lambdaQuery()
                    .eq(FixDomain::getAccountUuid, accountUUID).eq(FixDomain::getDomainStatus, 1));

                // 拍卖
                List<AuctionDomain> auctionDomains =
                    auctionDomainMapper.selectList(Wrappers.<AuctionDomain>lambdaQuery()
                        .eq(AuctionDomain::getSellerUuid, accountUUID).eq(AuctionDomain::getDomainStatus, 1));

                if (CollectionUtil.isNotEmpty(orderBsnInfos)) {
                    flag = false;
                } else if (CollectionUtil.isNotEmpty(rechargeWithdrawRecords)) {
                    flag = false;
                } else if (CollectionUtil.isNotEmpty(movieOrders)) {
                    flag = false;
                } else if (CollectionUtil.isNotEmpty(fixDomains)) {
                    flag = false;
                } else if (CollectionUtil.isNotEmpty(auctionDomains)) {
                    flag = false;
                }
                if (accountLogoutConditionRecord == null) {
                    accountLogoutConditionRecord1.setConditionId(id).setAccountUuid(accountUUID);
                    if (flag) {
                        accountLogoutConditionRecord1.setIsIgnore(2);
                    } else {
                        accountLogoutConditionRecord1.setIsIgnore(0);
                    }
                    accountLogoutConditionRecordMapper.insert(accountLogoutConditionRecord1);
                } else {
                    Integer isIgnore = accountLogoutConditionRecord.getIsIgnore();
                    if (isIgnore == 0) {
                        if (flag) {
                            accountLogoutConditionRecord.setIsIgnore(2);
                        } else {
                            accountLogoutConditionRecord.setIsIgnore(0);
                        }
                        accountLogoutConditionRecordMapper.updateById(accountLogoutConditionRecord);
                    }
                }
                break;
            case 4:
                // 查询账号是否持有域名
                List<DomainAccount> domainAccounts = domainAccountMapper.selectList(
                    Wrappers.<DomainAccount>lambdaQuery().eq(DomainAccount::getAccountUuid, accountUUID)
                        .eq(DomainAccount::getTransferState, 1).eq(DomainAccount::getStatus, 0));

                if (accountLogoutConditionRecord == null) {
                    accountLogoutConditionRecord1.setConditionId(id).setAccountUuid(accountUUID);
                    if (CollectionUtil.isEmpty(domainAccounts)) {
                        accountLogoutConditionRecord1.setIsIgnore(2);
                    } else {
                        accountLogoutConditionRecord1.setIsIgnore(0);
                    }
                    accountLogoutConditionRecordMapper.insert(accountLogoutConditionRecord1);
                } else {
                    Integer isIgnore = accountLogoutConditionRecord.getIsIgnore();
                    if (isIgnore == 0) {
                        if (CollectionUtil.isEmpty(domainAccounts)) {
                            accountLogoutConditionRecord.setIsIgnore(2);
                        } else {
                            accountLogoutConditionRecord.setIsIgnore(0);
                        }
                        accountLogoutConditionRecordMapper.updateById(accountLogoutConditionRecord);
                    }
                }
                break;
        }
    }

    /**
     * 登录判断手机号是否存在
     *
     * @param account
     * @return boolean
     * <AUTHOR>
     * @date 2024/03/27
     */
    private boolean handleLoginScenario(String account) {
        Boolean phoneNumberIsExists = accountMapper.phoneNumberIsExists(account);
        Assert.isTrue(phoneNumberIsExists, ResponseEnum.PhoneNumberDoesNotExist.getMsg());
        return true;
    }

    /**
     * 验证码放入缓存
     *
     * @param account
     * @param verificationCode
     */
    private void setVerificationCode(String account, String verificationCode) {
        String operateUUID = YL_UUID;
        // 验证码redis的key
        String verificationCodeKey = "lj:verificationCodeKey:" + operateUUID + ":" + account;
        // 从缓存中获取验证码 30秒
        redisUtils.set(verificationCodeKey, verificationCode, 60L);
    }

    /**
     * 设置邀请用户信息 不判断是否相同经销商信息
     *
     * @param inviteCode 邀请码
     * @param accountEntity 用户信息
     */
    private String setInviteInfo(String inviteCode, Account accountEntity) {
        // String operateUUID = HeadUtil.getOperaterUUID();
        String operateUUID = globalConfigService.getGlobalConfig(YL_UUID);
        if (StrUtil.isNotBlank(inviteCode)) {
            Account parentAccount = accountMapper
                .selectOne(Wrappers.<Account>lambdaQuery().eq(Account::getInviteCode, inviteCode));
            Assert.notNull(parentAccount, ResponseEnum.InvalidInvitationCode.getMsg());
            // 如果邀请码有效绑定上级用户id
            // 当前用户经销商为上架用户经销商operateUUid
            accountEntity.setParentUuid(parentAccount.getUuid());
            operateUUID = parentAccount.getOperateUuid();
        }
        return operateUUID;
    }

    /**
     * 校验用户注册手机号
     *
     * @param phone 手机号
     * @param prompt 提示语
     */
    private void validateRegistePhone(String phone, String prompt) {
        Boolean phoneNumberIsExists = accountMapper.phoneNumberIsExists(phone);
        Assert.isFalse(phoneNumberIsExists, prompt);
    }

    /**
     * 设置uuid
     *
     * @param operateUUID 经销商uuid
     * @param phoneNumber 手机号
     * @param accountEntity 用户信息
     */
    private void setUUID(String operateUUID, String phoneNumber, Account accountEntity) {
        // 获取uuid前缀
        About about =
            aboutMapper.selectOne(Wrappers.<About>lambdaQuery().eq(About::getOperateUuid, operateUUID));
        Assert.notNull(about, ResponseEnum.CommonConfigurationMissing.getMsg());
        String uuidPrefix = about.getUuidPrefix();
        Assert.notBlank(uuidPrefix, ResponseEnum.MissingPrefix.getMsg());
        // 生成uuid后缀
        String uuidSuffix = doTaskWithTimeout(uuidPrefix);
        Assert.notBlank(uuidSuffix, "uuid异常");
        String uuid = uuidPrefix + uuidSuffix;
        // 设置UUID
        accountEntity.setUuid(uuid);
        accountEntity.setUuidSuffix(uuidSuffix);
    }

    /**
     * 生成uuid后缀
     *
     * @param uuidPrefix
     * @return
     */
    public String doTaskWithTimeout(String uuidPrefix) {
        ExecutorService executor = Executors.newSingleThreadExecutor();
        // 提交任务并获取Future
        Future<String> future = executor.submit(() -> {
            // 执行耗时的任务
            return getUUIDSuffix(uuidPrefix);
        });
        String uuidSuffix = "";
        try {
            // 在指定时间内等待结果
            uuidSuffix = future.get(TIMEOUT_DURATION, TimeUnit.SECONDS);
        } catch (TimeoutException e) {
            // 超时异常，打印异常日志
            log.error("获取生成uuid超时 uuidPrefix:{}", uuidPrefix);
        } catch (Exception e) {
            // 其他异常，打印异常日志
            log.error("An error occurred: " + e.getMessage());
        } finally {
            // 关闭ExecutorService
            executor.shutdown();
        }
        return uuidSuffix;
    }

    /**
     * @param
     * @return java.lang.String
     * @Author: yinlu
     * @Description: 获取UUID后缀
     **/
    public String getUUIDSuffix(String uuidPrefix) {
        // 判断uuid是否存在
        String uuidSuffix = "";
        while (true) {
            // 随机生成UUID后缀
            uuidSuffix = UUIdUtil.createUUId();
            // 组装UUID
            String uuid = uuidPrefix + uuidSuffix;
            Boolean isExists = accountMapper.UUIDIsExists(uuid);
            if (!isExists) {
                break;
            }
        }
        return uuidSuffix;
    }

    /**
     * @param
     * @return java.lang.String
     * @Author: yinlu
     * @Description: 生成默认昵称
     **/
    private String getDefaultNickname() {
        String nickname = "默认" + CharUtil.getRandomNum(8);
        NicknameLibrary nicknameLibrary =
            nicknameLibraryMapper.selectOne(Wrappers.<NicknameLibrary>lambdaQuery()
                .eq(NicknameLibrary::getIsUser, 0).orderByDesc(NicknameLibrary::getId).last("limit 1"));
        if (nicknameLibrary != null) {
            nickname = nicknameLibrary.getNickName();
            // 修改昵称已使用
            nicknameLibrary.setIsUser(1);
            nicknameLibraryMapper.updateById(nicknameLibrary);
        }
        return nickname;
    }

    private String getDefaultNicknameV1() {
        String nickname = "游客" + CharUtil.getRandomNum(8);
        return nickname;
    }

    private String getDefaultNicknameV2() {
        String nickname = "注销" + CharUtil.getRandomNum(8);
        return nickname;
    }

    /**
     * 添加用户邀请码
     *
     * @param accountInfo 用户uuid
     * @return
     */
    public void addInviteCode(Account accountInfo) {
        Assert.notNull(accountInfo, ResponseEnum.UserDoesNotExist.getMsg());
        String operateUUID = accountInfo.getOperateUuid();

        Operate operateInfo = operateMapper
            .selectOne(Wrappers.<Operate>lambdaQuery().eq(Operate::getOpearteNumber, operateUUID));

        Assert.notNull(operateInfo, ResponseEnum.DealerUuidDoesNotExist.getMsg());

        Integer operateSort = operateInfo.getSort();
        Assert.notNull(operateSort, ResponseEnum.DealerSerialNumberCannotBeEmpty.getMsg());

        String countStr = accountMapper.getInviteCodeMaxSort(operateUUID);

        if (StrUtil.isNotBlank(countStr)) {
            // +1
            countStr = String.valueOf(Integer.parseInt(countStr) + 1);
        } else {
            countStr = String.valueOf(1);
        }
        // 补零操作
        String operateSortStr = String.valueOf(operateSort);

        // 判断是否需要补零
        if (operateSortStr.length() < 3) {
            operateSortStr = String.format("%03d", Integer.parseInt(operateSortStr));
        }
        if (countStr.length() < 3) {
            countStr = String.format("%03d", Integer.parseInt(countStr));
        }
        String invicode = operateSortStr + countStr;
        accountInfo.setInviteCode(invicode);
    }

    /**
     * 添加用户邀请码
     *
     * @param accountInfo 用户uuid
     * @return
     */
    public void addInviteCode1(Account accountInfo) {
        Assert.notNull(accountInfo, ResponseEnum.UserDoesNotExist.getMsg());
        Long id = accountInfo.getId();
        // 判断是否需要补零
        String invicode = String.format("%06d", id);

        invicode = checkInvicode(invicode);
        // 更新邀请码
        accountMapper.update(null,
            Wrappers.<Account>lambdaUpdate().eq(Account::getId, id).set(Account::getInviteCode, invicode));
    }

    private String checkInvicode(String invicode) {
        // 判断验证码和旧数据是否会重复
        Account parentAccount =
            accountMapper.selectOne(Wrappers.<Account>lambdaQuery().eq(Account::getInviteCode, invicode));
        if (parentAccount != null) {
            log.error("重复邀请码" + invicode);
            log.error("重复邀请码" + invicode);
            log.error("重复邀请码" + invicode);
            Long id1 = parentAccount.getId();
            // 判断是否需要补零
            invicode = String.format("%06d", id1);
            return checkInvicode(invicode);
        } else {
            return invicode;
        }
    }

    /**
     * 添加用户资产信息
     *
     * @param account
     */
    private void addRechargeAssetsInfo(Account account) {
        RechargeAssets rechargeAssets = rechargeAssetsMapper.selectOne(
            Wrappers.<RechargeAssets>lambdaQuery().eq(RechargeAssets::getAccountUuid, account.getUuid()));
        if (rechargeAssets == null) {
            rechargeAssets = new RechargeAssets();
            rechargeAssets.setTotalAmount(BigDecimal.ZERO);
            rechargeAssets.setBalance(BigDecimal.ZERO);
            rechargeAssets.setFreeze(BigDecimal.ZERO);
            rechargeAssets.setOperateUuid(account.getOperateUuid());
            rechargeAssets.setAccountUuid(account.getUuid());
            rechargeAssetsMapper.insert(rechargeAssets);
        }
    }

    /**
     * 校验用户登录失败次数
     *
     * @param servletContext
     * @param operateuuidAndPhone
     * @return
     */
    private boolean checkLock(ServletContext servletContext, String operateuuidAndPhone) {
        Object obj = servletContext.getAttribute(operateuuidAndPhone);
        if (obj == null) {
            return true;
        }
        JSONObject json = JSON.parseObject(JSON.toJSONString(obj));
        Integer num = json.getInteger("num");
        Date date = json.getDate("lastDate");
        long timeDifference = ((new Date().getTime() - date.getTime()) / 60 / 1000);
        return num < 3 || timeDifference >= 5;
    }

    /**
     * 新增用户登录失败次数
     * 
     * @param servletContext
     * @param operateuuidAndPhone
     */
    private void addFailNum(ServletContext servletContext, String operateuuidAndPhone) {
        Object obj = servletContext.getAttribute(operateuuidAndPhone);
        JSONObject json;
        int num = 0;
        if (obj == null) {
            json = new JSONObject();
        } else {
            json = JSON.parseObject(JSON.toJSONString(obj));
            num = json.getInteger("num");
            Date date = json.getDate("lastDate");
            long timeDifference = ((new Date().getTime() - date.getTime()) / 60 / 1000);
            if (timeDifference >= 5) {
                num = 0;
            }
        }
        json.put("num", num + 1);
        json.put("lastDate", new Date());
        servletContext.setAttribute(operateuuidAndPhone, json);
    }

    /**
     * 清理用户登录失败的记录
     *
     * @param servletContext
     * @param operateuuidAndPhone
     */
    private void cleanFailNum(ServletContext servletContext, String operateuuidAndPhone) {
        servletContext.removeAttribute(operateuuidAndPhone);
    }

    private String registerAndLogin(HttpServletRequest request, String account, String inviteCode) {
        Account accountEntity = new Account();
        // 设置并校验邀请码信息
        String operateUUID = setInviteInfo(inviteCode, accountEntity);

        // 校验经销商信息
        Operate operateInfo = operateMapper
            .selectOne(Wrappers.<Operate>lambdaQuery().eq(Operate::getOpearteNumber, operateUUID));
        Assert.notNull(operateInfo, ResponseEnum.DealerDoesNotExist.getMsg());
        // 设置经销商uuid
        accountEntity.setOperateUuid(operateUUID);

        // 判断注册手机号信息
        validateRegistePhone(account, ResponseEnum.PleaseDoNotRegisterAgain.getMsg());

        // 设置手机号
        accountEntity.setPhoneNumber(account);
        // 设置注册来源
        accountEntity.setRegistrationSource(3);
        // 记录Ip地址和注册时间
        // 获取访问的ip
        String ipAddr = IpUtil.getRealIp(request);
        accountEntity.setRegisterIp(ipAddr);
        accountEntity.setRecentLoginIp(ipAddr);

        Date date = new Date();
        accountEntity.setRegisterTime(date);
        accountEntity.setRecentLoginTime(date);

        // 设置uuid
        setUUID(operateUUID, account, accountEntity);

        // 设置默认头像
        String defaultPortrait = globalConfigService.getGlobalConfig(TOURIST_AVATAR);

        accountEntity.setHeadPortrait(defaultPortrait);
        // 设置默认昵称
        String defaultNickname = getDefaultNicknameV1();
        accountEntity.setNickName(defaultNickname);

        // 生成邀请码
        // addInviteCode(accountEntity);

        // 保存账户信息
        accountMapper.insert(accountEntity);

        // 生成邀请码
        addInviteCode1(accountEntity);

        // 添加用户资产信息
        addRechargeAssetsInfo(accountEntity);
        // 添加登录信息
        // addAccountLoginInfo(account,ipAddr,1,"","注册");
        // 新用户--赠送优惠券
        // giftCoupon(accountEntity.getOperateUuid(),accountEntity.getUuid());

        StpUtil.login(accountEntity.getUuid());
        return StpUtil.getTokenValue();
    }

    private String loginByPasswordAndtourist(HttpServletRequest request, String account) {
        ServletContext servletContext = request.getServletContext();
        String operateuuidAndPhone = null;
        Account accountEntity =
            accountMapper.selectOne(Wrappers.<Account>lambdaQuery().eq(Account::getPhoneNumber, account));
        // 更新用户最近的登录信息
        accountEntity.setRecentLoginIp(IpUtil.getRealIp(request));
        accountEntity.setRecentLoginTime(new Date());
        updateById(accountEntity);
        StpUtil.login(accountEntity.getUuid());
        String tokenValue = StpUtil.getTokenValue();
        // 添加登录信息
        return tokenValue;
    }

    /**
     * 校验是否是注销用户和注销期限是否满一年
     *
     * <AUTHOR>
     * @date 2024/06/21
     */
    private void checkUserLogoutime(String phoneNumber) {
        List<AccountLogout> accountLogouts =
            accountLogoutMapper.selectList(Wrappers.<AccountLogout>lambdaQuery()
                .eq(AccountLogout::getPhoneNumber, phoneNumber).orderByDesc(AccountLogout::getLogoutTime));
        if (CollectionUtil.isNotEmpty(accountLogouts)) {
            AccountLogout accountLogout = accountLogouts.get(0);
            Date logoutTime = accountLogout.getLogoutTime();
            // 比较当前时间和注销时间是否大于一年
            Boolean aBoolean = DateCompare(logoutTime, new Date(), 1);
            if (!aBoolean) {
                throw new ServiceException(ResponseEnum.CancellationLessThanOneYearOld);
            }
        }
    }

    /**
     * 判断两个日期间是否超过的年数
     *
     * @param time1
     * @param time2
     * @param numYear
     * @return {@link Boolean }
     * <AUTHOR>
     * @date 2024/06/21
     */
    public Boolean DateCompare(Date time1, Date time2, int numYear) {
        Date time3 = add(time1, Calendar.YEAR, numYear);
        if (time3.getTime() < time2.getTime()) {
            return true;
        }
        return false;
    }

    /**
     * 时间加减
     *
     * @param date
     * @param calendarField ：Calendar.YEAR/ Calendar.MONTH /Calendar.DAY
     * @param amount
     * @return {@link Date }
     * <AUTHOR>
     * @date 2024/06/21
     */
    public Date add(final Date date, final int calendarField, final int amount) {
        if (date == null) {
            return null;
        }
        final Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(calendarField, amount);
        return c.getTime();
    }


}
