package com.lj.auth.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lj.auth.common.R;
import com.lj.auth.domain.Account;
import com.lj.auth.domain.vo.AccountVo;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotBlank;

public interface ModuleAccountService extends IService<Account> {


    /**
     * 验证码登录
     *
     * @param account 手机号
     * @param varificationCode 验证码
     * @return {@link R }
     */
    R loginByCodeWithModule(HttpServletRequest request, String account, String varificationCode);


    /**
     * 密码登录
     * @param request
     * @param account
     * @param password
     * @return
     */
    R loginByPasswordWithModule(HttpServletRequest request, String account, String password);



    AccountVo getInfoVoByUUIDWithModule(String accountUuid);


    Account queryByDidWithModule(String didSymbol);

    Account queryByUUIDWithModule(String accountUuid);

    Account queryByPhoneWithModule(String phoneNumber);


    R setPasswordWithModule(String accountUuid,String account, String varificationCode, String password);


    R setPhoneNumberWithModule(String accountUuid,String account, String varificationCode);


    R setPayPasswordWithModule(String accountUuid, String account, String varificationCode, String payPassword);


    /**
     * 对比缓存中的验证码
     *
     */
    boolean compareVerificationCodesWithModule(String accountUuid, String account, String verificationCode);

    /**
     * 设置图像
     *
     * @param accountUuid uuid
     * @param portrait 图像
     * @param nftId nftId
     * @param type 图像类型 1-普通图像 2-nft图像
     * @return {@link R }
     */
    R setPortraitWithModule(String accountUuid, String portrait, Long nftId, Integer type);


    R setNickNameWithModule(String accountUuid, String nickName, String domainNickName, Integer type);


    /**
     * 同步用户登录信息
     * @param accountUuid
     * @param ip
     * @return {@link Object }
     * <AUTHOR>
     * @date 2025/04/09
     */
    Object logInMessageRecordService(@NotBlank(message = "uuid不能为空") String accountUuid, @NotBlank(message = "ip地址不能为空") String ip,String application);
}
