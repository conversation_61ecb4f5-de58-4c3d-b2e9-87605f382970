package com.lj.auth.service;

import com.lj.auth.common.R;
import com.lj.auth.domain.vo.OrderNotifyRequest;
import com.lj.auth.domain.vo.PayNotifyVo;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description
 * @date 2023/6/20 16:07
 */
public interface AggregatePayService {

    /**
     * app下单支付
     *
     * @param merOrderId
     * @param jsCode     临时登录凭证
     * @param returnUrl  订单展示页面
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/07/08
     */
    R appPay(String merOrderId, String jsCode, String returnUrl);

    /**
     * 关闭未支付订单
     * 
     * @param merOrderId
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/03
     */
    R appClose(String merOrderId);

    /**
     * app订单交易查询
     * 
     * @param merOrderId 商户订单号
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/03
     */
    R appQuery(String merOrderId);

    R appNotify(PayNotifyVo data);

    /**
     * 获取支付方式配置
     *
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/03
     */
    R getPayConfigService();

    /**
     * 获取微信小程序支付跳转链接 并下单
     *
     * @param amount 金额
     * @param payType 支付方式
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/10
     */
    R getWeiXinLinkService(BigDecimal amount, int payType,String remark);

    /**
     * 创建订单
     *
     * @param request
     * @param amount
     * @param remark
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/11/22
     */
    R createOrderService(HttpServletRequest request,BigDecimal amount, String remark);

    R appNotify1(OrderNotifyRequest data);
}
