package com.lj.auth.service;

import com.lj.auth.common.R;
import com.lj.auth.domain.Account;

import java.util.List;

public interface CommonAuthService {

    /**
     * 异步缓存用户信息
     * 
     * @param userInfo
     */
    void cacheUserInformation(Account userInfo);

    /**
     * 获取用户基础信息
     * 
     * @param uuid
     * @return {@link R }
     */
    R getBaseUserInfoService(String uuid);

    /**
     * 批量获取用户基础信息
     * 
     * @param didSymbols
     * @return {@link R }
     */
    R getBatchBaseUserInfoAllService(List<String> didSymbols);

    /**
     * 批量获取用户基础信息
     *
     * @param didSymbols
     * @param type
     * @return {@link R }
     */
    R getBatchBaseUserInfoService(List<String> didSymbols, Integer type);

    /**
     * 发送验证码
     * 
     * @param account
     * @param verificationCode
     * @return {@link R }
     */
    R sentCaptcha(String account, String verificationCode);
}
