package com.lj.auth.service.impl;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.locks.ReentrantLock;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import cn.hutool.core.util.ObjectUtil;
import com.lj.auth.common.R;
import com.lj.auth.domain.*;
import com.lj.auth.domain.vo.OrderNotifyRequest;
import com.lj.auth.domain.vo.PayNotifyVo;
import com.lj.auth.exception.ResponseEnum;
import com.lj.auth.exception.ServiceException;
import com.lj.auth.feginClient.IOrderService;
import com.lj.auth.mapper.*;
import com.lj.auth.pay.AggregatePay.AppPay.Close;
import com.lj.auth.pay.AggregatePay.AppPay.CommonParameters;
import com.lj.auth.pay.AggregatePay.AppPay.Precreate;
import com.lj.auth.pay.AggregatePay.AppPay.Query;
import com.lj.auth.pay.AggregatePay.MiniPay.MiniPayUtil;
import com.lj.auth.service.AccountService;
import com.lj.auth.service.AggregatePayService;
import com.lj.auth.service.GlobalConfigService;
import com.lj.auth.util.IpUtil;
import com.lj.auth.util.RedisUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import static com.lj.auth.common.CommonConstant.*;

/**
 * <AUTHOR>
 * @Description
 * @date 2023/6/20 16:07
 */
@Slf4j
@Service
public class AggregatePayServiceImpl implements AggregatePayService {
    private final ReentrantLock lock = new ReentrantLock();
    @Resource
    private RechargeRecordMapper rechargeRecordMapper;
    @Resource
    private RechargeAssetsMapper rechargeAssetsMapper;
    @Resource
    private AccountService accountService;
    @Resource
    private MiniPayUtil miniPayUtil;
    @Resource
    private RedisUtils redisUtils;
    @Resource
    private GlobalConfigService globalConfigService;
    @Resource
    private RechargeWithdrawRecordMapper rechargeWithdrawRecordMapper;
    @Resource
    private RechargeFlowMapper rechargeFlowMapper;
    @Resource
    private IOrderService  iOrderService;
    @Value("${payCallbackNotifyAddress}")
    private String payCallbackNotifyAddress;

    /**
     * app下单支付
     *
     * @param amount 金额
     * @param payType 支付方式：1:微信 2:支付宝
     * @param jsCode 临时登录凭证
     * @param returnUrl 订单展示页面
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/03
     */
    @Override
    public R appPay(String merOrderId, String jsCode, String returnUrl) {
        Account userInfo = accountService.getUserInfo();
        //校验用户有没有DID
        if (StrUtil.isEmpty(userInfo.getDidSymbol())) {
            return R.error(ResponseEnum.NotDIDCertifiedPlease);
        }
        String accountUUID = userInfo.getUuid();
        try {
            // 充值订单表
            RechargeWithdrawRecord rechargeWithdrawRecord = rechargeWithdrawRecordMapper.selectOne(Wrappers
                .<RechargeWithdrawRecord>lambdaQuery().eq(RechargeWithdrawRecord::getTranNumber, merOrderId));

            BigDecimal amount = rechargeWithdrawRecord.getAmount();
            Integer payType = rechargeWithdrawRecord.getPayType();
            Date createTime = new Date();
            // 充值金额转换为 分
            BigInteger totalAmount =
                amount.multiply(new BigDecimal("100")).setScale(0, RoundingMode.DOWN).toBigInteger();
            String response = "";
            if (payType == 1) {// 微信支付
                // response = Precreate.createWechatOrder(createTime, merOrderId, totalAmount, returnUrl);
                response = miniPayUtil.unifiedOrder(merOrderId, createTime, totalAmount, jsCode);

            } else if (payType == 2) {// 支付宝支付
                response = Precreate.createAliOrder(createTime, merOrderId, totalAmount, returnUrl);
            }
            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("orderId", rechargeWithdrawRecord.getId());
            resultMap.put("orderInfo", JSONObject.parseObject(response));
            resultMap.put("merOrderId", merOrderId);
            return R.okData(resultMap);
        } catch (Exception e) {
            e.printStackTrace();
            R.error(ResponseEnum.PaymentOrderFailed);
            return R.error("");
        }
    }

    @Override
    public R appClose(String merOrderId) {
        // Account userInfo = accountService.getUserInfo();
        Date createTime = new Date();
        try {
            String response = Close.closeOrder(createTime, merOrderId);
            JSONObject dataToMap = JSONObject.parseObject(response);
            String errCode = dataToMap.getString("errCode");
            if (!"SUCCESS".equals(errCode)) {
                String errMsg = dataToMap.getString("errMsg");
                return R.error(errMsg);
            }
            String status = dataToMap.getString("status");
            if ("TRADE_CLOSED".equals(status)) {
                // 修改充值订单状态为交易已关闭
                RechargeWithdrawRecord rechargeWithdrawRecord =
                    rechargeWithdrawRecordMapper.selectOne(Wrappers.<RechargeWithdrawRecord>lambdaQuery()
                        .eq(RechargeWithdrawRecord::getTranNumber, merOrderId));
                if (rechargeWithdrawRecord == null) {
                    return R.error(ResponseEnum.RechargeOrderDoesNotExist);
                }
                // 状态( 1-审核中，2-审核失败，3-提现/充值成功 4-用户取消)
                rechargeWithdrawRecordMapper.update(null,
                    Wrappers.<RechargeWithdrawRecord>lambdaUpdate()
                        .eq(RechargeWithdrawRecord::getTranNumber, merOrderId)
                        .set(RechargeWithdrawRecord::getState, 4));
                return R.ok();
            }
            return R.error(status);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return R.error();
    }

    @Override
    @Transactional
    public R appQuery(String merOrderId) {
        Date createTime = new Date();
        String response = Query.queryOrder(createTime, merOrderId);
        if (response != null) {
            JSONObject responseJson = JSONObject.parseObject(response);
            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("data", responseJson);
            // 同步检查订单支付状态
            checkOrderStatus(JSON.toJSONString(responseJson));

            return R.ok(resultMap);
        }
        return R.error(ResponseEnum.QueryFailed);
    }

    public void checkOrderStatus(String data) {
        Map map = JSON.parseObject(data, Map.class);
        String state = map.get("errCode").toString();
        if ("SUCCESS".equals(state)) {
            try {
                String merOrderId = map.get("merOrderId").toString();
                String totalAmount = map.get("totalAmount").toString();
                String invoiceAmount = map.get("invoiceAmount").toString();

                String status = map.get("status").toString();

                PayNotifyVo payNotifyVo = new PayNotifyVo();

                payNotifyVo.setStatus(status);

                payNotifyVo.setMerOrderId(merOrderId);
                payNotifyVo.setBuyerPayAmount(invoiceAmount);
                payNotifyVo.setTotalAmount(totalAmount);
                appNotify(payNotifyVo);
            } catch (Exception e) {
                log.error(e.getMessage());
                log.error(e.getMessage());
                log.error(e.getMessage());
            }

        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R appNotify(PayNotifyVo data) {
        String status = data.getStatus();
        String merOrderId = data.getMerOrderId();
        if (!"TRADE_SUCCESS".equals(status)) {
            return R.error(status);
        }
        if (StrUtil.isNotEmpty(merOrderId)) {
            RechargeWithdrawRecord rechargeWithdrawRecord = rechargeWithdrawRecordMapper.selectOne(Wrappers
                .<RechargeWithdrawRecord>lambdaQuery().eq(RechargeWithdrawRecord::getTranNumber, merOrderId));
            if (rechargeWithdrawRecord == null) {
                return R.error("FAILED");
            }
            // 判断订单状态是否修改过来---避免重复回调
            Integer state = rechargeWithdrawRecord.getState();
            if (state == 3) {
                return R.error("SUCCESS");
            }
            // 订单金额 单位：分
            String totalAmount = data.getTotalAmount();
            // 实付金额 单位：分
            String buyerPayAmount = data.getBuyerPayAmount();
            // 可能是红包暂不校验
            // if (!totalAmount.equals(buyerPayAmount)) {
            // log.error("pc支付回调：订单金额:" + totalAmount + ",与实收金额:" + buyerPayAmount + ",不一致");
            // return R.error("FAILED");
            // }
            // BigDecimal realReceiptAmount = new BigDecimal(buyerPayAmount);
            // if (realReceiptAmount
            // .compareTo(rechargeWithdrawRecord.getAmount().multiply(new BigDecimal(100))) != 0) {
            // log.error("pc支付回调：实收金额:" + realReceiptAmount + ",与订单金额:" + rechargeWithdrawRecord.getAmount()
            // + ",不一致");
            // return R.error("FAILED");
            // }
            Date updateTime = new Date();
            String accountUuid = rechargeWithdrawRecord.getAccountUuid();
            BigDecimal amount = rechargeWithdrawRecord.getAmount();
            /**
             * 在对业务数据进行状态检查和处理之前，这里要使用数据锁进行并发控制，以避免函数重入导致的数据混乱 尝试获取锁成功之后才去处理数据，相比于同步锁，这里不会去等待，获取不到则直接返回
             */
            if (lock.tryLock()) {
                if ("TRADE_SUCCESS".equals(status)) {
                    try {
                        // 支付成功
                        // 状态( 1-审核中，2-审核失败，3-提现/充值成功 4-用户取消 5-超时 6-待支付)
                        rechargeWithdrawRecordMapper.update(null,
                            Wrappers.<RechargeWithdrawRecord>lambdaUpdate()
                                .eq(RechargeWithdrawRecord::getTranNumber, merOrderId)
                                .set(RechargeWithdrawRecord::getState, 3)
                                .set(RechargeWithdrawRecord::getUpdateTime, updateTime))
                        ;
                        log.info("修改充值记录表成功，流水号：{}", merOrderId);
                        // 给对应用户加资产
                        RechargeAssets rechargeAssets = rechargeAssetsMapper.selectOne(Wrappers
                            .<RechargeAssets>lambdaQuery().eq(RechargeAssets::getAccountUuid, accountUuid));
                        BigDecimal balance = rechargeAssets.getBalance();
                        BigDecimal currentBalance = balance.add(amount);
                        rechargeAssetsMapper.update(null, Wrappers.<RechargeAssets>lambdaUpdate()
                            .eq(RechargeAssets::getAccountUuid, accountUuid)
                            .set(RechargeAssets::getBalance, currentBalance).set(
                                RechargeAssets::getTotalAmount, rechargeAssets.getTotalAmount().add(amount)));
                        log.info("增加资产表成功，资产id:{},增加金额:{}", rechargeAssets.getId(), amount);
                        // TODO 组装消息发送
                        log.info("支付回调成功================");
                        log.info("支付回调成功================");
                        log.info("支付回调成功================");
                        return R.ok("SUCCESS");
                    } finally {
                        // 要主动释放锁
                        lock.unlock();
                    }
                }
            }
        }

        return R.error("FAILED");
    }

    /**
     * 获取支付方式配置
     * 
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/03
     */
    @Override
    public R getPayConfigService() {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("WEIXINPAYSTATE", globalConfigService.getGlobalConfig(WEIXINPAYSTATE));
        resultMap.put("ALIPAYSTATE", globalConfigService.getGlobalConfig(ALIPAYSTATE));
        return R.okData(resultMap);
    }

    /**
     * 微信小程序支付跳转链接并下单
     * 
     * @param amount 金额
     * @param payType 支付方式
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/10
     */
    @Override
    @Transactional
    public R getWeiXinLinkService(BigDecimal amount, int payType,String remark) {
        long timeStamp = DateUtil.date().getTime();
        // 订单号必须以35H9开头
        String merOrderId = "384G" + timeStamp;
        Account userInfo = accountService.getUserInfo();
        String accountUUID = userInfo.getUuid();

        // 资产流水表
        RechargeFlow rechargeFlow = new RechargeFlow();
        // 资金订单表
        RechargeWithdrawRecord rechargeWithdrawRecord = new RechargeWithdrawRecord();

        rechargeWithdrawRecord.setOperateUuid(userInfo.getOperateUuid()).setAccountUuid(accountUUID)
            .setSourceFunds(2).setAmount(amount).setTranNumber(merOrderId).setPlatform(3).setType(1)
                .setRemark(remark)
            .setState(6).setPayType(payType);

        rechargeFlow.setOperateUuid(userInfo.getOperateUuid()).setAccountUuid(accountUUID).setAmount(amount)
            .setType(1);

        // 充值时的余额
        RechargeAssets rechargeAssets = rechargeAssetsMapper.selectOne(
            Wrappers.<RechargeAssets>lambdaQuery().eq(RechargeAssets::getAccountUuid, accountUUID));
        // 资产信息为空则生成资产信息
        if (null == rechargeAssets) {
            rechargeAssets = new RechargeAssets();
            rechargeAssets.setTotalAmount(BigDecimal.ZERO);
            rechargeAssets.setBalance(BigDecimal.ZERO);
            rechargeAssets.setFreeze(BigDecimal.ZERO);
            rechargeAssets.setAccountUuid(accountUUID);
            rechargeAssets.setOperateUuid(userInfo.getOperateUuid());
            rechargeAssetsMapper.insert(rechargeAssets);
        }
        rechargeWithdrawRecord.setBalance(rechargeAssets.getBalance());
        // 插入充值订单
        rechargeWithdrawRecordMapper.insert(rechargeWithdrawRecord);
        rechargeFlow.setOrderId(rechargeWithdrawRecord.getId());
        // 插入资金流水 2支付宝
        if (payType == 2) {
            rechargeFlowMapper.insert(rechargeFlow);
            log.info("插入流水记录id", rechargeFlow.getId());
            log.info("插入流水记录id", rechargeFlow.getId());
            log.info("插入流水记录id", rechargeFlow.getId());
        }

        String weixinLink = miniPayUtil.getWeixinLink(merOrderId, userInfo.getOperateUuid());
        Map<String, String> map = new HashMap<>();
        map.put("merOrderId", merOrderId);
        map.put("linkUrl", weixinLink);
        return R.okData(map);
    }

    /**
     * 创建订单
     *
     * @param amount
     * @param remark
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/11/22
     */
    @Override
    @Transactional
    public R createOrderService(HttpServletRequest request,BigDecimal amount, String remark) {
        Map<String, String> map = new HashMap<>();
        Account userInfo = accountService.getUserInfo();
        String accountUUID = userInfo.getUuid();

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("orderTitle", "余额充值");
        jsonObject.put("didSymbol", userInfo.getDidSymbol());
        jsonObject.put("applicationId", 1);
        jsonObject.put("accountUUID", userInfo.getUuid());
        jsonObject.put("orderAmount", amount);
        jsonObject.put("actualAmount", amount);
        jsonObject.put("payExpiredTime", DateUtil.formatDateTime(DateUtil.offsetMinute(new Date(), 15)));
        jsonObject.put("payCallbackNotifyAddress",payCallbackNotifyAddress+"aggregatePay/appNotify1");
        jsonObject.put("channelAppId", request.getHeader("channelAppId"));
        jsonObject.put("ipAddr", IpUtil.getRealIp(request));

        R order = iOrderService.createOrder(jsonObject);
        if (ObjectUtil.equals(order.get("code"), 200)) {
            Object data = order.get("data");
            Map orderMap = JSON.parseObject(JSON.toJSONString(data), Map.class);
            if (orderMap != null) {
                String orderNumber = orderMap.get("orderNumber").toString();
                // 资产流水表
//                RechargeFlow rechargeFlow = new RechargeFlow();
                // 资金订单表
                RechargeWithdrawRecord rechargeWithdrawRecord = new RechargeWithdrawRecord();
                rechargeWithdrawRecord.setOperateUuid(userInfo.getOperateUuid()).setAccountUuid(accountUUID)
                    .setSourceFunds(2).setAmount(amount).setTranNumber(orderNumber).setPlatform(3).setType(1)
                    .setRemark(remark).setState(6);
//                rechargeFlow.setOperateUuid(userInfo.getOperateUuid()).setAccountUuid(accountUUID)
//                    .setAmount(amount).setType(1);
                // 充值时的余额
                RechargeAssets rechargeAssets = rechargeAssetsMapper.selectOne(
                    Wrappers.<RechargeAssets>lambdaQuery().eq(RechargeAssets::getAccountUuid, accountUUID));
                // 资产信息为空则生成资产信息
                if (null == rechargeAssets) {
                    rechargeAssets = new RechargeAssets();
                    rechargeAssets.setTotalAmount(BigDecimal.ZERO);
                    rechargeAssets.setBalance(BigDecimal.ZERO);
                    rechargeAssets.setFreeze(BigDecimal.ZERO);
                    rechargeAssets.setAccountUuid(accountUUID);
                    rechargeAssets.setOperateUuid(userInfo.getOperateUuid());
                    rechargeAssetsMapper.insert(rechargeAssets);
                }
                rechargeWithdrawRecord.setBalance(rechargeAssets.getBalance());
                // 插入充值订单
                rechargeWithdrawRecordMapper.insert(rechargeWithdrawRecord);
//                rechargeFlow.setOrderId(rechargeWithdrawRecord.getId());
                // 插入资金流水 2支付宝 如果这里使用微信小程序支付都插入
//                rechargeFlowMapper.insert(rechargeFlow);
                map.put("merOrderId", orderNumber);
            }
        } else {
           return order;
        }
        return R.okData(map);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R appNotify1(OrderNotifyRequest data) {
        Integer status = data.getPayStatus();
        String merOrderId = data.getOrderNumber();
        Integer payType = data.getPayType();
        if (!ObjectUtil.equals(status,1)) {
            return R.error("FAILED");
        }
        if (StrUtil.isNotEmpty(merOrderId)) {
            RechargeWithdrawRecord rechargeWithdrawRecord = rechargeWithdrawRecordMapper.selectOne(Wrappers
                    .<RechargeWithdrawRecord>lambdaQuery().eq(RechargeWithdrawRecord::getTranNumber, merOrderId));
            if (rechargeWithdrawRecord == null) {
                return R.error("FAILED");
            }
            // 判断订单状态是否修改过来---避免重复回调
            Integer state = rechargeWithdrawRecord.getState();
            if (state == 3) {
                return R.error("SUCCESS");
            }
            // 订单金额 单位：分
            BigDecimal buyerPayAmount = data.getPayAmount();
            // 实付金额 单位：分
            BigDecimal orderAmount = rechargeWithdrawRecord.getAmount();



            Date updateTime = new Date();
            String accountUuid = rechargeWithdrawRecord.getAccountUuid();
            BigDecimal amount = rechargeWithdrawRecord.getAmount();
            /**
             * 在对业务数据进行状态检查和处理之前，这里要使用数据锁进行并发控制，以避免函数重入导致的数据混乱 尝试获取锁成功之后才去处理数据，相比于同步锁，这里不会去等待，获取不到则直接返回
             */
            if (lock.tryLock()) {
                if (ObjectUtil.equals(status,1)) {
                    try {
                        // 支付成功
                        // 状态( 1-审核中，2-审核失败，3-提现/充值成功 4-用户取消 5-超时 6-待支付)
                        rechargeWithdrawRecordMapper.update(null,
                                Wrappers.<RechargeWithdrawRecord>lambdaUpdate()
                                        .eq(RechargeWithdrawRecord::getTranNumber, merOrderId)
                                        .set(RechargeWithdrawRecord::getState, 3)
                                        .set(RechargeWithdrawRecord::getPayType, payType)
                                        .set(RechargeWithdrawRecord::getUpdateTime, updateTime))
                        ;
                        log.info("修改充值记录表成功，流水号：{}", merOrderId);
                        // 给对应用户加资产
                        RechargeAssets rechargeAssets = rechargeAssetsMapper.selectOne(Wrappers
                                .<RechargeAssets>lambdaQuery().eq(RechargeAssets::getAccountUuid, accountUuid));
                        BigDecimal balance = rechargeAssets.getBalance();
                        BigDecimal currentBalance = balance.add(amount);
                        rechargeAssetsMapper.update(null, Wrappers.<RechargeAssets>lambdaUpdate()
                                .eq(RechargeAssets::getAccountUuid, accountUuid)
                                .set(RechargeAssets::getBalance, currentBalance).set(
                                        RechargeAssets::getTotalAmount, rechargeAssets.getTotalAmount().add(amount)));
                        //增加流水
                        RechargeFlow rechargeFlow = new RechargeFlow();
                        rechargeFlow.setOperateUuid(rechargeWithdrawRecord.getOperateUuid())
                                .setOrderId(rechargeWithdrawRecord.getId())
                                .setAccountUuid(accountUuid).setAmount(amount).setType(1);
                        rechargeFlowMapper.insert(rechargeFlow);
                        log.info("增加资产表成功，资产id:{},增加金额:{}", rechargeAssets.getId(), amount);
                        // TODO 组装消息发送
                        log.info("支付回调成功================");
                        log.info("支付回调成功================");
                        log.info("支付回调成功================");
                        return R.ok("SUCCESS");
                    } finally {
                        // 要主动释放锁
                        lock.unlock();
                    }
                }
            }
        }
        return R.error("FAILED");
    }

}
