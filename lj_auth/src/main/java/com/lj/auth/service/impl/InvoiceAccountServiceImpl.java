package com.lj.auth.service.impl;

import com.lj.auth.common.R;
import com.lj.auth.exception.ResponseEnum;
import com.lj.auth.exception.ServiceException;
import com.lj.auth.mapper.RechargeRecordMapper;
import com.lj.auth.util.PageUtils;
import com.lj.auth.util.UUIdUtil;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.*;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lj.auth.domain.InvoiceAccount;
import com.lj.auth.mapper.InvoiceAccountMapper;
import com.lj.auth.service.InvoiceAccountService;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

@Service
public class InvoiceAccountServiceImpl extends ServiceImpl<InvoiceAccountMapper, InvoiceAccount>
    implements InvoiceAccountService {
    @Resource
    private RechargeRecordMapper rechargeRecordMapper;
    @Resource
    private InvoiceAccountMapper invoiceAccountMapper;

    @Override
    @Transactional
    public R addService(InvoiceAccount invoiceAccount) {
        // 个人只能开普票
        if (Objects.equals(invoiceAccount.getType(), 1)
            && !Objects.equals(invoiceAccount.getTicketType(), 1)) {
            throw new ServiceException(ResponseEnum.InvoiceTypeMismatch);
        }
        // 普票--电子发票
        if (Objects.equals(invoiceAccount.getTicketType(), 1)
            && !Objects.equals(invoiceAccount.getTicketMedium(), 2)) {
            throw new ServiceException(ResponseEnum.MismatchedInvoicingMedia);
        }
        // 专票--纸质发票
        if (Objects.equals(invoiceAccount.getTicketType(), 2)
            && !Objects.equals(invoiceAccount.getTicketMedium(), 1)) {
            throw new ServiceException(ResponseEnum.MismatchedInvoicingMedia);
        }
        // 统计发票金额
        String orderIds = invoiceAccount.getRechargeRecordIds();
        List<String> ids = Arrays.asList(orderIds.split(","));
        BigDecimal ticketAmount = rechargeRecordMapper.ticketAmount(ids, invoiceAccount.getAccountUuid());
        if (Objects.equals(ticketAmount, BigDecimal.ZERO)) {
            throw new ServiceException(ResponseEnum.OrderParameterAbnormality);
        }
        invoiceAccount.setTicketAmount(ticketAmount);
        invoiceAccount.setExamineState(1);// 审核状态 1-待审核 2-审核通过 3-审核驳回
        invoiceAccount.setTicketUuid("FP" + UUIdUtil.createUUId(15));
        this.save(invoiceAccount);
        // 修改开票状态
        int i = rechargeRecordMapper.updateTicketState(ids, invoiceAccount.getAccountUuid());
        if (i < 0) {
            throw new ServiceException(ResponseEnum.OrderParameterAbnormality);
        }
        return R.ok();
    }

    @Override
    public R getPageService(Integer page, Integer pageSize, String accountUUID, Integer ticketType,
        Integer examineState, Date startTime, Date endTime, String ticketUuid) {
        Integer totalCount = invoiceAccountMapper.invoiceAccountCount(accountUUID, ticketType, examineState,
            startTime, endTime, ticketUuid);

        int start = (page - 1) * pageSize;
        List<InvoiceAccount> list = new ArrayList<>();
        if (start < totalCount) {
            list = invoiceAccountMapper.getInvoiceAccountPage(start, pageSize, accountUUID, ticketType,
                examineState, startTime, endTime, ticketUuid);
        }
        PageUtils pageUtils = new PageUtils(totalCount, pageSize, page, list);
        return R.okData(pageUtils);
    }

    /**
     * @param id ID
     * @param accountUUID 用户uuid
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/28
     */
    @Override
    public R getInfoService(Long id, String accountUUID) {
        Map<String, Object> map = new HashMap<>();
        InvoiceAccount invoiceAccount = invoiceAccountMapper.selectById(id);
        if (null == invoiceAccount) {
            throw new ServiceException(ResponseEnum.InvoiceParameterAbnormality);
        }
        String orderIds = invoiceAccount.getRechargeRecordIds();
        List<String> bsnOrderIds = Arrays.asList(orderIds.split(","));
        List<Map<String, String>> orderBsnInfo =
            rechargeRecordMapper.getInvoicInfoByIds(bsnOrderIds, accountUUID);
        map.put("invoiceAccount", invoiceAccount);
        map.put("orderInfos", orderBsnInfo);

        return R.okData(map);
    }
}
