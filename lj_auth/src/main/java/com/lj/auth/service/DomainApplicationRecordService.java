package com.lj.auth.service;

import com.lj.auth.common.R;
import com.lj.auth.domain.DomainApplicationRecord;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lj.auth.util.PageUtils;

/**
 * <p>
 * 域名应用记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-07
 */
public interface DomainApplicationRecordService extends IService<DomainApplicationRecord> {

    /**
     * 新增域名应用纪录
     *
     * @param accountUuid 用户uuid
     * @param opbChainId 链框架ID
     * @param domain 域名
     * @param manageId 应用Id
     * <AUTHOR>
     * @date 2024/04/07
     */
    void addRecord(String accountUuid, Integer opbChainId, String domain, int manageId);

    /**
     * 修改域名应用纪录
     *
     * @param accountUuid 用户uuid
     * @param opbChainId 链框架Id
     * @param newOpbChainId 链框架Id
     * @param domain 域名
     * @param manageId 应用id
     * <AUTHOR>
     * @date 2024/04/07
     */
    void updateRecord(String accountUuid, Integer opbChainId, Integer newOpbChainId, String domain,
        int manageId);

    /**
     * 移除域名应用纪录
     *
     * @param accountUuid 用户uuid
     * @param opbChainId 链框架id
     * @param domain 域名
     * @param manageId 应用id
     * <AUTHOR>
     * @date 2024/04/07
     */
    void deleteRecord(String accountUuid, Integer opbChainId, String domain, int manageId);

    /**
     * 获取应用域名数量
     * 
     * @param accountUUID 用户uuid
     * @return {@link Integer }
     * <AUTHOR>
     * @date 2024/04/07
     */
    Integer getDomainApplicationCount(String accountUUID);

    /**
     * 获取域名应用列表分页查询
     * 
     * @param accountUUID 用户uuid
     * @param page 页
     * @param pageSize 页码
     * @return {@link PageUtils }
     * <AUTHOR>
     * @date 2024/04/07
     */
    PageUtils getDomainApplicationList(String accountUUID, Integer page, Integer pageSize);

    /**
     * 获取未应用列表
     * 
     * @param accountUUID 用户uuid
     * @param page 页
     * @param pageSize 页码
     * @return {@link PageUtils }
     * <AUTHOR>
     * @date 2024/04/07
     */
    PageUtils getDomainNotApplicationList(String accountUUID, Integer page, Integer pageSize);

    /**
     * 域名应用详情
     * 
     * @param domain 域名
     * @param accountUUID 用户uuid
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/07
     */
    R getDomainApplicationDetail(String domain, String accountUUID);
}
