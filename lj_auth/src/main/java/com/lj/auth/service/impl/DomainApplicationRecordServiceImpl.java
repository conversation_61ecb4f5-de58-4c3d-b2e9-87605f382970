package com.lj.auth.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lj.auth.common.R;
import com.lj.auth.domain.DomainAccount;
import com.lj.auth.domain.DomainApplicationRecord;
import com.lj.auth.domain.vo.ApplicationDetailVo;
import com.lj.auth.exception.ResponseEnum;
import com.lj.auth.exception.ServiceException;
import com.lj.auth.mapper.DomainAccountMapper;
import com.lj.auth.mapper.DomainApplicationRecordMapper;
import com.lj.auth.service.DomainApplicationRecordService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lj.auth.util.PageUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 域名应用记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-07
 */
@Service
public class DomainApplicationRecordServiceImpl
    extends ServiceImpl<DomainApplicationRecordMapper, DomainApplicationRecord>
    implements DomainApplicationRecordService {
    @Resource
    private DomainApplicationRecordMapper domainApplicationRecordMapper;
    @Resource
    private DomainAccountMapper domainAccountMapper;

    /**
     * 新增域名应用纪录
     *
     * @param accountUuid 用户uuid
     * @param opbChainId 链框架ID
     * @param domain 域名
     * @param manageId 应用Id
     * <AUTHOR>
     * @date 2024/04/07
     */
    @Override
    public void addRecord(String accountUuid, Integer opbChainId, String domain, int manageId) {
        DomainApplicationRecord domainApplicationRecord = new DomainApplicationRecord();
        domainApplicationRecord.setDomain(domain);
        domainApplicationRecord.setApplicationManageId(manageId);
        domainApplicationRecord.setAccountUuid(accountUuid);
        domainApplicationRecord.setOpbChainId(opbChainId);
        this.save(domainApplicationRecord);
    }

    /**
     * 修改域名应用纪录
     *
     * @param accountUuid 用户uuid
     * @param opbChainId 链框架Id
     * @param newOpbChainId 链框架Id
     * @param domain 域名
     * @param manageId 应用id
     * <AUTHOR>
     * @date 2024/04/07
     */
    @Override
    public void updateRecord(String accountUuid, Integer opbChainId, Integer newOpbChainId, String domain,
        int manageId) {
        DomainApplicationRecord one = this.getOne(Wrappers.<DomainApplicationRecord>lambdaQuery()
            .eq(DomainApplicationRecord::getAccountUuid, accountUuid)
            .eq(DomainApplicationRecord::getDomain, domain)
            .eq(DomainApplicationRecord::getOpbChainId, opbChainId)
            .eq(DomainApplicationRecord::getApplicationManageId, manageId));
        if (one == null) {
            throw new ServiceException(ResponseEnum.DomainNameAddressMappingError);
        }
        this.update(
            Wrappers.<DomainApplicationRecord>lambdaUpdate().eq(DomainApplicationRecord::getId, one.getId())
                .set(DomainApplicationRecord::getOpbChainId, newOpbChainId));

    }

    /**
     * 移除域名应用纪录
     *
     * @param accountUuid 用户uuid
     * @param opbChainId 链框架id
     * @param domain 域名
     * @param manageId 应用id
     * <AUTHOR>
     * @date 2024/04/07
     */
    @Override
    public void deleteRecord(String accountUuid, Integer opbChainId, String domain, int manageId) {
        this.remove(Wrappers.<DomainApplicationRecord>lambdaQuery()
            .eq(DomainApplicationRecord::getAccountUuid, accountUuid)
            .eq(DomainApplicationRecord::getOpbChainId, opbChainId)
            .eq(DomainApplicationRecord::getDomain, domain)
            .eq(DomainApplicationRecord::getApplicationManageId, manageId));
    }

    /**
     * 获取域名应用数量
     * 
     * @param accountUUID 用户uuid
     * @return {@link Integer }
     * <AUTHOR>
     * @date 2024/04/07
     */
    @Override
    public Integer getDomainApplicationCount(String accountUUID) {
        List<DomainApplicationRecord> list = this.list(Wrappers.<DomainApplicationRecord>lambdaQuery()
            .eq(DomainApplicationRecord::getAccountUuid, accountUUID));
        // 对相同域名去重
        // 根据某个字段进行去重和统计
        Map<String, Long> domainList = list.stream()
            .collect(Collectors.groupingBy(DomainApplicationRecord::getDomain, Collectors.counting()));
        int size = domainList.keySet().size();
        return size;
    }

    /**
     * 获取应用列表分页查询
     * 
     * @param accountUUID 用户uuid
     * @param page 页
     * @param pageSize 页码
     * @return {@link PageUtils }
     * <AUTHOR>
     * @date 2024/04/07
     */
    @Override
    public PageUtils getDomainApplicationList(String accountUUID, Integer page, Integer pageSize) {
        int start = (page - 1) * pageSize;
        Integer count = domainApplicationRecordMapper.groupByDomainCount(accountUUID);
        List<Map<String, Object>> result =
            domainApplicationRecordMapper.groupByDomain(accountUUID, start, pageSize);
        PageUtils pageUtils = new PageUtils(count, pageSize, page, result);
        return pageUtils;
    }

    /**
     * 获取未应用列表
     * 
     * @param accountUUID 用户uuid
     * @param page 页
     * @param pageSize 页码
     * @return {@link PageUtils }
     * <AUTHOR>
     * @date 2024/04/07
     */
    @Override
    public PageUtils getDomainNotApplicationList(String accountUUID, Integer page, Integer pageSize) {
        List<String> result = domainApplicationRecordMapper.getApplicationList(accountUUID);

        List<DomainAccount> domainAccounts = domainAccountMapper.selectList(
            new LambdaQueryWrapper<DomainAccount>().eq(DomainAccount::getAccountUuid, accountUUID));

        List<String> allDomain =
            domainAccounts.stream().map(DomainAccount::getDomain).collect(Collectors.toList());

        allDomain.removeAll(result);
        List<String> subList =
            allDomain.stream().skip((page - 1) * pageSize).limit(pageSize).collect(Collectors.toList());
        PageUtils pageUtils = new PageUtils(allDomain.size(), pageSize, page, subList);
        return pageUtils;
    }

    /**
     * 获取域名应用详情
     * 
     * @param domain 域名
     * @param accountUUID 用户uuid
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/07
     */
    @Override
    public R getDomainApplicationDetail(String domain, String accountUUID) {
        List<ApplicationDetailVo> result =
            domainApplicationRecordMapper.getApplicationDetail(domain, accountUUID);
        return R.okData(result);
    }
}
