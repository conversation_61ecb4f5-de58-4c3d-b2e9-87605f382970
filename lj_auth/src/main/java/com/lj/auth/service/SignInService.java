package com.lj.auth.service;

import com.lj.auth.common.R;
import com.lj.auth.domain.SignIn;
import com.baomidou.mybatisplus.extension.service.IService;

public interface SignInService extends IService<SignIn> {

    /**
     * 查询签到纪录
     * 
     * @param page 页
     * @param pageSize 页大小
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/02
     */
    R signInDetailService(Integer page, Integer pageSize);

    /**
     * 查询签到周期纪录
     * 
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/02
     */
    R weeklyCheckInRecordService();

    /**
     * 签到
     * 
     * @param address 链地址
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/02
     */
    R signInService(String address);

    /**
     * 签到规则
     * 
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/02
     */
    R signInRulesService();

    /**
     * 签到记录详情
     * 
     * @param id
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/10
     */
    R signInDetailRecordService(Integer id);
}
