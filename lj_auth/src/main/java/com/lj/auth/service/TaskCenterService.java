package com.lj.auth.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lj.auth.common.R;
import com.lj.auth.domain.TaskCenter;

import javax.servlet.http.HttpServletRequest;

/**
 * <p>
 * 任务中心 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-28
 */
public interface TaskCenterService extends IService<TaskCenter> {

    /**
     * 统计个人完成的任务
     *
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/05/28
     */
    R statisticalTasksService();

    /**
     * 获取限时任务列表
     *
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/05/28
     */
    R getLimitedTimeTaskService();

    /**
     * 获取任务类型
     *
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/05/28
     */
    R getCenterTypeService();

    /**
     * 返回任务列表展示--日常任务-周期任务-新手任务
     *
     * @param request
     * @param taskTypeId
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/05/28
     */
    R getTaskListService(HttpServletRequest request,Integer taskTypeId);

    /**
     * 邀请用户列表展示
     *
     * @param page
     * @param pageSize
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/05/29
     */
    R getNewUserTaskListService(Integer page, Integer pageSize);

    /**
     * 领取 sp
     *
     * @param id -任务id
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/05/30
     */
    R getReceiceService(Integer id, String address);

    /**
     * 一件领取sp
     *
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/05/30
     */
    R getOneClickClaimService(String address);

    /**
     * 统计获得总SP
     *
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/05/30
     */
    R getStatisticalSPService();

    /**
     * 浏览社区热门动态30s
     *
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/05/30
     */
    R getDynamic30SService();

    /**
     * 浏览社区最新动态30s
     *
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/05/30
     */
    R getNewDynamic30SService();

    /**
     * 奖励明细列表
     * 
     * @param page
     * @param pageSize
     * @param type 0-全部 1-签到 2-任务
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/06/03
     */
    R getListSpDetail(Integer page, Integer pageSize, Integer type);

    /**
     * @param hash
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/06/03
     */
    R getListSpDetailRecord(String hash);

    /**
     * 获取任务中心签到和任务统计
     * 
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/06/04
     */
    R getTaskCenterStatisticsService();

    /**
     * 获取任务中心任务变动推送
     * 
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/06/04
     */
    R getTaskChangesService();

    /**
     * 任务领取状态
     * 
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/07/03
     */
    R isReceiveStateService();

    /**
     * 首页红点数量
     *
     * @param request
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/03/13
     */
    R numberOfRedDotsService(HttpServletRequest request);

    /**
     * @param request
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/03/18
     */
    R listRedDotsCountService(HttpServletRequest request);
}
