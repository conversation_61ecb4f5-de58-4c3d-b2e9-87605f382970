package com.lj.auth.service.impl;

import com.lj.auth.domain.NoticeAccount;
import com.lj.auth.domain.SystemNotice;
import com.lj.auth.mapper.NoticeAccountMapper;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lj.auth.mapper.TransferNoticeMapper;
import com.lj.auth.domain.TransferNotice;
import com.lj.auth.service.TransferNoticeService;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

@Service
public class TransferNoticeServiceImpl extends ServiceImpl<TransferNoticeMapper, TransferNotice>
    implements TransferNoticeService {
    @Resource
    private NoticeAccountMapper noticeAccountMapper;

    /**
     * 添加用户转账通知
     *
     * @param accountUUID 用户uuid
     * @param title 公告标题
     * @param content 公告内容
     * @param description 描述
     * <AUTHOR>
     * @date 2024/04/02
     */
    @Override
    @Transactional
    public void addUserNoticeMessage(String uuid, String title, String content, String description) {
        TransferNotice transferNotice =
            new TransferNotice().setContent(content).setTitle(title).setDescription(description);
        this.save(transferNotice);
        // 插入通知人
        NoticeAccount noticeAccount =
            new NoticeAccount().setNoticeId(transferNotice.getId()).setAccountUuid(uuid).setSource(2);
        noticeAccountMapper.insert(noticeAccount);
    }
}
