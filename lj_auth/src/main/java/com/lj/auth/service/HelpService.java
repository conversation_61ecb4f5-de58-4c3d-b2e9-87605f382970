package com.lj.auth.service;

import com.lj.auth.common.R;
import com.lj.auth.domain.Help;
import com.baomidou.mybatisplus.extension.service.IService;

public interface HelpService extends IService<Help> {

    /**
     * 获取帮助中心列表
     *
     * @param page     页
     * @param pageSize 页大小
     * @param keyword  关键字
     * @param type
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/29
     */
    R getHelpListService(int page, int pageSize, String keyword,Integer type);
}
