package com.lj.auth.service.impl;

import com.lj.auth.common.R;
import com.lj.auth.domain.TransactionType;
import com.lj.auth.domain.vo.TransactionTypeVo;
import com.lj.auth.mapper.TransactionTypeMapper;
import com.lj.auth.service.TransactionTypeService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 交易类型表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-07
 */
@Service
public class TransactionTypeServiceImpl extends ServiceImpl<TransactionTypeMapper, TransactionType>
    implements TransactionTypeService {

    /**
     * 获取交易类型
     * 
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/07
     */
    @Override
    public R getTransactionTypeService() {
        // List<TransactionType> list = this.list();
        List<TransactionTypeVo> list = new ArrayList<>();
        list.add(new TransactionTypeVo().setId(1L).setType("全部类型").setTypeId(0).setCreateTime(new Date())
                .setUpdateTime(new Date()));
        list.add(new TransactionTypeVo().setId(2L).setType("支出").setTypeId(1).setCreateTime(new Date())
            .setUpdateTime(new Date()));
        list.add(new TransactionTypeVo().setId(3L).setType("收入").setTypeId(2).setCreateTime(new Date())
            .setUpdateTime(new Date()));
        list.sort(Comparator.comparingInt(TransactionTypeVo::getTypeId));
        return R.okData(list);
    }
}
