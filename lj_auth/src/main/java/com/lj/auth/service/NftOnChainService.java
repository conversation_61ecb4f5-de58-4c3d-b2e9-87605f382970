package com.lj.auth.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lj.auth.common.R;
import com.lj.auth.domain.Nft;
import com.lj.auth.domain.OrderDomainRenewalNft;
import org.web3j.protocol.core.methods.response.TransactionReceipt;

public interface NftOnChainService extends IService<Nft>{

     void transferNFTProcessV2(Long domainRenewalNFTId , String recycleAddress);
     void transferNFTProcess(Long domainRenewalNFTId , String recycleAddress);
     void refundNFTProcess(Long orderDomainRenewalNftRefundId);
    TransactionReceipt transferNFT(OrderDomainRenewalNft orderDomainRenewalNft, String platformAccountAddress, String contractAddress, String NFTOwnerAddress, String NFTRecycleAddress, Integer tokenId);
    R queryTransactionReceipt(String transactionHash);
    TransactionReceipt setApprovalForAll(String privateKey,String contractAddress,String approvalAddress,Boolean approved);
    Boolean isApprovedForAll( String ownerAddress, String contractAddress, String approvedAddress);
}
