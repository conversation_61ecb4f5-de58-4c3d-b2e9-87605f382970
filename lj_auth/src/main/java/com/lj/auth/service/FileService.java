
package com.lj.auth.service;


import com.lj.auth.common.R;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * FileService manage.
 *
 * <AUTHOR>
 * @date 2023/10/18
 */
public interface FileService {

    /**
     * 上传单张照片
     * 
     * @param picture
     * @return {@link R}
     */
    R upload(MultipartFile picture,String folderName);

    /**
     * 上传多张照片
     * 
     * @param pictures
     * @return {@link R}
     */
    R multiUpload(List<MultipartFile> pictures,String folderName);

}
