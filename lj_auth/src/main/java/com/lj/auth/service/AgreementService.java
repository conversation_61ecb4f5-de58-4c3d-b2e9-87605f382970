package com.lj.auth.service;

import com.lj.auth.common.R;
import com.lj.auth.domain.Param.*;
import com.lj.auth.domain.Result.ValidAgreementVersionResult;
import com.lj.auth.util.PageUtils;

import javax.servlet.http.HttpServletRequest;

public interface AgreementService  {


    R syncRecord(String accountUUID);
    R syncAccountAgreementRecord(String accountUUID);
    ValidAgreementVersionResult validVersion(HttpServletRequest request);
    R agreementConfirm(AgreementConfirmParam agreementConfirmParam);
    PageUtils versionList(AgreementVersionListParam agreementVersionListParam);
    R editAgreement(EditAgreementParam editAgreementParam);

    PageUtils accountVersionRecord(AccountAgreementReadedRecordParam accountAgreementReadedRecordParam);
}
