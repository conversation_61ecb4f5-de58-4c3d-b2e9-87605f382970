package com.lj.auth.service;

import com.lj.auth.domain.TransferNotice;
import com.baomidou.mybatisplus.extension.service.IService;

public interface TransferNoticeService extends IService<TransferNotice> {

    /**
     * 添加用户转账通知
     * 
     * @param accountUUID 用户uuid
     * @param title 公告标题
     * @param content 公告内容
     * @param description 描述
     * <AUTHOR>
     * @date 2024/04/02
     */
    void addUserNoticeMessage(String accountUUID, String title, String content, String description);
}
