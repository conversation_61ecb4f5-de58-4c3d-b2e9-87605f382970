package com.lj.auth.service;

import com.alibaba.fastjson2.JSONObject;
import com.lj.auth.common.R;

/**
 * @author: wxm
 * @description:
 */
public interface HomePageService {

    R getPart1(String channel);

    R getPart2(String channel);

    R getPart3(String channel,String version);

    /**
     * 获取话费功能区
     * @param channel
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/01/15
     */
    R getPhonePart(String channel);
}
