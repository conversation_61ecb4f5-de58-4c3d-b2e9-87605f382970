package com.lj.auth.service;

import com.lj.auth.common.R;
import com.lj.auth.domain.LjAbout;
import com.baomidou.mybatisplus.extension.service.IService;

import java.net.InetSocketAddress;

public interface AboutService extends IService<LjAbout> {

    /**
     * 获取关于我们配置
     * 
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/27
     */
    R commonConfig();

    /**
     * 获取客服相关信息
     * 
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/16
     */
    R getCustomerService();

    /**
     * 获取邀请海报
     * 
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/18
     */
    R getPosterService();

    /**
     * 获取基础logo
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/25
     */
    R getLogoService();

    /**
     * 增加接口记录
     *
     * @param satoken
     * @param channel
     * @param edition
     * @param ip
     * @param path
     * @param requestParam
     */
    void addInterfaceRecordService(String satoken, String channel, String edition, String ip, String path, String requestParam);
}
