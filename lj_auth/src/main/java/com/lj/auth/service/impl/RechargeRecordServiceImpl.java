package com.lj.auth.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.lj.auth.common.R;
import com.lj.auth.domain.*;
import com.lj.auth.domain.vo.RechargeFlowVo;
import com.lj.auth.domain.vo.WithdrawServiceChargeInfo;
import com.lj.auth.mapper.*;
import com.lj.auth.service.*;
import com.lj.auth.util.DingTalkPushUtil2;
import com.lj.auth.util.LockUtil;
import com.lj.auth.util.PageUtils;
import com.lj.auth.util.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.message.ReusableMessage;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;
import org.web3j.abi.datatypes.Int;

import javax.annotation.Resource;

import static com.lj.auth.common.CommonConstant.*;

@Service
@Slf4j
public class RechargeRecordServiceImpl extends ServiceImpl<RechargeRecordMapper, RechargeRecord>
    implements RechargeRecordService {
    @Resource
    private RechargeAssetsMapper rechargeAssetsMapper;
    @Resource
    private AccountService accountService;
    @Resource
    private AccountMapper accountMapper;
    @Resource
    private RechargeRecordMapper rechargeRecordMapper;
    @Resource
    private AccountCardService accountCardService;
    @Resource
    private EnergyRechargeFlowMapper energyRechargeFlowMapper;
    @Resource
    private GlobalConfigService globalConfigService;
    @Resource
    private RechargeAssetsService rechargeAssetsService;
    @Resource
    private RechargeFlowMapper rechargeFlowMapper;
    @Resource
    private AccountCardMapper accountCardMapper;
    @Resource
    private RechargeWithdrawRecordMapper rechargeWithdrawRecordMapper;
    @Resource
    private TransactionTypeMapper transactionTypeMapper;
    @Resource
    private OrderMapper orderMapper;
    @Resource
    private OrderBsnInfoMapper orderBsnInfoMapper;
    @Resource
    private ChainService chainService;
    @Resource
    private FixDomainMapper fixDomainMapper;
    @Resource
    private DomainAccountMapper domainAccountMapper;
    @Resource
    private AuctionDomainMapper auctionDomainMapper;
    @Resource
    private MovieOrderMapper movieOrderMapper;
    @Resource
    private AggregatePayService aggregatePayService;
    @Resource
    private OrderDomainRenewalInfoMapper orderDomainRenewalInfoMapper;
    @Resource
    private DomainTransferRecordMapper domainTransferRecordMapper;
    @Resource
    private OrderDomainRenewalRebateMapper orderDomainRenewalRebateMapper;
    @Resource
    private OrderDomainRenewalMapper orderDomainRenewalMapper;
    @Resource
    private DidRebateMapper didRebateMapper;
    @Resource
    private OperateMapper operateMapper;
    @Resource
    private PhoneRechargeRecordMapper phoneRechargeRecordMapper;
    @Resource
    private OperateApplyOrderMapper operateApplyOrderMapper;
    @Resource
    private RedissonClient redissonClient;

    @Resource
    private LockUtil lockUtil;

    /**
     * 新增资产纪录
     * 
     * @param accountUuid 用户uuid
     * @param amount 金额
     * @param reqTransactionSn 流水号
     * @param type 类型 1:充值 2:提现 3：域名注册4：返佣 5：服务订购6:能量充值
     * @return {@link RechargeRecord }
     * <AUTHOR>
     * @date 2024/04/02
     */
    @Override
    @Transactional
    public RechargeRecord insertRecord(String accountUuid, BigDecimal amount, String reqTransactionSn,
        int type) {
        RechargeAssets one = rechargeAssetsMapper.selectOne(
            Wrappers.<RechargeAssets>lambdaQuery().eq(RechargeAssets::getAccountUuid, accountUuid));

        RechargeRecord rechargeRecord = new RechargeRecord();
        rechargeRecord.setAccountUuid(accountUuid);
        rechargeRecord.setType(type);
        rechargeRecord.setAmount(amount);
        rechargeRecord.setTranNumber(reqTransactionSn);
        rechargeRecord.setPlatform(3);
        rechargeRecord.setPayType(3);// 账户余额
        rechargeRecord.setBalance(one.getBalance());
        this.save(rechargeRecord);
        // 资产流水表
        RechargeFlow rechargeFlow = new RechargeFlow();
        rechargeFlow.setOperateUuid(one.getOperateUuid()).setAccountUuid(one.getAccountUuid())
            .setOrderId(rechargeRecord.getId()).setAmount(amount).setType(26);
        rechargeFlowMapper.insert(rechargeFlow);

        return rechargeRecord;
    }

    /**
     * 修改资产消耗纪录
     * 
     * @param reqTransactionSn 交易流水号
     * @param state 状态( 1=成功2=充值中\审核中3=失败4=用户取消)
     * <AUTHOR>
     * @date 2024/04/02
     */
    @Override
    public void updateRecord(String reqTransactionSn, int state) {
        this.update(null, Wrappers.<RechargeRecord>lambdaUpdate()
            .eq(RechargeRecord::getTranNumber, reqTransactionSn).set(RechargeRecord::getState, state));
    }

    /**
     * 资金明细
     * 
     * @param page 页
     * @param pageSize 页码
     * @param type 0-全部类型 1:充值 2:提现 3：域名注册4：返佣 5：服务订购6:能量充值 7-购买域名消耗 8-购买域名退款 9-一口价支出，10一口价收入 11-竞价支出 12-竞价退款 13-竞价收入
     * @param date 日期
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/03
     */
    @Override
    public R queryFundListService(int page, int pageSize, Integer type, Date date) {

        DecimalFormat decimalFormat = new DecimalFormat("#.##");
        Account userInfo = accountService.getUserInfo();
        // 统计耗时
        long startTime = System.currentTimeMillis();

        Page<RechargeFlowVo> rechargeFlowVoPage =
            rechargeRecordMapper.getFundListPage(new Page(page, pageSize), userInfo.getUuid(), type, date);
        List<RechargeFlowVo> rechargeFlowVos = rechargeFlowVoPage.getRecords();
        // 耗时
        // long endTime = System.currentTimeMillis();
        // log.error("queryFundListService耗时：" + (endTime - startTime) + "ms");

        // long startTime2 = System.currentTimeMillis();
        // 封装参数
        for (RechargeFlowVo rechargeFlowVo : rechargeFlowVos) {
            Integer typeId = rechargeFlowVo.getTypeId();
            // 展现实际金额
            String realAmount = rechargeFlowVo.getRealAmount();
            String amount = rechargeFlowVo.getAmount();
            if (StrUtil.isNotBlank(realAmount) && typeId != 2 && typeId != 21) {
                String symbol = amount.substring(0, 1);
                rechargeFlowVo.setAmount(symbol + realAmount);
                rechargeFlowVo.setRealAmount(amount);
            }
            String s = fundDetailsStatus1(rechargeFlowVo.getId());
            // 对amount 和real amount 小数进行处理
            rechargeFlowVo.setAmount(decimalFormat.format(new BigDecimal(rechargeFlowVo.getAmount())));
            if (StrUtil.isNotBlank(rechargeFlowVo.getRealAmount())) {
                rechargeFlowVo
                    .setRealAmount(decimalFormat.format(new BigDecimal(rechargeFlowVo.getRealAmount())));
            }
            rechargeFlowVo.setTypeState(s);
        }
        // 耗时
        // long endTime2 = System.currentTimeMillis();
        // log.error("queryFundListService--22耗时：" + (endTime2 - startTime2) + "ms");
        PageUtils pageUtils = new PageUtils(rechargeFlowVoPage.getTotal(), pageSize, page, rechargeFlowVos);
        return R.okData(pageUtils);
    }

    /**
     * 获取资金明细详情
     * 
     * @param id id
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/03
     */
    @Override
    public R queryFundDetailService(Integer id) {
        Map<String, Object> result = new HashMap<>();
        RechargeFlow rechargeFlow = rechargeFlowMapper.selectById(id);
        // 资金订单表
        RechargeWithdrawRecord rechargeWithdrawRecord =
            rechargeWithdrawRecordMapper.selectById(rechargeFlow.getOrderId());
        Integer state = rechargeWithdrawRecord.getState();
        Integer type = rechargeFlow.getType();

        TransactionType transactionType = transactionTypeMapper
            .selectOne(Wrappers.<TransactionType>lambdaQuery().eq(TransactionType::getTypeId, type));
        Integer bigType = transactionType.getBigType();

        // Object amount = amountSymbol(rechargeFlow.getAmount(), type);
        BigDecimal amount = decimalConversion(rechargeFlow.getAmount());
        BigDecimal realAmount = rechargeFlow.getRealAmount();
        if (rechargeFlow.getRealAmount() != null) {
            realAmount = decimalConversion(rechargeFlow.getRealAmount());
        }
        switch (type) {
            // 充值
            case 1:
                result.put("tranNumber", rechargeWithdrawRecord.getTranNumber());
                result.put("amount", amount);
                result.put("payType", payType(rechargeWithdrawRecord.getPayType()));
                result.put("time", rechargeWithdrawRecord.getCreateTime());
                result.put("typeState", fundDetailsStatus(rechargeFlow.getId()));
                result.put("typeName", getTranTypeName(rechargeFlow.getType()));
                result.put("typeId", type);
                result.put("bigType", bigType);
                // 主动请求回调确认充值
                // 资金订单表
                RechargeWithdrawRecord rechargeWithdrawRecord1 =
                    rechargeWithdrawRecordMapper.selectById(rechargeFlow.getOrderId());
                Integer state1 = rechargeWithdrawRecord1.getState();
                Integer sourceFunds = rechargeWithdrawRecord1.getSourceFunds();
                // 待支付状态 6 灵界app
                if (state1 == 6 && sourceFunds == 2) {
                    // 主动 去查回调状态
                    aggregatePayService.appQuery(rechargeWithdrawRecord1.getTranNumber());
                }
                break;
            // 提现
            case 2:
                result.put("tranNumber", rechargeWithdrawRecord.getTranNumber());
                result.put("amount", amount);
                result.put("realAmount", rechargeFlow.getRealAmount().toString());
                result.put("payType", payType(rechargeWithdrawRecord.getPayType()));
                result.put("time", rechargeWithdrawRecord.getCreateTime());
                result.put("typeState", fundDetailsStatus(rechargeFlow.getId()));
                result.put("typeName", getTranTypeName(rechargeFlow.getType()));
                // 提现信息
                result.put("realName", rechargeWithdrawRecord.getCardName());
                result.put("cardNumber", rechargeWithdrawRecord.getCardNumber());
                result.put("bank", rechargeWithdrawRecord.getBank());
                result.put("typeId", type);
                result.put("bigType", bigType);
                break;
            // 购买域名消耗
            case 3:
                // 获取域名订单
                Order order = orderMapper.selectById(rechargeWithdrawRecord.getOrderId());
                // 获取bsn订单详情
                OrderBsnInfo orderBsnInfo = orderBsnInfoMapper.selectOne(
                    Wrappers.<OrderBsnInfo>lambdaQuery().eq(OrderBsnInfo::getOrderId, order.getId()));

                // 交易流水号
                result.put("tranNumber", order.getTradeCode());
                // 域名原始价格
                result.put("primitiveAmount", decimalConversion(order.getPrimitiveAmount()));
                // 优惠金额
                result.put("discountAmount", (order.getDiscountAmount()));
                // 实付金额
                result.put("totalAmount", order.getTotalAmount().toString());
                // 支付方式
                result.put("payType", payType(order.getPayType()));
                // 支付状态
                result.put("typeState", fundDetailsStatus(rechargeFlow.getId()));
                result.put("typeName", getTranTypeName(rechargeFlow.getType()));
                // 交易时间
                result.put("time", order.getCreateTime());
                // 域名信息
                // 域名
                result.put("domainName", order.getDomainName());
                // 注册时长
                result.put("duration", order.getDuration());
                // 注册时间
                result.put("registerTime", order.getCreateTime());
                // 到期时间
                String time = "";
                if (orderBsnInfo != null) {
                    time = orderBsnInfo.getExpirationTime();
                }
                result.put("expirationTime", time);

                result.put("typeId", type);
                result.put("bigType", bigType);
                break;
            // 购买域名退款
            case 4:
                // 获取域名订单
                Order order4 = orderMapper.selectById(rechargeWithdrawRecord.getOrderId());
                // 获取bsn订单详情
                OrderBsnInfo orderBsnInfo4 = orderBsnInfoMapper.selectOne(
                    Wrappers.<OrderBsnInfo>lambdaQuery().eq(OrderBsnInfo::getOrderId, order4.getId()));

                // 交易流水号
                result.put("tranNumber", order4.getTradeCode());
                // 域名原始价格
                result.put("primitiveAmount", decimalConversion(order4.getPrimitiveAmount()));
                // 优惠金额
                result.put("discountAmount", decimalConversion(order4.getDiscountAmount()));
                // 实付金额
                result.put("totalAmount", "+" + order4.getTotalAmount().toString());
                // 支付方式
                result.put("payType", payType(order4.getPayType()));
                // 支付状态
                result.put("typeState", fundDetailsStatus(rechargeFlow.getId()));
                result.put("typeName", getTranTypeName(rechargeFlow.getType()));
                // 交易时间
                result.put("time", order4.getCreateTime());

                // 退款金额
                result.put("refundAmount", amount);
                // 退款方式
                result.put("refundPayType", "余额");
                // 退款状态
                result.put("refundState", fundDetailsStatus(rechargeFlow.getId()));
                // 退款时间
                result.put("refundtime", rechargeWithdrawRecord.getCreateTime());

                // 域名信息
                // 域名
                result.put("domainName", order4.getDomainName());
                // 注册时长
                result.put("duration", order4.getDuration());
                // 注册时间
                result.put("registerTime", order4.getCreateTime());
                // 到期时间
                String time4 = "";
                if (orderBsnInfo4 != null) {
                    time4 = orderBsnInfo4.getExpirationTime();
                }
                result.put("expirationTime", time4);
                result.put("typeId", type);
                result.put("bigType", bigType);
                break;
            // 划转
            case 5:
                result.put("tranNumber", rechargeWithdrawRecord.getTranNumber());
                result.put("amount", amount);
                result.put("payType", payType(3));
                result.put("time", rechargeWithdrawRecord.getCreateTime());
                result.put("typeState", fundDetailsStatus(rechargeFlow.getId()));
                result.put("typeName", getTranTypeName(rechargeFlow.getType()));
                result.put("typeId", type);
                result.put("bigType", bigType);
                break;
            // 下级注册推广返佣
            case 6:
                result.put("tranNumber", rechargeWithdrawRecord.getTranNumber());
                result.put("amount", amount);
                result.put("payType", payType(3));
                result.put("time", rechargeWithdrawRecord.getCreateTime());
                result.put("typeState", fundDetailsStatus(rechargeFlow.getId()));
                result.put("typeName", getTranTypeName(rechargeFlow.getType()));
                result.put("typeId", type);
                result.put("bigType", bigType);
                break;
            // 自己注册域名返佣
            case 7:
                result.put("tranNumber", rechargeWithdrawRecord.getTranNumber());
                result.put("amount", amount);
                result.put("payType", payType(3));
                result.put("time", rechargeWithdrawRecord.getCreateTime());
                result.put("typeState", fundDetailsStatus(rechargeFlow.getId()));
                result.put("typeName", getTranTypeName(rechargeFlow.getType()));
                result.put("typeId", type);
                result.put("bigType", bigType);
                break;
            // 推广大使升级消耗
            case 8:
                // result.put("tranNumber", rechargeWithdrawRecord.getTranNumber());
                result.put("amount", amount);
                result.put("payType", payType(rechargeWithdrawRecord.getPayType()));
                result.put("time", rechargeWithdrawRecord.getCreateTime());
                result.put("typeState", fundDetailsStatus(rechargeFlow.getId()));
                result.put("typeName", getTranTypeName(rechargeFlow.getType()));
                result.put("typeId", type);
                result.put("bigType", bigType);
                break;
            // 申请经销商消耗
            case 9:
                result.put("tranNumber", rechargeWithdrawRecord.getTranNumber());
                result.put("amount", amount);
                result.put("payType", payType(rechargeWithdrawRecord.getPayType()));
                result.put("time", rechargeWithdrawRecord.getCreateTime());
                result.put("typeState", fundDetailsStatus(rechargeFlow.getId()));
                result.put("typeName", getTranTypeName(rechargeFlow.getType()));
                result.put("typeId", type);
                result.put("bigType", bigType);
                break;
            // 实名DID及个人身份信息凭证申领（消耗）
            case 10:
                result.put("tranNumber", rechargeWithdrawRecord.getTranNumber());
                result.put("amount", amount);
                result.put("payType", payType(rechargeWithdrawRecord.getPayType()));
                result.put("time", rechargeWithdrawRecord.getCreateTime());
                result.put("typeState", fundDetailsStatus(rechargeFlow.getId()));
                result.put("typeName", getTranTypeName(rechargeFlow.getType()));
                result.put("typeId", type);
                result.put("bigType", bigType);
                break;
            // 实名DID及个人身份信息凭证申领（退款）
            case 11:
                result.put("tranNumber", rechargeWithdrawRecord.getTranNumber());
                result.put("amount", amount);
                result.put("payType", payType(rechargeWithdrawRecord.getPayType()));
                result.put("time", rechargeWithdrawRecord.getCreateTime());
                result.put("typeState", fundDetailsStatus(rechargeFlow.getId()));
                result.put("typeName", getTranTypeName(rechargeFlow.getType()));
                result.put("typeId", type);
                result.put("bigType", bigType);
                break;
            // 信息凭证申领（消耗）
            case 12:
                result.put("tranNumber", rechargeWithdrawRecord.getTranNumber());
                result.put("amount", amount);
                result.put("payType", rechargeWithdrawRecord.getPayType());
                result.put("time", rechargeWithdrawRecord.getCreateTime());
                result.put("typeState", fundDetailsStatus(rechargeFlow.getId()));
                result.put("typeName", getTranTypeName(rechargeFlow.getType()));
                result.put("typeId", type);
                result.put("bigType", bigType);
                break;
            // 信息凭证申领（退款）
            case 13:
                result.put("tranNumber", rechargeWithdrawRecord.getTranNumber());
                result.put("amount", amount);
                result.put("payType", payType(rechargeWithdrawRecord.getPayType()));
                result.put("time", rechargeWithdrawRecord.getCreateTime());
                result.put("typeState", fundDetailsStatus(rechargeFlow.getId()));
                result.put("typeName", getTranTypeName(rechargeFlow.getType()));
                result.put("typeId", type);
                result.put("bigType", bigType);
                break;
            // DID申领
            case 14:
                result.put("tranNumber", rechargeWithdrawRecord.getTranNumber());
                result.put("amount", amount);
                result.put("payType", payType(rechargeWithdrawRecord.getPayType()));
                result.put("time", rechargeWithdrawRecord.getCreateTime());
                result.put("typeState", fundDetailsStatus(rechargeFlow.getId()));
                result.put("typeName", getTranTypeName(rechargeFlow.getType()));
                result.put("typeId", type);
                result.put("bigType", bigType);
                break;
            // DID申领退款
            case 15:
                result.put("tranNumber", rechargeWithdrawRecord.getTranNumber());
                result.put("amount", amount);
                result.put("payType", payType(rechargeWithdrawRecord.getPayType()));
                result.put("time", rechargeWithdrawRecord.getCreateTime());
                result.put("typeState", fundDetailsStatus(rechargeFlow.getId()));
                result.put("typeName", getTranTypeName(rechargeFlow.getType()));
                result.put("typeId", type);
                result.put("bigType", bigType);
                break;
            // 恢复私钥
            case 16:
                result.put("tranNumber", rechargeWithdrawRecord.getTranNumber());
                result.put("amount", amount);
                result.put("payType", rechargeWithdrawRecord.getPayType());
                result.put("time", rechargeWithdrawRecord.getCreateTime());
                result.put("typeState", fundDetailsStatus(rechargeFlow.getId()));
                result.put("typeName", getTranTypeName(rechargeFlow.getType()));
                result.put("typeId", type);
                result.put("bigType", bigType);
                break;
            // 恢复私钥（退款）
            case 17:
                result.put("tranNumber", rechargeWithdrawRecord.getTranNumber());
                result.put("amount", amount);
                result.put("payType", payType(rechargeWithdrawRecord.getPayType()));
                result.put("time", rechargeWithdrawRecord.getCreateTime());
                result.put("typeState", fundDetailsStatus(rechargeFlow.getId()));
                result.put("typeName", getTranTypeName(rechargeFlow.getType()));
                result.put("typeId", type);
                result.put("bigType", bigType);
                break;
            // 域名转让
            case 18:
                // 获取域名订单
                Order order18 = orderMapper.selectById(rechargeWithdrawRecord.getOrderId());
                // 获取bsn订单详情
                OrderBsnInfo orderBsnInfo18 = orderBsnInfoMapper.selectOne(
                    Wrappers.<OrderBsnInfo>lambdaQuery().eq(OrderBsnInfo::getOrderId, order18.getId()));
                result.put("tranNumber", rechargeWithdrawRecord.getTranNumber());
                result.put("amount", amount);
                result.put("payType", payType(rechargeWithdrawRecord.getPayType()));
                result.put("time", rechargeWithdrawRecord.getCreateTime());
                result.put("typeState", fundDetailsStatus(rechargeFlow.getId()));
                result.put("typeName", getTranTypeName(rechargeFlow.getType()));
                // 域名信息
                // 域名
                result.put("domainName", order18.getDomainName());
                // 注册时长
                result.put("duration", order18.getDuration());
                // 注册时间
                result.put("registerTime", order18.getCreateTime());
                // 到期时间
                String time18 = "";
                if (orderBsnInfo18 != null) {
                    time18 = orderBsnInfo18.getExpirationTime();
                }
                result.put("expirationTime", time18);
                result.put("typeId", type);
                result.put("bigType", bigType);

                break;
            // 19-门户用户资产划转到运营端
            case 19:
                result.put("transferTo", "普通账户");
                result.put("amount", amount);
                result.put("payType", payType(3));
                result.put("time", rechargeWithdrawRecord.getCreateTime());
                result.put("typeState", fundDetailsStatus1(rechargeFlow.getId()));
                result.put("typeName", getTranTypeName(rechargeFlow.getType()));
                result.put("typeId", type);
                result.put("bigType", bigType);
                break;
            // // 市场购买域名退款
            // case 20:
            // if (flowType == 6) {
            // typeState = "已退款";
            // }
            // break;
            // 一口价支出
            case 21:
                // 获取交易市场域名订单详情
                FixDomain fixDomain = fixDomainMapper.selectById(rechargeFlow.getOrderId());
                // 获取域名详情
                DomainAccount domainAccount = domainAccountMapper.selectOne(Wrappers
                    .<DomainAccount>lambdaQuery().eq(DomainAccount::getDomain, fixDomain.getDomain()));
                result.put("tranNumber", fixDomain.getTransferUuid());
                result.put("amount", amount);
                result.put("payType", payType(3));
                result.put("time", fixDomain.getCreateTime());
                result.put("typeState", fundDetailsStatus(rechargeFlow.getId()));
                result.put("typeName", getTranTypeName(rechargeFlow.getType()));
                // 域名信息
                result.put("source", "市场（一口价）");
                result.put("domain", fixDomain.getDomain());
                // 注册时长
                long years = DateUtil.betweenYear(DateUtil.parse(domainAccount.getRegisterTime()),
                    DateUtil.parse(domainAccount.getExpirationTime()), false);
                result.put("registrationDuration", years);
                result.put("typeId", type);
                result.put("bigType", bigType);
                break;
            // 一口价收入
            case 22:
                // 获取交易市场域名订单详情
                FixDomain fixDomain22 = fixDomainMapper.selectById(rechargeFlow.getOrderId());
                // 获取域名详情
                DomainAccount domainAccount22 = domainAccountMapper.selectOne(Wrappers
                    .<DomainAccount>lambdaQuery().eq(DomainAccount::getDomain, fixDomain22.getDomain()));

                result.put("tranNumber", fixDomain22.getTransferUuid());
                result.put("amount", realAmount);
                result.put("payType", payType(3));
                result.put("time", fixDomain22.getCreateTime());
                result.put("typeState", fundDetailsStatus(rechargeFlow.getId()));
                result.put("typeName", getTranTypeName(rechargeFlow.getType()));
                // 域名信息
                result.put("source", "市场（一口价）");
                result.put("domain", fixDomain22.getDomain());
                // 注册时长
                long years22 = DateUtil.betweenYear(DateUtil.parse(domainAccount22.getRegisterTime()),
                    DateUtil.parse(domainAccount22.getExpirationTime()), false);
                result.put("registrationDuration", years22);
                result.put("typeId", type);
                result.put("bigType", bigType);
                break;
            // 竞价支出
            case 23:
                // 获取交易市场域名订单详情
                AuctionDomain auctionDomain23 = auctionDomainMapper.selectById(rechargeFlow.getOrderId());
                result.put("tranNumber", auctionDomain23.getTransferUuid());
                result.put("amount", amount);
                result.put("payType", payType(3));
                result.put("time", auctionDomain23.getCreateTime());
                result.put("typeState", fundDetailsStatus(rechargeFlow.getId()));
                result.put("typeName", getTranTypeName(rechargeFlow.getType()));
                // 域名信息
                result.put("source", "市场（竞价）");
                result.put("domain", auctionDomain23.getDomain());
                result.put("typeId", type);
                result.put("bigType", bigType);
                break;
            // 竞价退款
            case 24:
                AuctionDomain auctionDomain24 = auctionDomainMapper.selectById(rechargeFlow.getOrderId());
                result.put("tranNumber", auctionDomain24.getTransferUuid());
                result.put("amount", amount);
                result.put("payType", payType(3));
                result.put("time", auctionDomain24.getCreateTime());
                result.put("typeState", fundDetailsStatus(rechargeFlow.getId()));
                result.put("typeName", getTranTypeName(rechargeFlow.getType()));
                // 域名信息
                result.put("source", "市场（竞价）");
                result.put("domain", auctionDomain24.getDomain());
                result.put("typeId", type);
                result.put("bigType", bigType);
                break;
            // 竞价收入
            case 25:
                AuctionDomain auctionDomain25 = auctionDomainMapper.selectById(rechargeFlow.getOrderId());
                result.put("tranNumber", auctionDomain25.getTransferUuid());
                result.put("amount", realAmount);
                result.put("payType", payType(3));
                result.put("time", auctionDomain25.getCreateTime());
                result.put("typeState", fundDetailsStatus(rechargeFlow.getId()));
                result.put("typeName", getTranTypeName(rechargeFlow.getType()));
                // 域名信息
                result.put("source", "市场（竞价）");
                result.put("domain", auctionDomain25.getDomain());
                result.put("typeId", type);
                result.put("bigType", bigType);
                break;
            // 能量充值
            case 26:
                RechargeRecord rechargeRecord = rechargeRecordMapper.selectById(rechargeFlow.getOrderId());
                // 获取能连充值记录
                EnergyRechargeFlow energyRechargeFlow =
                    energyRechargeFlowMapper.selectOne(Wrappers.<EnergyRechargeFlow>lambdaQuery()
                        .eq(EnergyRechargeFlow::getReqTransactionSn, rechargeRecord.getTranNumber()));
                // 获取灵戒链信息
                Chain chain = chainService.getLjChainInfoService();

                result.put("tranNumber", rechargeRecord.getTranNumber());
                result.put("amount", rechargeRecord.getAmount());
                result.put("payType", payType(rechargeRecord.getPayType()));
                result.put("time", rechargeRecord.getCreateTime());
                result.put("typeState", fundDetailsStatus(rechargeFlow.getId()));
                result.put("typeName", getTranTypeName(rechargeFlow.getType()));
                // 封装充值信息
                // 链类型
                result.put("chainType", chain.getChainName());
                result.put("address", energyRechargeFlow.getAddress());
                // 充值gas
                result.put("gas", energyRechargeFlow.getGas());
                result.put("typeId", type);
                result.put("bigType", bigType);
                break;
            // 影票下单支出
            case 27:
                MovieOrder movieOrder27 = movieOrderMapper.selectById(rechargeFlow.getOrderId());
                result.put("tranNumber", movieOrder27.getThirdOrderId());
                result.put("amount", amount);
                Integer typeId = movieOrder27.getPayType();
                if (typeId == 1) {
                    typeId = 3;
                } else if (typeId == 2) {
                    typeId = 7;
                }
                // 优惠金额
                result.put("discountAmount", movieOrder27.getDiscountAmount());
                result.put("payType", payType(typeId));
                result.put("time", rechargeWithdrawRecord.getCreateTime());
                result.put("typeState", fundDetailsStatus(rechargeFlow.getId()));
                result.put("typeName", getTranTypeName(rechargeFlow.getType()));
                result.put("typeId", type);
                result.put("bigType", bigType);
                break;
            // 影票已结算扣除冻结
            case 28:
                MovieOrder movieOrder28 = movieOrderMapper.selectById(rechargeFlow.getOrderId());
                result.put("tranNumber", movieOrder28.getThirdOrderId());
                result.put("amount", amount);
                Integer typeId28 = movieOrder28.getPayType();
                if (typeId28 == 1) {
                    typeId28 = 3;
                } else if (typeId28 == 2) {
                    typeId28 = 7;
                }
                result.put("payType", payType(typeId28));

                result.put("time", rechargeWithdrawRecord.getCreateTime());
                result.put("typeState", fundDetailsStatus(rechargeFlow.getId()));
                result.put("typeName", getTranTypeName(rechargeFlow.getType()));
                result.put("typeId", type);
                result.put("bigType", bigType);
                break;
            // 影票订单关闭返还资产
            case 29:
                MovieOrder movieOrder29 = movieOrderMapper.selectById(rechargeFlow.getOrderId());
                result.put("tranNumber", movieOrder29.getThirdOrderId());
                result.put("amount", amount);
                Integer typeId29 = movieOrder29.getPayType();
                if (typeId29 == 1) {
                    typeId29 = 3;
                } else if (typeId29 == 2) {
                    typeId29 = 7;
                }
                result.put("payType", payType(typeId29));

                result.put("time", rechargeWithdrawRecord.getCreateTime());
                result.put("typeState", fundDetailsStatus(rechargeFlow.getId()));
                result.put("typeName", getTranTypeName(rechargeFlow.getType()));
                result.put("typeId", type);
                result.put("bigType", bigType);
                break;
            // 域名续费（消耗）
            case 30:
                OrderDomainRenewalInfo orderDomainRenewalInfo30 =
                    orderDomainRenewalInfoMapper.selectById(rechargeFlow.getOrderId());

                result.put("tranNumber", orderDomainRenewalInfo30.getTranNumber());
                result.put("amount", amount);

                Integer payType30 = orderDomainRenewalInfo30.getPayType();
                if (payType30 == 1) {
                    payType30 = 3;
                } else if (payType30 == 2) {
                    payType30 = 8;
                }

                result.put("payType", payType(payType30));

                result.put("time", orderDomainRenewalInfo30.getCreateTime());
                result.put("typeState", fundDetailsStatus(rechargeFlow.getId()));
                result.put("typeName", getTranTypeName(rechargeFlow.getType()));
                result.put("typeId", type);
                result.put("bigType", bigType);
                break;
            // 域名续费（退款）
            case 31:
                OrderDomainRenewalInfo orderDomainRenewalInfo31 =
                    orderDomainRenewalInfoMapper.selectById(rechargeFlow.getOrderId());

                result.put("tranNumber", orderDomainRenewalInfo31.getTranNumber());
                result.put("amount", amount);

                Integer payType31 = orderDomainRenewalInfo31.getPayType();
                if (payType31 == 1) {
                    payType31 = 3;
                } else if (payType31 == 2) {
                    payType31 = 8;
                }

                result.put("payType", payType(payType31));
                result.put("time", orderDomainRenewalInfo31.getCreateTime());
                result.put("typeState", fundDetailsStatus(rechargeFlow.getId()));
                result.put("typeName", getTranTypeName(rechargeFlow.getType()));
                result.put("typeId", type);
                result.put("bigType", bigType);
                break;
            // 域名续费（下级返佣）
            case 32:
                OrderDomainRenewalInfo orderDomainRenewalInfo32 =
                    orderDomainRenewalInfoMapper.selectById(rechargeFlow.getOrderId());

                result.put("tranNumber", orderDomainRenewalInfo32.getTranNumber());
                result.put("amount", amount);
                Integer payType32 = orderDomainRenewalInfo32.getPayType();

                if (payType32 == 1) {
                    payType32 = 3;
                } else if (payType32 == 2) {
                    payType32 = 8;
                }

                result.put("payType", payType(payType32));

                result.put("time", orderDomainRenewalInfo32.getCreateTime());
                result.put("typeState", fundDetailsStatus(rechargeFlow.getId()));
                result.put("typeName", getTranTypeName(rechargeFlow.getType()));
                result.put("typeId", type);
                result.put("bigType", bigType);
                break;
            // 域名续费（自身返佣）
            case 33:
                OrderDomainRenewalInfo orderDomainRenewalInfo33 =
                    orderDomainRenewalInfoMapper.selectById(rechargeFlow.getOrderId());

                result.put("tranNumber", orderDomainRenewalInfo33.getTranNumber());
                result.put("amount", amount);

                Integer payType33 = orderDomainRenewalInfo33.getPayType();
                if (payType33 == 1) {
                    payType33 = 3;
                } else if (payType33 == 2) {
                    payType33 = 8;
                }

                result.put("payType", payType(payType33));
                result.put("time", orderDomainRenewalInfo33.getCreateTime());
                result.put("typeState", fundDetailsStatus(rechargeFlow.getId()));
                result.put("typeName", getTranTypeName(rechargeFlow.getType()));
                result.put("typeId", type);
                result.put("bigType", bigType);
                break;
        }

        return R.okData(result);
    }

    @Override
    public R queryFundDetailService1(Integer id) {
        Map<String, Object> result = new HashMap<>();
        RechargeFlow rechargeFlow = rechargeFlowMapper.selectById(id);
        // 资金订单表
        RechargeWithdrawRecord rechargeWithdrawRecord =
            rechargeWithdrawRecordMapper.selectById(rechargeFlow.getOrderId());
        Integer state = rechargeWithdrawRecord.getState();
        Integer type = rechargeFlow.getType();

        TransactionType transactionType = transactionTypeMapper
            .selectOne(Wrappers.<TransactionType>lambdaQuery().eq(TransactionType::getTypeId, type));
        Integer bigType = transactionType.getBigType();

        // Object amount = amountSymbol(rechargeFlow.getAmount(), type);
        BigDecimal amount = decimalConversion(rechargeFlow.getAmount());
        BigDecimal realAmount = rechargeFlow.getRealAmount();
        if (rechargeFlow.getRealAmount() != null) {
            realAmount = decimalConversion(rechargeFlow.getRealAmount());
        }
        result.put("orderNumber", rechargeFlow.getOrderNumber());
        switch (type) {
            // 充值
            case 1:
                result.put("tranNumber", rechargeWithdrawRecord.getTranNumber());
                result.put("amount", amount);
                result.put("payType", payType(rechargeWithdrawRecord.getPayType()));
                result.put("time", rechargeWithdrawRecord.getCreateTime());
                result.put("typeState", fundDetailsStatus1(rechargeFlow.getId()));
                result.put("typeName", getTranTypeName(rechargeFlow.getType()));
                result.put("typeId", type);
                result.put("bigType", bigType);
                // 主动请求回调确认充值
                // 资金订单表
                RechargeWithdrawRecord rechargeWithdrawRecord1 =
                    rechargeWithdrawRecordMapper.selectById(rechargeFlow.getOrderId());
                Integer state1 = rechargeWithdrawRecord1.getState();
                Integer sourceFunds = rechargeWithdrawRecord1.getSourceFunds();
                // 待支付状态 6 灵界app
                if (state1 == 6 && sourceFunds == 2) {
                    // 主动 去查回调状态
                    aggregatePayService.appQuery(rechargeWithdrawRecord1.getTranNumber());
                }
                break;
            // 提现
            case 2:
                result.put("tranNumber", rechargeWithdrawRecord.getTranNumber());
                result.put("amount", amount);
                result.put("realAmount", realAmount.toString());
                result.put("payType", payType(rechargeWithdrawRecord.getPayType()));
                result.put("time", rechargeWithdrawRecord.getCreateTime());
                result.put("typeState", fundDetailsStatus1(rechargeFlow.getId()));
                result.put("typeName", getTranTypeName(rechargeFlow.getType()));
                result.put("rate", rechargeWithdrawRecord.getRate());
                if (state == 2) {
                    result.put("reason", rechargeWithdrawRecord.getReason());
                }
                // 提现信息
                result.put("realName", rechargeWithdrawRecord.getCardName());
                result.put("cardNumber", rechargeWithdrawRecord.getCardNumber());
                result.put("bank", rechargeWithdrawRecord.getBank());
                result.put("typeId", type);
                result.put("bigType", bigType);
                break;
            // 购买域名消耗
            case 3:
                // 判断是否是新订单
                String orderNumber3 = rechargeFlow.getOrderNumber();

                // 获取bsn订单详情
                OrderBsnInfo orderBsnInfo = new OrderBsnInfo();
                Order order = new Order();
                // 新订单
                if (StrUtil.isNotBlank(orderNumber3)) {
                    orderBsnInfo = orderBsnInfoMapper.selectOne(
                        Wrappers.<OrderBsnInfo>lambdaQuery().eq(OrderBsnInfo::getOrderNumber, orderNumber3));
                    order = orderMapper
                        .selectOne(Wrappers.<Order>lambdaQuery().eq(Order::getOrderNumber, orderNumber3));
                } else {
                    // 获取域名订单
                    order = orderMapper.selectById(rechargeWithdrawRecord.getOrderId());
                    orderBsnInfo = orderBsnInfoMapper.selectOne(
                        Wrappers.<OrderBsnInfo>lambdaQuery().eq(OrderBsnInfo::getOrderId, order.getId()));
                }
                // 交易流水号
                result.put("tranNumber", orderBsnInfo.getTradeCode());
                // 域名原始价格
                result.put("primitiveAmount", decimalConversion(order.getPrimitiveAmount()));
                // 优惠金额
                result.put("discountAmount", decimalConversion(order.getDiscountAmount()));
                // 实付金额
                result.put("totalAmount", decimalConversion(order.getTotalAmount()));
                // 支付方式
                result.put("payType", payType(order.getPayType() == null ? 3 : order.getPayType()));
                // 支付状态
                result.put("typeState", fundDetailsStatus1(rechargeFlow.getId()));
                result.put("typeName", getTranTypeName(rechargeFlow.getType()));
                // 交易时间
                result.put("time", order.getCreateTime());
                // 域名信息
                // 域名
                result.put("domainName", order.getDomainName());
                // 注册时长
                // result.put("duration", order.getDuration());
                // // 注册时间
                // result.put("registerTime", order.getCreateTime());
                // // 到期时间
                // String time = "";
                // if (orderBsnInfo != null) {
                // time = orderBsnInfo.getExpirationTime();
                // }
                // result.put("expirationTime", time);

                result.put("typeId", type);
                result.put("bigType", bigType);
                if (orderBsnInfo != null) {
                    result.put("orderId", orderBsnInfo.getId());
                    result.put("type", 1);
                }
                break;
            // 购买域名退款
            case 4:
                // 获取域名订单
                Order order4 = orderMapper.selectById(rechargeWithdrawRecord.getOrderId());
                // 获取bsn订单详情
                OrderBsnInfo orderBsnInfo4 = orderBsnInfoMapper.selectOne(
                    Wrappers.<OrderBsnInfo>lambdaQuery().eq(OrderBsnInfo::getOrderId, order4.getId()));

                // 交易流水号
                result.put("tranNumber", order4.getTradeCode());
                // 域名原始价格
                result.put("primitiveAmount", decimalConversion(order4.getPrimitiveAmount()));
                // 优惠金额
                result.put("discountAmount", decimalConversion(order4.getDiscountAmount()));
                // 实付金额
                result.put("totalAmount", decimalConversion(order4.getTotalAmount()));
                // 支付方式
                result.put("payType", payType(order4.getPayType() == null ? 3 : order4.getPayType()));
                // 支付状态
                result.put("typeState", "已支付");
                result.put("typeName", getTranTypeName(3));
                // 交易时间
                result.put("time", order4.getCreateTime());

                // 退款金额
                result.put("refundAmount", amount);
                // 退款方式
                result.put("refundPayType", "余额");
                // 退款状态
                result.put("refundState", fundDetailsStatus1(rechargeFlow.getId()));
                // 退款时间
                result.put("refundtime", rechargeWithdrawRecord.getCreateTime());

                // 域名信息
                // 域名
                result.put("domainName", order4.getDomainName());
                // // 注册时长
                // result.put("duration", order4.getDuration());
                // // 注册时间
                // result.put("registerTime", order4.getCreateTime());
                // // 到期时间
                // String time4 = "";
                // if (orderBsnInfo4 != null) {
                // time4 = orderBsnInfo4.getExpirationTime();
                // }
                // result.put("expirationTime", time4);
                result.put("typeId", type);
                result.put("bigType", bigType);
                if (orderBsnInfo4 != null) {
                    result.put("orderId", orderBsnInfo4.getId());
                    result.put("type", 1);
                }
                break;
            // 划转
            case 5:
                // result.put("tranNumber", rechargeWithdrawRecord.getTranNumber());
                result.put("transferTo", "运营账户");
                result.put("amount", amount);
                result.put("payType", payType(3));
                result.put("time", rechargeWithdrawRecord.getCreateTime());
                result.put("typeState", fundDetailsStatus1(rechargeFlow.getId()));
                result.put("typeName", getTranTypeName(rechargeFlow.getType()));
                result.put("typeId", type);
                result.put("bigType", bigType);
                break;
            // 下级注册推广返佣
            case 6:
                // 获取域名订单
                Order order6 = orderMapper.selectById(rechargeWithdrawRecord.getOrderId());
                // 获取bsn订单详情
                OrderBsnInfo orderBsnInfo6 = orderBsnInfoMapper.selectOne(
                    Wrappers.<OrderBsnInfo>lambdaQuery().eq(OrderBsnInfo::getOrderId, order6.getId()));
                result.put("tranNumber", rechargeWithdrawRecord.getTranNumber());
                result.put("amount", amount);
                result.put("payType", payType(3));
                result.put("time", rechargeWithdrawRecord.getCreateTime());
                result.put("typeState", fundDetailsStatus1(rechargeFlow.getId()));
                result.put("typeName", getTranTypeName(rechargeFlow.getType()));
                result.put("typeId", type);
                result.put("bigType", bigType);
                result.put("source", orderBsnInfo6.getDomain());
                result.put("proportion", orderBsnInfo6.getSuperiorAccountScale());
                result.put("primitiveAmount", decimalConversion(orderBsnInfo6.getPrimitiveAmount()));
                break;
            // 自己注册域名返佣
            case 7:
                // 获取域名订单
                Order order7 = orderMapper.selectById(rechargeWithdrawRecord.getOrderId());
                // 获取bsn订单详情
                OrderBsnInfo orderBsnInfo7 = orderBsnInfoMapper.selectOne(
                    Wrappers.<OrderBsnInfo>lambdaQuery().eq(OrderBsnInfo::getOrderId, order7.getId()));
                result.put("tranNumber", rechargeWithdrawRecord.getTranNumber());
                result.put("amount", amount);
                result.put("payType", payType(3));
                result.put("time", rechargeWithdrawRecord.getCreateTime());
                result.put("typeState", fundDetailsStatus1(rechargeFlow.getId()));
                result.put("typeName", getTranTypeName(rechargeFlow.getType()));
                result.put("typeId", type);
                result.put("bigType", bigType);
                result.put("source", orderBsnInfo7.getDomain());
                result.put("proportion", orderBsnInfo7.getThisAccountScale());
                result.put("primitiveAmount", decimalConversion(orderBsnInfo7.getPrimitiveAmount()));
                result.put("orderId", orderBsnInfo7.getId());
                result.put("type", 1);
                break;
            // 推广大使升级消耗
            case 8:
                // result.put("tranNumber", rechargeWithdrawRecord.getTranNumber());
                result.put("amount", amount);
                result.put("payType", payType(rechargeWithdrawRecord.getPayType()));
                result.put("time", rechargeWithdrawRecord.getCreateTime());
                result.put("typeState", fundDetailsStatus1(rechargeFlow.getId()));
                result.put("typeName", getTranTypeName(rechargeFlow.getType()));
                result.put("typeId", type);
                result.put("bigType", bigType);
                result.put("tranNumber", "DS" + rechargeWithdrawRecord.getCreateTime().getTime());
                break;
            // 申请经销商消耗
            case 9:
                // 查询经销商申请单
                OperateApplyOrder operateApplyOrder =
                    operateApplyOrderMapper.selectById(rechargeFlow.getOrderId());
                result.put("tranNumber", operateApplyOrder.getOrderNumber());
                result.put("amount", amount);
                result.put("payType", payType(operateApplyOrder.getPayType()));
                result.put("time", operateApplyOrder.getCreateTime());
                result.put("typeState", fundDetailsStatus1(rechargeFlow.getId()));
                result.put("typeName", getTranTypeName(rechargeFlow.getType()));
                result.put("typeId", type);
                result.put("bigType", bigType);
                break;
            // 实名DID及个人身份信息凭证申领（消耗）
            case 10:
                result.put("tranNumber", rechargeWithdrawRecord.getTranNumber());
                result.put("amount", amount);
                result.put("payType", payType(rechargeWithdrawRecord.getPayType()));
                result.put("time", rechargeWithdrawRecord.getCreateTime());
                result.put("typeState", fundDetailsStatus1(rechargeFlow.getId()));
                result.put("typeName", getTranTypeName(rechargeFlow.getType()));
                result.put("typeId", type);
                result.put("bigType", bigType);
                break;
            // 实名DID及个人身份信息凭证申领（退款）
            case 11:
                result.put("tranNumber", rechargeWithdrawRecord.getTranNumber());
                result.put("amount", amount);
                result.put("payType", payType(rechargeWithdrawRecord.getPayType()));
                result.put("time", rechargeWithdrawRecord.getCreateTime());
                result.put("typeState", fundDetailsStatus1(rechargeFlow.getId()));
                result.put("typeName", getTranTypeName(rechargeFlow.getType()));
                result.put("typeId", type);
                result.put("bigType", bigType);
                break;
            // 信息凭证申领（消耗）
            case 12:
                result.put("tranNumber", rechargeWithdrawRecord.getTranNumber());
                result.put("amount", amount);
                result.put("payType", payType(rechargeWithdrawRecord.getPayType()));
                result.put("time", rechargeWithdrawRecord.getCreateTime());
                result.put("typeState", fundDetailsStatus1(rechargeFlow.getId()));
                result.put("typeName", getTranTypeName(rechargeFlow.getType()));
                result.put("typeId", type);
                result.put("bigType", bigType);
                break;
            // 信息凭证申领（退款）
            case 13:
                result.put("tranNumber", rechargeWithdrawRecord.getTranNumber());
                result.put("amount", amount);
                result.put("payType", payType(rechargeWithdrawRecord.getPayType()));
                result.put("time", rechargeWithdrawRecord.getCreateTime());
                result.put("typeState", fundDetailsStatus1(rechargeFlow.getId()));
                result.put("typeName", getTranTypeName(rechargeFlow.getType()));
                result.put("typeId", type);
                result.put("bigType", bigType);
                break;
            // DID申领
            case 14:
                result.put("tranNumber", rechargeWithdrawRecord.getTranNumber());
                result.put("amount", amount);
                result.put("payType", payType(rechargeWithdrawRecord.getPayType()));
                result.put("time", rechargeWithdrawRecord.getCreateTime());
                result.put("typeState", fundDetailsStatus1(rechargeFlow.getId()));
                result.put("typeName", getTranTypeName(rechargeFlow.getType()));
                result.put("typeId", type);
                result.put("bigType", bigType);
                break;
            // DID申领退款
            case 15:
                result.put("tranNumber", rechargeWithdrawRecord.getTranNumber());
                result.put("amount", amount);
                result.put("payType", payType(rechargeWithdrawRecord.getPayType()));
                result.put("time", rechargeWithdrawRecord.getCreateTime());
                result.put("typeState", fundDetailsStatus1(rechargeFlow.getId()));
                result.put("typeName", getTranTypeName(rechargeFlow.getType()));
                result.put("typeId", type);
                result.put("bigType", bigType);
                break;
            // 恢复私钥
            case 16:
                result.put("tranNumber", rechargeWithdrawRecord.getTranNumber());
                result.put("amount", amount);
                result.put("payType", payType(rechargeWithdrawRecord.getPayType()));
                result.put("time", rechargeWithdrawRecord.getCreateTime());
                result.put("typeState", fundDetailsStatus1(rechargeFlow.getId()));
                result.put("typeName", getTranTypeName(rechargeFlow.getType()));
                result.put("typeId", type);
                result.put("bigType", bigType);
                break;
            // 恢复私钥（退款）
            case 17:
                result.put("tranNumber", rechargeWithdrawRecord.getTranNumber());
                result.put("amount", amount);
                result.put("payType", payType(rechargeWithdrawRecord.getPayType()));
                result.put("time", rechargeWithdrawRecord.getCreateTime());
                result.put("typeState", fundDetailsStatus1(rechargeFlow.getId()));
                result.put("typeName", getTranTypeName(rechargeFlow.getType()));
                result.put("typeId", type);
                result.put("bigType", bigType);
                break;
            // 域名转让
            case 18:
                // 获取域名订单
                Order order18 = orderMapper.selectById(rechargeWithdrawRecord.getOrderId());
                // 获取bsn订单详情
                OrderBsnInfo orderBsnInfo18 = orderBsnInfoMapper.selectOne(
                    Wrappers.<OrderBsnInfo>lambdaQuery().eq(OrderBsnInfo::getOrderId, order18.getId()));
                result.put("tranNumber", rechargeWithdrawRecord.getTranNumber());
                result.put("amount", amount);
                result.put("totalServicePrice", order18.getPrimitiveAmount() == null ? amount
                    : decimalConversion(order18.getPrimitiveAmount()));
                result.put("payType", payType(rechargeWithdrawRecord.getPayType()));
                result.put("time", rechargeWithdrawRecord.getCreateTime());
                result.put("typeState", fundDetailsStatus1(rechargeFlow.getId()));
                result.put("typeName", getTranTypeName(rechargeFlow.getType()));
                result.put("domain", orderBsnInfo18.getDomain());
                // 获取域名信息
                Integer transferState = orderBsnInfo18.getTransferState();
                // 1=>装让
                if (ObjectUtil.equals(transferState, 1)) {
                    Map<String, Object> transferDomainResult = orderBsnInfoMapper
                        .selectTransferDomain(orderBsnInfo18.getDomainServeSn(), transferState);
                    Map<String, Object> transferDomainResult1 =
                        orderBsnInfoMapper.selectTransferDomain(orderBsnInfo18.getDomainServeSn(), 2);
                    result.put("transferState", transferDomainResult.get("transferState"));
                    result.put("transferor", transferDomainResult.get("realName"));
                    result.put("recipient", transferDomainResult1.get("realName"));
                } else if (ObjectUtil.equals(transferState, 2)) {
                    Map<String, Object> transferDomainResult = orderBsnInfoMapper
                        .selectTransferDomain(orderBsnInfo18.getDomainServeSn(), transferState);
                    Map<String, Object> transferDomainResult1 =
                        orderBsnInfoMapper.selectTransferDomain(orderBsnInfo18.getDomainServeSn(), 2);
                    result.put("transferState", transferDomainResult.get("transferState"));
                    result.put("transferor", transferDomainResult1.get("realName"));
                    result.put("recipient", transferDomainResult.get("realName"));
                }
                result.put("typeId", type);
                result.put("bigType", bigType);
                result.put("orderId", orderBsnInfo18.getId());
                result.put("type", 2);
                break;
            // 19-门户用户资产划转到运营端
            case 19:
                result.put("transferTo", "普通账户");
                result.put("amount", amount);
                result.put("payType", payType(3));
                result.put("time", rechargeWithdrawRecord.getCreateTime());
                result.put("typeState", fundDetailsStatus1(rechargeFlow.getId()));
                result.put("typeName", getTranTypeName(rechargeFlow.getType()));
                result.put("typeId", type);
                result.put("bigType", bigType);
                break;
            // 一口价支出
            case 21:
                // 获取交易市场域名订单详情
                FixDomain fixDomain = fixDomainMapper.selectById(rechargeFlow.getOrderId());
                DomainTransferRecord domainTransferRecord =
                    domainTransferRecordMapper.selectOne(Wrappers.<DomainTransferRecord>lambdaQuery()
                        .eq(DomainTransferRecord::getTransferUuid, fixDomain.getTransferUuid()));
                // 获取域名详情
                DomainAccount domainAccount = domainAccountMapper.selectOne(Wrappers
                    .<DomainAccount>lambdaQuery().eq(DomainAccount::getDomain, fixDomain.getDomain()));
                result.put("tranNumber", fixDomain.getTransferUuid());
                result.put("amount", amount);
                result.put("payType", payType(3));
                result.put("time", fixDomain.getCreateTime());
                result.put("typeState", fundDetailsStatus1(rechargeFlow.getId()));
                result.put("typeName", getTranTypeName(rechargeFlow.getType()));
                // 域名信息
                // result.put("source", "市场（一口价）");
                // result.put("domain", fixDomain.getDomain());
                // // 注册时长
                // long years = DateUtil.betweenYear(DateUtil.parse(domainAccount.getRegisterTime()),
                // DateUtil.parse(domainAccount.getExpirationTime()), false);
                // result.put("registrationDuration", years);
                result.put("typeId", type);
                result.put("bigType", bigType);
                result.put("orderId", domainTransferRecord.getId());
                result.put("type", 4);
                break;
            // 一口价收入
            case 22:
                // 获取交易市场域名订单详情
                FixDomain fixDomain22 = fixDomainMapper.selectById(rechargeFlow.getOrderId());
                DomainTransferRecord domainTransferRecord22 =
                    domainTransferRecordMapper.selectOne(Wrappers.<DomainTransferRecord>lambdaQuery()
                        .eq(DomainTransferRecord::getTransferUuid, fixDomain22.getTransferUuid()));
                // 获取域名详情
                DomainAccount domainAccount22 = domainAccountMapper.selectOne(Wrappers
                    .<DomainAccount>lambdaQuery().eq(DomainAccount::getDomain, fixDomain22.getDomain()));

                result.put("tranNumber", fixDomain22.getTransferUuid());
                result.put("realAmount", realAmount);
                result.put("servicePrice", domainTransferRecord22.getServicePrice());
                result.put("rate", fixDomain22.getRate());
                result.put("amount", amount);
                result.put("payType", payType(3));
                result.put("time", fixDomain22.getCreateTime());
                result.put("typeState", fundDetailsStatus1(rechargeFlow.getId()));
                result.put("typeName", getTranTypeName(rechargeFlow.getType()));
                // 域名信息
                // result.put("source", "市场（一口价）");
                // result.put("domain", fixDomain22.getDomain());
                // // 注册时长
                // long years22 = DateUtil.betweenYear(DateUtil.parse(domainAccount22.getRegisterTime()),
                // DateUtil.parse(domainAccount22.getExpirationTime()), false);
                // result.put("registrationDuration", years22);
                result.put("typeId", type);
                result.put("bigType", bigType);
                result.put("orderId", domainTransferRecord22.getId());
                result.put("type", 4);
                break;
            // 竞价支出
            case 23:
                // 获取交易市场域名订单详情
                AuctionDomain auctionDomain23 = auctionDomainMapper.selectById(rechargeFlow.getOrderId());

                DomainTransferRecord domainTransferRecord23 =
                    domainTransferRecordMapper.selectOne(Wrappers.<DomainTransferRecord>lambdaQuery()
                        .eq(DomainTransferRecord::getTransferUuid, auctionDomain23.getTransferUuid()));

                result.put("tranNumber", auctionDomain23.getTransferUuid());
                result.put("amount", amount);
                result.put("payType", payType(3));
                result.put("time", auctionDomain23.getCreateTime());
                result.put("typeState", fundDetailsStatus1(rechargeFlow.getId()));
                result.put("typeName", getTranTypeName(rechargeFlow.getType()));
                // 域名信息
                // result.put("source", "市场（竞价）");
                // result.put("domain", auctionDomain23.getDomain());
                result.put("typeId", type);
                result.put("bigType", bigType);
                if (domainTransferRecord23 != null) {
                    result.put("orderId", domainTransferRecord23.getId());
                    result.put("type", 5);
                }
                break;
            // 竞价退款
            case 24:
                AuctionDomain auctionDomain24 = auctionDomainMapper.selectById(rechargeFlow.getOrderId());
                result.put("tranNumber", auctionDomain24.getTransferUuid());
                result.put("amount", amount);
                result.put("payType", payType(3));
                result.put("time", auctionDomain24.getCreateTime());
                result.put("typeState", fundDetailsStatus1(rechargeFlow.getId()));
                result.put("typeName", getTranTypeName(rechargeFlow.getType()));
                // 域名信息
                result.put("typeId", type);
                result.put("bigType", bigType);
                break;
            // 竞价收入
            case 25:
                AuctionDomain auctionDomain25 = auctionDomainMapper.selectById(rechargeFlow.getOrderId());
                DomainTransferRecord domainTransferRecord25 =
                    domainTransferRecordMapper.selectOne(Wrappers.<DomainTransferRecord>lambdaQuery()
                        .eq(DomainTransferRecord::getTransferUuid, auctionDomain25.getTransferUuid()));
                result.put("tranNumber", auctionDomain25.getTransferUuid());
                // 实际到账
                result.put("amount", realAmount);
                // 收款金额
                result.put("amountCollected", decimalConversion(auctionDomain25.getTransactionPrice()));
                result.put("payType", payType(3));
                result.put("time", auctionDomain25.getCreateTime());
                result.put("typeState", fundDetailsStatus1(rechargeFlow.getId()));
                result.put("typeName", getTranTypeName(rechargeFlow.getType()));
                result.put("rate", auctionDomain25.getRate());
                result.put("servicePrice", auctionDomain25.getServicePrice());
                // 域名信息
                // result.put("source", "市场（竞价）");
                result.put("domain", auctionDomain25.getDomain());
                result.put("typeId", type);
                result.put("bigType", bigType);
                if (domainTransferRecord25 != null) {
                    result.put("orderId", domainTransferRecord25.getId());
                    result.put("type", 5);
                }
                break;
            // 能量充值
            case 26:
                RechargeRecord rechargeRecord = rechargeRecordMapper.selectById(rechargeFlow.getOrderId());
                // 获取能连充值记录
                EnergyRechargeFlow energyRechargeFlow =
                    energyRechargeFlowMapper.selectOne(Wrappers.<EnergyRechargeFlow>lambdaQuery()
                        .eq(EnergyRechargeFlow::getReqTransactionSn, rechargeRecord.getTranNumber()));
                // 获取灵戒链信息
                Chain chain = chainService.getLjChainInfoService();

                result.put("tranNumber", rechargeRecord.getTranNumber());
                result.put("amount", decimalConversion(rechargeRecord.getAmount()));
                result.put("payType", payType(rechargeRecord.getPayType()));
                result.put("time", rechargeRecord.getCreateTime());
                result.put("typeState", fundDetailsStatus1(rechargeFlow.getId()));
                result.put("typeName", getTranTypeName(rechargeFlow.getType()));
                // 封装充值信息
                // 链类型
                result.put("chainType", chain.getChainName());
                result.put("address", energyRechargeFlow.getAddress());
                // 充值gas
                result.put("gas", energyRechargeFlow.getGas());
                result.put("typeId", type);
                result.put("bigType", bigType);
                break;
            // 影票下单支出
            case 27:
                MovieOrder movieOrder27 = movieOrderMapper.selectById(rechargeFlow.getOrderId());
                result.put("tranNumber", movieOrder27.getThirdOrderId());
                result.put("amount", amount);
                result.put("discountAmount",movieOrder27.getDiscountAmount());
                result.put("realAmount",movieOrder27.getRealAmount());
                Integer typeId = movieOrder27.getPayType();
                if (typeId == 1) {
                    typeId = 3;
                } else if (typeId == 2) {
                    typeId = 7;
                }

                result.put("payType", payType(typeId));
                result.put("time", movieOrder27.getPayTime());
                result.put("typeState", fundDetailsStatus1(rechargeFlow.getId()));
                result.put("typeName", getTranTypeName(rechargeFlow.getType()));
                result.put("typeId", type);
                result.put("bigType", bigType);
                // result.put("orderId", movieOrder27.getId());
                break;
            // 影票已结算扣除冻结
            case 28:
                MovieOrder movieOrder28 = movieOrderMapper.selectById(rechargeFlow.getOrderId());
                result.put("tranNumber", movieOrder28.getThirdOrderId());
                result.put("amount", amount);
                Integer typeId28 = movieOrder28.getPayType();
                if (typeId28 == 1) {
                    typeId28 = 3;
                } else if (typeId28 == 2) {
                    typeId28 = 7;
                }
                result.put("payType", payType(typeId28));

                result.put("time", rechargeWithdrawRecord.getCreateTime());
                result.put("typeState", fundDetailsStatus1(rechargeFlow.getId()));
                result.put("typeName", getTranTypeName(rechargeFlow.getType()));
                result.put("typeId", type);
                result.put("bigType", bigType);
                break;
            // 影票订单关闭返还资产
            case 29:
                MovieOrder movieOrder29 = movieOrderMapper.selectById(rechargeFlow.getOrderId());
                result.put("tranNumber", movieOrder29.getThirdOrderId());
                result.put("amount", amount);
                result.put("discountAmount",movieOrder29.getDiscountAmount());
                result.put("realAmount",movieOrder29.getRealAmount());
                Integer typeId29 = movieOrder29.getPayType();
                if (typeId29 == 1) {
                    typeId29 = 3;
                } else if (typeId29 == 2) {
                    typeId29 = 7;
                }
                result.put("payType", payType(typeId29));

                result.put("time", movieOrder29.getPayTime());
                result.put("typeState", fundDetailsStatus1(rechargeFlow.getId()));
                result.put("typeName", getTranTypeName(rechargeFlow.getType()));
                result.put("typeId", type);
                result.put("bigType", bigType);
                // result.put("orderId", movieOrder29.getId());
                break;
            // 域名续费（消耗）
            case 30:
                // 判断是否是新订单
                String orderNumber = rechargeFlow.getOrderNumber();
                Date time = null;
                // 折扣金额
                BigDecimal totalDiscountAmount = BigDecimal.ZERO;
                // 原始金额
                BigDecimal totalOriginalAmount = BigDecimal.ZERO;
                String tranNumber = "";
                // 实付金额
                BigDecimal totalAmount = BigDecimal.ZERO;
                // 支付方式
                Integer payType30 = 1;
                Long id1 = null;
                List<OrderDomainRenewalInfo> orderDomainRenewalInfos = new ArrayList<>();
                // 新订单
                if (orderNumber != null) {
                    OrderDomainRenewal orderDomainRenewal =
                        orderDomainRenewalMapper.selectById(rechargeFlow.getOrderId());

                    orderDomainRenewalInfos =
                        orderDomainRenewalInfoMapper.selectList(Wrappers.<OrderDomainRenewalInfo>lambdaQuery()
                            .eq(OrderDomainRenewalInfo::getOrderId, orderDomainRenewal.getId()));

                    time = orderDomainRenewal.getCreateTime();
                    totalDiscountAmount = orderDomainRenewal.getDiscountAmount();
                    totalOriginalAmount = orderDomainRenewal.getOriginalAmount();
                    tranNumber = orderDomainRenewal.getTranNumber();
                    totalAmount = orderDomainRenewal.getTotalAmount();
                    payType30 = orderDomainRenewal.getPayType();
                    id1 = orderDomainRenewal.getId();
                    // 旧订单
                } else {

                    OrderDomainRenewalInfo orderDomainRenewalInfo =
                        orderDomainRenewalInfoMapper.selectById(rechargeFlow.getOrderId());
                    OrderDomainRenewal orderDomainRenewal =
                        orderDomainRenewalMapper.selectById(orderDomainRenewalInfo.getOrderId());

                    orderDomainRenewalInfos =
                        orderDomainRenewalInfoMapper.selectList(Wrappers.<OrderDomainRenewalInfo>lambdaQuery()
                            .eq(OrderDomainRenewalInfo::getOrderId, orderDomainRenewal.getId()));

                    time = orderDomainRenewal.getCreateTime();
                    totalDiscountAmount =
                        orderDomainRenewalInfos.stream().map(OrderDomainRenewalInfo::getDiscountAmount)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    totalOriginalAmount =
                        orderDomainRenewalInfos.stream().map(OrderDomainRenewalInfo::getOriginalAmount)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    tranNumber = orderDomainRenewal.getTranNumber();
                    totalAmount = orderDomainRenewal.getTotalAmount();
                    payType30 = orderDomainRenewal.getPayType();
                    id1 = orderDomainRenewal.getId();
                }
                result.put("tranNumber", tranNumber);
                result.put("amount", decimalConversion(totalAmount));
                result.put("discountAmount", decimalConversion(totalDiscountAmount));
                result.put("originalAmount", decimalConversion(totalOriginalAmount));
                if (payType30 == 1) {
                    payType30 = 3;
                } else if (payType30 == 3) {
                    payType30 = 1;
                } else if (payType30 == 4) {
                    payType30 = 2;
                }
                result.put("payType", payType(payType30));
                result.put("time", time);
                result.put("typeState", fundDetailsStatus1(rechargeFlow.getId()));
                result.put("typeName", getTranTypeName(rechargeFlow.getType()));
                result.put("typeId", type);
                result.put("bigType", bigType);
                result.put("orderId", id1);
                result.put("orderList", orderDomainRenewalInfos);
                result.put("type", 3);
                break;
            // 域名续费（退款）
            case 31:
                String orderNumber31 = rechargeFlow.getOrderNumber();
                String tranNumber31 = "";
                BigDecimal amount31 = BigDecimal.ZERO;
                // 折扣金额
                BigDecimal totalDiscountAmount31 = BigDecimal.ZERO;
                // 原始金额
                BigDecimal totalOriginalAmount31 = BigDecimal.ZERO;
                // 支付方式
                Integer payType31 = 1;
                Long id31 = null;
                Date time31 = null;
                // 新订单
                if (orderNumber31 != null) {
                    OrderDomainRenewal orderDomainRenewal =
                            orderDomainRenewalMapper.selectById(rechargeFlow.getOrderId());
                    tranNumber31 = orderDomainRenewal.getTranNumber();
                    amount31 = orderDomainRenewal.getTotalAmount();
                    payType31= orderDomainRenewal.getPayType();
                    id31 = orderDomainRenewal.getId();
                    time=orderDomainRenewal.getCreateTime();
                }else {
                    OrderDomainRenewalInfo orderDomainRenewalInfo31=
                    orderDomainRenewalInfoMapper.selectById(rechargeFlow.getOrderId());
                    tranNumber31 = orderDomainRenewalInfo31.getTranNumber();
                    amount31 = orderDomainRenewalInfo31.getAmount();
                    totalDiscountAmount31 = orderDomainRenewalInfo31.getDiscountAmount();
                    totalOriginalAmount31 = orderDomainRenewalInfo31.getOriginalAmount();
                    payType31 = orderDomainRenewalInfo31.getPayType();
                    id31 = orderDomainRenewalInfo31.getId();
                    time=orderDomainRenewalInfo31.getCreateTime();
                }

                result.put("tranNumber", tranNumber31);
                result.put("amount", decimalConversion(amount31));
                result.put("discountAmount", decimalConversion(totalDiscountAmount31));
                result.put("originalAmount", decimalConversion(totalOriginalAmount31));
                if (payType31 == 1) {
                    payType31 = 3;
                } else if (payType31 == 2) {
                    payType31 = 8;
                }

                // 退款金额
                result.put("refundAmount", amount);
                // 退款方式
                result.put("refundPayType", "余额");
                // 退款状态
                result.put("refundState", fundDetailsStatus1(rechargeFlow.getId()));
                // 退款时间
                result.put("refundtime", rechargeWithdrawRecord.getCreateTime());

                result.put("payType", payType(payType31));
                result.put("time", time31);
                result.put("typeState", "已支付");
                result.put("typeName", getTranTypeName(30));
                result.put("typeId", type);
                result.put("bigType", bigType);
                result.put("orderId",id31);
                result.put("type", 3);
                break;
            // 域名续费（下级返佣）=续费返佣
            case 32:
                OrderDomainRenewalInfo orderDomainRenewalInfo32 =
                    orderDomainRenewalInfoMapper.selectById(rechargeFlow.getOrderId());

                OrderDomainRenewalRebate orderDomainRenewalRebate =
                    orderDomainRenewalRebateMapper.selectOne(Wrappers.<OrderDomainRenewalRebate>lambdaQuery()
                        .eq(OrderDomainRenewalRebate::getOrderInfoId, rechargeFlow.getOrderId()));

                result.put("tranNumber", orderDomainRenewalInfo32.getTranNumber());
                result.put("amount", amount);
                Integer payType32 = orderDomainRenewalInfo32.getPayType();

                if (payType32 == 1) {
                    payType32 = 3;
                } else if (payType32 == 2) {
                    payType32 = 8;
                }

                result.put("payType", payType(payType32));

                result.put("source", orderDomainRenewalInfo32.getDomain());
                result.put("proportion", orderDomainRenewalRebate.getParentAccountScale());
                result.put("primitiveAmount", decimalConversion(orderDomainRenewalInfo32.getAmount()));

                result.put("time", orderDomainRenewalInfo32.getCreateTime());
                result.put("typeState", fundDetailsStatus1(rechargeFlow.getId()));
                result.put("typeName", getTranTypeName(rechargeFlow.getType()));
                result.put("typeId", type);
                result.put("bigType", bigType);
                result.put("orderId", orderDomainRenewalRebate.getId());
                result.put("type", 3);
                break;
            // 域名续费（自身返佣）
            case 33:
                OrderDomainRenewalInfo orderDomainRenewalInfo33 =
                    orderDomainRenewalInfoMapper.selectById(rechargeFlow.getOrderId());

                OrderDomainRenewalRebate orderDomainRenewalRebate33 =
                    orderDomainRenewalRebateMapper.selectOne(Wrappers.<OrderDomainRenewalRebate>lambdaQuery()
                        .eq(OrderDomainRenewalRebate::getOrderInfoId, rechargeFlow.getOrderId()));

                result.put("tranNumber", orderDomainRenewalInfo33.getTranNumber());

                result.put("amount", amount);

                Integer payType33 = orderDomainRenewalInfo33.getPayType();
                if (payType33 == 1) {
                    payType33 = 3;
                } else if (payType33 == 2) {
                    payType33 = 8;
                }

                result.put("payType", payType(payType33));
                result.put("time", orderDomainRenewalInfo33.getCreateTime());

                result.put("source", orderDomainRenewalInfo33.getDomain());
                result.put("proportion", orderDomainRenewalRebate33.getThisAccountScale());
                result.put("primitiveAmount", decimalConversion(orderDomainRenewalInfo33.getAmount()));

                result.put("typeState", fundDetailsStatus1(rechargeFlow.getId()));
                result.put("typeName", getTranTypeName(rechargeFlow.getType()));
                result.put("typeId", type);
                result.put("bigType", bigType);
                result.put("orderId", orderDomainRenewalRebate33.getId());
                result.put("type", 3);
                break;
            // 实名DID及信息凭证（下级返佣）
            case 34:
                // did返佣关联表
                DidRebate didRebate = didRebateMapper.selectOne(Wrappers.<DidRebate>lambdaQuery()
                    .eq(DidRebate::getType, 1).eq(DidRebate::getOrderId, rechargeFlow.getOrderId()));
                // 流水号
                result.put("tranNumber", rechargeFlow.getOrderId());
                // 类型
                result.put("typeName", getTranTypeName(rechargeFlow.getType()));

                Account account = accountMapper.selectOne(
                    Wrappers.<Account>lambdaQuery().eq(Account::getUuid, didRebate.getAccountUuid()));
                // 返佣来源
                result.put("source", account.getNickName());

                // 返佣金额
                result.put("amount", amount);
                // 返佣比例
                result.put("proportion", didRebate.getParentAccountScale());
                // 实际金额
                result.put("originalAmount", decimalConversion(didRebate.getAmount()));

                // 返佣状态
                result.put("typeState", fundDetailsStatus1(rechargeFlow.getId()));
                // result.put("payType", payType(3));

                // 时间
                result.put("time", rechargeFlow.getCreateTime());
                result.put("typeId", type);

                break;
            // 实名DID及信息凭证（自身返佣）
            case 35:
                // did返佣关联表
                DidRebate didRebate35 = didRebateMapper.selectOne(Wrappers.<DidRebate>lambdaQuery()
                    .eq(DidRebate::getType, 1).eq(DidRebate::getOrderId, rechargeFlow.getOrderId()));
                // 流水号
                result.put("tranNumber", rechargeFlow.getOrderId());
                // 类型
                result.put("typeName", getTranTypeName(rechargeFlow.getType()));

                Account account35 = accountMapper.selectOne(
                    Wrappers.<Account>lambdaQuery().eq(Account::getUuid, didRebate35.getAccountUuid()));
                // 返佣来源
                result.put("source", account35.getNickName());

                // 实付金额
                result.put("originalAmount", decimalConversion(didRebate35.getAmount()));
                // 返佣金额
                result.put("amount", amount);
                // 返佣比例
                result.put("proportion", didRebate35.getThisAccountScale());

                // 返佣状态
                result.put("typeState", fundDetailsStatus1(rechargeFlow.getId()));
                // result.put("payType", payType(3));

                // 时间
                result.put("time", rechargeFlow.getCreateTime());
                result.put("typeId", type);
                break;
            // 信息凭证（下级返佣）
            case 36:
                // did返佣关联表
                DidRebate didRebate36 = didRebateMapper.selectOne(Wrappers.<DidRebate>lambdaQuery()
                    .eq(DidRebate::getType, 1).eq(DidRebate::getOrderId, rechargeFlow.getOrderId()));
                // 流水号
                result.put("tranNumber", rechargeFlow.getOrderId());
                // 类型
                result.put("typeName", getTranTypeName(rechargeFlow.getType()));

                Account account36 = accountMapper.selectOne(
                    Wrappers.<Account>lambdaQuery().eq(Account::getUuid, didRebate36.getAccountUuid()));
                // 返佣来源
                result.put("source", account36.getNickName());

                // 实付金额
                result.put("originalAmount", decimalConversion(didRebate36.getAmount()));
                // 返佣比例
                result.put("proportion", didRebate36.getParentAccountScale());
                // 返佣金额
                result.put("amount", amount);

                // 返佣状态
                result.put("typeState", fundDetailsStatus1(rechargeFlow.getId()));
                // result.put("payType", payType(3));

                // 时间
                result.put("time", rechargeFlow.getCreateTime());
                result.put("typeId", type);
                break;
            // 信息凭证（自身返佣）
            case 37:
                // did返佣关联表
                DidRebate didRebate37 = didRebateMapper.selectOne(Wrappers.<DidRebate>lambdaQuery()
                    .eq(DidRebate::getType, 1).eq(DidRebate::getOrderId, rechargeFlow.getOrderId()));
                // 流水号
                result.put("tranNumber", rechargeFlow.getOrderId());
                // 类型
                result.put("typeName", getTranTypeName(rechargeFlow.getType()));

                Account account37 = accountMapper.selectOne(
                    Wrappers.<Account>lambdaQuery().eq(Account::getUuid, didRebate37.getAccountUuid()));
                // 返佣来源
                result.put("source", account37.getNickName());

                // 实付金额
                result.put("originalAmount", decimalConversion(didRebate37.getAmount()));
                // 返佣比例
                result.put("proportion", didRebate37.getThisAccountScale());
                // 返佣金额
                result.put("amount", amount);

                // 返佣状态
                result.put("typeState", fundDetailsStatus1(rechargeFlow.getId()));
                // result.put("payType", payType(3));

                // 时间
                result.put("time", rechargeFlow.getCreateTime());
                result.put("typeId", type);
                break;
            // 实名DID(下级返佣)
            case 38:
                // did返佣关联表
                DidRebate didRebate38 = didRebateMapper.selectOne(Wrappers.<DidRebate>lambdaQuery()
                    .eq(DidRebate::getType, 1).eq(DidRebate::getOrderId, rechargeFlow.getOrderId()));
                // 流水号
                result.put("tranNumber", rechargeFlow.getOrderId());
                // 类型
                result.put("typeName", getTranTypeName(rechargeFlow.getType()));

                Account account38 = accountMapper.selectOne(
                    Wrappers.<Account>lambdaQuery().eq(Account::getUuid, didRebate38.getAccountUuid()));
                // 返佣来源
                result.put("source", account38.getNickName());

                // 实付金额
                result.put("originalAmount", decimalConversion(didRebate38.getAmount()));
                // 返佣比例
                result.put("proportion", didRebate38.getParentAccountScale());
                // 返佣金额
                result.put("amount", amount);

                // 返佣状态
                result.put("typeState", fundDetailsStatus1(rechargeFlow.getId()));
                // result.put("payType", payType(3));

                // 时间
                result.put("time", rechargeFlow.getCreateTime());
                result.put("typeId", type);
                break;
            // 实名DID（自身返佣）
            case 39:
                // did返佣关联表
                DidRebate didRebate39 = didRebateMapper.selectOne(Wrappers.<DidRebate>lambdaQuery()
//                    .eq(DidRebate::getType, 1)
                        .eq(DidRebate::getOrderId, rechargeFlow.getOrderId()));
                // 流水号
                result.put("tranNumber", rechargeFlow.getOrderId());
                // 类型
                result.put("typeName", getTranTypeName(rechargeFlow.getType()));

                Account account39 = accountMapper.selectOne(
                    Wrappers.<Account>lambdaQuery().eq(Account::getUuid, didRebate39.getAccountUuid()));
                // 返佣来源
                result.put("source", account39.getNickName());

                // 实付金额
                result.put("originalAmount", decimalConversion(didRebate39.getAmount()));
                // 返佣比例
                result.put("proportion", didRebate39.getThisAccountScale());
                // 返佣金额
                result.put("amount", amount);

                // 返佣状态
                result.put("typeState", fundDetailsStatus1(rechargeFlow.getId()));
                // result.put("payType", payType(3));

                // 时间
                result.put("time", rechargeFlow.getCreateTime());
                result.put("typeId", type);
                break;
            // 回复私钥（下级返佣）
            case 40:
                // did返佣关联表
                DidRebate didRebate40 = didRebateMapper.selectOne(Wrappers.<DidRebate>lambdaQuery()
                    .eq(DidRebate::getType, 1).eq(DidRebate::getOrderId, rechargeFlow.getOrderId()));
                // 流水号
                result.put("tranNumber", rechargeFlow.getOrderId());
                // 类型
                result.put("typeName", getTranTypeName(rechargeFlow.getType()));

                Account account40 = accountMapper.selectOne(
                    Wrappers.<Account>lambdaQuery().eq(Account::getUuid, didRebate40.getAccountUuid()));
                // 返佣来源
                result.put("source", account40.getNickName());

                // 实付金额
                result.put("originalAmount", decimalConversion(didRebate40.getAmount()));
                // 返佣比例
                result.put("proportion", didRebate40.getParentAccountScale());
                // 返佣金额
                result.put("amount", amount);

                // 返佣状态
                result.put("typeState", fundDetailsStatus1(rechargeFlow.getId()));
                // result.put("payType", payType(3));

                // 时间
                result.put("time", rechargeFlow.getCreateTime());
                result.put("typeId", type);
                break;
            // 恢复私钥（自身返佣）
            case 41:
                // did返佣关联表
                DidRebate didRebate41 = didRebateMapper.selectOne(Wrappers.<DidRebate>lambdaQuery()
                    .eq(DidRebate::getType, 1).eq(DidRebate::getOrderId, rechargeFlow.getOrderId()));
                // 流水号
                result.put("tranNumber", rechargeFlow.getOrderId());
                // 类型
                result.put("typeName", getTranTypeName(rechargeFlow.getType()));

                Account account41 = accountMapper.selectOne(
                    Wrappers.<Account>lambdaQuery().eq(Account::getUuid, didRebate41.getAccountUuid()));
                // 返佣来源
                result.put("source", account41.getNickName());

                // 实付金额
                result.put("originalAmount", decimalConversion(didRebate41.getAmount()));
                // 返佣比例
                result.put("proportion", didRebate41.getThisAccountScale());
                // 返佣金额
                result.put("amount", amount);

                // 返佣状态
                result.put("typeState", fundDetailsStatus1(rechargeFlow.getId()));
                // result.put("payType", payType(3));

                // 时间
                result.put("time", rechargeFlow.getCreateTime());
                result.put("typeId", type);
                break;
            // 话费充值
            case 42:
                // 话费充值订单表
                PhoneRechargeRecord phoneRechargeRecord =
                    phoneRechargeRecordMapper.selectById(rechargeFlow.getOrderId());
                // 流水号
                result.put("tranNumber", phoneRechargeRecord.getTranNumber());
                // 类型
                result.put("typeName", getTranTypeName(rechargeFlow.getType()));
                // 实付金额
                result.put("originalAmount", decimalConversion(phoneRechargeRecord.getRealAmount()));
                // 优惠金额
                result.put("discountAmount", phoneRechargeRecord.getDiscountAmount());
                // 充值金额
                result.put("amount", phoneRechargeRecord.getAmount());
                // 支付方式
                result.put("payType", payType(phoneRechargeRecord.getPayType()));
                // 支付状态
                result.put("typeState", "已支付");
                // 支付时间
                result.put("time", phoneRechargeRecord.getPayTime());
                // 充值号码
                result.put("phone", phoneRechargeRecord.getRechargeNumber());
                result.put("typeId", type);
                break;
            // 话费充值退款
            case 43:
                // 话费充值订单表
                PhoneRechargeRecord phoneRechargeRecord43 =
                    phoneRechargeRecordMapper.selectById(rechargeFlow.getOrderId());
                // 流水号
                result.put("tranNumber", phoneRechargeRecord43.getTranNumber());
                // 类型
                result.put("typeName", getTranTypeName(rechargeFlow.getType()));
                // 实付金额
                result.put("originalAmount", decimalConversion(phoneRechargeRecord43.getRealAmount()));
                // 优惠金额
                result.put("discountAmount", phoneRechargeRecord43.getDiscountAmount());
                // 充值金额
                result.put("amount", phoneRechargeRecord43.getAmount());
                // 支付方式
                result.put("payType", payType(phoneRechargeRecord43.getPayType()));
                // 支付状态
                result.put("typeState", "已支付");
                // 支付时间
                result.put("time", phoneRechargeRecord43.getPayTime());

                // 退款金额
                result.put("refundAmount", phoneRechargeRecord43.getRefundAmount());
                // 退款方式
                result.put("refundPayType", phoneRechargeRecord43.getPayType());
                // 退款状态
                result.put("refundState", fundDetailsStatus1(rechargeFlow.getId()));
                // 退款时间
                result.put("refundtime", phoneRechargeRecord43.getRefundTime());
                // 充值号码
                result.put("phone", phoneRechargeRecord43.getRechargeNumber());
                result.put("typeId", type);
                break;
            // 一口价退款
            case 44:
                // 获取交易市场域名订单详情
                FixDomain fixDomain44 = fixDomainMapper.selectById(rechargeFlow.getOrderId());
                // 获取域名详情
                DomainAccount domainAccount44 = domainAccountMapper.selectOne(Wrappers
                    .<DomainAccount>lambdaQuery().eq(DomainAccount::getDomain, fixDomain44.getDomain()));
                result.put("tranNumber", fixDomain44.getTransferUuid());
                result.put("amount", amount);
                result.put("payType", payType(3));
                result.put("time", fixDomain44.getCreateTime());
                result.put("typeState", fundDetailsStatus1(rechargeFlow.getId()));
                result.put("typeName", getTranTypeName(rechargeFlow.getType()));
                result.put("typeId", type);
                result.put("bigType", bigType);
                result.put("orderId", rechargeFlow.getOrderId());
                result.put("type", 4);
                break;
        }

        return R.okData(result);
    }


    /**
     * 添加提现纪录
     *
     * @param amount
     * @param cardId
     * @return {@link R }
     */
    @Override
    public R addWithdrawalRecordService(BigDecimal amount, Integer cardId) {

        // 计算提现手续费
        WithdrawServiceChargeInfo withdrawServiceChargeInfo = withdrawServChargeService(amount);
        // //服务费
        BigDecimal serviceCharge = withdrawServiceChargeInfo.getServiceCharge();
        // 实际金额
        BigDecimal realAmount = withdrawServiceChargeInfo.getActualAmount();
        Account userInfo = accountService.getUserInfo();
        // 校验是否存在审核中的提现记录
        List<RechargeWithdrawRecord> rebateWithdrawRecords =
            rechargeWithdrawRecordMapper.selectList(Wrappers.<RechargeWithdrawRecord>lambdaQuery()
                .eq(RechargeWithdrawRecord::getAccountUuid, userInfo.getUuid())
                .eq(RechargeWithdrawRecord::getState, 1));
        if (CollectionUtil.isNotEmpty(rebateWithdrawRecords)) {
            return R.error("您有未完成的提现订单，请在订单完成后再申请");
        }
        String key = "Withdrawal_Local:" + userInfo.getUuid();
        // 阻塞锁的方式处理领取NFT
        String cancelOrderProcessResult = lockUtil.executeWithBlockingLock(key, () -> {
            this.extracted(amount, cardId, userInfo, withdrawServiceChargeInfo, serviceCharge);
            return null;
        });
        // 异步发送钉钉消息推送
        dingdingMessagePush(userInfo.getOperateUuid(), userInfo.getRealName(), amount);
        return R.ok();
    }

    @Override
    @Transactional
    public void extracted(BigDecimal amount, Integer cardId, Account userInfo, WithdrawServiceChargeInfo withdrawServiceChargeInfo, BigDecimal serviceCharge) {
            // 检验余额
            RechargeAssets rechargeAssets = rechargeAssetsService.checkAssets(amount);
            BigDecimal balance = rechargeAssets.getBalance();

            // 获取银行卡信息
            AccountCard accountCard = accountCardMapper.selectOne(Wrappers.<AccountCard>lambdaQuery()
                    .eq(AccountCard::getId, cardId).eq(AccountCard::getAccountUuid, userInfo.getUuid()));

            long timeStamp = DateUtil.date().getTime();
            // 交易流水号
            String merOrderId = "YM" + timeStamp;

            // 生成提现订单
            RechargeWithdrawRecord rechargeWithdrawRecord = new RechargeWithdrawRecord();
            rechargeWithdrawRecord.setOperateUuid(userInfo.getOperateUuid());
            rechargeWithdrawRecord.setAccountUuid(userInfo.getUuid());
            rechargeWithdrawRecord.setCardNumber(accountCard.getCardNumber().replaceAll(" ", ""));
            rechargeWithdrawRecord.setBank(accountCard.getBank());
            rechargeWithdrawRecord.setCardName(accountCard.getName());
            rechargeWithdrawRecord.setAmount(amount);
            BigDecimal actualAmount = withdrawServiceChargeInfo.getActualAmount();
            rechargeWithdrawRecord.setRealAmount(actualAmount);

            rechargeWithdrawRecord.setRate(serviceCharge.toPlainString());
            rechargeWithdrawRecord.setBalance(balance);

            rechargeWithdrawRecord.setType(2);
            rechargeWithdrawRecord.setTranNumber(merOrderId);
            rechargeWithdrawRecord.setPayType(4);
            rechargeWithdrawRecord.setTaxRate(withdrawServiceChargeInfo.getTaxRate());
            rechargeWithdrawRecord.setTaxAmount(withdrawServiceChargeInfo.getTaxAmount());
            rechargeWithdrawRecord.setSourceFunds(2);
            // 插入提现记录
            rechargeWithdrawRecordMapper.insert(rechargeWithdrawRecord);
            // 资产流水表
            RechargeFlow rechargeFlow = new RechargeFlow();
            rechargeFlow.setOperateUuid(userInfo.getOperateUuid()).setAccountUuid(userInfo.getUuid())
                    .setRealAmount(actualAmount).setOrderId(rechargeWithdrawRecord.getId()).setAmount(amount)
                    .setType(2);
            rechargeFlowMapper.insert(rechargeFlow);
            // 增加资金冻结 减余额 增加资产冻结
            rechargeAssetsService.addFreeze(amount, userInfo.getUuid());
    }

    /**
     * 获取提现费率
     * 
     * @param withdrawAmount 提现金额
     * @return {@link WithdrawServiceChargeInfo }
     * <AUTHOR>
     * @date 2024/04/03
     */
    @Override
    public WithdrawServiceChargeInfo withdrawServChargeService(BigDecimal withdrawAmount) {
        WithdrawServiceChargeInfo withdrawServiceChargeInfo = new WithdrawServiceChargeInfo();
        String withdrawServiceCharge = globalConfigService.getGlobalConfig(WITHDRAW_SERVICE_CHARGE);
        Assert.notNull(withdrawServiceCharge, "配置缺失请联系管理员");
        BigDecimal serviceCharge = new BigDecimal(withdrawServiceCharge);
        BigDecimal actualAmount = withdrawAmount.subtract(serviceCharge);
        withdrawServiceChargeInfo.setWithdrawAmount(withdrawAmount);
        withdrawServiceChargeInfo.setServiceCharge(serviceCharge);
        withdrawServiceChargeInfo.setActualAmount(actualAmount);
        return withdrawServiceChargeInfo;
    }

    /**
     * 资金关联订单信息
     * 
     * @param orderId
     * @param typeId
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/06/05
     */
    @Override
    public R getFundRelatedOrdersService(Integer orderId, Integer typeId) {
        Map<String, Object> result = new HashMap<>();
        switch (typeId) {
            // 购买域名消耗
            case 3:
                result = orderBsnInfoMapper.selectDomainOrderInfo(orderId);
                Object payType = result.get("payType");
                if (payType == null) {
                    payType = 3;
                }
                result.put("payType", payType((Integer)(payType)));
                break;
            // 购买域名退款
            case 4:
                result = orderBsnInfoMapper.selectDomainOrderInfoTuikuan(orderId);
                Object payType4 = result.get("payType");
                if (payType4 == null) {
                    payType4 = 3;
                }
                result.put("payType", payType((Integer)(payType4)));
                break;

            // 域名转让
            case 18:
                // 获取域名信息
                OrderBsnInfo orderBsnInfo18 = orderBsnInfoMapper
                    .selectOne(Wrappers.<OrderBsnInfo>lambdaQuery().eq(OrderBsnInfo::getId, orderId));
                result = orderBsnInfoMapper.selectDomainInfo(orderBsnInfo18.getDomain(),
                    orderBsnInfo18.getAccountUuid(), orderBsnInfo18.getDomainServeSn());
                Integer transferState = orderBsnInfo18.getTransferState();
                // 1=>装让
                if (ObjectUtil.equals(transferState, 1)) {
                    Map<String, Object> transferDomainResult = orderBsnInfoMapper
                        .selectTransferDomain(orderBsnInfo18.getDomainServeSn(), transferState);
                    Map<String, Object> transferDomainResult1 =
                        orderBsnInfoMapper.selectTransferDomain(orderBsnInfo18.getDomainServeSn(), 2);
                    result.put("transferState", transferDomainResult.get("transferState"));
                    result.put("transferor", transferDomainResult.get("realName"));
                    result.put("recipient", transferDomainResult1.get("realName"));
                } else if (ObjectUtil.equals(transferState, 2)) {
                    Map<String, Object> transferDomainResult = orderBsnInfoMapper
                        .selectTransferDomain(orderBsnInfo18.getDomainServeSn(), transferState);
                    Map<String, Object> transferDomainResult1 =
                        orderBsnInfoMapper.selectTransferDomain(orderBsnInfo18.getDomainServeSn(), 2);
                    result.put("transferState", transferDomainResult.get("transferState"));
                    result.put("transferor", transferDomainResult1.get("realName"));
                    result.put("recipient", transferDomainResult.get("realName"));
                }
                result.put("transferTime", orderBsnInfo18.getCreateTime());

                break;
            // 一口价支出
            case 21:
                // 获取交易市场域名订单详情
                FixDomain fixDomain = fixDomainMapper.selectById(orderId);
                Integer payStatus = fixDomain.getPayStatus();
                // 获取域名信息
                result = orderBsnInfoMapper.selectDomainInfoMarket(fixDomain.getDomain(),
                    fixDomain.getAccountUuid(), fixDomain.getTransferUuid());
                // 订单编号
                result.put("tranNumber", fixDomain.getTransferUuid());
                // 创建时间
                result.put("createTime", fixDomain.getCreateTime());
                // 订单类型
                result.put("orderType", "一口价");
                // 交易类型
                result.put("transactionType", "已购入");
                // 交易金额
                result.put("amount", fixDomain.getPrice());
                // 支付方式
                result.put("payType", payType(3));
                result.put("typeState", "已支付");
                result.put("time", fixDomain.getCreateTime());
                result.put("sellerNickName", fixDomain.getAccountNickName());
                break;
            // 一口价收入
            case 22:
                // 获取交易市场域名订单详情
                FixDomain fixDomain22 = fixDomainMapper.selectById(orderId);
                Integer payStatus22 = fixDomain22.getPayStatus();
                // 获取域名信息
                result = orderBsnInfoMapper.selectDomainInfoMarket(fixDomain22.getDomain(),
                    fixDomain22.getAccountUuid(), fixDomain22.getTransferUuid());
                // 订单编号
                result.put("tranNumber", fixDomain22.getTransferUuid());
                // 创建时间
                result.put("createTime", fixDomain22.getCreateTime());
                // 订单类型
                result.put("orderType", "一口价");
                // 交易类型
                result.put("transactionType", "已出售");
                // 交易金额
                result.put("amount", fixDomain22.getPrice());
                // 支付方式
                result.put("payType", payType(3));
                result.put("time", fixDomain22.getCreateTime());
                result.put("typeState", "已到账");
                result.put("buyerNickName", fixDomain22.getBuyerNickName());
                break;
            // 竞价支出
            case 23:
                // 获取交易市场域名订单详情
                AuctionDomain auctionDomain23 = auctionDomainMapper.selectById(orderId);
                result = orderBsnInfoMapper.selectDomainInfoMarket(auctionDomain23.getDomain(),
                    auctionDomain23.getBuyerUuid(), auctionDomain23.getTransferUuid());
                // 订单编号
                result.put("tranNumber", auctionDomain23.getTransferUuid());
                // 创建时间
                result.put("createTime", auctionDomain23.getCreateTime());
                // 订单类型
                result.put("orderType", "竞价");
                // 交易类型
                result.put("transactionType", "已购入");
                // 交易金额
                result.put("amount", auctionDomain23.getPrice());
                // 支付方式
                result.put("payType", payType(3));
                result.put("time", auctionDomain23.getCreateTime());
                result.put("typeState", "已支付");
                result.put("sellerNickName", auctionDomain23.getSellerNickName());
                break;
            // 竞价退款
            case 24:
                RechargeFlow rechargeFlow = rechargeFlowMapper.selectOne(Wrappers.<RechargeFlow>lambdaQuery()
                    .eq(RechargeFlow::getType, 24).eq(RechargeFlow::getOrderId, orderId));
                // 获取交易市场域名订单详情
                AuctionDomain auctionDomain24 = auctionDomainMapper.selectById(orderId);
                result = orderBsnInfoMapper.selectDomainInfoMarket(auctionDomain24.getDomain(),
                    auctionDomain24.getBuyerUuid(), auctionDomain24.getTransferUuid());
                // 订单编号
                result.put("tranNumber", auctionDomain24.getTransferUuid());
                // 创建时间
                result.put("createTime", auctionDomain24.getCreateTime());
                // 订单类型
                result.put("orderType", "竞价");
                // 交易类型
                result.put("transactionType", "已退款");
                // 交易金额
                result.put("amount", rechargeFlow.getAmount());
                // 支付方式
                result.put("payType", payType(3));
                result.put("time", rechargeFlow.getCreateTime());
                result.put("typeState", "已到账");
                break;
            // 竞价收入
            case 25:
                // 获取交易市场域名订单详情
                AuctionDomain auctionDomain25 = auctionDomainMapper.selectById(orderId);
                result = orderBsnInfoMapper.selectDomainInfoMarket(auctionDomain25.getDomain(),
                    auctionDomain25.getBuyerUuid(), auctionDomain25.getTransferUuid());
                // 订单编号
                result.put("tranNumber", auctionDomain25.getTransferUuid());
                // 创建时间
                result.put("createTime", auctionDomain25.getCreateTime());
                // 订单类型
                result.put("orderType", "竞价");
                // 交易类型
                result.put("transactionType", "已出售");
                // 交易金额
                result.put("amount",
                    auctionDomain25.getTransactionPrice().subtract(auctionDomain25.getServicePrice()));
                // 支付方式
                result.put("payType", payType(3));
                result.put("time", auctionDomain25.getCreateTime());
                result.put("typeState", "已到账");
                result.put("buyerNickName", auctionDomain25.getBuyerNickName());
                break;
            case 27:
                MovieOrder movieOrder27 = movieOrderMapper.selectById(orderId);
                result.put("tranNumber", movieOrder27.getThirdOrderId());
                result.put("createTime", movieOrder27.getCreateTime());
                result.put("orderStatus", movieOrder27.getOrderStatusStr());
                result.put("ticketTime", movieOrder27.getTicketTime());
                result.put("serialNumber", movieOrder27.getThirdOrderId());
                result.put("serialNumber", movieOrder27.getThirdOrderId());
                result.put("amount", movieOrder27.getInitPrice());
                Integer typeId27 = movieOrder27.getPayType();
                if (typeId27 == 1) {
                    typeId27 = 3;
                } else if (typeId27 == 2) {
                    typeId27 = 7;
                }
                result.put("payType", payType(typeId27));
                result.put("time", movieOrder27.getCreateTime());
                break;
            case 29:
                MovieOrder movieOrder29 = movieOrderMapper.selectById(orderId);
                result.put("tranNumber", movieOrder29.getThirdOrderId());
                result.put("createTime", movieOrder29.getCreateTime());
                result.put("orderStatus", movieOrder29.getOrderStatusStr());
                result.put("ticketTime", movieOrder29.getTicketTime());
                result.put("serialNumber", movieOrder29.getThirdOrderId());
                result.put("serialNumber", movieOrder29.getThirdOrderId());
                result.put("amount", movieOrder29.getInitPrice());
                Integer typeId29 = movieOrder29.getPayType();
                if (typeId29 == 1) {
                    typeId29 = 3;
                } else if (typeId29 == 2) {
                    typeId29 = 7;
                }
                result.put("payType", payType(typeId29));
                result.put("time", movieOrder29.getCreateTime());
                break;
            // 域名续费（消耗）
            case 30:
                OrderDomainRenewalInfo orderDomainRenewalInfo30 =
                    orderDomainRenewalInfoMapper.selectById(orderId);
                result = domainAccountMapper.selectRenewalInfo(orderDomainRenewalInfo30.getDomain(),
                    orderDomainRenewalInfo30.getTranNumber());
                result.put("createTime", orderDomainRenewalInfo30.getCreateTime());
                result.put("oldExpirationTime", orderDomainRenewalInfo30.getOldExpirationTime());
                String statusStr = "续费中";
                Integer status = orderDomainRenewalInfo30.getStatus();
                if (ObjectUtil.equals(status, 2)) {
                    statusStr = "续费成功";
                } else if (ObjectUtil.equals(status, 3)) {
                    statusStr = "续费失败";
                }

                result.put("status", statusStr);
                result.put("duration", orderDomainRenewalInfo30.getDuration());
                result.put("orderNumber", orderDomainRenewalInfo30.getTranNumber());
                Integer payType30 = orderDomainRenewalInfo30.getPayType();
                if (payType30 == 1) {
                    payType30 = 3;
                } else if (payType30 == 2) {
                    payType30 = 8;
                }
                result.put("typeState", "已支付");
                result.put("payType", payType(payType30));
                result.put("typeName", getTranTypeName(30));
                result.put("amount", orderDomainRenewalInfo30.getAmount());
                result.put("discountAmount", orderDomainRenewalInfo30.getDiscountAmount());
                result.put("originalAmount", orderDomainRenewalInfo30.getOriginalAmount());
                break;
            // 域名续费（退款）
            case 31:
                OrderDomainRenewalInfo orderDomainRenewalInfo31 =
                    orderDomainRenewalInfoMapper.selectById(orderId);
                result = domainAccountMapper.selectRenewalInfo(orderDomainRenewalInfo31.getDomain(),
                    orderDomainRenewalInfo31.getTranNumber());
                result.put("createTime", orderDomainRenewalInfo31.getCreateTime());
                result.put("oldExpirationTime", orderDomainRenewalInfo31.getOldExpirationTime());
                String statusStr31 = "续费中";
                Integer status31 = orderDomainRenewalInfo31.getStatus();
                if (ObjectUtil.equals(status31, 2)) {
                    statusStr31 = "续费成功";
                } else if (ObjectUtil.equals(status31, 3)) {
                    statusStr31 = "续费失败";
                }

                result.put("status", statusStr31);
                result.put("duration", orderDomainRenewalInfo31.getDuration());
                result.put("orderNumber", orderDomainRenewalInfo31.getTranNumber());
                Integer payType31 = orderDomainRenewalInfo31.getPayType();
                if (payType31 == 1) {
                    payType31 = 3;
                } else if (payType31 == 2) {
                    payType31 = 8;
                }
                result.put("typeState", "已退款");
                result.put("payType", payType(payType31));
                result.put("typeName", getTranTypeName(31));
                result.put("amount", orderDomainRenewalInfo31.getAmount());
                result.put("discountAmount", orderDomainRenewalInfo31.getDiscountAmount());
                result.put("originalAmount", orderDomainRenewalInfo31.getOriginalAmount());
                break;
        }
        return R.okData(result);
    }

    /**
     * 取消提现
     * 
     * @param id
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/06/06
     */
    @Override
    @Transactional
    public R getCancelReflectionService(Integer id) {
        RechargeFlow rechargeFlow = rechargeFlowMapper.selectById(id);
        // 资金订单表
        RechargeWithdrawRecord rechargeWithdrawRecord =
            rechargeWithdrawRecordMapper.selectById(rechargeFlow.getOrderId());
        Integer state = rechargeWithdrawRecord.getState();
        Integer type = rechargeFlow.getType();
        // 提现金额
        BigDecimal amount = rechargeWithdrawRecord.getAmount();
        // 修改体现状态
        if (state == 1 && ObjectUtil.equals(type, 2)) {
            rechargeWithdrawRecordMapper.update(null,
                Wrappers.<RechargeWithdrawRecord>lambdaUpdate()
                    .eq(RechargeWithdrawRecord::getId, rechargeWithdrawRecord.getId())
                    .set(RechargeWithdrawRecord::getState, 4));
            // 返回冻结金额
            // 个人资产
            Account userInfo = accountService.getUserInfo();
            RechargeAssets rechargeAssets = rechargeAssetsMapper.selectOne(Wrappers
                .<RechargeAssets>lambdaQuery().eq(RechargeAssets::getAccountUuid, userInfo.getUuid()));
            BigDecimal balance = rechargeAssets.getBalance();
            BigDecimal freeze = rechargeAssets.getFreeze();
            // 增加余额
            BigDecimal currentBalance = balance.add(amount);
            // 减去冻结
            BigDecimal currentFreeze = freeze.subtract(amount);
            rechargeAssetsMapper.update(null,
                Wrappers.<RechargeAssets>lambdaUpdate().eq(RechargeAssets::getAccountUuid, userInfo.getUuid())
                    .set(RechargeAssets::getBalance, currentBalance)
                    .set(RechargeAssets::getFreeze, currentFreeze));
        }
        return R.ok();
    }

    /**
     * 门户远程调用使用
     * @param uuid
     * @param page
     * @param pageSize
     * @param type
     * @param date
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/01/17
     */
    @Override
    public R queryFundListFService(String uuid, int page, int pageSize, Integer type, Date date) {
        DecimalFormat decimalFormat = new DecimalFormat("#.##");
        // 统计耗时
        long startTime = System.currentTimeMillis();

        Page<RechargeFlowVo> rechargeFlowVoPage =
            rechargeRecordMapper.getFundListPage(new Page(page, pageSize), uuid, type, date);
        List<RechargeFlowVo> rechargeFlowVos = rechargeFlowVoPage.getRecords();
        // 耗时
        // long endTime = System.currentTimeMillis();
        // log.error("queryFundListService耗时：" + (endTime - startTime) + "ms");

        // long startTime2 = System.currentTimeMillis();
        // 封装参数
        for (RechargeFlowVo rechargeFlowVo : rechargeFlowVos) {
            Integer typeId = rechargeFlowVo.getTypeId();
            // 展现实际金额
            String realAmount = rechargeFlowVo.getRealAmount();
            String amount = rechargeFlowVo.getAmount();
            if (StrUtil.isNotBlank(realAmount) && typeId != 2 && typeId != 21) {
                String symbol = amount.substring(0, 1);
                rechargeFlowVo.setAmount(symbol + realAmount);
                rechargeFlowVo.setRealAmount(amount);
            }
            String s = fundDetailsStatus1(rechargeFlowVo.getId());
            // 对amount 和real amount 小数进行处理
            rechargeFlowVo.setAmount(decimalFormat.format(new BigDecimal(rechargeFlowVo.getAmount())));
            if (StrUtil.isNotBlank(rechargeFlowVo.getRealAmount())) {
                rechargeFlowVo
                    .setRealAmount(decimalFormat.format(new BigDecimal(rechargeFlowVo.getRealAmount())));
            }
            rechargeFlowVo.setTypeState(s);
        }
        // 耗时
        // long endTime2 = System.currentTimeMillis();
        // log.error("queryFundListService--22耗时：" + (endTime2 - startTime2) + "ms");
        PageUtils pageUtils = new PageUtils(rechargeFlowVoPage.getTotal(), pageSize, page, rechargeFlowVos);
        return R.okData(pageUtils);
    }

    /**
     * 资金明细状态判断
     * 
     * @param id 资金流水id
     * @return {@link String }
     * <AUTHOR>
     * @date 2024/04/12
     */ // =======工具方法===================
    private String fundDetailsStatus(Long id) {
        String typeState = "";
        // 资金流水表
        RechargeFlow rechargeFlow = rechargeFlowMapper.selectById(id);
        // 只市场用 1-提现中 2-冻结中 3-提现成功 4-支付成功 5-已到账 6-已退款 7-待支付 8-已关闭 9-充值成功
        Integer flowType = rechargeFlow.getFlowType();

        switch (rechargeFlow.getType()) {
            // 充值
            case 1:
                // 资金订单表
                RechargeWithdrawRecord rechargeWithdrawRecord1 =
                    rechargeWithdrawRecordMapper.selectById(rechargeFlow.getOrderId());
                Integer state1 = rechargeWithdrawRecord1.getState();
                if (state1 == 6) {
                    typeState = "待支付";
                } else if (state1 == 3) {
                    typeState = "已到账";
                } else if (state1 == 5) {
                    typeState = "支付超时";
                }
                break;
            // 提现
            case 2:
                // 资金订单表
                RechargeWithdrawRecord rechargeWithdrawRecord2 =
                    rechargeWithdrawRecordMapper.selectById(rechargeFlow.getOrderId());
                Integer state2 = rechargeWithdrawRecord2.getState();
                if (state2 == 1) {
                    typeState = "提现中";
                } else if (state2 == 2) {
                    typeState = "提现失败";
                } else if (state2 == 3) {
                    typeState = "已提现";
                } else if (state2 == 4) {
                    typeState = "用户取消";
                }
                break;
            // 购买域名消耗
            case 3:
                typeState = "已支付";
                break;
            // 购买域名退款
            case 4:
                // 资金订单表
                typeState = "已退款";
                break;
            // 划转
            case 5:
                typeState = "划转成功";
                break;
            // 下级注册推广返佣
            case 6:
                typeState = "已返佣";
                break;
            // 自己注册域名返佣
            case 7:
                typeState = "已返佣";
                break;
            // 推广大使升级消耗
            case 8:
                typeState = "已支付";
                break;
            // 申请经销商消耗
            case 9:
                typeState = "已支付";
                break;
            // 实名DID及个人身份信息凭证申领（消耗）
            case 10:
                // 资金订单表
                RechargeWithdrawRecord rechargeWithdrawRecord10 =
                    rechargeWithdrawRecordMapper.selectById(rechargeFlow.getOrderId());
                Integer state10 = rechargeWithdrawRecord10.getState();
                Integer refundState10 = rechargeWithdrawRecord10.getRefundState();
                if (state10 == 3) {
                    if (refundState10 == 5) {
                        typeState = "已支付";
                    } else if (refundState10 == 1) {
                        typeState = "已支付(包含退款)";
                    }
                }
                break;
            // 实名DID及个人身份信息凭证申领（退款）
            case 11:
                typeState = "已退款";
                break;
            // 信息凭证申领（消耗）
            case 12:
                // 资金订单表
                RechargeWithdrawRecord rechargeWithdrawRecord12 =
                    rechargeWithdrawRecordMapper.selectById(rechargeFlow.getOrderId());
                Integer state12 = rechargeWithdrawRecord12.getState();
                Integer refundState12 = rechargeWithdrawRecord12.getRefundState();
                if (state12 == 3) {
                    if (refundState12 == 5) {
                        typeState = "已支付";
                    } else if (refundState12 == 1) {
                        typeState = "已支付(包含退款)";
                    }
                }
                break;
            // 信息凭证申领（退款）
            case 13:
                typeState = "已退款";
                break;
            // DID申领
            case 14:
                // 资金订单表
                RechargeWithdrawRecord rechargeWithdrawRecord14 =
                    rechargeWithdrawRecordMapper.selectById(rechargeFlow.getOrderId());
                Integer state14 = rechargeWithdrawRecord14.getState();
                Integer refundState14 = rechargeWithdrawRecord14.getRefundState();
                if (state14 == 3) {
                    if (refundState14 == 5) {
                        typeState = "已支付";
                    } else if (refundState14 == 1) {
                        typeState = "已支付(包含退款)";
                    }
                }
                break;
            // DID申领退款
            case 15:
                typeState = "已退款";
                break;
            // 恢复私钥
            case 16:
                // 资金订单表
                RechargeWithdrawRecord rechargeWithdrawRecord16 =
                    rechargeWithdrawRecordMapper.selectById(rechargeFlow.getOrderId());
                Integer state16 = rechargeWithdrawRecord16.getState();
                Integer refundState16 = rechargeWithdrawRecord16.getRefundState();
                if (state16 == 3) {
                    if (refundState16 == 5) {
                        typeState = "已支付";
                    } else if (refundState16 == 1) {
                        typeState = "已支付(包含退款)";
                    }
                }
                break;
            // 恢复私钥（退款）
            case 17:
                typeState = "已退款";
                break;
            // 域名转让
            case 18:
                typeState = "已支付";
                break;
            // // 市场购买域名消耗
            // 划转
            case 19:
                typeState = "划转成功";
                break;
            // // 市场购买域名退款
            // case 20:
            //
            // typeState = "已退款";
            // break;
            // 一口价支出
            case 21:
                typeState = "已支付";
                break;
            // 一口价收入
            case 22:
                typeState = "已到账";
                break;
            // 竞价支出
            case 23:
                typeState = "已支付";
                break;
            // 竞价退款
            case 24:
                typeState = "已退款";
                break;
            // 竞价收入
            case 25:
                typeState = "已到账";
                break;
            // 能量充值
            case 26:
                typeState = "已支付";
                break;

            // 影票下单支出
            case 27:
                typeState = "支付成功";
                break;

            // 影票已结算扣除冻结
            case 28:
                typeState = "出票成功(扣款)";
                break;

            // 影票订单关闭返还资产
            case 29:
                typeState = "出票失败(退款)";
                break;

            // 域名续费（消耗）
            case 30:
                typeState = "已支付";
                break;
            // 域名续费（退款）
            case 31:
                typeState = "已退款";
                break;

            // 域名续费（下级返佣）
            case 32:
                typeState = "已返佣";
                break;

            // 域名续费（自身返佣）
            case 33:
                typeState = "已返佣";
                break;
        }
        return typeState;
    }

    private String fundDetailsStatus1(Long id) {
        String typeState = "";
        // 资金流水表
        RechargeFlow rechargeFlow = rechargeFlowMapper.selectById(id);
        // 只市场用 1-提现中 2-冻结中 3-提现成功 4-支付成功 5-已到账 6-已退款 7-待支付 8-已关闭 9-充值成功
        Integer flowType = rechargeFlow.getFlowType();

        switch (rechargeFlow.getType()) {
            // 充值
            case 1:
                // 资金订单表
                RechargeWithdrawRecord rechargeWithdrawRecord1 =
                    rechargeWithdrawRecordMapper.selectById(rechargeFlow.getOrderId());
                Integer state1 = rechargeWithdrawRecord1.getState();
                if (state1 == 6) {
                    typeState = "待支付";
                } else if (state1 == 3) {
                    typeState = "充值成功";
                } else if (state1 == 5) {
                    typeState = "支付超时";
                } else if (state1 == 4) {
                    typeState = "用户取消";
                }
                break;
            // 提现
            case 2:
                // 资金订单表
                RechargeWithdrawRecord rechargeWithdrawRecord2 =
                    rechargeWithdrawRecordMapper.selectById(rechargeFlow.getOrderId());
                Integer state2 = rechargeWithdrawRecord2.getState();
                if (state2 == 1) {
                    typeState = "提现中";
                } else if (state2 == 2) {
                    typeState = "提现失败";
                } else if (state2 == 3) {
                    typeState = "提现成功";
                } else if (state2 == 4) {
                    typeState = "用户取消";
                }
                break;
            // 购买域名消耗
            case 3:
                typeState = "已支付";
                break;
            // 购买域名退款
            case 4:
                // 资金订单表
                typeState = "已退款";
                break;
            // 划转
            case 5:
                typeState = "划转成功";
                break;
            // 下级注册推广返佣
            case 6:
                typeState = "已返佣";
                break;
            // 自己注册域名返佣
            case 7:
                typeState = "已返佣";
                break;
            // 推广大使升级消耗
            case 8:
                typeState = "已支付";
                break;
            // 申请经销商消耗
            case 9:
                typeState = "已支付";
                break;
            // 实名DID及个人身份信息凭证申领（消耗）
            case 10:
                // 资金订单表
                RechargeWithdrawRecord rechargeWithdrawRecord10 =
                    rechargeWithdrawRecordMapper.selectById(rechargeFlow.getOrderId());
                Integer state10 = rechargeWithdrawRecord10.getState();
                Integer refundState10 = rechargeWithdrawRecord10.getRefundState();
                if (state10 == 3) {
                    if (refundState10 == 5) {
                        typeState = "已支付";
                    } else if (refundState10 == 1) {
                        typeState = "已支付(包含退款)";
                    }
                }
                break;
            // 实名DID及个人身份信息凭证申领（退款）
            case 11:
                typeState = "已退款";
                break;
            // 信息凭证申领（消耗）
            case 12:
                // 资金订单表
                RechargeWithdrawRecord rechargeWithdrawRecord12 =
                    rechargeWithdrawRecordMapper.selectById(rechargeFlow.getOrderId());
                Integer state12 = rechargeWithdrawRecord12.getState();
                Integer refundState12 = rechargeWithdrawRecord12.getRefundState();
                if (state12 == 3) {
                    if (refundState12 == 5) {
                        typeState = "已支付";
                    } else if (refundState12 == 1) {
                        typeState = "已支付(包含退款)";
                    }
                }
                break;
            // 信息凭证申领（退款）
            case 13:
                typeState = "已退款";
                break;
            // DID申领
            case 14:
                // 资金订单表
                RechargeWithdrawRecord rechargeWithdrawRecord14 =
                    rechargeWithdrawRecordMapper.selectById(rechargeFlow.getOrderId());
                Integer state14 = rechargeWithdrawRecord14.getState();
                Integer refundState14 = rechargeWithdrawRecord14.getRefundState();
                if (state14 == 3) {
                    if (refundState14 == 5) {
                        typeState = "支付成功";
                    } else if (refundState14 == 1) {
                        typeState = "已支付(包含退款)";
                    }
                }
                break;
            // DID申领退款
            case 15:
                typeState = "已退款";
                break;
            // 恢复私钥
            case 16:
                // 资金订单表
                RechargeWithdrawRecord rechargeWithdrawRecord16 =
                    rechargeWithdrawRecordMapper.selectById(rechargeFlow.getOrderId());
                Integer state16 = rechargeWithdrawRecord16.getState();
                Integer refundState16 = rechargeWithdrawRecord16.getRefundState();
                if (state16 == 3) {
                    if (refundState16 == 5) {
                        typeState = "已支付";
                    } else if (refundState16 == 1) {
                        typeState = "已支付(包含退款)";
                    }
                }
                break;
            // 恢复私钥（退款）
            case 17:
                typeState = "已退款";
                break;
            // 域名转让
            case 18:
                typeState = "已支付";
                break;
            // 划转
            case 19:
                typeState = "划转成功";
                break;
            // // 市场购买域名退款
            // case 20:
            //
            // typeState = "已退款";
            // break;
            // 一口价支出
            case 21:
                typeState = "已支付";
                break;
            // 一口价收入
            case 22:
                typeState = "已到账";
                break;
            // 竞价支出
            case 23:
                typeState = "已支付";
                break;
            // 竞价退款
            case 24:
                typeState = "已退款";
                break;
            // 竞价收入
            case 25:
                typeState = "已到账";
                break;
            // 能量充值
            case 26:
                typeState = "已支付";
                break;

            // 影票下单支出
            case 27:
                typeState = "已支付";
                break;

            // 影票已结算扣除冻结
            case 28:
                typeState = "出票成功(扣款)";
                break;

            // 影票订单关闭返还资产
            case 29:
                typeState = "出票失败(退款)";
                break;

            // 域名续费（消耗）
            case 30:
                typeState = "已支付";
                OrderDomainRenewalInfo orderDomainRenewalInfo =
                    orderDomainRenewalInfoMapper.selectById(rechargeFlow.getOrderId());
                OrderDomainRenewal orderDomainRenewal =
                    orderDomainRenewalMapper.selectById(orderDomainRenewalInfo.getOrderId());
                Integer payState = orderDomainRenewal.getPayState();
                Integer refundState = orderDomainRenewal.getRefundState();
                if (payState == 1) {
                    typeState = "待支付";
                } else if (payState == 3) {
                    typeState = "支付失败";
                } else if (payState == 4) {
                    typeState = "支付超时";
                }
                if (refundState == 2) {
                    typeState = "已支付(部分退款)";
                } else if (refundState == 3) {
                    typeState = "已支付(全部退款)";
                }
                break;

            // 域名续费（退款）
            case 31:
                typeState = "已退款";
                break;

            // 域名续费（下级返佣）
            case 32:
                typeState = "已返佣";
                break;

            // 域名续费（自身返佣）
            case 33:
                typeState = "已返佣";
                break;
            case 34:
            case 35:
            case 36:
            case 37:
            case 38:
            case 39:
            case 40:
            case 41:
                // did返佣关联表
//                DidRebate didRebate41 = didRebateMapper.selectOne(Wrappers.<DidRebate>lambdaQuery()
//                    // .eq(DidRebate::getType, 1)
//                    .eq(DidRebate::getOrderId, rechargeFlow.getOrderId()));
//                Integer idRebate = didRebate41.getRebateState();
//                if (idRebate == 1) {
//                    typeState = "代发放";
//                } else if (idRebate == 2) {
//                    typeState = "发放中";
//                } else if (idRebate == 3) {
//                    typeState = "已发放";
//                } else if (idRebate == 3) {
//                    typeState = "订单失效（不返佣）";
//                }
                typeState = "已发放";
                break;
            case 42:
                typeState = "已支付";
                break;
            case 43:
                typeState = "已退款";
                break;
            case 44:
                typeState = "退款成功";
                break;
            case 57:
                typeState = "已支付";
                break;
            case 58:
                typeState = "已退款";
                break;
            case 59:
                typeState = "已返佣";
                break;
        }
        return typeState;
    }

    private String payType(Integer typeId) {
        String payType = "";
        switch (typeId) {
            case 1:
                payType = "微信";
                break;
            case 2:
                payType = "支付宝";
                break;
            case 3:
                payType = "余额";
                break;
            case 4:
                payType = "银行卡";
                break;
            case 5:
                payType = "pc聚合支付";
                break;
            case 6:
                payType = "抖音支付";
                break;

            case 7:
                payType = "源力值支付";
                break;

            case 8:
                payType = "NFT支付";
                break;

        }
        return payType;

    }

    /**
     * 获取type类型
     * 
     * @param typeId typeId
     * @return {@link String }
     * <AUTHOR>
     * @date 2024/04/12
     */
    private String getTranTypeName(Integer typeId) {
        TransactionType transactionType = transactionTypeMapper
            .selectOne(Wrappers.<TransactionType>lambdaQuery().eq(TransactionType::getTypeId, typeId));
        return transactionType.getType();
    }

    /**
     * 金额符号转换
     * 
     * @param amount
     * @param bigType 大类型：1-支出2-收入
     * @return {@link BigDecimal }
     * <AUTHOR>
     * @date 2024/05/15
     */
    private Object amountSymbol(BigDecimal amount, Integer typeId) {
        TransactionType transactionType = transactionTypeMapper
            .selectOne(Wrappers.<TransactionType>lambdaQuery().eq(TransactionType::getTypeId, typeId));
        Integer bigType = transactionType.getBigType();
        // 支出
        if (bigType == 1) {
            return "-" + amount.toString();
            // 收入
        } else if (bigType == 2) {
            return "+" + amount.toString();
        }
        return amount;
    }

    private BigDecimal decimalConversion(BigDecimal amount) {
        DecimalFormat decimalFormat = new DecimalFormat("#.##");
        return new BigDecimal(decimalFormat.format(amount));
    }

    @Async("ljThreadPool")
    public void dingdingMessagePush(String operateUUID, String realName, BigDecimal amount) {
        // 获取经销商信息
        Operate operate =
            operateMapper.selectOne(Wrappers.<Operate>lambdaQuery().eq(Operate::getAccountUuid, operateUUID));
        try {
            DingTalkPushUtil2.sendTextMsg(
                "实名DID：" + operate.getNickName() + "，用户：" + realName + "，提现：" + amount + "元，提现时间："
                    + DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN) + "，提现状态：提现中，审核状态：待审核",
                Lists.newArrayList(), Lists.newLinkedList(), false);
        } catch (Exception e) {
            log.error("提现钉钉推送消息异常");
            e.printStackTrace();
        }
    }

}
