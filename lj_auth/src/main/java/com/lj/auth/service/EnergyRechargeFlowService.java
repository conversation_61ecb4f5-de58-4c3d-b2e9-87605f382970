package com.lj.auth.service;

import com.lj.auth.common.R;
import com.lj.auth.domain.EnergyRechargeFlow;
import com.baomidou.mybatisplus.extension.service.IService;

import java.math.BigDecimal;
import java.util.Date;
import java.util.concurrent.Future;

public interface EnergyRechargeFlowService extends IService<EnergyRechargeFlow> {

    /**
     * 自研链能量充值
     * 
     * @param amount 金额
     * @param payPassword 支付密码
     * @param address 链地址
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/02
     */
    R addAddressEnergyService(BigDecimal amount, String payPassword, String address);

    /**
     * 主币能量充值
     *
     * @param key key
     * @param toAddress 接收地址
     * @param amount 数量
     * @param type 平台类型
     * @param data 额外数据
     * @return {@link String }
     * <AUTHOR>
     * @date 2024/04/02
     */
    String transactionMainCurrency(String key, String toAddress, BigDecimal amount, Integer type,
        String data);

    /**
     * 能量补充
     * 
     * @param key
     * @param toAddress
     * @param amount
     * @param type
     * @param data
     * @return {@link String }
     * <AUTHOR>
     * @date 2024/04/24
     */
    String transactionMainCurrency1(String key, String toAddress, BigDecimal amount, Integer type,
        String data);

    /**
     * 主节点补发
     * @param key
     * @param toAddress
     * @param amount
     * @param type
     * @param data
     * @return {@link String }
     * <AUTHOR>
     * @date 2024/12/17
     */
    String transactionMainCurrency2(String key, String toAddress, BigDecimal amount, Integer type,
                                    String data);

    /**
     * 获取云链能量定价
     * 
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/02
     */
    R getYlGasQuantityService();

    /**
     * 获取个人能量
     * 
     * @param address
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/02
     */
    R getPersonalEnergyService(String address);

    /**
     * 能量明细（充值）
     * 
     * @param page 页
     * @param pageSize 页大小
     * @param type 交易类型：1=充值2=消耗
     * @param date 日期
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/02
     */
    R queryEnergyDetailService(int page, int pageSize, Integer type, Date date);

    /**
     * 更具hash获取交易详情
     * 
     * @param hash 哈希
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/02
     */
    R queryHashDetailService(String hash);

    /**
     * 域名解析地址
     * 
     * @param domain 域名
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/02
     */
    R addressResolutionService(String domain);

    /**
     * 能量二维码接收背景
     * 
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/02
     */
    R energyQRCodeBackgroundService();

    /**
     * 能量明细（链上交易）
     *
     * @param page 页
     * @param pageSize 页大小
     * @param address 地址
     * @param type 0-全部 -1转入 2-转出
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/02
     */
    R getWalletAddressAllOfPageService(int page, int pageSize, String address, int type);

    /**
     * 获取转让服务费
     * 
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/07
     */
    R getServiceChargeService();

    /**
     * 钱转换sp
     * 
     * @param amount
     * @return {@link String }
     * <AUTHOR>
     * @date 2024/04/24
     */
    public String moneyToGas1(BigDecimal amount);

    /**
     * 源力 概览
     * 
     * @param address 链地址
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/06/04
     */
    R getSpOverviewService(String address);

    R getSpOverviewService1(String address);

    /**
     * sp单价趋势
     * 
     * @param type 1-近一个月 2-近三个月
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/06/04
     */
    R getSpUnitPriceTrend(int type);

    /**
     * 获取流通市值
     * 
     * @return {@link BigDecimal }
     * <AUTHOR>
     * @date 2024/07/10
     */
    BigDecimal obtainCirculatingMarketValue();

    /**
     * SP单价趋势
     *
     * @param type 1-日 2-周 3-月
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/06/04
     */
    R spUnitPriceKLineService(int type, int page, int pageSize);
}
