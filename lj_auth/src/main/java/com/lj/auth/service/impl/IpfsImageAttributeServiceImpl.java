package com.lj.auth.service.impl;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lj.auth.mapper.IpfsImageAttributeMapper;
import com.lj.auth.domain.IpfsImageAttribute;
import com.lj.auth.service.IpfsImageAttributeService;

/**
 * <AUTHOR>
 * @Description
 * @date 2024/4/27 15:17
 */
@Service
public class IpfsImageAttributeServiceImpl extends ServiceImpl<IpfsImageAttributeMapper, IpfsImageAttribute> implements IpfsImageAttributeService {
    
}




