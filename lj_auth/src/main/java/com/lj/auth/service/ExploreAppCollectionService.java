package com.lj.auth.service;

import com.lj.auth.common.R;
import com.lj.auth.domain.ExploreAppCollection;
import com.baomidou.mybatisplus.extension.service.IService;

public interface ExploreAppCollectionService extends IService<ExploreAppCollection> {

    /**
     * 收藏
     * 
     * @param appId 应用appId
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/01
     */
    R addCollectionService(Integer appId);

    /**
     * 取消收藏
     * 
     * @param id
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/01
     */
    R unAddCollectionService(Integer id);

    /**
     * 获取收藏列表
     * 
     * @param page 页
     * @param pageSize 页大小
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/01
     */
    R getCollectionListService(int page, int pageSize);
}
