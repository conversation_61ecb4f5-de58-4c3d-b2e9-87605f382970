package com.lj.auth.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.HexUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lj.auth.common.R;
import com.lj.auth.domain.*;
import com.lj.auth.domain.resp.ValidateApproveResp;
import com.lj.auth.exception.ServiceException;
import com.lj.auth.mapper.*;
import com.lj.auth.service.GlobalConfigService;
import com.lj.auth.service.NftGetRecordService;
import com.lj.auth.service.NftOnChainService;
import com.lj.auth.task.CheckSpOverview;
import com.lj.auth.util.BNBUtil;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.web3j.abi.TypeReference;
import org.web3j.abi.datatypes.*;
import org.web3j.abi.datatypes.generated.Uint256;
import org.web3j.protocol.core.methods.response.*;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.util.*;
import java.util.concurrent.TimeUnit;
import static com.lj.auth.domain.PlatformAccount.STATUS_SHOW;

@Slf4j
@Service
public class NftOnChainServiceImpl extends ServiceImpl<NftMapper, Nft> implements NftOnChainService {
    private static final BigInteger GAS_LIMIT = new BigInteger("3000000");

    @Resource
    private NftMapper nftMapper;
    @Resource
    private ContractMapper contractMapper;
    @Resource
    private NftTransferRecordMapper nftTransferRecordMapper;

    @Resource
    private PlatformAccountMapper platformAccountMapper;

    @Resource
    private OrderDomainRenewalNftMapper orderDomainRenewalNftMapper;
    @Resource
    private OrderDomainRenewalNftRefundMapper orderDomainRenewalNftRefundMapper;


    /**
     * 是否授权所有
     * @param ownerAddress              NFT拥有者地址
     * @param contractAddress           NFT合约地址
     * @param approvedAddress           授权地址
     * @return
     * @throws Exception
     */
    @Override
    public Boolean isApprovedForAll (String ownerAddress, String contractAddress, String approvedAddress){
        Boolean isApprovedForAll = null;
        try {
            String functionName = "isApprovedForAll";
            List<Type> inputParameters = new ArrayList<>();
            inputParameters.add(new Address(ownerAddress));
            inputParameters.add(new Address(approvedAddress));
            List<TypeReference<?>> outputParameters = new ArrayList<>();
            outputParameters.add(new TypeReference<Uint256>() {
            });
            Function function = new Function(functionName, inputParameters, outputParameters);
            List<Type> types = BNBUtil.sendCallTransaction(approvedAddress, contractAddress, function);
            BigInteger result = (BigInteger) types.get(0).getValue();
             isApprovedForAll = BooleanUtil.toBoolean(result.toString());
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException("获取授权地址失败");
        }
        return isApprovedForAll;
    }



    @Override
    public void transferNFTProcessV2(Long domainRenewalNFTId , String recycleAddress) {
        OrderDomainRenewalNft orderDomainRenewalNft = orderDomainRenewalNftMapper.selectById(domainRenewalNFTId);
        Assert.notNull(orderDomainRenewalNft, "NFT续费记录不存在");
        orderDomainRenewalNft.setHash("233");
        orderDomainRenewalNftMapper.updateById(orderDomainRenewalNft);
    }



    /**
     * 上链处理
     * @param domainRenewalNFTId
     * @param recycleAddress
     */
    @Override
    @Transactional
    public void transferNFTProcess(Long domainRenewalNFTId , String recycleAddress) {
        OrderDomainRenewalNft orderDomainRenewalNft = orderDomainRenewalNftMapper.selectById(domainRenewalNFTId);
        Assert.notNull(orderDomainRenewalNft, "NFT续费记录不存在");
        //授权接收人地址
        String accreditToAddress = orderDomainRenewalNft.getAccreditToAddress();
        //合约地址
        String contractAddress = orderDomainRenewalNft.getContractAddress();
        //tokenId
        Integer tokenId = orderDomainRenewalNft.getToken();
        //交易状态 1-待交易 2-交易中(已提交上链) 3-交易完成/成功 4-交易失败
        Integer tranState = orderDomainRenewalNft.getTranState();

        Assert.equals(tranState, OrderDomainRenewalNft.TRAN_STATE_PROCESSING, "NFT续费记录状态异常");
        Nft nft = nftMapper.queryByTokenId(contractAddress, tokenId, Nft.NFT_STATUS_NORMAL, false);
        Assert.notNull(nft,"NFT不存在");
        String approveAddress = nft.getApproveAddress();
        Assert.isTrue(nft.getIsApprove(),"NFT未授权");
        Assert.notBlank(approveAddress,"NFT未授权成功");
        Assert.equals(approveAddress,accreditToAddress,"授权地址异常");

        //tokenId
        //NFT拥有者地址
        String nftOwnerAddress = nft.getHolder();
        //转让NFT 给回收地址
        TransactionReceipt  transactionReceipt = transferNFT(orderDomainRenewalNft,accreditToAddress, contractAddress, nftOwnerAddress, recycleAddress, tokenId);

        String transactionHash = transactionReceipt.getTransactionHash();
        //同步域名续费NFT 相关信息
        orderDomainRenewalNft.setFromAddress(nftOwnerAddress);
        orderDomainRenewalNft.setToAddress(recycleAddress);
        orderDomainRenewalNft.setHash(transactionHash);
        orderDomainRenewalNft.setSponsorTransferAddress(accreditToAddress);
        //修改状态为处理完成
        orderDomainRenewalNft.setTranState(OrderDomainRenewalNft.TRAN_STATE_SUCCESS);
        orderDomainRenewalNft.setUpdateTime(new Date());
        orderDomainRenewalNftMapper.updateById(orderDomainRenewalNft);




        //同步NFT的拥有者信息
        nft.setHolder(recycleAddress);
        nft.setIsApprove(false);
        nft.setApproveAddress("0");
        nft.setUpdateTime(new Date());
        nftMapper.updateById(nft);

        //保存NFT转让记录
        saveTransferRecord(transactionReceipt);
    }


    /**
     * NFT退款上链处理
     * @param orderDomainRenewalNftRefundId
     */
    @Override
    public void refundNFTProcess(Long orderDomainRenewalNftRefundId) {
        OrderDomainRenewalNftRefund orderDomainRenewalNftRefund = orderDomainRenewalNftRefundMapper.selectById(orderDomainRenewalNftRefundId);
        Assert.notNull(orderDomainRenewalNftRefund,"NFT退款信息不存在");

        Integer tranState = orderDomainRenewalNftRefund.getTranState();
        Assert.equals(tranState, OrderDomainRenewalNftRefund.TRAN_STATE_PROCESSING, "NFT退款状态异常");

        //NFT续费信息
        Long orderNftId = orderDomainRenewalNftRefund.getOrderNftId();
        OrderDomainRenewalNft orderDomainRenewalNft = orderDomainRenewalNftMapper.selectById(orderNftId);
        Assert.notNull(orderDomainRenewalNft, "NFT续费信息不存在");
        Integer NFTRenewalStatus = orderDomainRenewalNft.getTranState();
        Assert.equals(NFTRenewalStatus, OrderDomainRenewalNft.TRAN_STATE_SUCCESS, "NFT续费状态异常");
        //NFT 回收地址
        String recycleAddress = orderDomainRenewalNft.getToAddress().toLowerCase();
        //NFT 续费初始拥有者地址
        String NFTInitAddress = orderDomainRenewalNft.getFromAddress().toLowerCase();
        //合约地址
        String contractAddress = orderDomainRenewalNft.getContractAddress();
        Integer tokenId = orderDomainRenewalNft.getToken();

        String nftChainOwnerAddress = BNBUtil.getNFTOwner(recycleAddress, contractAddress, tokenId);
        Assert.equals(nftChainOwnerAddress, recycleAddress, "NFT拥有者地址信息异常,tokenId:{},nftChainOwnerAddress:{},recycleAddress:{}",tokenId,nftChainOwnerAddress,recycleAddress);

        Nft nft = nftMapper.queryByTokenId(contractAddress, tokenId, Nft.NFT_STATUS_NORMAL, false);
        Assert.notNull(nft,"NFT不存在");
        //NFT 退款 回收地址转回给续费NFT地址
        TransactionReceipt  transactionReceipt = refundNFT(orderDomainRenewalNftRefund,recycleAddress, contractAddress, NFTInitAddress, tokenId);

        String transactionHash = transactionReceipt.getTransactionHash();
        //同步NFT 退款 相关信息
        orderDomainRenewalNftRefund.setFromAddress(recycleAddress);
        orderDomainRenewalNftRefund.setToAddress(NFTInitAddress);
        orderDomainRenewalNftRefund.setHash(transactionHash);
        //修改状态为处理完成
        orderDomainRenewalNftRefund.setTranState(OrderDomainRenewalNft.TRAN_STATE_SUCCESS);
        orderDomainRenewalNftRefund.setUpdateTime(new Date());
        orderDomainRenewalNftRefundMapper.updateById(orderDomainRenewalNftRefund);

        //同步NFT的拥有者信息
        nft.setHolder(NFTInitAddress);
        nft.setUpdateTime(new Date());
        nftMapper.updateById(nft);

        //保存NFT退款记录
        saveTransferRecord(transactionReceipt);

    }


    /**
     * 转让NFT 到平台回收地址
     * @param platformAccountAddress
     * @param contractAddress
     * @param NFTOwnerAddress
     * @param NFTRecycleAddress
     * @param tokenId
     * @return
     */

    public TransactionReceipt transferNFT(OrderDomainRenewalNft orderDomainRenewalNft,String platformAccountAddress, String contractAddress, String NFTOwnerAddress, String NFTRecycleAddress, Integer tokenId) {
        TransactionReceipt transactionReceipt = null;
        try {
            String functionName = "transferFrom";
            List<Type> inputParameters = new ArrayList<>();
            inputParameters.add(new Address(NFTOwnerAddress));
            inputParameters.add(new Address(NFTRecycleAddress));
            inputParameters.add(new Uint256(BigInteger.valueOf(tokenId)));
            List<TypeReference<?>> outputParameters = new ArrayList<>();


            PlatformAccount platformAccount = platformAccountMapper.queryByAddressAndStatus(platformAccountAddress, PlatformAccount.TYPE_DOMAIN_REGISTER_RESOLVE_ACCOUNT, STATUS_SHOW);
            String platformAccountPrivateKey = platformAccount.getPrivateKey();
            Function function = new Function(functionName, inputParameters, outputParameters);
            String transactionHash = BNBUtil.sendTransaction(contractAddress, platformAccountPrivateKey, function, GAS_LIMIT);
            //判断交易状态
            if (StrUtil.isNotEmpty(transactionHash)) {
                //判断交易状态
                transactionReceipt = getTransactionReceipt(transactionHash);
                if (!transactionReceipt.isStatusOK()) {
                    log.error("NFT回收交易回执失败 contractAddress:{},NFTOwnerAddress:{},tokenId:{},NFTRecycleAddress:{},transactionHash:{}", contractAddress, NFTOwnerAddress,NFTRecycleAddress, tokenId,transactionHash);
                    if(ObjectUtil.isNotNull(orderDomainRenewalNft)) {
                        orderDomainRenewalNft.setHash(transactionHash);
                        //修改状态为处理完成
                        orderDomainRenewalNft.setTranState(OrderDomainRenewalNft.TRAN_STATE_FAIL);
                        orderDomainRenewalNft.setUpdateTime(new Date());
                        orderDomainRenewalNftMapper.updateById(orderDomainRenewalNft);
                    }
                    throw new ServiceException("hash: " + transactionHash + "NFT回收失败");
                } else {
                    log.warn("NFT回收成功 contractAddress:{},NFTOwnerAddress:{},tokenId:{},transactionHash:{}", contractAddress, NFTOwnerAddress, JSON.toJSONString(tokenId),transactionHash);
                }
            } else {
                throw new ServiceException("contractAddress " + contractAddress + " NFTOwnerAddress " + NFTOwnerAddress + "tokenId"+tokenId+" NFT回收失败");
            }

        } catch (Exception ex) {
            throw new RuntimeException(ex);
        }
        return transactionReceipt;
    }



    /**
     * NFT 从回收地址转到用户地址
     * @param orderDomainRenewalNftRefund
     * @param NFTRecycleAddress
     * @param contractAddress
     * @param NFTOwnerAddress
     * @param tokenId
     * @return
     */

    public TransactionReceipt refundNFT(OrderDomainRenewalNftRefund orderDomainRenewalNftRefund,String NFTRecycleAddress, String contractAddress, String NFTOwnerAddress, Integer tokenId) {
        TransactionReceipt transactionReceipt = null;
        try {
            String functionName = "transferFrom";
            List<Type> inputParameters = new ArrayList<>();
            inputParameters.add(new Address(NFTRecycleAddress));
            inputParameters.add(new Address(NFTOwnerAddress));
            inputParameters.add(new Uint256(BigInteger.valueOf(tokenId)));
            List<TypeReference<?>> outputParameters = new ArrayList<>();


            PlatformAccount platformAccount = platformAccountMapper.queryByAddressAndStatus(NFTRecycleAddress, PlatformAccount.TYPE_NFT_RECYCLE_ACCOUNT, STATUS_SHOW);
            String platformAccountPrivateKey = platformAccount.getPrivateKey();
            Function function = new Function(functionName, inputParameters, outputParameters);
            String transactionHash = BNBUtil.sendTransaction(contractAddress, platformAccountPrivateKey, function, GAS_LIMIT);
            //判断交易状态
            if (StrUtil.isNotEmpty(transactionHash)) {
                //判断交易状态
                transactionReceipt = getTransactionReceipt(transactionHash);
                if (!transactionReceipt.isStatusOK()) {
                    log.error("NFT 退款失败 contractAddress:{},NFTOwnerAddress:{},tokenId:{},NFTRecycleAddress:{},transactionHash:{}", contractAddress, NFTOwnerAddress,NFTRecycleAddress, tokenId,transactionHash);
                    if(ObjectUtil.isNotNull(orderDomainRenewalNftRefund)) {
                        orderDomainRenewalNftRefund.setHash(transactionHash);
                        //修改状态为处理完成
                        orderDomainRenewalNftRefund.setTranState(OrderDomainRenewalNft.TRAN_STATE_FAIL);
                        orderDomainRenewalNftRefund.setUpdateTime(new Date());
                        orderDomainRenewalNftRefundMapper.updateById(orderDomainRenewalNftRefund);
                    }
                    throw new ServiceException("hash: " + transactionHash + "NFT回收失败");
                } else {
                    log.warn("NFT 退款 成功 contractAddress:{},NFTOwnerAddress:{},tokenId:{},transactionHash:{}", contractAddress, NFTOwnerAddress, JSON.toJSONString(tokenId),transactionHash);
                }
            } else {
                throw new ServiceException("contractAddress " + contractAddress + " NFTOwnerAddress " + NFTOwnerAddress + "tokenId"+tokenId+" NFT 退款失败");
            }

        } catch (Exception ex) {
            throw new RuntimeException(ex);
        }
        return transactionReceipt;
    }

    @Override
    public R queryTransactionReceipt(String transactionHash) {
        TransactionReceipt transactionReceipt = getTransactionReceipt(transactionHash);
        boolean statusOK = transactionReceipt.isStatusOK();
        Map result=new HashMap();
        result.put("transactionReceipt",transactionReceipt);
        result.put("交易状态",statusOK);
        return R.okData(result);
    }

    /**
     * 授权当前地址
     * @param  privateKey      初始地址私钥
     * @param  contractAddress 合约地址
     * @param  approvalAddress 授权地址
     * @param  approved         true 授权 false撤销授权
     * @return
     */
    @Override
    public TransactionReceipt setApprovalForAll(String privateKey, String contractAddress,String approvalAddress,Boolean approved) {
        TransactionReceipt transactionReceipt = null;
        try {
            String functionName = "setApprovalForAll";
            List<Type> inputParameters = new ArrayList<>();
            inputParameters.add(new Address(approvalAddress));
            inputParameters.add(new Bool(approved));
            List<TypeReference<?>> outputParameters = new ArrayList<>();
            outputParameters.add(new TypeReference<Uint256>() {
            });
            Function function = new Function(functionName, inputParameters, outputParameters);
            String transactionHash = BNBUtil.sendTransaction(contractAddress, privateKey, function, GAS_LIMIT);
            //判断交易状态
            if (StrUtil.isNotEmpty(transactionHash)) {
                //判断交易状态
                transactionReceipt = getTransactionReceipt(transactionHash);
                if (!transactionReceipt.isStatusOK()) {
                    throw new ServiceException("hash: " + transactionHash + "NFT授权失败");
                } else {
                    log.warn("NFT授权成功 contractAddress:{},approvalAddress:{},approved:{}", contractAddress, approvalAddress,approved);
                }
            } else {
                throw new ServiceException("contractAddress " + contractAddress + " approvalAddress " + approvalAddress + " NFT授权成功");
            }

        } catch (Exception ex) {
            throw new RuntimeException(ex);
        }
        return transactionReceipt;
    }

    /**
     * 添加交易记录
     * @param transactionReceipt
     * @return
     */
    public R saveTransferRecord(TransactionReceipt transactionReceipt) {
            try {
                String transactionHash = transactionReceipt.getTransactionHash();
                BigInteger gasUsed = transactionReceipt.getGasUsed();
                List<Log> logs = transactionReceipt.getLogs();
                Log log = logs.get(0);
                List<String> topics = log.getTopics();
                String fromAddress = "0x" + topics.get(1).substring(2).replaceAll("^(0+)","");
                String toAddress = "0x" + topics.get(2).substring(2).replaceAll("^(0+)","");
                Integer tokenId = HexUtil.hexToInt(topics.get(3).substring(2).replaceAll("^(0+)",""));
                BigInteger blockNumber = transactionReceipt.getBlockNumber();
                String contractAddress = transactionReceipt.getTo();
                //保存到数据库
                EthTransaction ethTransaction = BNBUtil.ethGetTransactionByHash(transactionHash);
                Optional<Transaction> transaction = ethTransaction.getTransaction();
                Transaction transactionInfo = transaction.get();
                BigInteger gasPrice = transactionInfo.getGasPrice();
                BigInteger value = transactionInfo.getValue();
                BigInteger energyUsed = gasUsed.multiply(gasPrice).add(value);


                //nft交易记录表新增数据
                //根据合约地址查询合约信息
                LambdaQueryWrapper<Contract> queryWrapper1 = new LambdaQueryWrapper<>();
                queryWrapper1.eq(Contract::getContractAddress, contractAddress);
                Contract contract = contractMapper.selectOne(queryWrapper1);
                Long contractId = contract.getId();
                NftTransferRecord nftTransferRecord = NftTransferRecord.builder()
                        .opbChainId(contract.getOpbChainId())
                        .contractId(contractId)
                        .tokenId(tokenId)
                        .contractAddress(contractAddress)
                        .fromAddress(fromAddress)
                        .toAddress(toAddress)
                        .energyValue(energyUsed.toString())
                        .transactionHash(transactionHash)
                        .blockNumber(blockNumber.intValue())
                        //交易类型  1-铸币 2-转账  3-销毁  4-单个token授权  5-批量授权
                        .transactionType(3)
                        //0-交易失败   1-交易成功
                        .status(1)
                        .build();
                nftTransferRecordMapper.insert(nftTransferRecord);
            } catch (Exception e) {
                e.printStackTrace();
                log.error("保存交易记录失败");
            }
            return R.ok();
    }


    /**
     * 交易hash查询交易回执
     * @param transactionHash
     * @return
     */
    public TransactionReceipt getTransactionReceipt (String transactionHash){
        try {
            EthGetTransactionReceipt ethGetTransactionReceipt = BNBUtil.ethGetTransactionReceipt(transactionHash);
            Optional<TransactionReceipt> optional = ethGetTransactionReceipt.getTransactionReceipt();
            int count = 0;
            if (!optional.isPresent()) {
                while (count < 20) {
                    count++;
                    //等待1秒
                    log.info("等待" + count + "秒");
                    TimeUnit.SECONDS.sleep(1);
                    ethGetTransactionReceipt = BNBUtil.ethGetTransactionReceipt(transactionHash);
                    optional = ethGetTransactionReceipt.getTransactionReceipt();
                    if (optional.isPresent()) {
                        return optional.get();
                    }
                }
                throw new RuntimeException(transactionHash + " 你太慢了");
            }
            return optional.get();
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException("获取交易回执失败");
        }
    }


}
