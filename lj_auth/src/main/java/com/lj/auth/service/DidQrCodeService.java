package com.lj.auth.service;

import com.alibaba.fastjson.JSONObject;
import com.lj.auth.common.R;
import com.lj.auth.domain.DidQrCode;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

public interface DidQrCodeService extends IService<DidQrCode> {

    /**
     * 获取DID信息二维码
     * 
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/07/16
     */
    R getQrCodeService(String appId);

    /**
     * 通过二维码信息获取DID
     *
     * @param data
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/07/16
     */
    R getDIDByQrService(HttpServletRequest request, Map<String, Object> data);
}
