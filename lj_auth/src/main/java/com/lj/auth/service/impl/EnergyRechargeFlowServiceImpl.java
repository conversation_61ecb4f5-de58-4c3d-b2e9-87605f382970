package com.lj.auth.service.impl;

import static com.lj.auth.common.CommonConstant.*;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.*;

import javax.annotation.Resource;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.web3j.utils.Convert;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lj.auth.client.YlBesuChainClientCall;
import com.lj.auth.common.R;
import com.lj.auth.domain.*;
import com.lj.auth.domain.vo.EnergyAssetsVo;
import com.lj.auth.domain.vo.EnergyRechargeFlowVo;
import com.lj.auth.domain.vo.Sp24hoursVo;
import com.lj.auth.exception.ResponseEnum;
import com.lj.auth.mapper.*;
import com.lj.auth.service.*;
import com.lj.auth.util.BNBUtil;
import com.lj.auth.util.PageUtils;
import com.lj.auth.util.RedisUtils;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class EnergyRechargeFlowServiceImpl extends ServiceImpl<EnergyRechargeFlowMapper, EnergyRechargeFlow>
    implements EnergyRechargeFlowService {
    @Value("${readImagepath}")
    private String readImagepath;// 图片访问路径,存到数据库
    @Value("${ylChainId}")
    private Integer chainId;
    @Resource
    private AccountService accountService;
    @Resource
    private RechargeRecordService rechargeRecordService;
    @Resource
    private RechargeAssetsService rechargeAssetsService;
    @Resource
    private GlobalConfigService globalConfigService;
    @Resource
    private TransferNoticeService transferNoticeService;
    @Resource
    private PlatformAccountService platformAccountService;
    @Resource
    private EnergyRechargeFlowMapper energyRechargeFlowMapper;
    @Resource
    private AccountDomainAddressMapper accountDomainAddressMapper;
    @Resource
    private YlBesuChainClientCall ylBesuChainClientCall;
    @Resource
    private ChainMapper chainMapper;
    @Resource
    private MovieOrderMapper movieOrderMapper;
    @Resource
    private Sp24hoursMapper sp24hoursMapper;
    @Resource
    private RedisUtils redisUtils;
    @Resource
    private TaskCenterUserMapper taskCenterUserMapper;
    @Resource
    private ContributionMapper contributionMapper;
    @Resource
    private DayKLineMapper dayKLineMapper;
    @Resource
    private WeekKLineMapper weekKLineMapper;
    @Resource
    private MonthKLineMapper monthKLineMapper;

    /**
     * 自研链能量充值
     *
     * @param amount 金额
     * @param payPassword 支付密码
     * @param address 链地址
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/02
     */
    @Override
    @Transactional
    public R addAddressEnergyService(BigDecimal amount, String payPassword, String address) {
        Account userInfo = accountService.getUserInfo();
        // 校验支付密码
        BCryptPasswordEncoder encoder = new BCryptPasswordEncoder();
        // 校验密码 password
        if (!encoder.matches(payPassword, userInfo.getPayPassword())) {
            return R.error(ResponseEnum.PaymentPasswordError);
        }
        // 校验账户余额
        RechargeAssets rechargeAssets = rechargeAssetsService.checkAssets(amount);

        Map<String, Object> paramMap = new HashMap<>();
        Map<String, String> energyMap = new HashMap<>();

        long timeStamp = DateUtil.date().getTime();
        String reqTransactionSn = "ENE" + timeStamp;
        // 插入充值记录
        EnergyRechargeFlow energyRechargeFlow = new EnergyRechargeFlow();
        energyRechargeFlow.setAccountUuid(userInfo.getUuid()).setBusinessMoney(amount).setType(1)
            .setAddress(address).setRechargeState(1).setReqTransactionSn(reqTransactionSn);

        // 插入资产消耗记录 能量充值Type 改为26
        RechargeRecord rechargeRecord =
            rechargeRecordService.insertRecord(userInfo.getUuid(), amount, reqTransactionSn, 26);

        // 获取能量定价并转换
        String gas = moneyToGas1(amount);
        // 能量转换 ==> wei
        BigDecimal gas1 = (new BigDecimal(gas)).multiply(new BigDecimal(BigInteger.TEN.pow(18)));
        energyRechargeFlow.setGas(String.valueOf(gas1));
        this.save(energyRechargeFlow);
        log.info("充值记录插入完成");

        // 添加用户能量资产
        String hash = transactionMainCurrency(YL_ENERGY_RECHARGE, address, new BigDecimal(gas), 2,
            ENERGY_RECHARGE_HEX_REMARKS);
        // 修改记录
        if (StrUtil.isNotBlank(hash) && !(hash.equals("null"))) {
            this.update(null,
                Wrappers.<EnergyRechargeFlow>lambdaUpdate()
                    .eq(EnergyRechargeFlow::getReqTransactionSn, reqTransactionSn)
                    .set(EnergyRechargeFlow::getHash, hash).set(EnergyRechargeFlow::getRechargeState, 10));
            log.info("能量资产添加完成");
            // 修改消耗记录
            rechargeRecordService.updateRecord(reqTransactionSn, 1);
            // 扣除资产
            rechargeAssetsService.updateAssets(userInfo.getUuid(), amount);

            log.info("充值完成");
            // 能量 转账通知
            String content = "充值能量:" + gas + "到地址:" + address;
            transferNoticeService.addUserNoticeMessage(userInfo.getUuid(), "充值能量", content, "充值能量");
        } else {
            // 充值失败
            this.update(null,
                Wrappers.<EnergyRechargeFlow>lambdaUpdate()
                    .eq(EnergyRechargeFlow::getReqTransactionSn, reqTransactionSn)
                    .set(EnergyRechargeFlow::getRechargeState, 5));
        }
        return R.okData(rechargeRecord.getId());
    }

    /**
     * 主币能量充值
     *
     * @param key key
     * @param toAddress 接收地址
     * @param amount 数量
     * @param type 用处 1-领NFT 2-平台能量充值3-签到赠送 4-公司收款退款账户 5-域名注册解析管理账户
     * @param data 额外数据
     * @return {@link String }
     * <AUTHOR>
     * @date 2024/04/02
     */
    @Override
    public String transactionMainCurrency(String key, String toAddress, BigDecimal amount, Integer type,
        String data) {
        // 执行充值操作
        String result = BNBUtil.transaction(toAddress, platformAccountService.getYlAccountPrivate(key, type),
            amount, data);
        return result;
    }

    @Override
    public String transactionMainCurrency1(String key, String toAddress, BigDecimal amount, Integer type,
        String data) {
        String gasPriceData = globalConfigService.getGlobalConfig(GASPRICEGWI);
        // 从数据库动态获取
        BigInteger gasPrice =
            Convert.toWei(BigDecimal.valueOf(Long.valueOf(gasPriceData)), Convert.Unit.GWEI).toBigInteger();
        // 执行充值操作
        String result = BNBUtil.transaction1(toAddress, platformAccountService.getYlAccountPrivate(key, type),
            amount, gasPrice, data);
        return result;
    }

    /**
     * 主节点补发
     * @param key
     * @param toAddress
     * @param amount
     * @param type
     * @param data
     * @return {@link String }
     * <AUTHOR>
     * @date 2024/12/17
     */
    @Override
    public String transactionMainCurrency2(String key, String toAddress, BigDecimal amount, Integer type,
                                           String data) {
        String gasPriceData = globalConfigService.getGlobalConfig(GASPRICEGWI);
        // 从数据库动态获取
        BigInteger gasPrice =
                Convert.toWei(BigDecimal.valueOf(Long.valueOf(gasPriceData)), Convert.Unit.GWEI).toBigInteger();
        // 执行充值操作
        String result = BNBUtil.transaction1(toAddress, platformAccountService.getYlAccountPrivate(key, type),
                amount, gasPrice, data);
        return result;
    }

    /**
     * 获取云链能量定价
     *
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/02
     */
    @Override
    public R getYlGasQuantityService() {
        String globalConfig = globalConfigService.getGlobalConfig(YL_GASQUANTITY);
        return R.okData(globalConfig);
    }

    /**
     * 获取个人能量
     *
     * @param address
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/02
     */
    @Override
    public R getPersonalEnergyService(String address) {
        Account userInfo = accountService.getUserInfo();
        BigInteger balance = BNBUtil.getBalance(address);
        EnergyAssetsVo energyAssetsVo = new EnergyAssetsVo();
        energyAssetsVo.setAccountUuid(userInfo.getUuid());
        energyAssetsVo.setAddress(address);
        energyAssetsVo.setGas(String.valueOf(balance));
        return R.okData(energyAssetsVo);
    }

    /**
     * 能量明细（充值）
     *
     * @param page 页
     * @param pageSize 页大小
     * @param type 交易类型：1=充值2=消耗
     * @param date 日期
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/02
     */
    @Override
    public R queryEnergyDetailService(int page, int pageSize, Integer type, Date date) {
        Account userInfo = accountService.getUserInfo();
        Integer totalCount = energyRechargeFlowMapper.queryEnergyCount(userInfo.getUuid(), type, date);
        int start = (page - 1) * pageSize;
        List<EnergyRechargeFlow> energyRechargeFlowList =
            energyRechargeFlowMapper.getPage(start, pageSize, userInfo.getUuid(), type, date);

        List<EnergyRechargeFlowVo> energyRechargeFlowVoList = new ArrayList<>();
        // 封装充值信息
        for (EnergyRechargeFlow energyRechargeFlow : energyRechargeFlowList) {
            // 能量gas转换 gas=>sp 1sp = 10的18次方gas
            BigDecimal pow = new BigDecimal("10").pow(18);
            BigDecimal gas = new BigDecimal(energyRechargeFlow.getGas());
            BigDecimal sp = gas.divide(pow);
            energyRechargeFlow.setGas(sp + "sp");
            EnergyRechargeFlowVo energyRechargeFlowVo = new EnergyRechargeFlowVo();
            BeanUtils.copyProperties(energyRechargeFlow, energyRechargeFlowVo);
            // 获取充值信息
            rechargeMessageEncapsulation(energyRechargeFlowVo);
            energyRechargeFlowVoList.add(energyRechargeFlowVo);
        }
        PageUtils pageUtils = new PageUtils(totalCount, pageSize, page, energyRechargeFlowVoList);
        return R.okData(pageUtils);
    }

    /**
     * 更具hash获取交易详情
     *
     * @param hash 哈希
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/02
     */
    @Override
    public R queryHashDetailService(String hash) {
        return ylBesuChainClientCall.getHashDetail(hash);
    }

    /**
     * 域名解析地址
     *
     * @param domain
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/02
     */
    @Override
    public R addressResolutionService(String domain) {
        Account walletAccount = accountService.getUserInfo();
        AccountDomainAddress accountDomainAddress =
            accountDomainAddressMapper.selectOne(Wrappers.<AccountDomainAddress>lambdaQuery()
                .eq(AccountDomainAddress::getAccountUuid, walletAccount.getUuid())
                .eq(AccountDomainAddress::getDomain, domain).eq(AccountDomainAddress::getState, true)
                .eq(AccountDomainAddress::getOpbChainId, chainId));
        if (accountDomainAddress == null) {
            return R.error(ResponseEnum.ParsingFailed);
        }
        return R.okData(accountDomainAddress.getAddress());
    }

    /**
     * 能量二维码接收背景
     *
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/02
     */
    @Override
    public R energyQRCodeBackgroundService() {
        String globalConfig = globalConfigService.getGlobalConfig(ENERGY_QR_COD_EBACKGROUND);
        return R.ok(readImagepath + globalConfig);
    }

    /**
     * 能量明细（链上交易）
     *
     * @param page 页
     * @param pageSize 页大小
     * @param address 地址
     * @param type 0-全部 -1转入 2-转出
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/02
     */
    @Override
    public R getWalletAddressAllOfPageService(int page, int pageSize, String address, int type) {
        return ylBesuChainClientCall.getAddressAllOfPage(page, pageSize, address, type);
    }

    /**
     * 获取转让服务费
     *
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/07
     */
    @Override
    public R getServiceChargeService() {
        Map<String, Object> result = new HashMap<>();

        String globalConfig = globalConfigService.getGlobalConfig(DOMAIN_TRANSFER_SERVICE_FEE);
        // 获取能量费转让地址
        String ylAccount = platformAccountService.getYlAccountAddress(DOMAIN_TRANSFER_SERVICE_FEE_ADDRESS, 4);
        result.put(DOMAIN_TRANSFER_SERVICE_FEE, globalConfig);
        result.put(DOMAIN_TRANSFER_SERVICE_FEE_ADDRESS, ylAccount);
        return R.okData(result);
    }
    // =====工具方法===========================

    /**
     * 云链平台能量换算
     *
     * @param amount
     * @return {@link String}
     */
    @Override
    public String moneyToGas1(BigDecimal amount) {
        // 一元 定价
        String value = globalConfigService.getGlobalConfig(YL_GASQUANTITY);
        BigDecimal fixedPrice = new BigDecimal(value);
        BigDecimal gasResult = amount.multiply(fixedPrice);
        return gasResult.toString();
    }

    /**
     * 源力概览
     *
     * @param address -链地址
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/06/04
     */
    @Override
    public R getSpOverviewService(String address) {

        Map<String, Object> result = new HashMap<>();
        Account userInfo = accountService.getUserInfo();
        String key = "spOverview" + ":" + userInfo.getUuid();
        Object cacheResult = redisUtils.get(key);
        if (cacheResult == null) {
            // 总量 SP
            BigDecimal spTotal = new BigDecimal(globalConfigService.getGlobalConfig(YL_SP_Total));
            // 流通量SP
            BigDecimal circulationSp = circulationSp();
            // 剩余量 SP
            BigDecimal surplusSp = spTotal.subtract(circulationSp);
            // 时间
            Date time = new Date();
            BigDecimal circulatingMarketValue = obtainCirculatingMarketValue();

            // 单价
            BigDecimal unitPriceSp = circulatingMarketValue.divide(circulationSp, 6, RoundingMode.HALF_DOWN);

            BigDecimal unitPriceSp1 = circulatingMarketValue.divide(circulationSp, 18, RoundingMode.HALF_UP);
            // 获取昨天的单价
//            Sp24hours sp24hours = sp24hoursMapper.selectOne(Wrappers.<Sp24hours>lambdaQuery()
//                .eq(Sp24hours::getCreateTime, DateUtil.beginOfDay(DateUtil.yesterday())));
            List<Sp24hours> sp24hoursList = sp24hoursMapper.selectList((Wrappers.<Sp24hours>lambdaQuery()
                    .eq(Sp24hours::getCreateTime, DateUtil.beginOfDay(DateUtil.yesterday()))));
            String spPrice = sp24hoursList.get(0).getSpPrice();
            // // 涨跌幅
            String riseAndFallRange = riseAndFallRange(new BigDecimal(spPrice), unitPriceSp1);
            BigDecimal holdingSP = new BigDecimal("0");
            BigDecimal holdingSpMarketValue = new BigDecimal("0");
            if (StrUtil.isNotBlank(address)) {
                // 持有SP
                holdingSP = new BigDecimal(BNBUtil.getBalance(address))
                    .divide(new BigDecimal(BigInteger.TEN.pow(18)), 3, RoundingMode.HALF_UP);
                // 持有SP 市值
                holdingSpMarketValue = unitPriceSp.multiply(holdingSP);
            }
            result.put("spTotal", spTotal);
            result.put("circulationSp", circulationSp.setScale(3, RoundingMode.HALF_UP));
            result.put("surplusSp", surplusSp.setScale(3, RoundingMode.HALF_UP));
            result.put("time", DateUtil.format(time, DatePattern.NORM_DATETIME_PATTERN));
            result.put("circulatingMarketValue", circulatingMarketValue);
            result.put("unitPriceSp", unitPriceSp);
            result.put("riseAndFallRange", riseAndFallRange);
            result.put("holdingSP", holdingSP);
            result.put("holdingSpMarketValue", holdingSpMarketValue);
            redisUtils.set(key, result, 5);
        }
        return R.okData(redisUtils.get(key));
    }

    @Override
    public R getSpOverviewService1(String address) {

        Map<String, Object> result = new HashMap<>();
        Account userInfo = accountService.getUserInfo();
        String key = "spOverview" + ":" + userInfo.getUuid();
        Object cacheResult = redisUtils.get(key);
        if (cacheResult == null) {
            // 总量 SP
            BigDecimal spTotal = new BigDecimal(globalConfigService.getGlobalConfig(YL_SP_Total));
            // 流通量SP
            BigDecimal circulationSp = circulationSp();
            // 剩余量 SP
            BigDecimal surplusSp = spTotal.subtract(circulationSp);
            // 时间
            Date time = new Date();

            BigDecimal circulatingMarketValue = obtainCirculatingMarketValue();
            // 单价
            BigDecimal unitPriceSp = new BigDecimal("0");
            // 涨跌幅
            String riseAndFallRange = "0%";

            // 获取日 K 今天的记录
            DateTime createTime = DateUtil.beginOfDay(time);
            DayKLine dayKLine = dayKLineMapper
                .selectOne(Wrappers.<DayKLine>lambdaQuery().eq(DayKLine::getCreateTime, createTime));
            if (dayKLine != null) {
                // 单价
                unitPriceSp = dayKLine.getSpPrice().setScale(6, RoundingMode.HALF_DOWN);
                // 涨跌幅
                riseAndFallRange = dayKLine.getRiseAndFall();
            }
            BigDecimal holdingSP = new BigDecimal("0");
            BigDecimal holdingSpMarketValue = new BigDecimal("0");
            if (StrUtil.isNotBlank(address)) {
                // 持有SP
                holdingSP = new BigDecimal(BNBUtil.getBalance(address))
                    .divide(new BigDecimal(BigInteger.TEN.pow(18)), 3, RoundingMode.HALF_DOWN);
                // 持有SP 市值
                holdingSpMarketValue = unitPriceSp.multiply(holdingSP);
            }
            // 总量sp
            result.put("spTotal", spTotal);
            // 流通量sp
            result.put("circulationSp", circulationSp.setScale(3, RoundingMode.HALF_DOWN));
            // 剩余量sp
            result.put("surplusSp", surplusSp.setScale(3, RoundingMode.HALF_DOWN));
            // 时间
            result.put("time", DateUtil.format(time, DatePattern.NORM_DATETIME_PATTERN));
            // 流通市值
            result.put("circulatingMarketValue", circulatingMarketValue);
            // sp单价
            result.put("unitPriceSp", unitPriceSp);
            // 涨跌幅
            result.put("riseAndFallRange", riseAndFallRange);
            // 持有sp
            result.put("holdingSP", holdingSP);
            // 持有市值
            result.put("holdingSpMarketValue", holdingSpMarketValue);
            redisUtils.set(key, result, 5);
        }
        return R.okData(redisUtils.get(key));
    }

    /**
     * 获取流通市值
     * 
     * @return {@link BigDecimal }
     * <AUTHOR>
     * @date 2024/07/10
     */
    @Override
    public BigDecimal obtainCirculatingMarketValue() {
        // 电影的流通市值
        BigDecimal movideAmount = movieOrderMapper.selectSpAmountSum();
        // 贡献值
        BigDecimal contributionSum = contributionMapper.sumAmount();
        // 流通市值
        BigDecimal circulatingMarketValue = movideAmount.add(contributionSum);
        return circulatingMarketValue;
    }

    /**
     * SP单价趋势
     *
     * @param type 1-日 2-周 3-月
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/06/04
     */
    @Override
    public R spUnitPriceKLineService(int type, int page, int pageSize) {
        List<Map<String, Object>> result = new ArrayList<>();
        int start = (page - 1) * pageSize;
        Integer totalCount = 0;
        if (type == 1) {
            // 查询日 K
            totalCount = dayKLineMapper.selectCount(null);
            result = dayKLineMapper.selectDayKLinePage(start, pageSize);

        }else if(type == 2){
            // 查询日 K
            totalCount = weekKLineMapper.selectCount(null);
            result = weekKLineMapper.selectWeekKLinePage(start, pageSize);
        }else if(type == 3){
            // 查询日 K
            totalCount = monthKLineMapper.selectCount(null);
            result = monthKLineMapper.selectMonthKLinePage(start, pageSize);
        }
        PageUtils pageUtils = new PageUtils(totalCount, pageSize, page, result);
        return R.okData(pageUtils);
    }

    /**
     * sp单价趋势
     * 
     * @param type 1-近一个月 2-近三个月
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/06/04
     */
    @Override
    public R getSpUnitPriceTrend(int type) {
        List<Sp24hoursVo> result = new ArrayList<>();
        if (type == 1) {
            result = sp24hoursMapper.select30List(30);
        } else if (type == 2) {
            result = sp24hoursMapper.select30List(90);
        }
        return R.okData(result);
    }

    /**
     * 充值信息封装
     *
     * @param energyRechargeFlowVo
     * <AUTHOR>
     * @date 2024/04/10
     */
    private void rechargeMessageEncapsulation(EnergyRechargeFlowVo energyRechargeFlowVo) {

        Chain chain = chainMapper.selectOne(Wrappers.<Chain>lambdaQuery().eq(Chain::getChainId, chainId));
        String hash = energyRechargeFlowVo.getHash();
        BigInteger block = new BigInteger("0");
        String to = "";
        if (StrUtil.isNotBlank(hash)) {
            Map<String, String> blockInfo = BNBUtil.getBlockInfo(hash);
            String num = blockInfo.get("blockNumber") == null ? "0" : blockInfo.get("blockNumber");
            block = new BigInteger(num);
            to = blockInfo.get("to");
        }
        energyRechargeFlowVo.setChainLogo(chain.getChainLogo()).setChainName(chain.getChainName())
            .setReceiveAddress(to).setFee("0").setHash(hash).setBlock(block);

    }

    /**
     * 流通SP 计算
     *
     * @param totalSp
     * @return {@link BigDecimal }
     * <AUTHOR>
     * @date 2024/06/04
     */
    private BigDecimal circulationSp() {

        BigDecimal result = taskCenterUserMapper.circulationVolumeValue();
        return result;
        //
        // BigDecimal accountRemaining = new BigDecimal("0");
        // // 获取平台地址 SP
        // List<PlatformAccount> platformAccounts = platformAccountService
        // .list(Wrappers.<PlatformAccount>lambdaQuery().eq(PlatformAccount::getStatus, 1));
        // for (PlatformAccount platformAccount : platformAccounts) {
        // BigInteger balance = BNBUtil.getBalance(platformAccount.getAddress());
        // accountRemaining =
        // accountRemaining.add(new BigDecimal(balance).divide(new BigDecimal(BigInteger.TEN.pow(18))));
        // }
        // return totalSp.subtract(accountRemaining);
    }

    /**
     * 涨跌幅
     * 
     * @param yesterDay
     * @param day
     * @return {@link String }
     * <AUTHOR>
     * @date 2024/06/04
     */
    private String riseAndFallRange(BigDecimal yesterDay, BigDecimal day) {
        BigDecimal difference = day.subtract(yesterDay);
        BigDecimal divide = difference.divide(yesterDay, 4, RoundingMode.HALF_UP);
        BigDecimal bigDecimal = divide.setScale(4, RoundingMode.HALF_UP);
        DecimalFormat decimalFormat = new DecimalFormat("#.##%");
        String format = decimalFormat.format(bigDecimal.doubleValue());
        return format;
    }

}
