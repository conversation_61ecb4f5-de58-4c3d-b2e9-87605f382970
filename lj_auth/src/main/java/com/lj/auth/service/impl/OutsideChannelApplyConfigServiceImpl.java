package com.lj.auth.service.impl;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.List;
import com.lj.auth.mapper.OutsideChannelApplyConfigMapper;
import com.lj.auth.domain.OutsideChannelApplyConfig;
import com.lj.auth.service.OutsideChannelApplyConfigService;
/**
* <AUTHOR>
* @Description 
* @date 2024/12/10 15:59
*/
@Service
public class OutsideChannelApplyConfigServiceImpl extends ServiceImpl<OutsideChannelApplyConfigMapper, OutsideChannelApplyConfig> implements OutsideChannelApplyConfigService{

    @Override
    public int updateBatchSelective(List<OutsideChannelApplyConfig> list) {
        return baseMapper.updateBatchSelective(list);
    }
    @Override
    public int batchInsert(List<OutsideChannelApplyConfig> list) {
        return baseMapper.batchInsert(list);
    }
}
