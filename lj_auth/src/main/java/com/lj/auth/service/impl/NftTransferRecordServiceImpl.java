package com.lj.auth.service.impl;

import com.lj.auth.common.R;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lj.auth.domain.NftTransferRecord;
import com.lj.auth.mapper.NftTransferRecordMapper;
import com.lj.auth.service.NftTransferRecordService;
@Service
public class NftTransferRecordServiceImpl extends ServiceImpl<NftTransferRecordMapper, NftTransferRecord> implements NftTransferRecordService{
    @Resource
    private NftTransferRecordMapper nftTransferRecordMapper;

    @Override
    public R getNftTransactions(Integer opbChainId, Integer contractId, Integer tokenId, String holderAddress, Integer type) {
        List<NftTransferRecord> transferRecords = nftTransferRecordMapper.getNftTransactions(opbChainId, contractId, tokenId, holderAddress, type);
        return R.okData(transferRecords);
    }

    @Override
    public R getAllNftTransactions(Integer opbChainId, String chainAccountAddress, Integer type, String time) {
        List<NftTransferRecord> transferRecords = nftTransferRecordMapper.getAllNftTransactions(opbChainId, chainAccountAddress, type, time);
        return R.okData(transferRecords);
    }
}
