package com.lj.auth.service;

import com.lj.auth.common.R;
import com.lj.auth.domain.Nft;
import com.baomidou.mybatisplus.extension.service.IService;
import com.rabbitmq.client.Channel;
import org.springframework.amqp.core.Message;
import org.web3j.abi.datatypes.generated.Uint256;
import org.web3j.protocol.core.methods.response.TransactionReceipt;

import java.io.IOException;
import java.util.List;

public interface NftService extends IService<Nft> {

    R autoBatchCreateNft(String chainAccountAddress, Integer nftCount);

    R autoBatchCreateNftV2(String chainAccountAddress, Integer nftCount);

    R setNftDescribe(Long id, String describe);

    R getTransferEstimateGas(Integer opbChainId, String contractAddress, String fromAddress, String toAddress,
        Integer tokenId);

    R getNFTBindStatus(Long tokenId);

    R NFTUnbind(Long tokenId, String chainAccountAddress);

    R saveTransferRecord(Integer opbChainId, String transactionHash);

    R saveBurnRecord(Integer opbChainId, String transactionHash);

    R getNfts(Integer opbChainId, String chainAccount, String nftName);

    R getNftsV2(Integer pageNum, Integer pageSize, Integer opbChainId, String chainAccount, Integer queryType,
        String nftName);

    R getNftDetails(Long id);

    R getNftDetailsNFTList(Integer pageNum, Integer pageSize, Long nftid);

    R getNftDetailsv2(Long id);

    R getNftList(Long id, String nftName);

    R getNftListV2(Integer pageNum, Integer pageSize, Long id, String nftName);

    R getTransactionDetail(Long id);

    R getChainAccountAsset(String chainAccountAddress);

    R syncNFTUpchainInfo();

    R compensationFromNFTGetRecord(Long nftManualDisposeId, String plaftAddress);

    int getCurrentNum(String ylPlatformAccountAddress, String contractAddress);

    TransactionReceipt getTransactionReceipt(String transactionHash);

    String batchMint(String contractAddress, String chainAccountAddress, List<Uint256> tokenIds,
        String ylPlatformAccountPrivateKey);

    String queryIPFSTAttribute(String contractAddress, Integer tokenId, String pathCid);

    void upChainHandleV2(String content, Message message, Channel channel) throws IOException;

    void singleUpChainHandle(String content, Message message, Channel channel) throws IOException;

    void compensationMintUpChainHandle(String content, Message message, Channel channel) throws IOException;

    R validApprove(List<Integer> nftIds, String transactionHash);

    R transferToPlatformAddress(List<Long> domainRenewalNFTIds);

    R transferToUserAddress(List<Long> domainRenewalNFTRefundIds);

    /**
     * 查询链地址概览
     * 
     * @param address
     * @return {@link R }
     */
    R getAddressOverviewService(String address);

    /**
     * 合约列表
     *
     * @param page
     * @param pageSize
     * @return {@link R }
     */
    R getContractListService(Integer page, Integer pageSize);

    /**
     * NFT交易记录列表
     *
     * @param page
     * @param pageSize
     * @param address
     * @param tokenId
     * @return {@link R }
     */
    R nftTrRecordService(Integer page, Integer pageSize,String address,Integer tokenId);

    /**
     * nft资产
     *
     * @param page
     * @param pageSize
     * @param address
     * @return {@link R }
     */
    R nftPropertyService(Integer page, Integer pageSize,String address);

    /**
     * nft持有地址列表
     * @param page
     * @param pageSize
     * @return {@link R }
     */
    R nftHoldRecordService(Integer page, Integer pageSize);

    /**
     * 合约详情
     * @param id
     * @return {@link R }
     */
    R contractDetailService(Integer id);

    /**
     * nft详情
     * @param tokenId
     * @return {@link R }
     */
    R nftDetailService(Integer tokenId);
}
