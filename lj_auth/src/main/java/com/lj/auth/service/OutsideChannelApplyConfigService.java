package com.lj.auth.service;

import java.util.List;
import com.lj.auth.domain.OutsideChannelApplyConfig;
import com.baomidou.mybatisplus.extension.service.IService;
    /**
* <AUTHOR>
* @Description 
* @date 2024/12/10 15:59
*/
public interface OutsideChannelApplyConfigService extends IService<OutsideChannelApplyConfig>{


    int updateBatchSelective(List<OutsideChannelApplyConfig> list);

    int batchInsert(List<OutsideChannelApplyConfig> list);

}
