package com.lj.auth.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lj.auth.domain.*;
import com.lj.auth.domain.resp.ValidateApproveResp;
import com.lj.auth.exception.ServiceException;
import com.lj.auth.mapper.*;
import com.lj.auth.service.NftBeforeService;
import com.lj.auth.util.BNBUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.web3j.abi.TypeReference;
import org.web3j.abi.datatypes.*;
import org.web3j.abi.datatypes.generated.Uint256;
import javax.annotation.Resource;
import java.math.BigInteger;
import java.util.*;

@Slf4j
@Service
public class NftBeforeServiceImpl extends ServiceImpl<NftMapper, Nft> implements NftBeforeService {

    @Resource
    private NftMapper nftMapper;

    @Resource
    private OrderDomainRenewalNftRefundMapper orderDomainRenewalNftRefundMapper;
    @Resource
    private  PlatformAccountMapper platformAccountMapper;

    @Resource
    private OrderDomainRenewalNftMapper orderDomainRenewalNftMapper;



    /**
     * 是否授权所有
     * @param ownerAddress              NFT拥有者地址
     * @param contractAddress           NFT合约地址
     * @param approvedAddress           授权地址
     * @return
     * @throws Exception
     */
    public Boolean isApprovedForAll (String ownerAddress, String contractAddress, String approvedAddress){
        Boolean isApprovedForAll = null;
        try {
            String functionName = "isApprovedForAll";
            List<Type> inputParameters = new ArrayList<>();
            inputParameters.add(new Address(ownerAddress));
            inputParameters.add(new Address(approvedAddress));
            List<TypeReference<?>> outputParameters = new ArrayList<>();
            outputParameters.add(new TypeReference<Uint256>() {
            });
            Function function = new Function(functionName, inputParameters, outputParameters);
            List<Type> types = BNBUtil.sendCallTransaction(approvedAddress, contractAddress, function);
            BigInteger result = (BigInteger) types.get(0).getValue();
             isApprovedForAll = BooleanUtil.toBoolean(result.toString());
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException("获取授权地址失败");
        }
        return isApprovedForAll;
    }


    /**
     * 上链前处理
     * @param orderDomainRenewalNft
     */
    @Override
    @Transactional
    public Boolean beforeTransferNFT(OrderDomainRenewalNft orderDomainRenewalNft, String recycleAddress) {
        Integer tranState = orderDomainRenewalNft.getTranState();
        //如果不是待交易的直接跳过
        if (tranState != OrderDomainRenewalNft.TRAN_STATE_WAIT) {
            log.error("当前NFT续费状态不属于待交易,renewalNFTId:{}", orderDomainRenewalNft.getId());
            return false;
        }
        //授权接收人地址
        String accreditToAddress = orderDomainRenewalNft.getAccreditToAddress();
        //合约地址
        String contractAddress = orderDomainRenewalNft.getContractAddress();
        //tokenId
        Integer tokenId = orderDomainRenewalNft.getToken();
        Nft nft = nftMapper.queryByTokenId(contractAddress, tokenId, Nft.NFT_STATUS_NORMAL, false);
        if (nft == null) {
            log.error("当前NFT续费NFT不存在,contractAddress:{},tokenId:{}", contractAddress, tokenId);
            return false;
        }
        //同步一下链上NFT拥有者地址信息
        String nftOwnerAddress = nft.getHolder();
        String nftChainOwnerAddress = BNBUtil.getNFTOwner(accreditToAddress, contractAddress, tokenId);
        if (!nftOwnerAddress.equals(nftChainOwnerAddress)) {
            log.error("nft拥有者和链上信息不一致,NFTID:{},nftOwnerAddress:{},nftChainOwnerAddress:{}", nft.getId(), nftOwnerAddress, nftChainOwnerAddress);
            nft.setHolder(nftChainOwnerAddress);
            nftMapper.updateById(nft);
            return false;
        }

        //如果当前NFT的拥有者已经是回收地址了，直接返回
        if ((nftOwnerAddress.toLowerCase()).equals(recycleAddress.toLowerCase())) {
            log.error("当前NFT续费NFT的所有者已经是回收地址,renewalNFTId:{}", orderDomainRenewalNft.getId());
            return false;
        }

        //校验当前NFT是是否授权给平台地址
        ValidateApproveResp validateApproveResp = validateApproveInfo(nft, accreditToAddress, contractAddress);
        Boolean isSuccess = validateApproveResp.getIsSuccess();
        //授权失败
        if (!isSuccess) {
            log.error("当前NFT续费NFT授权失败,contractAddress:{}，accreditToAddress：{},tokenId:{}", contractAddress, accreditToAddress, tokenId);
            return false;
        }

        //修改状态为处理中
        orderDomainRenewalNft.setTranState(OrderDomainRenewalNft.TRAN_STATE_PROCESSING);
        orderDomainRenewalNft.setUpdateTime(new Date());
        orderDomainRenewalNftMapper.updateById(orderDomainRenewalNft);
        return true;
    }

    /**
     * NFT退款前处理
     * @param orderDomainRenewalNftRefund
     */
    @Override
    public Boolean beforeRefundToNFTOwnerAddress(OrderDomainRenewalNftRefund orderDomainRenewalNftRefund) {
        Integer tranState = orderDomainRenewalNftRefund.getTranState();
        Long nftRefundId = orderDomainRenewalNftRefund.getId();
        if(!tranState.equals(OrderDomainRenewalNftRefund.TRAN_STATE_WAIT)){
            log.error("NFT退款状态异常,nftRefundId:{}",nftRefundId);
            return false;
        }
        Long orderNftId = orderDomainRenewalNftRefund.getOrderNftId();
        OrderDomainRenewalNft orderDomainRenewalNft = orderDomainRenewalNftMapper.selectById(orderNftId);
        if(orderDomainRenewalNft==null){
            log.error("NFT续费信息不存在,nftRefundId:{}",nftRefundId);
            return false;
        }
        Integer NFTRenewalStatus = orderDomainRenewalNft.getTranState();
        if(!NFTRenewalStatus.equals(OrderDomainRenewalNft.TRAN_STATE_SUCCESS)){
            log.error("NFT续费状态异常,nftRefundId:{}",nftRefundId);
            return false;
        }
        String recycleAddress = orderDomainRenewalNft.getToAddress().toLowerCase();
        String contractAddress = orderDomainRenewalNft.getContractAddress().toLowerCase();
        Integer tokenId = orderDomainRenewalNft.getToken();

        PlatformAccount platformAccount = platformAccountMapper.queryByAddressAndStatus(recycleAddress, PlatformAccount.TYPE_NFT_RECYCLE_ACCOUNT, PlatformAccount.STATUS_SHOW);
        if(platformAccount==null){
            log.error("平台账户不存在,nftRefundId:{}",nftRefundId);
            return false;
        }

        Nft nft = nftMapper.queryByTokenId(contractAddress, tokenId, Nft.NFT_STATUS_NORMAL, false);
        Assert.notNull(nft,"NFT不存在,nftRefundId:{}",nftRefundId);

        //NFT拥有者地址
        String nftOwnerAddress = nft.getHolder().toLowerCase();
        if(!nftOwnerAddress.equals(recycleAddress)){
            log.error("NFT拥有者地址信息异常,nftRefundId:{},recycleAddress:{},nftOwnerAddress:{}",nftRefundId,recycleAddress,nftOwnerAddress);
            return false;
        }

        String nftChainOwnerAddress = BNBUtil.getNFTOwner(recycleAddress, contractAddress, tokenId);
        if(!nftChainOwnerAddress.equals(nftOwnerAddress)){
            log.error("NFT拥有者链上地址信息异常,nftRefundId:{},recycleAddress:{},nftChainOwnerAddress:{}",nftRefundId,recycleAddress,nftChainOwnerAddress);
            nft.setHolder(nftChainOwnerAddress);
            return false;
        }

        //修改状态为处理中
        orderDomainRenewalNftRefund.setTranState(OrderDomainRenewalNft.TRAN_STATE_PROCESSING);
        orderDomainRenewalNftRefund.setUpdateTime(new Date());
        orderDomainRenewalNftRefundMapper.updateById(orderDomainRenewalNftRefund);
        return true;
    }


    /**
     * 校验当前地址是否已经全部授权给授权地址
     * @param nft
     * @param platformAccountAddress
     * @param contractAddress
     * @return
     */
    private ValidateApproveResp validateApproveInfo(Nft nft, String platformAccountAddress, String contractAddress) {
        ValidateApproveResp validateApproveResp = new ValidateApproveResp();
        String message = "";
        Long nftId = nft.getId();
        validateApproveResp.setNFTId(nftId);
        Boolean isApprove = nft.getIsApprove();

        if (!isApprove) {
            validateApproveResp.setIsSuccess(false);
            message = "还未授权";
            validateApproveResp.setMessage(message);
            return validateApproveResp;
        }

        //查询NFT对应的授权信息
        String approveAddress = nft.getApproveAddress();
        if (StringUtils.isNotBlank(approveAddress) && ObjectUtil.equals(platformAccountAddress,approveAddress)) {
            return new ValidateApproveResp(nftId, true, "已授权成功");
        }
        //去链上校验授权信息
        String ownerAddress = nft.getHolder();
        Boolean isApprovedForAll = isApprovedForAll(ownerAddress, contractAddress, platformAccountAddress);
        if (isApprovedForAll) {
            nft.setApproveAddress(platformAccountAddress);
            nft.setUpdateTime(new Date());
            nftMapper.updateById(nft);
            validateApproveResp.setIsSuccess(true);
            message = "授权成功";
        } else {
            validateApproveResp.setIsSuccess(false);
            message = "授权失败";
        }
        validateApproveResp.setMessage(message);
        return validateApproveResp;
    }



}
