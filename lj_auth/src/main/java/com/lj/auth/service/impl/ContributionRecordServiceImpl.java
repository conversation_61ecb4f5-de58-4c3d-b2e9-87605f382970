package com.lj.auth.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lj.auth.common.R;
import com.lj.auth.domain.*;
import com.lj.auth.domain.Result.FriendCheckItemResult;
import com.lj.auth.domain.vo.*;
import com.lj.auth.exception.ResponseEnum;
import com.lj.auth.exception.ServiceException;
import com.lj.auth.feginClient.IOrderService;
import com.lj.auth.feginClient.ITXIMService;
import com.lj.auth.mapper.ContributionMapper;
import com.lj.auth.mapper.FollowMapper;
import com.lj.auth.mapper.LjPaymentTransactionRecordMapper;
import com.lj.auth.pay.AggregatePay.AppPay.CommonParameters;
import com.lj.auth.pay.AggregatePay.AppPay.Precreate;
import com.lj.auth.pay.AggregatePay.AppPay.Query;
import com.lj.auth.service.AccountService;
import com.lj.auth.service.GlobalConfigService;
import com.lj.auth.util.IpUtil;
import com.lj.auth.util.PageUtils;
import com.lj.auth.util.PropertiesRead;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.annotation.AfterReturning;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lj.auth.mapper.ContributionRecordMapper;
import com.lj.auth.service.ContributionRecordService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import static com.lj.auth.common.CommonConstant.*;

@Service
@Slf4j
public class ContributionRecordServiceImpl extends ServiceImpl<ContributionRecordMapper, ContributionRecord>
    implements ContributionRecordService {
    private final ReentrantLock lock = new ReentrantLock();
    @Resource
    private GlobalConfigService globalConfigService;
    @Resource
    private ContributionMapper contributionMapper;
    @Resource
    private AccountService accountService;
    @Resource
    private ContributionRecordMapper contributionRecordMapper;
    @Resource
    private LjPaymentTransactionRecordMapper ljPaymentTransactionRecordMapper;
    @Resource
    private RestTemplate restTemplate;
    @Value("${payCallbackNotifyAddress}")
    private String payCallbackNotifyAddress;
    @Resource
    private IOrderService iOrderService;
    @Resource
    private ITXIMService itximService;
    @Resource
    private FollowMapper  followMapper;

    /**
     * 获取支付配置
     * 
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/07/02
     */
    @Override
    public R getPayConfigService() {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("WEIXINPAYSTATE", globalConfigService.getGlobalConfig(GX_WEIXINPAYSTATE));
        resultMap.put("ALIPAYSTATE", globalConfigService.getGlobalConfig(GX_ALIPAYSTATE));
        resultMap.put("DOUYINPAYSTATE", globalConfigService.getGlobalConfig(GX_DOUYINPAYSTATE));
        return R.okData(resultMap);
    }

    /**
     * 排行榜
     * 
     * @param page 页码
     * @param pageSize 数量
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/07/02
     */
    @Override
    public R rankingListService(int page, int pageSize) {
        int start = (page - 1) * pageSize;
        Integer totalCount = contributionMapper.selectCount(null);
        List<Contribution> result = contributionMapper.selectRankList(start, pageSize);
        result.forEach(contribution -> {
            contribution.setAmount(decimalConversion(contribution.getAmount()));
            // 调整图像和昵称展示
            if (ObjectUtil.equals(contribution.getShowType(), 2)) {
                contribution.setNickName(contribution.getDomainNickName());
            }
            if (ObjectUtil.equals(contribution.getHeadPortraitType(), 2)) {
                contribution.setHeadPortrait(contribution.getNftImage());
            }
        });
        // 封装IM好友关系 排除掉本人
        Map<String, FriendCheckItemResult> friendCheckMap = new HashMap<>();
        String uuid = accountService.getUserInfo().getUuid();
        List<String> otherUuid = result.stream()
            .filter(contribution -> !contribution.getAccountUuid().equals(uuid)
                || StrUtil.isBlank(contribution.getDidSymbol()))
            .map(Contribution::getAccountUuid).collect(Collectors.toList());

        // 查询关注我的好友集合
        List<Follow> follows = followMapper.selectList(
            Wrappers.<Follow>lambdaQuery().eq(Follow::getFollowUuid, uuid).eq(Follow::getRemoveFlag, 0));
        // 获取关注用户的uuid集合
        List<String> followUuidList =
            follows.stream().map(Follow::getAccountUuid).collect(Collectors.toList());

        if (otherUuid.size() > 0) {
            try {
                R friendCheckResult = itximService.friendCheck(uuid, otherUuid);
                if ((Integer)friendCheckResult.get("code") == 200) {
                    String friendCheckResultStr = (String)friendCheckResult.get("data");
                    com.alibaba.fastjson.JSONObject accountCheckResultJSON =
                        com.alibaba.fastjson.JSONObject.parseObject(friendCheckResultStr);
                    log.info("校验好友信息返回结果:{}", accountCheckResultJSON);
                    com.alibaba.fastjson.JSONArray infoItem = accountCheckResultJSON.getJSONArray("InfoItem");
                    List<FriendCheckItemResult> friendCheckItemResultList =
                        infoItem.toJavaList(FriendCheckItemResult.class);
                    friendCheckMap = friendCheckItemResultList.stream().collect(
                        Collectors.toMap(FriendCheckItemResult::getTo_Account, friend -> friend, (a, b) -> {
                            throw new IllegalStateException("Duplicate to_account found");
                        }));
                    for (Contribution contribution2 : result) {
                        // 封装好友关系
                        String promotionAccountUUID =
                            converAccountUUID2IMAccountId(contribution2.getAccountUuid());
                        if (friendCheckMap.containsKey(promotionAccountUUID)) {
                            FriendCheckItemResult friendCheckItemResult =
                                friendCheckMap.get(promotionAccountUUID);
                            String relation = friendCheckItemResult.getRelation();
                            if (ObjectUtil.equals("CheckResult_Type_BothWay", relation)) {
                                contribution2.setImFriend(1);
                            }
                        }
                        // 封装关注关系排除自己
                        if (followUuidList.contains(contribution2.getAccountUuid())
                            && !uuid.equals(contribution2.getAccountUuid())) {
                            contribution2.setIsFollow(1);
                        }

                    }
                }
            } catch (Exception e) {
                log.error("获取im好友关系异常 error", e);
            }
        }
        PageUtils pageUtils = new PageUtils(totalCount, pageSize, page, result);
        return R.okData(pageUtils);
    }

    /**
     * 贡献记录
     * 
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/07/03
     */
    @Override
    public R contributionRecordsService() {
        List<MyContributionRecordVo> result = contributionRecordMapper.selectContributionRecordList();
        for (MyContributionRecordVo myContributionRecordVo : result) {
            myContributionRecordVo.setAmount( decimalConversion(myContributionRecordVo.getAmount()));
        }
        return R.okData(result);
    }

    /**
     * 我的贡献
     * 
     * @param page 页
     * @param pageSize 页码
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/07/04
     */
    @Override
    public R myContributionService(int page, int pageSize) {
        Account userInfo = accountService.getUserInfo();
        int start = (page - 1) * pageSize;
        Integer totalCount = this.count(Wrappers.<ContributionRecord>lambdaQuery()
            .eq(ContributionRecord::getAccountUuid, userInfo.getUuid()).eq(ContributionRecord::getState, 1));
        List<ContributionRecordVo> contributionRecordVoList =
            contributionRecordMapper.selectMyContributionLRecordList(userInfo.getUuid(), start, pageSize);
        for (ContributionRecordVo contestRecordVo : contributionRecordVoList) {
            contestRecordVo.setAmount( decimalConversion(contestRecordVo.getAmount()));
        }
        PageUtils pageUtils = new PageUtils(totalCount, pageSize, page, contributionRecordVoList);
        return R.okData(pageUtils);
    }

    /**
     * 贡献充值
     *
     * @param amount 金额
     * @param payType 支付方式：1-微信2-支付宝
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/07/08
     */
    @Override
    @Transactional
    public R getGXWeiXinLinkService(BigDecimal amount, int payType) {
        long timeStamp = DateUtil.date().getTime();
        // 订单号必须以35H9开头
        String merOrderId = "384G" + timeStamp;
        Account userInfo = accountService.getUserInfo();
        String accountUUID = userInfo.getUuid();
        // 创建贡献记录
        ContributionRecord contributionRecord = new ContributionRecord();
        contributionRecord.setAccountUuid(accountUUID).setAmount(amount).setPayType(payType);
        this.save(contributionRecord);

        // 创建贡献订单信息
        LjPaymentTransactionRecord ljPaymentTransactionRecord = new LjPaymentTransactionRecord();
        ljPaymentTransactionRecord.setPayOrderId(merOrderId);
        ljPaymentTransactionRecord.setPayAmount(amount);
        ljPaymentTransactionRecord.setPayType(payType);
        ljPaymentTransactionRecord.setPayTool(4);
        ljPaymentTransactionRecord.setOrderType(3);
        ljPaymentTransactionRecord.setAccountUuid(accountUUID);
        ljPaymentTransactionRecord.setOrderId(Long.valueOf(contributionRecord.getId()));
        ljPaymentTransactionRecordMapper.insert(ljPaymentTransactionRecord);
        String weixinLink = getWeixinLink(merOrderId, userInfo.getOperateUuid());
        Map<String, String> map = new HashMap<>();
        map.put("merOrderId", merOrderId);
        map.put("linkUrl", weixinLink);
        return R.okData(map);
    }

    /**
     * 我的贡献排行
     * 
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/07/08
     */
    @Override
    public R myRankingService() {
        Account userInfo = accountService.getUserInfo();
        Integer totalCount = this.count(Wrappers.<ContributionRecord>lambdaQuery()
            .eq(ContributionRecord::getAccountUuid, userInfo.getUuid()).eq(ContributionRecord::getState, 1));
        ContributionVo result = contributionMapper.selectMyRankList(userInfo.getUuid());
        if(result==null){
            result= contributionMapper.selectUserInfoList(userInfo.getUuid());
            result.setAmount(new BigDecimal(0));
            result.setRanking(0);
        }

        result.setCount(totalCount);
        return R.okData(result);
    }

    /**
     * app下单支付
     *
     * @param merOrderId 订单号
     * @param jsCode 临时登录凭证
     * @param returnUrl 订单展示页面
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/07/08
     */
    @Override
    public R appPay(String merOrderId, String jsCode, String returnUrl) {
        Account userInfo = accountService.getUserInfo();
        //校验用户有没有DID
        if (StrUtil.isEmpty(userInfo.getDidSymbol())) {
            return R.error(ResponseEnum.NotDIDCertifiedPlease);
        }
        String accountUUID = userInfo.getUuid();
        try {
            // 充值订单表
            LjPaymentTransactionRecord ljPaymentTransactionRecord =
                ljPaymentTransactionRecordMapper.selectOne(Wrappers.<LjPaymentTransactionRecord>lambdaQuery()
                    .eq(LjPaymentTransactionRecord::getPayOrderId, merOrderId));
            BigDecimal amount = ljPaymentTransactionRecord.getPayAmount();
            Integer payType = ljPaymentTransactionRecord.getPayType();
            Date createTime = new Date();
            // 充值金额转换为 分
            BigInteger totalAmount =
                amount.multiply(new BigDecimal("100")).setScale(0, RoundingMode.DOWN).toBigInteger();
            String response = "";
            if (payType == 1) {// 微信小程序走门户小程序
                // response = Precreate.createWechatOrder(createTime, merOrderId, totalAmount, returnUrl);
                // response = miniPayUtil.unifiedOrder(merOrderId, createTime, totalAmount, jsCode);

            } else if (payType == 2) {// 支付宝支付
                response = Precreate.createAliOrderGX(createTime, merOrderId, totalAmount, returnUrl);
            }
            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("orderId", ljPaymentTransactionRecord.getId());
            resultMap.put("orderInfo", JSONObject.parseObject(response));
            resultMap.put("merOrderId", merOrderId);
            return R.okData(resultMap);
        } catch (Exception e) {
            e.printStackTrace();
            R.error(ResponseEnum.PaymentOrderFailed);
            return R.error("");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R appNotify(PayNotifyVo data) {
        String status = data.getStatus();
        String merOrderId = data.getMerOrderId();
        if (!"TRADE_SUCCESS".equals(status)) {
            return R.error(status);
        }
        if (StrUtil.isNotEmpty(merOrderId)) {
            LjPaymentTransactionRecord ljPaymentTransactionRecord =
                ljPaymentTransactionRecordMapper.selectOne(Wrappers.<LjPaymentTransactionRecord>lambdaQuery()
                    .eq(LjPaymentTransactionRecord::getPayOrderId, merOrderId));
            if (ljPaymentTransactionRecord == null) {
                return R.error("FAILED");
            }
            // 判断订单状态是否修改过来---避免重复回调
            Integer state = ljPaymentTransactionRecord.getPayState();
            if (state == 2) {
                return R.error("SUCCESS");
            }
            // 订单金额 单位：分
//            String totalAmount = data.getTotalAmount();
            // 实付金额 单位：分
//            String buyerPayAmount = data.getBuyerPayAmount();
            // 可能是红包暂不校验
            // if (!totalAmount.equals(buyerPayAmount)) {
            // log.error("pc支付回调：订单金额:" + totalAmount + ",与实收金额:" + buyerPayAmount + ",不一致");
            // return R.error("FAILED");
            // }
            // BigDecimal realReceiptAmount = new BigDecimal(buyerPayAmount);
            // if (realReceiptAmount
            // .compareTo(rechargeWithdrawRecord.getAmount().multiply(new BigDecimal(100))) != 0) {
            // log.error("pc支付回调：实收金额:" + realReceiptAmount + ",与订单金额:" + rechargeWithdrawRecord.getAmount()
            // + ",不一致");
            // return R.error("FAILED");
            // }
            Date updateTime = new Date();
            String accountUuid = ljPaymentTransactionRecord.getAccountUuid();
            BigDecimal amount = ljPaymentTransactionRecord.getPayAmount();
            /**
             * 在对业务数据进行状态检查和处理之前，这里要使用数据锁进行并发控制，以避免函数重入导致的数据混乱 尝试获取锁成功之后才去处理数据，相比于同步锁，这里不会去等待，获取不到则直接返回
             */
            if (lock.tryLock()) {
                if ("TRADE_SUCCESS".equals(status)) {
                    try {
                        // 支付成功
                        // 状态( 1-审核中，2-审核失败，3-提现/充值成功 4-用户取消 5-超时 6-待支付)
                        ljPaymentTransactionRecordMapper.update(null,
                            Wrappers.<LjPaymentTransactionRecord>lambdaUpdate()
                                .eq(LjPaymentTransactionRecord::getPayOrderId, merOrderId)
                                .set(LjPaymentTransactionRecord::getPayState, 2));
                        // 修改贡献记录状态
                        this.update(null,
                            Wrappers.<ContributionRecord>lambdaUpdate()
                                .eq(ContributionRecord::getId, ljPaymentTransactionRecord.getOrderId())
                                .set(ContributionRecord::getState, 1));
                        log.info("修改贡献记录成功，流水号：{}", merOrderId);

                        // 给对应用户加资产
                        Contribution contribution = contributionMapper.selectOne(Wrappers
                            .<Contribution>lambdaQuery().eq(Contribution::getAccountUuid, accountUuid));
                        // 贡献信息为空生成贡献信息
                        if (null == contribution) {
                            Contribution contribution1 = new Contribution();
                            contribution1.setAmount(BigDecimal.ZERO);
                            contribution1.setAccountUuid(accountUuid);
                            contributionMapper.insert(contribution1);
                            contribution=contributionMapper.selectOne(Wrappers
                                    .<Contribution>lambdaQuery().eq(Contribution::getAccountUuid, accountUuid));
                        }
                        BigDecimal balance = contribution.getAmount();

                        BigDecimal currentBalance = balance.add(amount);

                        contributionMapper.update(null,
                            Wrappers.<Contribution>lambdaUpdate()
                                .eq(Contribution::getAccountUuid, accountUuid)
                                .set(Contribution::getAmount, currentBalance));

                        log.info("贡献增加成功，贡献ID:{},增加金额:{}", contribution.getId(), amount);
                        // TODO 组装消息发送
                        log.info("支付回调成功================");
                        log.info("支付回调成功================");
                        log.info("支付回调成功================");
                        return R.ok("SUCCESS");
                    } finally {
                        // 要主动释放锁
                        lock.unlock();
                    }
                }
            }
        }

        return R.error("FAILED");
    }

    @Override
    @Transactional
    public R appQuery(String merOrderId) {
        Date createTime = new Date();
        String response = Query.queryOrder(createTime, merOrderId);
        if (response != null) {
            JSONObject responseJson = JSONObject.parseObject(response);
            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("data", responseJson);
            // 同步检查订单支付状态
            checkOrderStatus(JSON.toJSONString(responseJson));

            return R.ok(resultMap);
        }
        return R.error(ResponseEnum.QueryFailed);
    }

    /**
     * 创建订单
     * @param amount
     * @param remark
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/12/03
     */
    @Override
    public R createOrderService(HttpServletRequest request,BigDecimal amount, String remark) {
        Map<String, String> map = new HashMap<>();
        Account userInfo = accountService.getUserInfo();
        //校验用户有没有DID
        if (StrUtil.isEmpty(userInfo.getDidSymbol())) {
            return R.error(ResponseEnum.NotDIDCertifiedPlease);
        }
        String accountUUID = userInfo.getUuid();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("orderTitle", "贡献");
        jsonObject.put("didSymbol", userInfo.getDidSymbol());
        jsonObject.put("applicationId", 12);
        jsonObject.put("accountUUID", userInfo.getUuid());
        jsonObject.put("orderAmount", amount);
        jsonObject.put("actualAmount", amount);
        jsonObject.put("payExpiredTime", DateUtil.formatDateTime(DateUtil.offsetMinute(new Date(), 15)));
        jsonObject.put("payCallbackNotifyAddress", payCallbackNotifyAddress + "contribution/appNotify1");
        jsonObject.put("channelAppId", request.getHeader("channelAppId"));
        jsonObject.put("ipAddr", IpUtil.getRealIp(request));
        R order = iOrderService.createOrder(jsonObject);
        if (ObjectUtil.equals(order.get("code"), 200)) {
            Object data = order.get("data");
            Map orderMap = JSON.parseObject(JSON.toJSONString(data), Map.class);
            if (orderMap != null) {
                String orderNumber = orderMap.get("orderNumber").toString();
                // 创建贡献记录
                ContributionRecord contributionRecord = new ContributionRecord();
                contributionRecord.setAccountUuid(accountUUID).setAmount(amount).setPayType(1)
                    .setOrderNumber(orderNumber);
                this.save(contributionRecord);
                map.put("merOrderId", orderNumber);
            }
        } else {
            return order;
        }
        return R.okData(map);
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R appNotify1(OrderNotifyRequest data) {
        Integer status = data.getPayStatus();
        String merOrderId = data.getOrderNumber();
        Integer payType = data.getPayType();
        if (payType == 3) {
            payType = 0;
        }
        if (!ObjectUtil.equals(status,1)) {
            return R.error("FAILED");
        }
        if (StrUtil.isNotEmpty(merOrderId)) {
            ContributionRecord contributionRecord = contributionRecordMapper.selectOne(Wrappers
                .<ContributionRecord>lambdaQuery().eq(ContributionRecord::getOrderNumber, merOrderId));
            if (contributionRecord == null) {
                return R.error("FAILED");
            }
            // 判断订单状态是否修改过来---避免重复回调
            Integer state = contributionRecord.getState();
            if (state == 1) {
                return R.error("SUCCESS");
            }
            Date updateTime = new Date();
            String accountUuid = contributionRecord.getAccountUuid();
            BigDecimal amount = contributionRecord.getAmount();
            /**
             * 在对业务数据进行状态检查和处理之前，这里要使用数据锁进行并发控制，以避免函数重入导致的数据混乱 尝试获取锁成功之后才去处理数据，相比于同步锁，这里不会去等待，获取不到则直接返回
             */
            if (lock.tryLock()) {
                if (ObjectUtil.equals(status,1)) {
                    try {
                        // 支付成功
                        // 状态( 1-审核中，2-审核失败，3-提现/充值成功 4-用户取消 5-超时 6-待支付)
                        contributionRecordMapper.update(null,
                                Wrappers.<ContributionRecord>lambdaUpdate()
                                        .eq(ContributionRecord::getOrderNumber, merOrderId)
                                        .set(ContributionRecord::getPayType, payType)
                                        .set(ContributionRecord::getState, 1)
                        );
                        log.info("修改贡献记录成功，流水号：{}", merOrderId);
                        // 给对应用户加资产
                        Contribution contribution = contributionMapper.selectOne(Wrappers
                                .<Contribution>lambdaQuery().eq(Contribution::getAccountUuid, accountUuid));
                        // 贡献信息为空生成贡献信息
                        if (null == contribution) {
                            Contribution contribution1 = new Contribution();
                            contribution1.setAmount(BigDecimal.ZERO);
                            contribution1.setAccountUuid(accountUuid);
                            contributionMapper.insert(contribution1);
                            contribution=contributionMapper.selectOne(Wrappers
                                    .<Contribution>lambdaQuery().eq(Contribution::getAccountUuid, accountUuid));
                        }
                        BigDecimal balance = contribution.getAmount();

                        BigDecimal currentBalance = balance.add(amount);
                        contributionMapper.update(null,
                                Wrappers.<Contribution>lambdaUpdate()
                                        .eq(Contribution::getAccountUuid, accountUuid)
                                        .set(Contribution::getAmount, currentBalance));

                        log.info("贡献增加成功，贡献ID:{},增加金额:{}", contribution.getId(), amount);
                        // TODO 组装消息发送
                        log.info("支付回调成功================");
                        log.info("支付回调成功================");
                        log.info("支付回调成功================");
                        return R.ok("SUCCESS");
                    } finally {
                        // 要主动释放锁
                        lock.unlock();
                    }
                }
            }
        }

        return R.error("FAILED");
    }

    public String getWeixinLink(String merOrderId, String operateUUID) {
        // 获取微信accessToken
        String url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid="
            + CommonParameters.wechatAppId + "&secret=" + CommonParameters.secret;
        ResponseEntity<JSONObject> exchange =
            restTemplate.exchange(url, HttpMethod.GET, null, JSONObject.class);
        String accessToken = (String)exchange.getBody().get("access_token");
        // 获取LinkUrl
        String linkUrl = getLinkUrl(accessToken, merOrderId, operateUUID, 1);
        return linkUrl;
    }

    public String getLinkUrl(String accessToken, String merOrderId, String operateUuid, Integer paymentType) {
        String url = "https://api.weixin.qq.com/wxa/generatescheme?access_token=" + accessToken;
        com.alibaba.fastjson.JSONObject bodyParam = new com.alibaba.fastjson.JSONObject();
        com.alibaba.fastjson.JSONObject childParam = new com.alibaba.fastjson.JSONObject();
        childParam.put("path", "/pages_cashier/cashier_desk/cashier_desk");
        // 拼接支付类型是余额 还是贡献
        childParam.put("query",
            "merOrderId=" + merOrderId + "&operateUuid=" + operateUuid + "&paymentType=" + paymentType);
        childParam.put("env_version", "release");
        bodyParam.put("jump_wxa", childParam);
        bodyParam.put("is_expire", true);
        bodyParam.put("expire_type", 1);
        bodyParam.put("expire_interval", 1);
        HttpHeaders headers = new HttpHeaders();
        HttpEntity<?> httpEntity = new HttpEntity<>(bodyParam.toJSONString(), headers);
        ResponseEntity<com.alibaba.fastjson.JSONObject> exchange =
            restTemplate.exchange(url, HttpMethod.POST, httpEntity, com.alibaba.fastjson.JSONObject.class);
        com.alibaba.fastjson.JSONObject body = exchange.getBody();
        return (String)body.get("openlink");
    }

    public void checkOrderStatus(String data) {
        Map map = JSON.parseObject(data, Map.class);
        String state = map.get("errCode").toString();
        if ("SUCCESS".equals(state)) {
            try {
                String merOrderId = map.get("merOrderId").toString();
                String totalAmount = map.get("totalAmount").toString();
                String invoiceAmount = map.get("invoiceAmount").toString();

                String status = map.get("status").toString();

                PayNotifyVo payNotifyVo = new PayNotifyVo();

                payNotifyVo.setStatus(status);

                payNotifyVo.setMerOrderId(merOrderId);
                payNotifyVo.setBuyerPayAmount(invoiceAmount);
                payNotifyVo.setTotalAmount(totalAmount);
                appNotify(payNotifyVo);
            } catch (Exception e) {
                log.error(e.getMessage());
                log.error(e.getMessage());
                log.error(e.getMessage());
            }

        }
    }


    private BigDecimal decimalConversion(BigDecimal amount) {
        DecimalFormat decimalFormat = new DecimalFormat("#.##");
        return new BigDecimal(decimalFormat.format(amount));
    }

    /**
     * 将账户UUID转换为IM的账号ID
     * @param paramAccountUUID
     * @return
     */
    public String converAccountUUID2IMAccountId(String paramAccountUUID){
        String accountUUID=paramAccountUUID;
        String ymlActive = PropertiesRead.getYmlActive();
        if(!"prod".equals(ymlActive)){
            if(!paramAccountUUID.startsWith("test_")){
                accountUUID="test_"+paramAccountUUID;
            }
        }
        return accountUUID;
    }
}
