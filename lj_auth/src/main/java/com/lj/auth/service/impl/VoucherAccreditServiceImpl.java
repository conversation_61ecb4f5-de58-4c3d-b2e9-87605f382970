package com.lj.auth.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lj.auth.common.R;
import com.lj.auth.domain.*;
import com.lj.auth.domain.vo.VoucherAccreditVo;
import com.lj.auth.exception.ResponseEnum;
import com.lj.auth.exception.ServiceException;
import com.lj.auth.mapper.*;
import com.lj.auth.service.AccountService;
import com.lj.auth.service.VoucherAccreditService;
import com.lj.auth.util.PageUtils;
import com.lj.auth.ws.PushInfo;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Description
 * @date 2024/4/26 15:58
 */
@Service
@Slf4j
public class VoucherAccreditServiceImpl extends ServiceImpl<VoucherAccreditMapper, VoucherAccredit>
    implements VoucherAccreditService {
    @Resource
    private VoucherAccreditMapper voucherAccreditMapper;
    @Resource
    private AccountInfoVoucherMapper accountInfoVoucherMapper;
    @Resource
    private GlobalConfigMapper globalConfigMapper;
    @Resource
    private AccountDidMapper accountDidMapper;
    @Resource
    private PushInfo pushInfo;
    @Resource
    private DidCheckInActivityMapper didCheckInActivityMapper;
    @Resource
    private AccountService accountService;
    @Resource
    private VcCheckInActivityMapper vcCheckInActivityMapper;
    @Value("${readImagepath}")
    private String readImagepath;

    /**
     * 凭证扫码签到 2024华中Web3.0大会-暨界外科技新品发布会 武汉界外科技有限公司
     *
     * @param vcId 凭证id
     */
    @Override
    public void voucherSignIn(String vcId) {
        AccountInfoVoucher accountInfoVoucher = accountInfoVoucherMapper
            .selectOne(new LambdaQueryWrapper<AccountInfoVoucher>().eq(AccountInfoVoucher::getVcId, vcId));
        if (null == accountInfoVoucher) {
            throw new ServiceException("无法识别的凭证标识");
        }

        String s = globalConfigMapper.queryConfig(GlobalConfig.VOUCHER_ORG_LOGO_LJ);

        Integer count = voucherAccreditMapper.selectCount(new LambdaQueryWrapper<VoucherAccredit>()
            .eq(VoucherAccredit::getVcId, vcId).eq(VoucherAccredit::getType, VoucherAccredit.type2));
        if (count > 0) {
            throw new ServiceException("重复签到");
        }

        VoucherAccredit voucherAccredit =
            VoucherAccredit.builder().accountUuid(accountInfoVoucher.getAccountUuid())
                .operateUuid(accountInfoVoucher.getOperateUuid()).vcId(vcId).orgName("武汉界外科技有限公司")
                .title("2024华中Web3.0大会-暨界外科技新品发布会").logo(s).type(VoucherAccredit.type2).build();
        voucherAccreditMapper.insert(voucherAccredit);

    }

    @Override
    public Integer getCountLj() {
        return voucherAccreditMapper.selectCount(
            new LambdaQueryWrapper<VoucherAccredit>().eq(VoucherAccredit::getType, VoucherAccredit.type2));
    }

    /**
     * 重庆签到-2024.5.22
     * 
     * @param vcId
     */
    @Override
    public void voucherSignInCq(String vcId) {
        AccountInfoVoucher accountInfoVoucher = accountInfoVoucherMapper
            .selectOne(new LambdaQueryWrapper<AccountInfoVoucher>().eq(AccountInfoVoucher::getVcId, vcId));
        if (null == accountInfoVoucher) {
            throw new ServiceException("无法识别的凭证标识");
        }

        String s = globalConfigMapper.queryConfig(GlobalConfig.VOUCHER_ORG_LOGO_CQ_PT);

        Integer count = voucherAccreditMapper.selectCount(new LambdaQueryWrapper<VoucherAccredit>()
            .eq(VoucherAccredit::getVcId, vcId).eq(VoucherAccredit::getType, VoucherAccredit.type3));
        if (count > 0) {
            throw new ServiceException("重复签到");
        }

        VoucherAccredit voucherAccredit =
            VoucherAccredit.builder().accountUuid(accountInfoVoucher.getAccountUuid())
                .operateUuid(accountInfoVoucher.getOperateUuid()).vcId(vcId).orgName("重庆市永川区派棠商业管理有限公司")
                .title("2024全网分布式域名及实名DID大会").logo(s).type(VoucherAccredit.type3).build();
        voucherAccreditMapper.insert(voucherAccredit);
    }

    @Override
    public Integer getCountCq() {
        return voucherAccreditMapper.selectCount(
            new LambdaQueryWrapper<VoucherAccredit>().eq(VoucherAccredit::getType, VoucherAccredit.type3));
    }

    /**
     * 重庆签到-2024.5.22
     * 
     * @param vcId
     */
    @Override
    public void voucherSignInCqkz(String vcId) {
        AccountInfoVoucher accountInfoVoucher = accountInfoVoucherMapper
            .selectOne(new LambdaQueryWrapper<AccountInfoVoucher>().eq(AccountInfoVoucher::getVcId, vcId));
        if (null == accountInfoVoucher) {
            throw new ServiceException("无法识别的凭证标识");
        }

        String s = globalConfigMapper.queryConfig(GlobalConfig.VOUCHER_ORG_LOGO_LJ);

        Integer count = voucherAccreditMapper.selectCount(new LambdaQueryWrapper<VoucherAccredit>()
            .eq(VoucherAccredit::getVcId, vcId).eq(VoucherAccredit::getType, VoucherAccredit.type4));
        if (count > 0) {
            throw new ServiceException("重复签到");
        }

        VoucherAccredit voucherAccredit =
            VoucherAccredit.builder().accountUuid(accountInfoVoucher.getAccountUuid())
                .operateUuid(accountInfoVoucher.getOperateUuid()).vcId(vcId).orgName("重庆林帮利科技有限公司")
                .title("全网分布式域名及实名DID大会(重庆·开州)").logo(s).type(VoucherAccredit.type4).build();
        voucherAccreditMapper.insert(voucherAccredit);
    }

    @Override
    public Integer getCountCqkz() {
        return voucherAccreditMapper.selectCount(
            new LambdaQueryWrapper<VoucherAccredit>().eq(VoucherAccredit::getType, VoucherAccredit.type4));
    }

    /**
     * 四川********
     *
     * @param vcId
     */
    @Override
    public void voucherSignInSsps(String vcId) {
        AccountInfoVoucher accountInfoVoucher = accountInfoVoucherMapper
            .selectOne(new LambdaQueryWrapper<AccountInfoVoucher>().eq(AccountInfoVoucher::getVcId, vcId));
        if (null == accountInfoVoucher) {
            throw new ServiceException("无法识别的凭证标识");
        }

        String s = globalConfigMapper.queryConfig(GlobalConfig.VOUCHER_ORG_LOGO_SSPS);

        Integer count = voucherAccreditMapper.selectCount(new LambdaQueryWrapper<VoucherAccredit>()
            .eq(VoucherAccredit::getVcId, vcId).eq(VoucherAccredit::getType, VoucherAccredit.type5));
        if (count > 0) {
            throw new ServiceException("重复签到");
        }

        VoucherAccredit voucherAccredit =
            VoucherAccredit.builder().accountUuid(accountInfoVoucher.getAccountUuid())
                .operateUuid(accountInfoVoucher.getOperateUuid()).vcId(vcId).orgName("晟世派商（四川）科技有限公司")
                .title("BSN全网分布式域名及可信数字身份DID四川启动大会").logo(s).type(VoucherAccredit.type5).build();
        voucherAccreditMapper.insert(voucherAccredit);
    }

    @Override
    public Integer getCountSsps() {
        return voucherAccreditMapper.selectCount(
            new LambdaQueryWrapper<VoucherAccredit>().eq(VoucherAccredit::getType, VoucherAccredit.type5));
    }

    /**
     * 添加凭证授权记录
     * 
     * @param didSymbol
     * @param logo
     * @param title
     * @param orgName
     * @param type
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/06/27
     */
    @Override
    public R addAuthorizationService(String didSymbol, String logo, String title, String orgName,
        Integer type, String code, String failReason) {
        DidCheckInActivity didCheckInActivity = didCheckInActivityMapper.selectById(type);
        Map message = JSON.parseObject(JSON.toJSONString(didCheckInActivity), Map.class);
        message.put("failReason", failReason);
        message.put("code", code);
        if (ObjectUtil.equals(code, "200")) {
            // 查询用户uuid
            AccountDid accountDid = accountDidMapper
                .selectOne(Wrappers.<AccountDid>lambdaQuery().eq(AccountDid::getDidSymbol, didSymbol));
            if (accountDid == null) {
                return R.error(ResponseEnum.UserDoesNotExist);
            }
            String accountUuid = accountDid.getAccountUuid();
            // 查询记录是否存在
            List<VoucherAccredit> voucherAccredits = voucherAccreditMapper.selectList(
                Wrappers.<VoucherAccredit>lambdaQuery().eq(VoucherAccredit::getAccountUuid, accountUuid)
                    .eq(VoucherAccredit::getCodeType, 1).eq(VoucherAccredit::getType, type));
            if (CollectionUtil.isEmpty(voucherAccredits)) {
                Date date = new Date();
                // 插入记录
                VoucherAccredit voucherAccredit = new VoucherAccredit();
                voucherAccredit.setAccountUuid(accountUuid);
                voucherAccredit.setOperateUuid(accountDid.getOperateUuid());
                voucherAccredit.setVcId(didSymbol);
                voucherAccredit.setLogo(logo);
                voucherAccredit.setTitle(title);
                voucherAccredit.setOrgName(orgName);
                voucherAccredit.setType(type);
                voucherAccredit.setType(type);
                voucherAccredit.setCreateTime(date);
                voucherAccredit.setCodeType(1);
                voucherAccreditMapper.insert(voucherAccredit);
                message.put("time", DateUtil.format(date, DatePattern.NORM_DATETIME_PATTERN));
            }
        }
        pushInfo.pushBrowserEthInfo(didSymbol, message);
        return R.ok();
    }

    /**
     * 授权记录
     *
     * @param type 0-全部 1-DID 2-个人身份信息凭证
     * @param page 页码
     * @param pageSize 数量
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/07/03
     */
    @Override
    public R authorizationRecordsService(Integer type, int page, int pageSize) {
        Account userInfo = accountService.getUserInfo();
        // 当 type =1 时需要把 3 的也差出来 酒店随机码
        Integer totalCount = voucherAccreditMapper.queryAccreditCount(userInfo.getUuid(), type);
        int start = (page - 1) * pageSize;
        List<VoucherAccreditVo> voucherAccreditList =
            voucherAccreditMapper.queryAccreditPage(start, pageSize, userInfo.getUuid(), type);
        if (!ObjectUtil.equals(type, 1)) {
            if (CollectionUtil.isNotEmpty(voucherAccreditList)) {
                List<Integer> types = voucherAccreditList.stream().map(VoucherAccreditVo::getType).distinct()
                    .collect(Collectors.toList());
                List<VcCheckInActivity> vcCheckInActivities = vcCheckInActivityMapper.selectBatchIds(types);
                for (VoucherAccreditVo voucherAccreditVo : voucherAccreditList) {
                    for (VcCheckInActivity vcCheckInActivity : vcCheckInActivities) {
                        // 如果 type=2 替换掉title
                        if (ObjectUtil.equals(voucherAccreditVo.getCodeType(), 2)) {
                            if (vcCheckInActivity.getId().equals(voucherAccreditVo.getType())) {
                                voucherAccreditVo.setTitle(vcCheckInActivity.getName());

                            }
                        }
                    }
                }
            }
        }
        for (VoucherAccreditVo voucherAccreditVo : voucherAccreditList) {
            String logo = voucherAccreditVo.getLogo();
            // 适配DID签到系统图片路径
            if (ObjectUtil.equals(voucherAccreditVo.getCodeType(), 1)) {
                voucherAccreditVo.setLogo("https://did-check.jiewai.pro/upimages/" + logo);
            } else {
                voucherAccreditVo.setLogo(readImagepath + logo);
            }
        }
        PageUtils pageUtils = new PageUtils(totalCount, pageSize, page, voucherAccreditList);
        return R.okData(pageUtils);
    }

    /**
     * 移除授权记录
     * 
     * @param ids
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/07/03
     */
    @Override
    public R isDeleteService(List<Integer> ids) {
        voucherAccreditMapper.update(null, Wrappers.<VoucherAccredit>lambdaUpdate()
            .in(VoucherAccredit::getId, ids).set(VoucherAccredit::getDelState, 2));
        return R.ok();
    }

    /**
     * 信息补全
     * 
     * @param didSymbol
     * @param message
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/03/25
     */
    @Override
    public R informationCompletionService(String didSymbol, String message) {
        log.info("信息补全：{}", message);
        pushInfo.pushBrowserEthInfo1(didSymbol, message);
        return R.ok();
    }
}
