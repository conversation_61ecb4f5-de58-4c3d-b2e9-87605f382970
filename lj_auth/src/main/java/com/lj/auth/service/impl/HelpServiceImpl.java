package com.lj.auth.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lj.auth.common.R;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lj.auth.mapper.HelpMapper;
import com.lj.auth.domain.Help;
import com.lj.auth.service.HelpService;

import javax.annotation.Resource;

@Service
public class HelpServiceImpl extends ServiceImpl<HelpMapper, Help> implements HelpService {
    @Resource
    private HelpMapper helpMapper;

    /**
     * 获取帮助中心列表
     *
     * @param page     页
     * @param pageSize 页大小
     * @param keyword  关键字
     * @param type
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/29
     */
    @Override
    public R getHelpListService(int page, int pageSize, String keyword, Integer type) {
        Page<Help> helpPage = helpMapper.selectPage(new Page<>(page, pageSize),
            Wrappers.<Help>lambdaQuery().eq(Help::getType, type)
                .and(wrapper -> wrapper.like(Help::getContent, keyword).or().like(Help::getTitle, keyword)));
        return R.okData(helpPage);
    }
}
