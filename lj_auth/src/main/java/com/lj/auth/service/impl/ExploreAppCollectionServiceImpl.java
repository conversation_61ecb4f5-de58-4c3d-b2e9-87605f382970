package com.lj.auth.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lj.auth.common.R;
import com.lj.auth.domain.Account;
import com.lj.auth.domain.ExploreApp;
import com.lj.auth.domain.vo.ExploreAppVo;
import com.lj.auth.mapper.ExploreAppMapper;
import com.lj.auth.service.AccountService;
import com.lj.auth.util.PageUtils;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lj.auth.domain.ExploreAppCollection;
import com.lj.auth.mapper.ExploreAppCollectionMapper;
import com.lj.auth.service.ExploreAppCollectionService;

import javax.annotation.Resource;

@Service
public class ExploreAppCollectionServiceImpl extends
    ServiceImpl<ExploreAppCollectionMapper, ExploreAppCollection> implements ExploreAppCollectionService {

    @Resource
    private AccountService accountService;
    @Resource
    private ExploreAppCollectionMapper exploreAppCollectionMapper;

    /**
     * @param appId 应用appId
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/01
     */
    @Override
    public R addCollectionService(Integer appId) {

        Account userInfo = accountService.getUserInfo();
        // 判断是否收藏过
        ExploreAppCollection one =
            this.getOne(Wrappers.<ExploreAppCollection>lambdaQuery().eq(ExploreAppCollection::getAppId, appId)
                .eq(ExploreAppCollection::getAccountUuid, userInfo.getUuid()));
        if (one != null) {
            return R.ok();
        }
        ExploreAppCollection exploreAppCollection =
            new ExploreAppCollection().setAppId(appId).setAccountUuid(userInfo.getUuid());
        this.save(exploreAppCollection);
        return R.okData(exploreAppCollection.getId());
    }

    /**
     * 取消收藏
     * 
     * @param id
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/01
     */
    @Override
    public R unAddCollectionService(Integer id) {
        Account userInfo = accountService.getUserInfo();
        // 判断是否收藏过--收藏列表
        ExploreAppCollection one = this.getOne(Wrappers.<ExploreAppCollection>lambdaQuery()
            .eq(ExploreAppCollection::getAccountUuid, userInfo.getUuid())
            .eq(ExploreAppCollection::getId, id));
        if (one == null) {
            one = this.getOne(Wrappers.<ExploreAppCollection>lambdaQuery()
                .eq(ExploreAppCollection::getAccountUuid, userInfo.getUuid())
                .eq(ExploreAppCollection::getAppId, id));
        }
        if (one != null) {
            this.removeById(one.getId());
            return R.okData(one.getId());
        }
        return R.ok();
    }

    /**
     * 获取收藏列表
     * 
     * @param page
     * @param pageSize
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/01
     */
    @Override
    public R getCollectionListService(int page, int pageSize) {
        Account userInfo = accountService.getUserInfo();
        Integer totalCount = exploreAppCollectionMapper.appCollectionCount(userInfo.getUuid());

        int start = (page - 1) * pageSize;

        List<ExploreAppVo> appCollectionList = new ArrayList<>();
        if (start < totalCount) {
            appCollectionList =
                exploreAppCollectionMapper.getAppCollectionPage(start, pageSize, userInfo.getUuid());
        }
        PageUtils pageUtils = new PageUtils(totalCount, pageSize, page, appCollectionList);

        return R.okData(pageUtils);
    }
}
