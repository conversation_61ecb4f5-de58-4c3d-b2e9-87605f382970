package com.lj.auth.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lj.auth.common.R;
import com.lj.auth.domain.Account;
import com.lj.auth.domain.AccountDid;
import com.lj.auth.domain.DidQrCodeRecord;
import com.lj.auth.domain.vo.QrVo;
import com.lj.auth.exception.ServiceException;
import com.lj.auth.mapper.AccountDidMapper;
import com.lj.auth.mapper.AccountMapper;
import com.lj.auth.mapper.DidQrCodeRecordMapper;
import com.lj.auth.service.AccountService;
import com.lj.auth.service.GlobalConfigService;
import com.lj.auth.util.IpUtil;
import com.lj.auth.util.MovieUtil;
import com.lj.auth.util.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lj.auth.domain.DidQrCode;
import com.lj.auth.mapper.DidQrCodeMapper;
import com.lj.auth.service.DidQrCodeService;
import static com.lj.auth.common.CommonConstant.*;

@Service
@Slf4j
public class DidQrCodeServiceImpl extends ServiceImpl<DidQrCodeMapper, DidQrCode>
    implements DidQrCodeService {
    @Resource
    private AccountService accountService;
    @Resource
    private DidQrCodeRecordMapper didQrCodeRecordMapper;
    @Resource
    private RedisUtils redisUtils;
    @Resource
    private GlobalConfigService globalConfigService;
    @Resource
    private AccountDidMapper accountDidMapper;
    @Resource
    private AccountMapper accountMapper;


    /**
     * 获取DID信息二维码
     * 
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/07/16
     */
    @Override
    public R getQrCodeService(String appId) {
        Map<String, Object> result = new HashMap<>();
        // 设置后台开关
        String codeSwitch = globalConfigService.getGlobalConfig(did_Qr_Code_Switch);
        Account userInfo = accountService.getUserInfo();
        String uuid = userInfo.getUuid();
        String didSymbol = userInfo.getDidSymbol();
        if (StrUtil.isBlank(didSymbol)) {
            return R.error("DID为空，请前往申领");
        }
        if (StrUtil.equals(codeSwitch, "1")) {
            // 获取平台信息
            DidQrCode didQrCode = this.getOne(
                Wrappers.<DidQrCode>lambdaQuery().eq(DidQrCode::getAppId, appId).eq(DidQrCode::getState, 1));
            if (didQrCode == null) {
                return R.error("平台暂未开放，请前往申请");
            }
            String applicationId = didQrCode.getApplicationId().toString();

            // 生成SN
            String sn = applicationId + "_" + getRandomNumStr(8);
            // 生成过期时间
            // 获取缓存过期时间
            String expiryTimeStr = globalConfigService.getGlobalConfig(DID_QR_CODE_EXPIRATION_TIME);
            // 计算5分钟后的时间
            // Date fiveMinutesLater = DateUtil.offsetSecond(DateUtil.date(), Integer.valueOf(expiryTimeStr));
            // 打印5分钟后的日期时间
            // log.info("5分钟后的时间: " + fiveMinutesLater.toString());
            // String expirationTime = fiveMinutesLater.toString();
            result.put("sn", sn);
            // 存入缓存
            redisUtils.set(sn, didSymbol, Long.valueOf(expiryTimeStr));
        } else {
            result.put("sn", didSymbol);
        }
        return R.okData(result);
    }

    /**
     * 通过二维码信息获取DID
     * 
     * @param data
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/07/16
     */
    @Override
    public R getDIDByQrService(HttpServletRequest request, Map<String, Object> data) {
        log.info("获取二维码信息请求参数：{}", data);
        if (MapUtil.isEmpty(data)) {
            return R.error("参数为空");
        }
        if (!(data.containsKey("sign") && data.containsKey("appId") && data.containsKey("sn")
            && data.containsKey("currentTimestamp"))) {
            return R.error("缺少参数");
        }
        String appId = data.get("appId").toString();
        // 获取平台信息
        DidQrCode didQrCode = this.getOne(
            Wrappers.<DidQrCode>lambdaQuery().eq(DidQrCode::getAppId, appId).eq(DidQrCode::getState, 1));
        if (didQrCode == null) {
            return R.error("平台暂未开放，请前往申请");
        }
        // 校验参数加密是否正确
        // 验证参数签名
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("sn", data.get("sn"));
        paramMap.put("currentTimestamp", data.get("currentTimestamp"));
        paramMap.put("appId", data.get("appId"));
        // 校验参数
        Map<String, Object> signedParamsMap =
            MovieUtil.getSignedParamsMap(paramMap, didQrCode.getAppSecret());
        if (!data.get("sign").equals(signedParamsMap.get("sign"))) {
            return R.error("参数签名错误");
        }
        // 校验时间戳
        String currentTimestamp = data.get("currentTimestamp").toString();
        Long currentTimestampL = Long.valueOf(currentTimestamp);
        // 计算五分钟之前的 Unix 时间戳
        Date date = new Date();
        DateTime dateTime = DateUtil.offsetSecond(date, -5 * 60);
        // 判断时间
        if (!(dateTime.getTime() <= currentTimestampL)) {
            log.info("当前时间戳：{}", dateTime.getTime());
            return R.error("不是当前时间");
        }
        Object didsymbol = null;
        // 判断 SN 是否是 DID
        String sn = (String)data.get("sn");
        if (sn.length() == 10) {
            didsymbol = redisUtils.get(sn);
            if (didsymbol == null) {
                return R.error("二维码已过期，请重新扫码");
            }
        } else {
            didsymbol = sn;
        }
        // 插入扫码记录
        DidQrCodeRecord didQrCodeRecord = new DidQrCodeRecord();
        didQrCodeRecord.setApplicationId(didQrCode.getApplicationId())
            .setExpirationTime(DateUtil.date(currentTimestampL));
        didQrCodeRecordMapper.insert(didQrCodeRecord);
        // 返回个人信息
        QrVo results = didQrCodeRecordMapper.selectDIDUserInfo(didsymbol);
        return R.okData(results);
    }

    public String getRandomNumStr(int len) {
        int randomNum = (int)(Math.random() * 90000000 + 10000000);
        String randomStr = String.valueOf(randomNum);
        return randomStr.substring(0, 8);
    }
}
