package com.lj.auth.service;

import com.lj.auth.common.R;
import com.lj.auth.domain.Account;
import com.lj.auth.domain.RechargeRecord;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lj.auth.domain.vo.WithdrawServiceChargeInfo;

import java.math.BigDecimal;
import java.util.Date;

public interface RechargeRecordService extends IService<RechargeRecord> {

    /**
     * 插入资产消耗纪录
     *
     * @param accountUuid 用户UUID
     * @param amount 金额
     * @param reqTransactionSn 交易流水号
     * @param type 类型 1:充值 2:提现 3：域名注册4：返佣 5：服务订购6:能量充值
     * @return {@link RechargeRecord }
     * <AUTHOR>
     * @date 2024/04/02
     */
    RechargeRecord insertRecord(String accountUuid, BigDecimal amount, String reqTransactionSn, int type);

    /**
     * 修改资产消耗记录
     *
     * @param reqTransactionSn 交易流水号
     * @param state 状态( 1=成功2=充值中\审核中3=失败4=用户取消)
     * <AUTHOR>
     * @date 2024/04/02
     */
    void updateRecord(String reqTransactionSn, int state);

    /**
     * 获取资金明细
     * 
     * @param page 页
     * @param pageSize 页码
     * @param type 0-全部类型 1:充值 2:提现 3：域名注册4：返佣 5：服务订购6:能量充值 7-购买域名消耗 8-购买域名退款 9-一口价支出，10一口价收入 11-竞价支出 12-竞价退款 13-竞价收入
     * @param date 日期
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/03
     */
    R queryFundListService(int page, int pageSize, Integer type, Date date);

    /**
     * 获取资金明细详情
     * 
     * @param id id
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/03
     */
    R queryFundDetailService(Integer id);

    R queryFundDetailService1(Integer id);

    /**
     * 添加提现记录
     * 
     * @param amount 金额
     * @param cardId 银行卡id
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/03
     */
    R addWithdrawalRecordService(BigDecimal amount, Integer cardId);

    void extracted(BigDecimal amount, Integer cardId, Account userInfo,
        WithdrawServiceChargeInfo withdrawServiceChargeInfo, BigDecimal serviceCharge);

    /**
     * 获取提现服务费率
     *
     * @param withdrawAmount 提现金额
     * @return {@link R}
     */
    WithdrawServiceChargeInfo withdrawServChargeService(BigDecimal withdrawAmount);

    /**
     * 资金关联订单信息
     * 
     * @param orderId
     * @param typeId
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/06/05
     */
    R getFundRelatedOrdersService(Integer orderId, Integer typeId);

    /**
     * 取消提现
     * @param id
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/06/06
     */
    R getCancelReflectionService(Integer id);

    /**
     * 门户远程调用使用
     * @param uuid
     * @param page
     * @param pageSize
     * @param type
     * @param date
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/01/17
     */
    R queryFundListFService(String uuid, int page, int pageSize, Integer type, Date date);
}
