package com.lj.auth.service;

import com.lj.auth.domain.NoticeAccount;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

public interface NoticeAccountService extends IService<NoticeAccount> {
    
    
    void noticeAccountService(String accountUuid, Integer versionId);
    
    int updateBatchSelective(List<NoticeAccount> list);
    
    int batchInsert(List<NoticeAccount> list);
}

