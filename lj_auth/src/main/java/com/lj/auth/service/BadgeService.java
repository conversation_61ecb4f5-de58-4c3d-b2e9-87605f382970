package com.lj.auth.service;

import com.alibaba.fastjson2.JSONObject;
import com.lj.auth.common.R;
import com.lj.auth.domain.Badge;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletRequest;

public interface BadgeService extends IService<Badge>{


    /**
     * 获取个人挂件列表
     * @return {@link String }
     */
    R getBadgePendantService();

    /**
     * @param request
     * @param paramJson
     * @return {@link R }
     */
    R setPendantService(HttpServletRequest request, JSONObject paramJson);

    /**
     * 获取个人最新徽章最多三个
     * @param request
     * @param paramJson
     * @return {@link R }
     */
    R latestBadgeService(HttpServletRequest request, JSONObject paramJson);

    /**
     * 获取徽章列表
     *
     * @param request
     * @param paramJson
     * @return {@link R }
     */
    R listService(HttpServletRequest request, JSONObject paramJson);

    /**
     * 设置徽章
     * @param request
     * @param paramJson
     * @return {@link R }
     */
    R setService(HttpServletRequest request, JSONObject paramJson);

    /**
     * 取消佩戴徽章
     *
     * @param request
     * @param paramJson
     * @return {@link R }
     */
    R cancelService(HttpServletRequest request, JSONObject paramJson);

    /**
     * 不佩戴挂件
     *
     * @param request
     * @param paramJson
     * @return {@link R }
     */
    R cancelPendantService(HttpServletRequest request, JSONObject paramJson);



    /**
     * 刷新挂件
     * @param request
     * @param paramJson
     * @return {@link R }
     */
    R refreshPendantService(HttpServletRequest request, JSONObject paramJson);
}
