package com.lj.auth.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lj.auth.common.R;
import com.lj.auth.domain.Account;
import com.lj.auth.service.AccountService;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lj.auth.domain.InvoiceMessage;
import com.lj.auth.mapper.InvoiceMessageMapper;
import com.lj.auth.service.InvoiceMessageService;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

@Service
public class InvoiceMessageServiceImpl extends ServiceImpl<InvoiceMessageMapper, InvoiceMessage>
    implements InvoiceMessageService {

    @Resource
    private AccountService accountService;
    @Resource
    private InvoiceMessageMapper invoiceMessageMapper;

    /**
     * 查询开票信息
     *
     * @param page 页
     * @param pageSize 页大小
     * @param type 主体类型 1-个人 2-企业 3-组织
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/28
     */
    @Override
    public R getPage(Integer type, Integer page, Integer pageSize) {
        Account userInfo = accountService.getUserInfo();
        Page<InvoiceMessage> reesult =
            this.page(new Page<>(page, pageSize), Wrappers.<InvoiceMessage>lambdaQuery()
                .eq(InvoiceMessage::getAccountUuid, userInfo.getUuid()).eq(InvoiceMessage::getType, type));
        return R.okData(reesult);
    }

    @Override
    public R addService(InvoiceMessage invoiceMessage) {
        this.save(invoiceMessage);
        return R.ok();
    }

    @Override
    public R updateService(InvoiceMessage invoiceMessage) {
        invoiceMessageMapper.updateInvoiceMessage(invoiceMessage);
        return R.ok();
    }

    @Override
    public R del(Long id) {
        this.removeById(id);
        return R.ok();
    }

    @Override
    @Transactional
    public R setDefault(Long id) {
        Account userInfo = accountService.getUserInfo();
        // 先设置所有地址为非默认
        boolean update = this.update(Wrappers.<InvoiceMessage>lambdaUpdate()
            .eq(InvoiceMessage::getAccountUuid, userInfo.getUuid()).set(InvoiceMessage::getState, 2));
        this.update(Wrappers.<InvoiceMessage>lambdaUpdate().eq(InvoiceMessage::getId, id)
            .set(InvoiceMessage::getState, 1));
        return R.ok();
    }
}
