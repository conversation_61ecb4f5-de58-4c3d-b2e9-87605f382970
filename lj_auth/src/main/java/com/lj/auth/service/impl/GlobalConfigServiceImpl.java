package com.lj.auth.service.impl;

import static com.lj.auth.common.CommonConstant.*;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lj.auth.common.R;
import com.lj.auth.domain.GlobalConfig;
import com.lj.auth.domain.ModuleSwitch;
import com.lj.auth.exception.ServiceException;
import com.lj.auth.mapper.GlobalConfigMapper;
import com.lj.auth.mapper.ModuleSwitchMapper;
import com.lj.auth.service.GlobalConfigService;
import com.lj.auth.util.RedisUtils;

import cn.hutool.core.util.StrUtil;

@Service
public class GlobalConfigServiceImpl extends ServiceImpl<GlobalConfigMapper, GlobalConfig>
    implements GlobalConfigService {
    @Resource
    private GlobalConfigMapper globalConfigMapper;
    @Resource
    private RedisUtils redisUtils;
    @Resource
    private ModuleSwitchMapper moduleSwitchMapper;

    @Override
    public R getInvoiceConfig() {
        Map result = redisUtils.get(INVOICE_CONFIG, Map.class);
        if (result == null) {
            Map invoiceMap = new HashMap<String, Object>();
            invoiceMap.put(TAX_RATE, globalConfigMapper.queryConfig(TAX_RATE));
            invoiceMap.put(INVOICE_CONTENT, globalConfigMapper.queryConfig(INVOICE_CONTENT));
            redisUtils.set(INVOICE_CONFIG, invoiceMap, 60L);
            return R.okData(invoiceMap);
        }
        return R.okData(result);
    }

    /**
     * 获取通用系统配置
     * 
     * @param key key
     * @return {@link String }
     * <AUTHOR>
     * @date 2024/04/01
     */
    @Override
    public String getGlobalConfig(String key) {
        String data = (String)redisUtils.get(key);
        if (StrUtil.isBlank(data)) {
            String value = globalConfigMapper.queryConfig(key);
            if (StrUtil.isBlank(value)) {
                throw new ServiceException("缺少配置请前往配置，key:" + key);
            } else {
                redisUtils.set(key, value, 60L);
            }
        } else {
            return data;
        }
        return (String)redisUtils.get(key);
    }

    /**
     * 获取签到配置
     *
     * @param key
     * @return {@link String}
     */
    @Override
    public List<BigDecimal> getSignGlobalConfig(String key) {
        String data = (String)redisUtils.get(key);
        if (StrUtil.isBlank(data)) {
            String value = globalConfigMapper.queryConfig(key);
            if (StrUtil.isBlank(value)) {
                throw new ServiceException("缺少配置请前往配置，key:" + key);
            } else {
                data = value;
                redisUtils.set(key, value, 60L);
            }
        }
        List<BigDecimal> bigDecimalList =
            Arrays.stream(data.split(",")).map(BigDecimal::new).collect(Collectors.toList());
        return bigDecimalList;
    }

    /**
     * 获取提现协议
     * 
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/03
     */
    @Override
    public R getWithdrawalProtocolService() {
        String result = (String)redisUtils.get(WITHDRAWAL_INSTRUCTIONS);
        if (StrUtil.isBlank(result)) {
            result = globalConfigMapper.queryConfig(WITHDRAWAL_INSTRUCTIONS);
            redisUtils.set(WITHDRAWAL_INSTRUCTIONS, result, 60L);
            return R.okData(result);
        }
        return R.okData(result);
    }

    /**
     * 获取功能模块开关
     * 
     * @param channel
     * @param version
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/04/07
     */
    @Override
    public R getModuleSwitchService(String channel, String version) {
        Map<String, Object> result = new HashMap<>();
        List<ModuleSwitch> functions = moduleSwitchMapper.selectList(Wrappers.<ModuleSwitch>lambdaQuery()
            .eq(ModuleSwitch::getChannel, channel).eq(ModuleSwitch::getVersion, version));
        Map<String, Object> map = new HashMap<>();
        // 转为map key为key value
        functions.forEach(item -> {
            map.put(item.getKey(), item.getValue());
        });
        result.put("data", map);
        return R.okData(result);
    }

    @Override
    public R addSwitchService(ModuleSwitch function) {
        moduleSwitchMapper.insert(function);
        return R.ok();
    }
}
