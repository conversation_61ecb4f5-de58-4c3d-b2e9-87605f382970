package com.lj.auth.service;

import com.lj.auth.common.R;
import com.lj.auth.domain.NftGetRecord;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lj.auth.domain.NftGetRecordDomain;
import com.lj.auth.domain.resp.InsertIPFSResp;
import com.lj.auth.domain.resp.InsertNftRecordPreResp;
import com.lj.auth.domain.resp.NftGetRecordResp;

import java.util.List;

public interface NftGetRecordService extends IService<NftGetRecord>{


    R queryGetNftInfo();

    NftGetRecordResp queryGetNftInfoV2();

    R queryGetNftRecord();

    R queryGetNftRecordV2(Integer pageNum, Integer pageSize);

    Long insertNftRecordPre(String uuid, String chainAccountAddress, Integer opbChainId, Integer count, String contractAddress);

    InsertNftRecordPreResp insertNftRecordPreV2(String uuid, String chainAccountAddress, Integer opbChainId, Integer count, String contractAddress);


    String insertNftRecord(String uuid, String chainAccountAddress, Integer opbChainId, Integer count, Integer isSuccess, List<Integer> tokenIds, String contractAddress,Long nftRecordId);


    InsertIPFSResp insertNftRecordV2(Long nftRecordId, NftGetRecordDomain nftGetRecordDomain , String uuid, String chainAccountAddress, Integer opbChainId, Integer isSuccess, Integer tokenId, String contractAddress);



     void validateMintInfo(String uuid, Integer count, String contractAddress, List<Integer> tokenIds);
     void validateMintInfoV2(String uuid, String contractAddress, Integer tokenId);
}
