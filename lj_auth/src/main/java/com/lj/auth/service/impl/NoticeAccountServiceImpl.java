package com.lj.auth.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lj.auth.domain.AccountDid;
import com.lj.auth.domain.NoticeAccount;
import com.lj.auth.domain.SystemNotice;
import com.lj.auth.domain.Version;
import com.lj.auth.mapper.AccountDidMapper;
import com.lj.auth.mapper.NoticeAccountMapper;
import com.lj.auth.mapper.SystemNoticeMapper;
import com.lj.auth.mapper.VersionMapper;
import com.lj.auth.service.NoticeAccountService;
import com.lj.auth.util.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Service
public class NoticeAccountServiceImpl extends ServiceImpl<NoticeAccountMapper, NoticeAccount> implements NoticeAccountService {
    @Resource
    private NoticeAccountMapper noticeAccountMapper;
    @Resource
    private AccountDidMapper accountDidMapper;
    @Resource
    private SystemNoticeMapper systemNoticeMapper;
    @Resource
    private VersionMapper versionMapper;
    
    private static final int BATCH_SIZE = 1000; // 增加每批处理的数据量
    private static final int MAX_BATCH_THREADS = 5; // 最大并发批次数
    private static final ExecutorService executorService = Executors.newFixedThreadPool(MAX_BATCH_THREADS); // 创建固定大小的线程池
    
    @Override
    // @Transactional
    public void noticeAccountService(String accountUuid, Integer versionId) {
        try {
            // 指定用户推送
            // if (StringUtils.isNotBlank(accountUuid)) {
            //     processOneAccount(accountUuid);
            //     return;
            // }
            
            // 创建系统通知
            List<SystemNotice> systemNoticeList = createSystemNotices(versionId);
            if (CollectionUtils.isEmpty(systemNoticeList)) {
                return;
            }
            
            // 符合条件的用户, 推送一条系统通知
            if (systemNoticeList.size() == 1) {
                // 使用分页查询处理大数据量
                long page = 1;
                long pageSize = BATCH_SIZE;
                
                while (true) {
                    // 分页查询AccountDid用户
                    LambdaQueryWrapper<AccountDid> wrapper = new LambdaQueryWrapper<>();
                    wrapper.isNull(AccountDid::getIdNoEnc)
                            .or()
                            .isNull(AccountDid::getIdNameEnc)
                            .or()
                            .isNull(AccountDid::getPhotoEnc);
                    Page<AccountDid> pageResult = accountDidMapper.selectPage(new Page<>(page, pageSize), wrapper);
                    List<AccountDid> accountDids = pageResult.getRecords();
                    
                    if (CollectionUtils.isEmpty(accountDids)) {
                        break;
                    }
                    
                    // 处理当前页的数据
                    SystemNotice systemNotice = systemNoticeList.get(0);
                    List<NoticeAccount> batchNoticeAccounts = new ArrayList<>();
                    
                    for (AccountDid accountDid : accountDids) {
                        NoticeAccount noticeAccount = buildNoticeAccount(
                                systemNotice.getId(),
                                accountDid.getAccountUuid(),
                                systemNotice.getSource()
                        );
                        batchNoticeAccounts.add(noticeAccount);
                    }
                    
                    // 批量保存通知记录
                    // saveBatch(batchNoticeAccounts, BATCH_SIZE);
                    noticeAccountMapper.batchInsert(batchNoticeAccounts);
                    
                    // 如果当前页是最后一页，结束循环
                    if (pageResult.getCurrent() >= pageResult.getPages()) {
                        break;
                    }
                    
                    page++;
                }
            }
        } catch (Exception e) {
            log.error("创建DID信息补全通知失败", e);
            throw new RuntimeException("创建DID信息补全通知失败", e);
        }
    }
    
    private void processOneAccount(String accountUuid) {
        if (StringUtils.isBlank(accountUuid)) {
            throw new IllegalArgumentException("账户UUID不能为空");
        }
        
        AccountDid accountDid = accountDidMapper.queryByAccountUuid(accountUuid);
        if (null == accountDid) {
            return;
        }
        
        // 创建系统通知
        List<SystemNotice> systemNoticeList = createSystemNotices(null);
        
        // 创建单个用户通知
        createSingleNoticeAccount(systemNoticeList, accountUuid);
    }
    
    private List<SystemNotice> createSystemNotices(Integer versionId) {
        List<Version> versionList;
        if (null == versionId) {
            versionList = versionMapper.selectList(null);
        } else {
            Version version = versionMapper.selectById(versionId);
            versionList = List.of(version);
        }
        if (CollectionUtils.isEmpty(versionList)) {
            return new ArrayList<>();
        }
        
        List<SystemNotice> systemNoticeList = new ArrayList<>();
        Date date = new Date();
        Date endDate = DateUtil.addDateDays(date, 180);
        
        for (Version version : versionList) {
            SystemNotice systemNotice = buildSystemNotice(date, endDate, version);
            systemNoticeMapper.insert(systemNotice);
            systemNoticeList.add(systemNotice);
        }
        
        return systemNoticeList;
    }
    
    private SystemNotice buildSystemNotice(Date startDate, Date endDate, Version version) {
        SystemNotice systemNotice = new SystemNotice();
        systemNotice.setTitle("DID信息补全");
        systemNotice.setDescription("DID信息补全");
        systemNotice.setContent("<p><img src=\"https://wallet.ylzh.pro/upimages/test/f90de95f-8789-4f08-81c9-0043e68392471742376772349.png\" alt=\"dfa\" width=\"960\" height=\"1278\" /></p>");
        systemNotice.setCreateTime(startDate);
        systemNotice.setType(2);
        systemNotice.setSource(4);
        systemNotice.setReminderStartTime(startDate);
        systemNotice.setReminderEndTime(endDate);
        systemNotice.setFrequency(2);
        systemNotice.setChannel(version.getPlatform());
        systemNotice.setEdition(version.getVersion());
        systemNotice.setIsAppoint(1);
        return systemNotice;
    }
    
    private void createSingleNoticeAccount(List<SystemNotice> systemNoticeList, String accountUuid) {
        if (CollectionUtils.isEmpty(systemNoticeList) || StringUtils.isBlank(accountUuid)) {
            return;
        }
        
        List<NoticeAccount> noticeAccounts = new ArrayList<>();
        for (SystemNotice systemNotice : systemNoticeList) {
            NoticeAccount noticeAccount = buildNoticeAccount(systemNotice.getId(), accountUuid, systemNotice.getSource());
            noticeAccounts.add(noticeAccount);
        }
        
        saveBatch(noticeAccounts);
    }
    
    private NoticeAccount buildNoticeAccount(Long noticeId, String accountUuid, Integer source) {
        NoticeAccount noticeAccount = new NoticeAccount();
        noticeAccount.setNoticeId(noticeId);
        noticeAccount.setAccountUuid(accountUuid);
        noticeAccount.setState(1);
        noticeAccount.setSource(source);
        noticeAccount.setReadTime(null);
        noticeAccount.setTodayIsRead(null);
        return noticeAccount;
    }
    
    @Override
    public int updateBatchSelective(List<NoticeAccount> list) {
        return baseMapper.updateBatchSelective(list);
    }
    
    @Override
    public int batchInsert(List<NoticeAccount> list) {
        return baseMapper.batchInsert(list);
    }
}

