package com.lj.auth.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lj.auth.common.R;
import com.lj.auth.domain.VoucherAccredit;

/**
 * <AUTHOR>
 * @Description
 * @date 2024/4/26 15:58
 */
public interface VoucherAccreditService extends IService<VoucherAccredit> {

    /**
     * 凭证扫码签到 2024华中Web3.0大会-暨界外科技新品发布会 武汉界外科技有限公司
     * 
     * @param vcId 凭证id
     */
    void voucherSignIn(String vcId);

    Integer getCountLj();

    /**
     *
     * @param vcId
     */
    void voucherSignInCq(String vcId);

    Integer getCountCq();

    /**
     * 重庆开州
     * 
     * @param vcId
     */
    void voucherSignInCqkz(String vcId);

    Integer getCountCqkz();

    /**
     * 四川20240619
     * 
     * @param vcId
     */
    void voucherSignInSsps(String vcId);

    Integer getCountSsps();

    /**
     * 添加凭证授权记录
     *
     * @param didSymbol
     * @param logo
     * @param title
     * @param orgName
     * @param type
     * @param code
     * @param failReason
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/06/27
     */
    R addAuthorizationService(String didSymbol, String logo, String title, String orgName, Integer type,
        String code, String failReason);

    /**
     * 授权记录
     *
     * @param type 0-全部 1-DID 2-个人身份信息凭证
     * @param page 页码
     * @param pageSize 数量
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/07/03
     */
    R authorizationRecordsService(Integer type, int page, int pageSize);

    /**
     * 移除授权记录
     * 
     * @param ids
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/07/03
     */
    R isDeleteService(List<Integer> ids);

    /**
     * DID信息补全
     * @param didSymbol
     * @param message
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/03/25
     */
    R informationCompletionService( String didSymbol, String message);
}
