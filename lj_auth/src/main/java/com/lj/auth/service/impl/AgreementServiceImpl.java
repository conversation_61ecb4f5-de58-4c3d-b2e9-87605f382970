package com.lj.auth.service.impl;

import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lj.auth.common.R;
import com.lj.auth.domain.Account;
import com.lj.auth.domain.AgreementAccountRecord;
import com.lj.auth.domain.AgreementVersion;
import com.lj.auth.domain.LjAbout;
import com.lj.auth.domain.Param.*;
import com.lj.auth.domain.Result.AccountAgreementDetail;
import com.lj.auth.domain.Result.PrivacyAgreementDetai;
import com.lj.auth.domain.Result.ValidAgreementVersionResult;
import com.lj.auth.domain.vo.AgreementAccountRecordVo;
import com.lj.auth.domain.vo.AgreementVersionVo;
import com.lj.auth.mapper.*;
import com.lj.auth.service.AccountService;
import com.lj.auth.service.AgreementService;
import com.lj.auth.util.PageUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @describe
 */
@Service
public class AgreementServiceImpl implements AgreementService {

    @Resource
    private AccountService accountService;

    @Resource
    private AccountMapper accountMapper;

    @Resource
    private AgreementAccountRecordMapper agreementAccountRecordMapper;

    @Resource
    private AgreementVersionMapper agreementVersionMapper;

    @Resource
    private LjAboutMapper ljAboutMapper;




    /**
     * 校验用户是否有版本更新记录
     * @param request
     * @return
     */
    @Transactional
    @Override
    public ValidAgreementVersionResult validVersion(HttpServletRequest request) {
        ValidAgreementVersionResult validAgreementVersionResult=new ValidAgreementVersionResult();

        AccountAgreementDetail accountAgreementDetail=new AccountAgreementDetail();
        PrivacyAgreementDetai privacyAgreementDetai=new PrivacyAgreementDetai();

        //获取用户信息
        Account userInfo = accountService.getUserInfo();
        Assert.notNull(userInfo,"用户信息不存在");
        String accountUUID = userInfo.getUuid();
        //注册来源：2-灵戒App 1-域名门户 0-CMS运营账号 3-游客模式
        Integer registrationSource = userInfo.getRegistrationSource();
        if(registrationSource==4){
            validAgreementVersionResult.setAccountAgreementDetail(accountAgreementDetail);
            validAgreementVersionResult.setPrivacyAgreementDetai(privacyAgreementDetai);
            return validAgreementVersionResult;
        }

        Date nowDate = new Date();

        //用户协议是否已读
        Boolean accountAgreemtnFlag=false;
        //隐私协议是否已读
        Boolean privacyAgreemtnFlag=false;

        //获取最新的协议版本

        //用户协议
        AgreementVersion accountAgreementVersion = agreementVersionMapper.queryLatestAgreement(AgreementVersion.AGREEMENT_TYPE_ACCOUNT_AGREEMENT);
        if(accountAgreementVersion!=null){
            accountAgreementDetail.setAccountAgreementLatestVersion(accountAgreementVersion.getVersionNumber());
        }else {
            accountAgreemtnFlag=true;
        }


        //隐私协议
        AgreementVersion privacyAgreementVersion = agreementVersionMapper.queryLatestAgreement(AgreementVersion.AGREEMENT_TYPE_PRIVACY_AGREEMENT);
        if(privacyAgreementVersion!=null){
            privacyAgreementDetai.setPrivacyAgreementLatestVersion(privacyAgreementVersion.getVersionNumber());
        }else {
            privacyAgreemtnFlag=true;
        }


        //判断是否为新用户
        AgreementAccountRecord latestAgreementAccountRecord = agreementAccountRecordMapper.queryLatestVersionByAccountUUID(accountUUID);

        //新用户，设置当前协议信息为已读
        if(latestAgreementAccountRecord==null){
            //设置用户协议已读
            if(accountAgreementVersion!=null){
                AgreementAccountRecord agreementAccountRecord=new AgreementAccountRecord();
                agreementAccountRecord.setVersionId(accountAgreementVersion.getVersionId());
                agreementAccountRecord.setAccountUuid(accountUUID);
                String confirmContent="用户阅读并接受"+accountAgreementVersion.getVersionTitle()+accountAgreementVersion.getVersionNumber();
                agreementAccountRecord.setConfirmContent(confirmContent);
                agreementAccountRecord.setReadAndConfimTime(nowDate);
                agreementAccountRecord.setUpdateTime(nowDate);
                agreementAccountRecordMapper.insert(agreementAccountRecord);

                accountAgreementDetail.setAccountAgreementReadAndConfirmVersion(accountAgreementVersion.getVersionNumber());
                accountAgreementDetail.setAccountAgreementReadAndConfirmTime(nowDate);
            }

            //设置隐私协议已读
            if(privacyAgreementVersion!=null){
                AgreementAccountRecord agreementAccountRecord=new AgreementAccountRecord();
                agreementAccountRecord.setVersionId(privacyAgreementVersion.getVersionId());
                agreementAccountRecord.setAccountUuid(accountUUID);
                agreementAccountRecord.setConfirmContent(getConfirmConent(privacyAgreementVersion));
                agreementAccountRecord.setReadAndConfimTime(nowDate);
                agreementAccountRecord.setUpdateTime(nowDate);
                agreementAccountRecordMapper.insert(agreementAccountRecord);

                privacyAgreementDetai.setPrivacyAgreementReadAndConfirmVersion(privacyAgreementVersion.getVersionNumber());
                privacyAgreementDetai.setPrivacyAgreementReadTime(nowDate);
            }

            //设置协议已读标识
            accountAgreemtnFlag=true;
            privacyAgreemtnFlag=true;

        }else {
            //判断用户协议是否已读
            if(accountAgreementVersion!=null){
                AgreementAccountRecord agreementAccountRecord = agreementAccountRecordMapper.queryByVersionIdAndAccountUUID(accountAgreementVersion.getVersionId(), accountUUID);
                //用户协议需要弹窗
                if(agreementAccountRecord!=null){
                    accountAgreemtnFlag=true;
                }
                AgreementAccountRecordVo accountLatestAgreemtnRecord = agreementAccountRecordMapper.queryLatestRecordVoByAccountUUIDAndType(accountUUID,AgreementVersion.AGREEMENT_TYPE_ACCOUNT_AGREEMENT);
                if(accountLatestAgreemtnRecord!=null){
                    String accountAgreementHistoryVersionNumber = accountLatestAgreemtnRecord.getVersionNumber();
                    accountAgreementDetail.setAccountAgreementReadAndConfirmVersion(accountAgreementHistoryVersionNumber);
                    accountAgreementDetail.setAccountAgreementReadAndConfirmTime(accountLatestAgreemtnRecord.getReadAndConfimTime());
                }
            }

            //判断隐私协议是否已读
            if(privacyAgreementVersion!=null){
                AgreementAccountRecord agreementAccountRecord = agreementAccountRecordMapper.queryByVersionIdAndAccountUUID(privacyAgreementVersion.getVersionId(), accountUUID);
                //隐私协议需要弹窗
                if(agreementAccountRecord!=null){
                    privacyAgreemtnFlag=true;
                }

                AgreementAccountRecordVo accountLatestAgreemtnRecord = agreementAccountRecordMapper.queryLatestRecordVoByAccountUUIDAndType(accountUUID,AgreementVersion.AGREEMENT_TYPE_PRIVACY_AGREEMENT);
                if(accountLatestAgreemtnRecord!=null){
                    String accountPrivacyHistoryVersionNumber = accountLatestAgreemtnRecord.getVersionNumber();
                    privacyAgreementDetai.setPrivacyAgreementReadAndConfirmVersion(accountPrivacyHistoryVersionNumber);
                    privacyAgreementDetai.setPrivacyAgreementReadTime(accountLatestAgreemtnRecord.getReadAndConfimTime());
                }
            }
        }
        validAgreementVersionResult.setAccountUUID(accountUUID);
        Integer popType=0;
        if(privacyAgreemtnFlag&&accountAgreemtnFlag){
            validAgreementVersionResult.setPopType(0);
            popType=0;
        }else if(privacyAgreemtnFlag && !accountAgreemtnFlag){
            popType=1;
        }else if(!privacyAgreemtnFlag && accountAgreemtnFlag){
            popType=2;
        }else {
            popType=3;
        }
        validAgreementVersionResult.setPopType(popType);
        validAgreementVersionResult.setAccountAgreementDetail(accountAgreementDetail);
        validAgreementVersionResult.setPrivacyAgreementDetai(privacyAgreementDetai);
        return validAgreementVersionResult;
    }


    /**
     * 协议确认已读
     * @param agreementConfirmParam
     * @return
     */
    @Transactional
    @Override
    public R agreementConfirm(AgreementConfirmParam agreementConfirmParam) {
        // 1:用户协议, 2:隐私协议, 3.用户协议+隐私协议
        Integer popType = agreementConfirmParam.getPopType();

        //获取用户信息
        Account userInfo = accountService.getUserInfo();
        Assert.notNull(userInfo,"用户信息不存在");
        String accountUUID = userInfo.getUuid();
        Date nowDate = new Date();

        //最新用户协议
        AgreementVersion accountAgreementVersion = agreementVersionMapper.queryLatestAgreement(AgreementVersion.AGREEMENT_TYPE_ACCOUNT_AGREEMENT);

        //最新隐私协议
        AgreementVersion privacyAgreementVersion = agreementVersionMapper.queryLatestAgreement(AgreementVersion.AGREEMENT_TYPE_PRIVACY_AGREEMENT);

        if(popType==1){
            //设置用户协议已读
            if(accountAgreementVersion!=null){
                AgreementAccountRecord currentAgreementVersionRecord = agreementAccountRecordMapper.queryByVersionIdAndAccountUUID(accountAgreementVersion.getVersionId(), accountUUID);
                if(currentAgreementVersionRecord==null){
                    AgreementAccountRecord agreementAccountRecord=new AgreementAccountRecord();
                    agreementAccountRecord.setVersionId(accountAgreementVersion.getVersionId());
                    agreementAccountRecord.setAccountUuid(accountUUID);
                    agreementAccountRecord.setConfirmContent(getConfirmConent(accountAgreementVersion));
                    agreementAccountRecord.setReadAndConfimTime(nowDate);
                    agreementAccountRecord.setUpdateTime(nowDate);
                    agreementAccountRecordMapper.insert(agreementAccountRecord);
                }

            }
        }else if(popType ==2){
            //设置隐私协议已读
            if(privacyAgreementVersion!=null){
                AgreementAccountRecord currentAgreementVersionRecord = agreementAccountRecordMapper.queryByVersionIdAndAccountUUID(privacyAgreementVersion.getVersionId(), accountUUID);
                if(currentAgreementVersionRecord==null) {
                    AgreementAccountRecord agreementAccountRecord = new AgreementAccountRecord();
                    agreementAccountRecord.setVersionId(privacyAgreementVersion.getVersionId());
                    agreementAccountRecord.setAccountUuid(accountUUID);
                    agreementAccountRecord.setConfirmContent(getConfirmConent(privacyAgreementVersion));
                    agreementAccountRecord.setReadAndConfimTime(nowDate);
                    agreementAccountRecord.setUpdateTime(nowDate);
                    agreementAccountRecordMapper.insert(agreementAccountRecord);
                }
            }

        }else if(popType==3){
            //设置用户协议已读
            if(accountAgreementVersion!=null){
                AgreementAccountRecord currentAgreementVersionRecord = agreementAccountRecordMapper.queryByVersionIdAndAccountUUID(accountAgreementVersion.getVersionId(), accountUUID);
                if(currentAgreementVersionRecord==null) {
                    AgreementAccountRecord agreementAccountRecord = new AgreementAccountRecord();
                    agreementAccountRecord.setVersionId(accountAgreementVersion.getVersionId());
                    agreementAccountRecord.setAccountUuid(accountUUID);
                    agreementAccountRecord.setConfirmContent(getConfirmConent(accountAgreementVersion));
                    agreementAccountRecord.setReadAndConfimTime(nowDate);
                    agreementAccountRecord.setUpdateTime(nowDate);
                    agreementAccountRecordMapper.insert(agreementAccountRecord);
                }
            }

            //设置隐私协议已读
            if(privacyAgreementVersion!=null){
                AgreementAccountRecord currentAgreementVersionRecord = agreementAccountRecordMapper.queryByVersionIdAndAccountUUID(privacyAgreementVersion.getVersionId(), accountUUID);
                if(currentAgreementVersionRecord==null) {
                    AgreementAccountRecord agreementAccountRecord = new AgreementAccountRecord();
                    agreementAccountRecord.setVersionId(privacyAgreementVersion.getVersionId());
                    agreementAccountRecord.setAccountUuid(accountUUID);
                    agreementAccountRecord.setConfirmContent(getConfirmConent(privacyAgreementVersion));
                    agreementAccountRecord.setReadAndConfimTime(nowDate);
                    agreementAccountRecord.setUpdateTime(nowDate);
                    agreementAccountRecordMapper.insert(agreementAccountRecord);
                }
            }
        }
        return R.ok();
    }

    /**
     * 分页查询协议列表
     * @param agreementVersionListParam
     * @return
     */
    @Override
    public PageUtils versionList(AgreementVersionListParam agreementVersionListParam) {
        Integer agreementType = agreementVersionListParam.getAgreementType();
        String searchKey = agreementVersionListParam.getSearchKey();
        int page = agreementVersionListParam.getPage();
        int pageSize = agreementVersionListParam.getPageSize();
        Page<AgreementVersionVo> agreementVersionVoPage = agreementVersionMapper.pageQueryByAgreementType(new Page<>(page, pageSize), agreementType, searchKey);
        return new PageUtils(agreementVersionVoPage);
    }


    /**
     * 添加或修改协议版本信息
     * @param editAgreementParam
     * @return
     */
    @Transactional
    @Override
    public R editAgreement(EditAgreementParam editAgreementParam) {
        Integer versionId = editAgreementParam.getVersionId();

        String versionTitleParam = editAgreementParam.getVersionTitle();
        //协议类型
        Integer agreementTypeParam = editAgreementParam.getAgreementType();
        //版本编号
        String versionNumberParam = editAgreementParam.getVersionNumber();
        //更新内容
        String updateContentParam = editAgreementParam.getUpdateContent();
        //协议内容
        String agreementContentParam = editAgreementParam.getAgreementContent();

        //最新的版本信息
        AgreementVersion latestAgreementVersion = agreementVersionMapper.queryLatestAgreement(agreementTypeParam);

        LjAbout ljAbout = ljAboutMapper.selectOne(null);
        Assert.notNull(ljAbout,"协议配置信息不存在，请联系管理员");

        //修改
        if(versionId!=null){
            AgreementVersion agreementVersion = agreementVersionMapper.selectById(versionId);
            Assert.notNull(agreementVersion,"版本信息不存在");

            //校验版本信息
            validateVersionNumber(latestAgreementVersion, versionNumberParam);
            agreementVersion.setAgreementType(agreementTypeParam);
            agreementVersion.setVersionTitle(versionTitleParam);
            agreementVersion.setVersionNumber(versionNumberParam);
            agreementVersion.setUpdateContent(updateContentParam);
            agreementVersion.setAgreementContent(agreementContentParam);
            //同步修改协议信息
            //用户协议
            if(agreementTypeParam==1){
                ljAbout.setUserAgreement(agreementContentParam);
            }else if(agreementTypeParam==2){
                ljAbout.setPrivacyAgreement(agreementContentParam);
            }
            ljAbout.setUpdateTime(new Date());
            ljAboutMapper.updateById(ljAbout);


            agreementVersion.setUpdateTime(new Date());
            agreementVersionMapper.updateById(agreementVersion);
        }else {
            //校验版本信息
            validateVersionNumber(latestAgreementVersion, versionNumberParam);
            //添加版本信息
            AgreementVersion agreementVersion=new AgreementVersion();
            agreementVersion.setAgreementType(agreementTypeParam);
            agreementVersion.setVersionTitle(versionTitleParam);
            agreementVersion.setVersionNumber(versionNumberParam);
            agreementVersion.setUpdateContent(updateContentParam);
            agreementVersion.setAgreementContent(agreementContentParam);
            agreementVersion.setCreateTime(new Date());
            agreementVersion.setUpdateTime(new Date());
            agreementVersionMapper.insert(agreementVersion);

            //同步修改协议信息
            //用户协议
            if(agreementTypeParam==1){
                ljAbout.setUserAgreement(agreementContentParam);
            }else if(agreementTypeParam==2){
                ljAbout.setPrivacyAgreement(agreementContentParam);
            }
            ljAbout.setUpdateTime(new Date());
            ljAboutMapper.updateById(ljAbout);
        }
        return R.ok();

    }





    /**
     * 校验版本信息
     * @param latestAgreementVersion
     * @param versionNumberParam
     */
    private void validateVersionNumber(AgreementVersion latestAgreementVersion,String versionNumberParam){
        if(latestAgreementVersion==null){
            return;
        }

        // 将版本号转换为浮点数进行比较
        double newVersionNumber = Double.parseDouble(versionNumberParam);
        double latestVersionNumber = Double.parseDouble(latestAgreementVersion.getVersionNumber());

        // 判断传入的版本号是否大于数据库中的版本号
        if (newVersionNumber <= latestVersionNumber) {
            throw new IllegalArgumentException("新版本号必须大于当前最新版本号");
        }
    }


    /**
     * 查询用户的协议已读记录
     * @param accountAgreementReadedRecordParam
     * @return
     */
    @Override
    public PageUtils accountVersionRecord(AccountAgreementReadedRecordParam accountAgreementReadedRecordParam) {
        String accountUUID = accountAgreementReadedRecordParam.getAccountUUID();
        Integer agreementType = accountAgreementReadedRecordParam.getAgreementType();
        Integer agreementVersionId = accountAgreementReadedRecordParam.getAgreementVersionId();
        int page = accountAgreementReadedRecordParam.getPage();
        int pageSize = accountAgreementReadedRecordParam.getPageSize();
        Page<AgreementAccountRecordVo> agreementAccountRecordVoPage = agreementAccountRecordMapper.pageQueryByAccountAndAgreementType(new Page<>(page,pageSize),accountUUID, agreementType, agreementVersionId);
        return new PageUtils(agreementAccountRecordVoPage);
    }


    /**
     * 同步版本阅读记录
     * @param accountUUID
     * @return
     */
    @Async
    @Override
    public R syncRecord(String accountUUID) {
        if (StringUtils.isNotBlank(accountUUID)) {
            singleSyncHistoryAccountAgreementReadRecord(accountUUID);
        } else {
            ExecutorService executor = Executors.newFixedThreadPool(10); // 创建线程池
            Boolean continueFlag = true;
            while (continueFlag) {
                Page<String> noAgreementRecordsAccountPage = accountMapper.pageQueryNoAgreementRecords(new Page(0, 100)); // 调整分页大小
                long total = noAgreementRecordsAccountPage.getTotal();
                if (total > 0) {
                    List<String> accountUUIDList = noAgreementRecordsAccountPage.getRecords();
                    for (String uuid : accountUUIDList) {
                        executor.submit(() -> singleSyncHistoryAccountAgreementReadRecord(uuid)); // 多线程处理
                    }
                } else {
                    continueFlag = false;
                }
            }
            executor.shutdown(); // 关闭线程池
            try {
                executor.awaitTermination(1, TimeUnit.HOURS); // 等待所有任务完成
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
        return R.ok();
    }


    /**
     * 同步单个用户信息
     * @param accountUUID
     * @return
     */
    @Override
    public R syncAccountAgreementRecord(String accountUUID) {
         singleSyncHistoryAccountAgreementReadRecord(accountUUID);
         return R.ok();
    }


    /**
     * 单个同步用户协议阅读记录
     * @param accountUUID
     */
    public void singleSyncHistoryAccountAgreementReadRecord(String accountUUID) {
        //获取用户信息
        Account userInfo = accountMapper.queryByUUID(accountUUID);
        Assert.notNull(userInfo,"用户信息不存在");
        Date nowDate = new Date();

        //获取最新的协议版本
        //用户协议
        AgreementVersion accountAgreementVersion = agreementVersionMapper.queryLatestAgreement(AgreementVersion.AGREEMENT_TYPE_ACCOUNT_AGREEMENT);
        //隐私协议
        AgreementVersion privacyAgreementVersion = agreementVersionMapper.queryLatestAgreement(AgreementVersion.AGREEMENT_TYPE_PRIVACY_AGREEMENT);

        //判断是否为新用户
        AgreementAccountRecord latestAgreementAccountRecord = agreementAccountRecordMapper.queryLatestVersionByAccountUUID(accountUUID);

        //新用户，设置当前协议信息为已读
        if(latestAgreementAccountRecord==null){
            //设置用户协议已读
            if(accountAgreementVersion!=null){
                AgreementAccountRecord agreementAccountRecord=new AgreementAccountRecord();
                agreementAccountRecord.setVersionId(accountAgreementVersion.getVersionId());
                agreementAccountRecord.setAccountUuid(accountUUID);
                agreementAccountRecord.setConfirmContent(getConfirmConent(accountAgreementVersion));
                agreementAccountRecord.setReadAndConfimTime(nowDate);
                agreementAccountRecord.setUpdateTime(nowDate);
                agreementAccountRecordMapper.insert(agreementAccountRecord);

            }

            //设置隐私协议已读
            if(privacyAgreementVersion!=null){
                AgreementAccountRecord agreementAccountRecord=new AgreementAccountRecord();
                agreementAccountRecord.setVersionId(privacyAgreementVersion.getVersionId());
                agreementAccountRecord.setAccountUuid(accountUUID);
                agreementAccountRecord.setConfirmContent(getConfirmConent(privacyAgreementVersion));
                agreementAccountRecord.setReadAndConfimTime(nowDate);
                agreementAccountRecord.setUpdateTime(nowDate);
                agreementAccountRecordMapper.insert(agreementAccountRecord);

            }
        }
    }


    private String getConfirmConent(AgreementVersion accountAgreementVersion){
        String confirmContent="用户阅读并接受"+accountAgreementVersion.getVersionTitle()+accountAgreementVersion.getVersionNumber();
        return confirmContent;
    }
}
