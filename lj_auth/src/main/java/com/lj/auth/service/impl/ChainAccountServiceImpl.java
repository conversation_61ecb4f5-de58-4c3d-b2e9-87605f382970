package com.lj.auth.service.impl;

import com.lj.auth.domain.ChainAccount;
import com.lj.auth.mapper.ChainAccountMapper;
import com.lj.auth.service.ChainAccountService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 链账户 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-07
 */
@Service
public class ChainAccountServiceImpl extends ServiceImpl<ChainAccountMapper, ChainAccount> implements ChainAccountService {

}
