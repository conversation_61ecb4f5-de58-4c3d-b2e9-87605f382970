package com.lj.auth.service.impl;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lj.auth.mapper.DidCheckInAccountMapper;
import com.lj.auth.domain.DidCheckInAccount;
import com.lj.auth.service.DidCheckInAccountService;
@Service
public class DidCheckInAccountServiceImpl extends ServiceImpl<DidCheckInAccountMapper, DidCheckInAccount> implements DidCheckInAccountService{

}
