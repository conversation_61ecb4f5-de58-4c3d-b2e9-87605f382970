package com.lj.auth.service.impl;

import java.util.*;

import static com.lj.auth.common.CommonConstant.*;

import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import com.lj.auth.domain.*;
import com.lj.auth.domain.vo.AccountVo;
import jnr.ffi.annotations.In;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lj.auth.common.R;
import com.lj.auth.domain.vo.AvatarFrameVo;
import com.lj.auth.domain.vo.BadgeListVo;
import com.lj.auth.domain.vo.BadgeVo;
import com.lj.auth.mapper.*;
import com.lj.auth.service.AccountService;
import com.lj.auth.service.BadgeService;
import com.lj.auth.util.RedisUtils;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class BadgeServiceImpl extends ServiceImpl<BadgeMapper, Badge> implements BadgeService {
    @Resource
    private AccountService accountService;
    @Resource
    private AvatarFrameMapper avatarFrameMapper;
    @Resource
    private UserAvatarFrameMapper userAvatarFrameMapper;
    @Resource
    private AccountMapper accountMapper;
    @Resource
    private BadgeMapper badgeMapper;
    @Resource
    private UserBadgeMapper userBadgeMapper;
    @Resource
    private RedisUtils redisUtils;
    @Resource
    private BadgeAndPendantHelper badgeAndPendantHelper;

    /**
     * 获取个人挂件列表
     *
     * @return {@link String }
     */
    @Override
    public R getBadgePendantService() {
        Map<String, Object> result = new HashMap<>();
        Account userInfo = accountService.getUserInfo();
        List<AvatarFrameVo> avatarFrameVoList = avatarFrameMapper.selectUserAvatarFrame(userInfo.getUuid());
        // 新增分组处理逻辑
        Map<String, List<AvatarFrameVo>> groupedFrames =
            avatarFrameVoList.stream().collect(Collectors.groupingBy(AvatarFrameVo::getName));
        List<AvatarFrameVo> processedList = new ArrayList<>();
        groupedFrames.forEach((name, frames) -> {
            // 增加集合空判断和空元素过滤
            boolean hasActivated = CollectionUtil.isNotEmpty(frames) && frames.stream()
                .filter(Objects::nonNull).anyMatch(f -> f.getState() != null && f.getState() == 1);

            if (hasActivated) {
                // 取最大ID（使用lambda表达式替代Comparator）
                frames.stream()
                    .max((a, b) -> Long.compare(Optional.ofNullable(a.getAvatarFrameId()).orElse(0),
                        Optional.ofNullable(b.getAvatarFrameId()).orElse(0)

                    )).ifPresent(processedList::add);
            } else {
                // 取最小ID（使用lambda表达式替代Comparator）
                frames.stream().min((a, b) -> Long.compare(a.getId(), b.getId()))
                    .ifPresent(processedList::add);
            }
        });
        // 更具挂件id倒叙
        processedList.sort(Comparator.comparing(AvatarFrameVo::getId));
        result.put("avatarFrameList", processedList);
        // 异步刷新挂件是否获取
        try {
            badgeAndPendantHelper.asynchronousRefreshPendant(userInfo);
        } catch (Exception e) {
            log.error("异步刷新挂件异常", e);
        } ;
        return R.okData(result);
    }

    /**
     * 设置个人挂件
     *
     * @param request
     * @param paramJson
     * @return {@link R }
     */
    @Transactional
    @Override
    public R setPendantService(HttpServletRequest request, JSONObject paramJson) {
        Account userInfo = accountService.getUserInfo();
        Integer id = paramJson.getInteger("id");
        if (id == null) {
            log.error("id参数为空");
            return R.ok();
        }
        // 判断用户是否有该挂件
        UserAvatarFrame userAvatarFrame = userAvatarFrameMapper
            .selectOne(Wrappers.<UserAvatarFrame>lambdaQuery().eq(UserAvatarFrame::getAvatarFrameId, id)
                .eq(UserAvatarFrame::getAccountUuid, userInfo.getUuid()));
        if (userAvatarFrame == null) {
            log.error("尚未激活该挂件", userInfo.getUuid(), id);
            return R.ok();
        } else {
            Integer wearFlag = userAvatarFrame.getWearFlag();
            // 已佩戴
            if (ObjectUtil.equals(wearFlag, 1)) {
                return R.ok();
            } else {
                // 未佩戴
                // 查询是否有其他佩戴挂件，挂件只能佩戴一个，修改其他佩戴挂件为未佩戴
                UserAvatarFrame userAvatarFrame1 = userAvatarFrameMapper.selectOne(Wrappers
                    .<UserAvatarFrame>lambdaQuery().eq(UserAvatarFrame::getAccountUuid, userInfo.getUuid())
                    .eq(UserAvatarFrame::getWearFlag, 1));
                if (userAvatarFrame1 != null) {
                    userAvatarFrameMapper.update(null,
                        Wrappers.<UserAvatarFrame>lambdaUpdate()
                            .eq(UserAvatarFrame::getId, userAvatarFrame1.getId())
                            .set(UserAvatarFrame::getWearFlag, 0));
                }
                // 佩戴新设置的挂件
                userAvatarFrameMapper.update(null,
                    Wrappers.<UserAvatarFrame>lambdaUpdate()
                        .eq(UserAvatarFrame::getId, userAvatarFrame.getId())
                        .set(UserAvatarFrame::getWearFlag, 1));
                // 同步用户表佩戴挂件
                accountMapper.update(null,
                    Wrappers.<Account>lambdaUpdate().eq(Account::getUuid, userInfo.getUuid())
                        .set(Account::getAvatarFrameImage, userAvatarFrame.getAvatarFrameImage()));
            }
        }
        return R.ok();
    }

    /**
     * 获取个人最新徽章最多三个
     *
     * @param request
     * @param paramJson
     * @return {@link R }
     */
    @Override
    public R latestBadgeService(HttpServletRequest request, JSONObject paramJson) {
        Map<String, Object> result = new HashMap<>();
        Account userInfo = accountService.getUserInfo();
        List<BadgeVo> badgeVos = userBadgeMapper.getUserBadge(userInfo.getUuid());
        result.put("userBadgeVoList", badgeVos);
        return R.okData(result);
    }

    /**
     * 获取徽章列表
     *
     * @param request
     * @param paramJson
     * @return {@link R }
     */
    @Override
    public R listService(HttpServletRequest request, JSONObject paramJson) {
        Map<String, Object> result = new HashMap<>();
        Account userInfo = accountService.getUserInfo();
        String uuid = paramJson.getString("uuid");
        if (StrUtil.isBlank(uuid)) {
            log.error("uuid参数为空");
            return R.okData(result);
        }
        List<BadgeListVo> badgeList = badgeMapper.selectUserBadge(uuid);
        Integer count = userBadgeMapper.selectCount(
            Wrappers.<UserBadge>lambdaQuery().eq(UserBadge::getAccountUuid, uuid).eq(UserBadge::getState, 1));
        // 获取用户信息
        AccountVo allUserInfo = accountService.getAllUserInfo(uuid);
        // 获取用户佩戴徽章
        BadgeListVo badgeVo = badgeMapper.selectOneUserBadge(uuid);
        result.put("badgeList", badgeList);
        result.put("badgeCount", count);
        result.put("accountVo", allUserInfo);
        result.put("badgeVo", badgeVo);
        // 异步刷新徽章是否获取
        try {
            badgeAndPendantHelper.asyncRefreshBadgeAcquisition(userInfo);
        } catch (Exception e) {
            log.error("异步刷新徽章异常", e);
        } ;
        return R.okData(result);
    }

    /**
     * 设置徽章
     *
     * @param request
     * @param paramJson
     * @return {@link R }
     */
    @Transactional
    @Override
    public R setService(HttpServletRequest request, JSONObject paramJson) {
        Account userInfo = accountService.getUserInfo();
        Integer id = paramJson.getInteger("id");
        if (id == null) {
            log.error("id参数为空");
            return R.ok();
        }
        // 判断用户是否有该徽章
        UserBadge userBadge = userBadgeMapper.selectOne(Wrappers.<UserBadge>lambdaQuery()
            .eq(UserBadge::getBadgeId, id).eq(UserBadge::getAccountUuid, userInfo.getUuid()));

        if (userBadge == null) {
            log.error("尚未激活该徽章", userInfo.getUuid(), id);
            return R.ok();
        } else {
            Integer wearFlag = userBadge.getWearFlag();
            // 已佩戴
            if (ObjectUtil.equals(wearFlag, 1)) {
                return R.ok();
            } else {
                // 未佩戴
                // 查询是否有其他佩戴徽章，徽章只能佩戴一个，修改其他佩戴徽章为未佩戴
                UserBadge userBadge1 = userBadgeMapper.selectOne(Wrappers.<UserBadge>lambdaQuery()
                    .eq(UserBadge::getWearFlag, 1).eq(UserBadge::getAccountUuid, userInfo.getUuid()));

                if (userBadge1 != null) {
                    userBadgeMapper.update(null, Wrappers.<UserBadge>lambdaUpdate()
                        .eq(UserBadge::getId, userBadge1.getId()).set(UserBadge::getWearFlag, 0));
                }
                // 佩戴新设置的徽章
                userBadgeMapper.update(null, Wrappers.<UserBadge>lambdaUpdate()
                    .eq(UserBadge::getId, userBadge.getId()).set(UserBadge::getWearFlag, 1));
                // 同步用户表佩戴徽章
                // 获取徽章信息
                Badge badge = badgeMapper.selectById(userBadge.getBadgeId());
                accountMapper.update(null,
                    Wrappers.<Account>lambdaUpdate().eq(Account::getUuid, userInfo.getUuid())
                        .set(Account::getBadgeImage, badge.getDefaultImage()));
            }
        }
        return R.ok();
    }

    /**
     * 取消佩戴徽章
     *
     * @param request
     * @param paramJson
     * @return {@link R }
     */
    @Transactional
    @Override
    public R cancelService(HttpServletRequest request, JSONObject paramJson) {
        Account userInfo = accountService.getUserInfo();
        Integer id = paramJson.getInteger("id");
        if (id == null) {
            log.error("id参数为空");
            return R.ok();
        }
        // 查询该徽章是否已存在且佩戴
        UserBadge userBadge = userBadgeMapper
            .selectOne(Wrappers.<UserBadge>lambdaQuery().eq(UserBadge::getAccountUuid, userInfo.getUuid())
                .eq(UserBadge::getBadgeId, id).eq(UserBadge::getWearFlag, 1));
        if (userBadge != null) {
            // 取消佩戴
            userBadge.setWearFlag(0);
            userBadgeMapper.updateById(userBadge);
            // 同步修改用户表徽章佩戴
            accountMapper.update(null, Wrappers.<Account>lambdaUpdate()
                .eq(Account::getUuid, userInfo.getUuid()).set(Account::getBadgeImage, ""));
        }
        return R.ok();
    }

    /**
     * 取消挂件佩戴
     * 
     * @param request
     * @param paramJson
     * @return {@link R }
     */
    @Transactional
    @Override
    public R cancelPendantService(HttpServletRequest request, JSONObject paramJson) {
        Account userInfo = accountService.getUserInfo();
        // 查询用户是否有已佩戴挂件
        UserAvatarFrame userAvatarFrame =
            userAvatarFrameMapper.selectOne(Wrappers.<UserAvatarFrame>lambdaQuery()
                .eq(UserAvatarFrame::getAccountUuid, userInfo.getUuid()).eq(UserAvatarFrame::getWearFlag, 1));
        if (userAvatarFrame != null) {
            // 修改为未佩戴
            userAvatarFrameMapper.update(null, Wrappers.<UserAvatarFrame>lambdaUpdate()
                .eq(UserAvatarFrame::getId, userAvatarFrame.getId()).set(UserAvatarFrame::getWearFlag, 0));
            // 同步修改用户表挂件为不佩戴
            accountMapper.update(null, Wrappers.<Account>lambdaUpdate()
                .eq(Account::getUuid, userInfo.getUuid()).set(Account::getAvatarFrameImage, ""));
        }
        return R.ok();
    }

    /**
     * 刷新挂件
     * 
     * @param request
     * @param paramJson
     * @return {@link R }
     */
    @Override
    public R refreshPendantService(HttpServletRequest request, JSONObject paramJson) {
        String accountUuid = paramJson.getString("accountUuid");
        Integer level = paramJson.getInteger("level");
        String name = paramJson.getString("name");
        if (StrUtil.isBlank(accountUuid) || StrUtil.isBlank(name)) {
            log.error("参数为空");
            return R.error("参数为空");
        }
        // 裂空者
        if (name.equals(SKY_SPLIT_BADGE)) {
            if (level == null) {
                log.error("参数为空");
                return R.error("参数为空");
            }
            String skyKey = AVATAR + ":" + accountUuid + ":" + level;
            AvatarFrame avatarFrame = avatarFrameMapper
                .selectOne(Wrappers.<AvatarFrame>lambdaQuery().eq(AvatarFrame::getId, level));
            // 判断裂空者挂件
            UserAvatarFrame userAvatarFrame = userAvatarFrameMapper.selectOne(
                Wrappers.<UserAvatarFrame>lambdaQuery().eq(UserAvatarFrame::getAccountUuid, accountUuid)
                    .eq(UserAvatarFrame::getAvatarFrameId, avatarFrame.getId()));
            if (userAvatarFrame == null&& !redisUtils.hasKey(skyKey)) {
                UserAvatarFrame newUserAvatarFrame = new UserAvatarFrame();
                newUserAvatarFrame.setAccountUuid(accountUuid);
                newUserAvatarFrame.setAvatarFrameId(avatarFrame.getId());
                newUserAvatarFrame.setState(1);
                newUserAvatarFrame.setAvatarFrameLevel(Integer.parseInt(avatarFrame.getId().toString()));
                newUserAvatarFrame.setAvatarFrameImage(avatarFrame.getDefaultImage());
                userAvatarFrameMapper.insert(newUserAvatarFrame);
                redisUtils.set(skyKey, true);
            }
            // 判断裂空者徽章
            String skySplitKey = BADGE + ":" + SKY_SPLIT_BADGE + ":" + accountUuid;
            Badge badge = badgeMapper.selectOne(Wrappers.<Badge>lambdaQuery().eq(Badge::getName, name));
            UserBadge userBadge = userBadgeMapper.selectOne(Wrappers.<UserBadge>lambdaQuery()
                .eq(UserBadge::getAccountUuid, accountUuid).eq(UserBadge::getBadgeId, badge.getId()));
            if (userBadge == null&&!redisUtils.hasKey(skySplitKey) ) {
                UserBadge newUserBadge = new UserBadge();
                newUserBadge.setAccountUuid(accountUuid);
                newUserBadge.setBadgeId(badge.getId());
                newUserBadge.setState(1);
                newUserBadge.setWearFlag(0);
                userBadgeMapper.insert(newUserBadge);
                redisUtils.set(skySplitKey, true);
            }

        }
        // NFT 挂件
        if (name.equals(NFT)) {
            if (level == null) {
                log.error("参数为空");
                return R.error("参数为空");
            }
            String skyKeyNFT = AVATAR + ":" + accountUuid + ":" + level;
            AvatarFrame avatarFrame = avatarFrameMapper
                .selectOne(Wrappers.<AvatarFrame>lambdaQuery().eq(AvatarFrame::getId, level));
            // 判断 NFT挂件
            UserAvatarFrame userAvatarFrame = userAvatarFrameMapper.selectOne(
                Wrappers.<UserAvatarFrame>lambdaQuery().eq(UserAvatarFrame::getAccountUuid, accountUuid)
                    .eq(UserAvatarFrame::getAvatarFrameId, avatarFrame.getId()));
            if (userAvatarFrame == null&&!redisUtils.hasKey(skyKeyNFT)) {
                UserAvatarFrame newUserAvatarFrame = new UserAvatarFrame();
                newUserAvatarFrame.setAccountUuid(accountUuid);
                newUserAvatarFrame.setAvatarFrameId(avatarFrame.getId());
                newUserAvatarFrame.setState(1);
                newUserAvatarFrame.setAvatarFrameLevel(Integer.parseInt(avatarFrame.getId().toString()));
                newUserAvatarFrame.setAvatarFrameImage(avatarFrame.getDefaultImage());
                userAvatarFrameMapper.insert(newUserAvatarFrame);
                redisUtils.set(skyKeyNFT, true);
            }
        }

        String promotionKey = BADGE + ":" + PROMOTION_AMBASSADOR_BADGE + ":" + accountUuid;

        if (name.equals(PROMOTION_AMBASSADOR_BADGE)) {
            // 判断裂空者徽章
            Badge badge = badgeMapper.selectOne(Wrappers.<Badge>lambdaQuery().eq(Badge::getName, name));
            UserBadge userBadge = userBadgeMapper.selectOne(Wrappers.<UserBadge>lambdaQuery()
                .eq(UserBadge::getAccountUuid, accountUuid).eq(UserBadge::getBadgeId, badge.getId()));
            if (userBadge == null&&!redisUtils.hasKey(promotionKey)) {
                UserBadge newUserBadge = new UserBadge();
                newUserBadge.setAccountUuid(accountUuid);
                newUserBadge.setBadgeId(badge.getId());
                newUserBadge.setState(1);
                newUserBadge.setWearFlag(0);
                userBadgeMapper.insert(newUserBadge);
                redisUtils.set(promotionKey, true);
            }
        }
        return R.ok();
    }
}