package com.lj.auth.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import com.lj.auth.common.R;
import com.lj.auth.config.RabbitConfig;
import com.lj.auth.domain.Contract;
import com.lj.auth.domain.PlatformAccount;
import com.lj.auth.domain.resp.InsertNftRecordPreResp;
import com.lj.auth.domain.vo.UpChainDataVo;
import com.lj.auth.domain.vo.UpChainDataVoV2;
import com.lj.auth.exception.ServiceException;
import com.lj.auth.mapper.*;
import com.lj.auth.service.NftGetRecordService;
import com.lj.auth.util.BNBUtil;
import com.lj.auth.util.LockUtil;
import com.lj.auth.util.PropertiesRead;
import com.lj.auth.util.UUIdUtil;
import lombok.extern.slf4j.Slf4j;

import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.web3j.abi.FunctionEncoder;
import org.web3j.abi.FunctionReturnDecoder;
import org.web3j.abi.TypeReference;
import org.web3j.abi.datatypes.*;
import org.web3j.abi.datatypes.generated.Uint256;
import org.web3j.crypto.Credentials;
import org.web3j.crypto.RawTransaction;
import org.web3j.crypto.TransactionEncoder;
import org.web3j.protocol.Web3j;
import org.web3j.protocol.core.DefaultBlockParameterName;
import org.web3j.protocol.core.methods.request.Transaction;
import org.web3j.protocol.core.methods.response.EthCall;
import org.web3j.protocol.core.methods.response.EthEstimateGas;
import org.web3j.protocol.core.methods.response.EthGetTransactionReceipt;
import org.web3j.protocol.core.methods.response.TransactionReceipt;
import org.web3j.protocol.http.HttpService;
import org.web3j.utils.Numeric;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class YlBlockChainServer {

    @Resource
    private ContractMapper contractMapper;
    @Resource
    private PlatformAccountMapper ylPlatformAccountMapper;
    @Resource
    private NftGetRecordService nftGetRecordService;

    @Resource
    private RabbitTemplate rabbitTemplate;

    @Value("${ipfs.url}")
    private String ipfsUrl;
    @Value("${ylChainId}")
    private Integer ylChainId;

    @Resource
    private LockUtil lockUtil;


    private static final BigInteger GAS_LIMIT = new BigInteger("3000000");


    /**
     *
     * @param chainAccountAddress 链账户
     * @param nftCount 铸币数量
     * @param uuid 用户uuid
     * @return
     */
    public R autoBatchCreateNft(String chainAccountAddress, Integer nftCount, String uuid) {
        //获取平台链账户地址
        LambdaQueryWrapper<PlatformAccount> queryWrapper1 = new LambdaQueryWrapper<>();
        queryWrapper1.eq(PlatformAccount::getStatus, 1)
        // 5：部署合约账户
                .eq(PlatformAccount::getType, 5);
        PlatformAccount ylPlatformAccount = ylPlatformAccountMapper.selectOne(queryWrapper1);
        if (ObjectUtil.isEmpty(ylPlatformAccount)) {
            throw new ServiceException("平台数据异常，请联系管理员");
        }
        //获取当前可用NFT合约信息
        LambdaQueryWrapper<Contract> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Contract::getOpbChainId, ylChainId)
                .eq(Contract::getContractType, 1)
                .eq(Contract::getToUse, 1)
                .eq(Contract::getStatus, 1)
                .orderByDesc(Contract::getCreateTime)
                .last("limit 1");
        Contract contract = contractMapper.selectOne(queryWrapper);
        Long nftRecordId = nftGetRecordService.insertNftRecordPre(uuid, chainAccountAddress, ylChainId, nftCount, contract.getContractAddress());
        if (nftRecordId == null) {
            throw new ServiceException("新增记录失败");
        }
        //构建上链信息
        UpChainDataVo upChainDataVo = UpChainDataVo.builder().chainAccountAddress(chainAccountAddress)
                .nftCount(nftCount)
                .uuid(uuid)
                .ylPlatformAccount(ylPlatformAccount)
                .contractId(contract.getId())
                .contractAddress(contract.getContractAddress())
                .nftRecordId(nftRecordId)
                .opbChainId(ylChainId)
                .build();
        String str = JSON.toJSONString(upChainDataVo);
        //放入rabbitmq
        rabbitTemplate.convertAndSend(RabbitConfig.MINT_UP_CHAIN_QUEUE,(Object) str, message -> {
            String randomUuid = UUIdUtil.createUUId(15);
            message.getMessageProperties().setMessageId(randomUuid);
            return message;
        });
        return R.ok();
    }


    /**
     *批量单个铸币
     * @param chainAccountAddress 链账户
     * @param nftCount 铸币数量
     * @param uuid 用户uuid
     * @return
     */
    public R autoBatchSingleCreateNft(String chainAccountAddress, Integer nftCount, String uuid) {
        String key = "autoBatchSingleCreateNft:" + chainAccountAddress;
        //阻塞锁的方式处理领取NFT
        String cancelOrderProcessResult = lockUtil.executeWithBlockingLock(key, () -> {
            authBatchSingleCreateNftProcess(chainAccountAddress, nftCount, uuid);
            return null;
        });
        return R.ok();
    }

    private void authBatchSingleCreateNftProcess(String chainAccountAddress, Integer nftCount, String uuid) {
        //获取平台链账户地址
        PlatformAccount ylPlatformAccount=ylPlatformAccountMapper.queryByTypeAndStatus(PlatformAccount.TYPE_DOMAIN_REGISTER_RESOLVE_ACCOUNT, PlatformAccount.STATUS_SHOW);
        Assert.notNull(ylPlatformAccount,"平台数据异常，请联系管理员");
        String platformAccountAddress = ylPlatformAccount.getAddress();


        //获取当前可用NFT合约信息
        Contract contract= contractMapper.queryByChainIDAndType(ylChainId, Contract.CONTRACT_TYPE_ERC721, Contract.STATUS_ENABLE, Contract.TO_USE_REGISTER_DOMAIN_NFT);
        Assert.notNull(contract,"合约数据异常，请联系管理员");
        String contractAddress = contract.getContractAddress();
        InsertNftRecordPreResp insertNftRecordResp = nftGetRecordService.insertNftRecordPreV2(uuid, chainAccountAddress, ylChainId, nftCount, contractAddress);
        List<Long> nftGetRecordDomainIds = insertNftRecordResp.getNftGetRecordDomainIds();
        Long nftGetRecordId = insertNftRecordResp.getNftGetRecordId();

        for (Long nftGetRecordDomainId : nftGetRecordDomainIds) {
            //构建上链信息
            UpChainDataVoV2 upChainDataVoV2 = UpChainDataVoV2.builder()
                    .opbChainId(ylChainId)
                    .platformAccountAddress(platformAccountAddress)
                    .contractAddress(contractAddress)
                    .nftGetRecordId(nftGetRecordId)
                    .chainAccountAddress(chainAccountAddress)
                    .accountUUID(uuid)
                    .nftRecordDomainId(nftGetRecordDomainId)
                    .build();
            String str = JSON.toJSONString(upChainDataVoV2);
            //放入rabbitmq
            rabbitTemplate.convertAndSend(RabbitConfig.SINGLE_MINT_UP_CHAIN_QUEUE,(Object) str, message -> {
                String randomUuid = UUIdUtil.createUUId(15);
                message.getMessageProperties().setMessageId(randomUuid);
                return message;
            });
        }
    }


    public BigInteger getTotalCount(String fromAddress, String contractAddress) throws IOException {
        Web3j web3j = Web3j.build(new HttpService(PropertiesRead.getYmlStringForActive("ylznlCurl")));
        String functionName = "totalCount";
        List<Type> inputParameters = new ArrayList<>();
        List<TypeReference<?>> outputParameters = new ArrayList<>();
        outputParameters.add(new TypeReference<Uint256>() {});
        Function function = new Function(functionName, inputParameters, outputParameters);
        String encode = FunctionEncoder.encode(function);
        Transaction ethCallTransaction = Transaction.createEthCallTransaction(fromAddress, contractAddress, encode);
        EthCall ethCall = web3j.ethCall(ethCallTransaction, DefaultBlockParameterName.LATEST).send();
        String value = ethCall.getValue();
        List<Type> decode = FunctionReturnDecoder.decode(value, function.getOutputParameters());
        return (BigInteger) decode.get(0).getValue();
    }


    public List<Type> sendCallTransaction(String from, String to, Function function) throws Exception{
        String encode = FunctionEncoder.encode(function);
        Transaction ethCallTransaction = Transaction.createEthCallTransaction(from, to, encode);
        EthCall ethCall = BNBUtil.ethCall(ethCallTransaction, DefaultBlockParameterName.LATEST);
        return FunctionReturnDecoder.decode(ethCall.getValue(), function.getOutputParameters());
    }


    public String sendTransaction(String chainAccountAddress, String contractAddress, String privateKey, Function function) throws Exception{
        Credentials credentials = Credentials.create(privateKey);
        String encode = FunctionEncoder.encode(function);
        BigInteger nonce = BNBUtil.getTransactionNonce(chainAccountAddress);
       // BigInteger gasPrice = BNBUtil.ethGasPrice().getGasPrice();
        BigInteger gasPrice = BigInteger.ZERO;
        RawTransaction transaction = RawTransaction.createTransaction(nonce, gasPrice, GAS_LIMIT, contractAddress, encode);
        byte[] signMessage = TransactionEncoder.signMessage(transaction, credentials);
        String hexValue = Numeric.toHexString(signMessage);
        return BNBUtil.ethSendRawTransaction(hexValue).getTransactionHash();
    }

    public TransactionReceipt getTransactionReceipt(String transactionHash) throws Exception{
        EthGetTransactionReceipt ethGetTransactionReceipt = BNBUtil.ethGetTransactionReceipt(transactionHash);
        Optional<TransactionReceipt> optional = ethGetTransactionReceipt.getTransactionReceipt();
        if (!optional.isPresent()) {
            while (true) {
                //等待1秒
                TimeUnit.SECONDS.sleep(1);
                ethGetTransactionReceipt = BNBUtil.ethGetTransactionReceipt(transactionHash);
                optional = ethGetTransactionReceipt.getTransactionReceipt();
                if (optional.isPresent()) {
                    break;
                }
            }
        }
        return optional.get();
    }

    public BigDecimal transferEstimateGas(String fromAddress, String contractAddress,Function function) throws Exception {
        String functionName = "transferPrice";
        List<Type> inputParameters = new ArrayList<>();
        List<TypeReference<?>> outputParameters = new ArrayList<>();
        outputParameters.add(new TypeReference<Uint256>() {
        });
        Function function1 = new Function(functionName, inputParameters, outputParameters);
        List<Type> types = sendCallTransaction(fromAddress, contractAddress, function1);
        BigInteger transferPrice = (BigInteger) types.get(0).getValue();
        String encode = FunctionEncoder.encode(function);
        BigInteger nonce = BNBUtil.getTransactionNonce(fromAddress);
        BigInteger gasPrice = BNBUtil.ethGasPrice().getGasPrice();
        BigInteger gasLimit = new BigInteger("2100000");
        Transaction transaction = new Transaction(fromAddress, nonce, gasPrice, gasLimit, contractAddress, transferPrice, encode);
        EthEstimateGas ethEstimateGas = BNBUtil.ethEstimateGas(transaction);
        BigInteger amountUsed = ethEstimateGas.getAmountUsed();
        BigInteger totalWeiUsed =  amountUsed.multiply(gasPrice).add(transferPrice);
        return new BigDecimal(totalWeiUsed).divide(BigDecimal.TEN.pow(18));
    }




}
