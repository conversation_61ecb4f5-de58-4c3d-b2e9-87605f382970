package com.lj.auth.service;

import com.alibaba.fastjson2.JSONObject;
import com.lj.auth.common.R;
import com.lj.auth.domain.Chain;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lj.auth.domain.Result.SpBalanceResp;
import com.lj.auth.domain.Result.SpTransferResp;
import com.lj.auth.domain.Result.TransferReq;
import com.lj.auth.domain.Result.TransferRequireInfo;

import java.util.List;
import java.util.Map;

public interface ChainService extends IService<Chain> {

    /**
     * 获取界外开放链信息
     * 
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/10
     */
    Chain getLjChainInfoService();

    /**
     * 获取链信息
     * 
     * @return {@link List }<{@link Chain }>
     * <AUTHOR>
     * @date 2024/04/16
     */
    List<Chain> getChainInfoService();
    SpBalanceResp getBalance(JSONObject jsonObject);
    SpTransferResp transferSP(JSONObject jsonObject);
    SpTransferResp transferFeign(JSONObject jsonObject);
    Map getServeToken(JSONObject jsonObject);
    String getSign(JSONObject jsonObject);
    TransferRequireInfo queryTxRequireInfo(JSONObject jsonObject);
    TransferReq queryTxInfo(JSONObject jsonObject);
    TransferReq queryTxInfo(String transactionHash);
}
