package com.lj.auth.service;

import com.lj.auth.domain.PlatformAccount;
import com.baomidou.mybatisplus.extension.service.IService;

public interface PlatformAccountService extends IService<PlatformAccount> {

    /**
     * 获取云链平台账户私钥
     * 
     * @param key key
     * @param type 用处 1-领NFT 2-平台能量充值3-签到赠送 4-公司收款退款账户 5-域名注册解析管理账户
     * @return {@link String }
     * <AUTHOR>
     * @date 2024/04/02
     */
    String getYlAccountPrivate(String key, Integer type);


    /**
     * 获取账户地址
     *
     * @param key
     * @param type
     * @return {@link String }
     * <AUTHOR>
     * @date 2024/05/08
     */
    String getYlAccountAddress(String key, Integer type);
}
