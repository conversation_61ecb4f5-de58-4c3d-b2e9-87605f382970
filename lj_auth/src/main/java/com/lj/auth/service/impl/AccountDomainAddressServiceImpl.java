package com.lj.auth.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lj.auth.common.R;
import com.lj.auth.domain.*;
import com.lj.auth.domain.vo.ChainAddressVo;
import com.lj.auth.exception.ResponseEnum;
import com.lj.auth.exception.ServiceException;
import com.lj.auth.mapper.*;
import com.lj.auth.service.AccountService;
import com.lj.auth.service.DomainAdminRecordService;
import com.lj.auth.service.DomainApplicationRecordService;
import com.lj.auth.util.BsnDomainUtil;
import com.lj.auth.util.DomainMappingUtilParam;
import com.lj.auth.util.PageUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lj.auth.service.AccountDomainAddressService;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

@Service
public class AccountDomainAddressServiceImpl extends
    ServiceImpl<AccountDomainAddressMapper, AccountDomainAddress> implements AccountDomainAddressService {
    @Resource
    private AccountService accountService;
    @Resource
    private AccountMapper accountMapper;
    @Resource
    private ChainAccountMapper chainAccountMapper;
    @Resource
    private ChainMapper chainMapper;
    @Resource
    private DomainApplicationRecordService domainApplicationRecordService;
    @Resource
    private AccountDomainAddressMapper accountDomainAddressMapper;
    @Resource
    private DomainAccountMapper domainAccountMapper;
    @Resource
    private ApplicationManageMapper applicationManageMapper;
    @Resource
    private DomainMappingUtilParam domainMappingUtilParam;

    @Value("${ylChainId}")
    private Integer chainId;

    /**
     * 设置域名地址映射
     * 
     * @param opbChainId 链框架ID
     * @param domain 域名
     * @param address 地址
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/07
     */
    @Override
    @Transactional
    public R domainNameMappingService(Integer opbChainId, String domain, String address) {
        Account userInfo = accountService.getUserInfo();
        if (!opbChainId.equals(chainId)) {
            // 判断该用户是否拥有该地址
            ChainAccount chainAccount = chainAccountMapper.selectOne(Wrappers.<ChainAccount>lambdaQuery()
                .eq(ChainAccount::getAccountUuid, userInfo.getUuid()).eq(ChainAccount::getAddress, address));
            if (chainAccount == null) {
                throw new ServiceException(ResponseEnum.EnterYourOwnChainAddress);
            }
        }
        // 一个域名只能绑定一条链一个地址
        AccountDomainAddress one = this.getOne(Wrappers.<AccountDomainAddress>lambdaQuery()
            .eq(AccountDomainAddress::getAccountUuid, userInfo.getUuid())
            .eq(AccountDomainAddress::getOpbChainId, opbChainId).eq(AccountDomainAddress::getDomain, domain)
            .eq(AccountDomainAddress::getState, true));
        if (one != null) {
            throw new ServiceException(ResponseEnum.AddressAlreadyBound);
        }
        // 云链链地址映射上链
        if (opbChainId.equals(chainId)) {
            // 绑定链地址上链
            boolean b = domainMappingUtilParam.setTextChainAccount(domain, address);
            if (b) {
                AccountDomainAddress accountDomainAddress = new AccountDomainAddress();
                accountDomainAddress.setAccountUuid(userInfo.getUuid());
                accountDomainAddress.setDomain(domain);
                accountDomainAddress.setAddress(address);
                accountDomainAddress.setOpbChainId(opbChainId);
                this.save(accountDomainAddress);
            }
        } else {
            AccountDomainAddress accountDomainAddress = new AccountDomainAddress();
            accountDomainAddress.setAccountUuid(userInfo.getUuid());
            accountDomainAddress.setDomain(domain);
            accountDomainAddress.setAddress(address);
            accountDomainAddress.setOpbChainId(opbChainId);
            this.save(accountDomainAddress);
        }
        // 修改域名绑定状态
        domainAccountMapper.update(null, Wrappers.<DomainAccount>lambdaUpdate()
            .eq(DomainAccount::getDomain, domain).set(DomainAccount::getChainAddressBindState, 2));
        // 域名应用记录
        domainApplicationRecordService.addRecord(userInfo.getUuid(), opbChainId, domain, 1);
        return R.ok();
    }

    /**
     * 修改域名地址映射
     * 
     * @param id id
     * @param opbChainId 链框架Id
     * @param address 地址
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/07
     */
    @Override
    @Transactional
    public R updateDomainNameMappingService(Integer id, Integer opbChainId, String address) {
        Account userInfo = accountService.getUserInfo();

        if (!opbChainId.equals(chainId)) {
            // 判断该用户是否拥有该地址
            ChainAccount chainAccount = chainAccountMapper.selectOne(Wrappers.<ChainAccount>lambdaQuery()
                .eq(ChainAccount::getAccountUuid, userInfo.getUuid()).eq(ChainAccount::getAddress, address));
            if (chainAccount == null) {
                throw new ServiceException(ResponseEnum.EnterYourOwnChainAddress);
            }
        }
        // 一个域名只能绑定一条链一个地址
        AccountDomainAddress one = this.getById(id);
        if (one != null) {
            if (one.getOpbChainId().equals(opbChainId) && one.getAddress().equals(address)) {
                return R.ok();
            } else {
                if (opbChainId.equals(chainId)) {
                    // 绑定链地址上链
                    boolean b = domainMappingUtilParam.setTextChainAccount(one.getDomain(), address);
                    if (b) {
                        this.update(null,
                            Wrappers.<AccountDomainAddress>lambdaUpdate().eq(AccountDomainAddress::getId, id)
                                .set(AccountDomainAddress::getOpbChainId, opbChainId)
                                .set(AccountDomainAddress::getAddress, address));
                    }
                } else {
                    this.update(null,
                        Wrappers.<AccountDomainAddress>lambdaUpdate().eq(AccountDomainAddress::getId, id)
                            .set(AccountDomainAddress::getOpbChainId, opbChainId)
                            .set(AccountDomainAddress::getAddress, address));
                }
                // 修改域名应用记录
                domainApplicationRecordService.updateRecord(userInfo.getUuid(), one.getOpbChainId(),
                    opbChainId, one.getDomain(), 1);

            }
        } else {
            throw new ServiceException(ResponseEnum.DomainNameUnbound);
        }
        return R.ok();
    }

    /**
     * 查询域名映射列表
     * 
     * @param domain 域名
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/07
     */
    @Override
    public R domainNameMappingListService(String domain) {
        Account userInfo = accountService.getUserInfo();
        List<AccountDomainAddress> result =
            accountDomainAddressMapper.getDomainMappingList(domain, userInfo.getUuid());
        return R.okData(result);
    }

    /**
     * 移除域名地址映射
     * 
     * @param id
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/07
     */
    @Override
    public R domainNameMappingDeleteService(Integer id) {
        Account userInfo = accountService.getUserInfo();
        AccountDomainAddress one = this.getById(id);
        if (one.getAccountUuid().equals(userInfo.getUuid())) {
            // 如果是云链 还需要移除 链上映射记录
            if (one.getOpbChainId().equals(chainId)) {
                boolean b = domainMappingUtilParam.clearBinad(one.getDomain());
                // 修改域名绑定状态
                domainAccountMapper.update(null, Wrappers.<DomainAccount>lambdaUpdate()
                        .eq(DomainAccount::getDomain, one.getDomain()).set(DomainAccount::getChainAddressBindState, 1));
            }
            this.update(null, Wrappers.<AccountDomainAddress>lambdaUpdate()
                .eq(AccountDomainAddress::getId, id).set(AccountDomainAddress::getState, false));
            domainApplicationRecordService.deleteRecord(userInfo.getUuid(), one.getOpbChainId(),
                one.getDomain(), 1);

        } else {
            throw new ServiceException(ResponseEnum.IllegalRemoval);
        }
        return R.ok();
    }

    @Override
    public R domainNameMappingDeleteService1(Integer id,String uuid) {
//        Account userInfo = accountService.getUserInfo();
        Account userInfo = accountMapper.queryByUUID(uuid);
        AccountDomainAddress one = this.getById(id);
        if (one.getAccountUuid().equals(userInfo.getUuid())) {
            // 如果是云链 还需要移除 链上映射记录
            if (one.getOpbChainId().equals(chainId)) {
                boolean b = domainMappingUtilParam.clearBinad(one.getDomain());
                // 修改域名绑定状态
                domainAccountMapper.update(null, Wrappers.<DomainAccount>lambdaUpdate()
                        .eq(DomainAccount::getDomain, one.getDomain()).set(DomainAccount::getChainAddressBindState, 1));
            }
            this.update(null, Wrappers.<AccountDomainAddress>lambdaUpdate()
                    .eq(AccountDomainAddress::getId, id).set(AccountDomainAddress::getState, false));
            domainApplicationRecordService.deleteRecord(userInfo.getUuid(), one.getOpbChainId(),
                    one.getDomain(), 1);

        } else {
            throw new ServiceException(ResponseEnum.IllegalRemoval);
        }
        return R.ok();
    }

    /**
     * 地址解析域名
     * @param address
     * @return {@link R }
     */
    @Override
    public R addressDomainNameResolutionService(String address) {
        AccountDomainAddress accountDomainAddress = accountDomainAddressMapper.selectOne(
            Wrappers.<AccountDomainAddress>lambdaQuery().eq(AccountDomainAddress::getAddress, address)
                .eq(AccountDomainAddress::getOpbChainId, chainId).eq(AccountDomainAddress::getState, true));
        if (accountDomainAddress == null) {
            return R.ok();
        }
        return R.okData(accountDomainAddress.getDomain());
    }

    @Override
    public R searchDomianDetailService(String domain) {
        return R.okData(pubSearch(domain));
    }

    /**
     * 获取域名应用数量
     * 
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/07
     */
    @Override
    public R queryDomainApplicationService() {
        Map<String, Object> result = new HashMap<>();
        Account userInfo = accountService.getUserInfo();
        String accountUUID = userInfo.getUuid();
        // 获取域名总数量
        Integer totalQuantity = domainAccountMapper.selectCount(
            new LambdaQueryWrapper<DomainAccount>().eq(DomainAccount::getAccountUuid, accountUUID));

        Integer applicationCount = domainApplicationRecordService.getDomainApplicationCount(accountUUID);
        result.put("totalQuantity", totalQuantity);
        result.put("applied", applicationCount);
        result.put("notApplied", totalQuantity == 0 ? 0 : totalQuantity - applicationCount);
        return R.okData(result);
    }

    /**
     * 获取域名应用
     * 
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/07
     */
    @Override
    public R queryDomainApplicationManageService() {
        List<ApplicationManage> applicationManages = applicationManageMapper
            .selectList(Wrappers.<ApplicationManage>lambdaQuery().orderByDesc(ApplicationManage::getSort));
        return R.okData(applicationManages);
    }

    /**
     * 获取应用列表
     * 
     * @param page 页
     * @param pageSize 页码
     * @param type 1-已经应用 2-未应用
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/07
     */
    @Override
    public R queryDomainApplicationListService(Integer page, Integer pageSize, Integer type) {
        Account userInfo = accountService.getUserInfo();
        String accountUUID = userInfo.getUuid();
        PageUtils result = null;
        // 获取已应用列表
        if (type == 1) {
            result = domainApplicationRecordService.getDomainApplicationList(accountUUID, page, pageSize);
        } else if (type == 2) {
            result = domainApplicationRecordService.getDomainNotApplicationList(accountUUID, page, pageSize);
        }
        return R.okData(result);
    }

    /**
     * 获取域名应用详情
     * 
     * @param domain 域名
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/07
     */
    @Override
    public R queryDomainApplicationDetailService(String domain) {
        Account userInfo = accountService.getUserInfo();
        String accountUUID = userInfo.getUuid();
        return domainApplicationRecordService.getDomainApplicationDetail(domain, accountUUID);
    }

    /**
     * 域名解析地址
     * @param domain
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/02/26
     */
    @Override
    public R domainResolveAddressService(String domain) {
        AccountDomainAddress accountDomainAddress =
            accountDomainAddressMapper.selectOne(Wrappers.<AccountDomainAddress>lambdaQuery()
                .eq(AccountDomainAddress::getDomain, domain)
                .eq(AccountDomainAddress::getOpbChainId, chainId)
                    .eq(AccountDomainAddress::getState, true));
        if (accountDomainAddress == null) {
            return R.error(ResponseEnum.DomainNameUnbound);
        }
        return R.okData(accountDomainAddress.getAddress());
    }

    // =====================解析方法

    /**
     * 查询域名详情(包含查询其他经销商)
     */
    public Map<String, Object> pubSearch(String domain) {
        Map<String, Object> map = new HashMap<>(9);

        String paramData = JSONObject.of("domain", domain).toString();
        JSONObject jsonObject = JSONObject.parseObject((String)BsnDomainUtil.pubSearch(paramData));
        Integer code = (Integer)jsonObject.get("code");
        JSONObject dataJson = (JSONObject)jsonObject.get("data");
        if (Objects.equals(code, 0)) {
            // 注销时间
            String deregistrationTime = (String)dataJson.get("deregistrationTime");
            // 域名
            domain = (String)dataJson.get("domain");
            // 过期时间
            String expirationTime = (String)dataJson.get("expirationTime");
            // 冻结原因
            String freezeReason = (String)dataJson.get("freezeReason");
            // 冻结时间
            String freezeTime = (String)dataJson.get("freezeTime");
            // 所有者账户地址
            String owner = (String)dataJson.get("owner");
            // 注册时间
            String registerTime = (String)dataJson.get("registerTime");
            // 注册商
            String registrar = (String)dataJson.get("registrar");
            // 域名状态 0=正常，1=冻结，2=过期，3=注销
            Integer status = (Integer)dataJson.get("status");

            JSONArray jsonArray = (JSONArray)dataJson.get("domainOnDetails");
            List<ChainAddressVo> chainAddressList = new ArrayList<>();
            if (null != jsonArray) {
                for (Object o : jsonArray) {
                    JSONObject object = (JSONObject)o;
                    Integer chainId = (Integer)object.get("chainId");
                    String chainTxHash = (String)object.get("chainTxHash");
                    String finishTime = (String)object.get("finishTime");
                    String resolverAddress = (String)object.get("resolverAddress");
                    Integer detailStatus = (Integer)object.get("status");

                    ChainAddressVo chainAddress = new ChainAddressVo();
                    if (Objects.equals(chainId, 1001)) {
                        chainAddress.setChainName("延安链");
                        chainAddress.setChainLogo(getChianLogo(1001));
                    } else if (Objects.equals(chainId, 1002)) {
                        chainAddress.setChainName("泰安链");
                        chainAddress.setChainLogo(getChianLogo(1002));
                    } else if (Objects.equals(chainId, 1003)) {
                        chainAddress.setChainName("武汉链");
                        chainAddress.setChainLogo(getChianLogo(1003));
                    } else if (Objects.equals(chainId, 2002)) {
                        chainAddress.setChainName("Polygon-Mainnet");
                        chainAddress.setChainLogo(getChianLogo(2002));
                    } else if (Objects.equals(chainId, 3001)) {
                        chainAddress.setChainName("Spartan-I Chain");
                        chainAddress.setChainLogo(getChianLogo(3001));
                    }
                    chainAddress.setContractAddress(resolverAddress);
                    if (null != finishTime) {
                        chainAddress.setWindTime(DateUtil.parse(finishTime, "yyyy-MM-dd HH:mm:ss"));
                    } else {
                        chainAddress.setWindTime(null);
                    }
                    if (Objects.equals(detailStatus, 2)) {
                        chainAddress.setState("已上链");
                    } else {
                        chainAddress.setState("未上链");
                    }
                    chainAddressList.add(chainAddress);
                }
            }
            map.put("deregistrationTime", deregistrationTime);
            map.put("domain", domain);
            map.put("expirationTime", expirationTime);
            map.put("freezeReason", freezeReason);
            map.put("freezeTime", freezeTime);
            map.put("owner", owner);
            map.put("registerTime", registerTime);
            map.put("registrar", registrar);
            map.put("status", status);
            map.put("chainAddressList", chainAddressList);
        }
        map.put("code", code);
        return map;
    }

    /**
     * 获取链log
     * 
     * @param chainId
     * @return {@link String }
     * <AUTHOR>
     * @date 2024/04/25
     */
    private String getChianLogo(Integer chainId) {
        if (chainId == null) {
            return "";
        }
        Chain chain = chainMapper.selectOne(Wrappers.<Chain>lambdaQuery().eq(Chain::getChainId, chainId));
        if (chain == null) {
            return "";
        }
        return chain.getChainLogo();
    }

}
