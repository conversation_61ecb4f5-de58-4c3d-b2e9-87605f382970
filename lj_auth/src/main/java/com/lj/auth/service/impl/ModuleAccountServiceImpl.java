package com.lj.auth.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lj.auth.common.CommonConstant;
import com.lj.auth.common.R;
import com.lj.auth.domain.*;
import com.lj.auth.domain.vo.AccountVo;
import com.lj.auth.exception.ResponseEnum;
import com.lj.auth.mapper.*;
import com.lj.auth.service.GlobalConfigService;
import com.lj.auth.service.ModuleAccountService;
import com.lj.auth.util.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import java.util.*;

import static com.lj.auth.common.CommonConstant.*;


@Service
@Slf4j
public class ModuleAccountServiceImpl extends ServiceImpl<AccountMapper, Account> implements ModuleAccountService {

    @Resource
    private AccountSupply accountSupply;

    @Resource
    private AccountMapper accountMapper;

    @Resource
    private OperateMapper operateMapper;
    @Resource
    private AboutMapper aboutMapper;
    @Resource
    private GlobalConfigService globalConfigService;

    @Resource
    private NftMapper nftMapper;
    @Resource
    private DomainMappingUtilParam domainMappingUtilParam;

    @Resource
    private TouristDevicePhoneMapper touristDevicePhoneMapper;



    /**
     * 模块验证码登录
     *
     * @param account 手机号
     * @param varificationCode 验证码
     */
    @Override
    @Transactional
    public R loginByCodeWithModule(HttpServletRequest request, String account, String varificationCode) {
        if (RegexUtil.isMobileSimple(account)) {
            accountSupply.handleLoginScenario(account);
        } else {
            return R.error(ResponseEnum.PhoneNumberFormatError);
        }
        // 查询用户信息
        Account userInfo =
                accountMapper.selectOne(Wrappers.<Account>lambdaQuery().eq(Account::getPhoneNumber, account));

        // 验证用户权限
        Integer blacklist = userInfo.getBlacklist();
        if (!blacklist.equals(CommonConstant.ACCOUNT_STATE_NORMAL)) {
            return R.error(ResponseEnum.AccountCannotLogInNormally);
        }

        // 判断验证码是否相等
        if (!accountSupply.verifyVerificationCode(account, varificationCode)) {
            return R.error(ResponseEnum.VerificationCodeError);
        }
        // 更新用户最近的登录信息
        userInfo.setRecentLoginIp(IpUtil.getRealIp(request));
        userInfo.setRecentLoginTime(new Date());
        if (!updateById(userInfo)) {
            return R.error(ResponseEnum.FailedToUpdateUserInformation);
        }
        String accountUuid = userInfo.getUuid();
        Map resultMap=new HashMap();
        resultMap.put("accountUuid",accountUuid);
        return R.okData(resultMap);
    }

    /**
     * 密码登录
     *
     * @param request 请求
     * @param account 手机号
     * @param password 密码
     * @return {@link R }
     */
    @Override
    public R loginByPasswordWithModule(HttpServletRequest request, String account, String password) {
        String source = request.getHeader("platformSource");
        String itemSource = request.getHeader("itemSource");
        if(StrUtil.isBlank(source)){
            source="APP";
            itemSource="DID";
        }
        Account accountEntity = null;
        ServletContext servletContext = request.getServletContext();
        String operateuuidAndPhone = null;
        if (RegexUtil.isMobileSimple(account)) {
            accountEntity =
                    accountMapper.selectOne(Wrappers.<Account>lambdaQuery().eq(Account::getPhoneNumber, account));
            if (accountEntity == null) {
                return R.error(ResponseEnum.PhoneNumberDoesNotExist);
            }
            operateuuidAndPhone = accountEntity.getOperateUuid() + account;
            // 效验账号是否锁定
            if (!accountSupply.checkLock(servletContext, operateuuidAndPhone)) {
                return R.error(ResponseEnum.ThisAccountHasBeenLockedFor5Minutes);
            }
        } else {
            return R.error(ResponseEnum.PhoneNumberFormatError);
        }
        // 判断用户是否存在
        if (accountEntity == null) {
            return R.error(ResponseEnum.PhoneNumberDoesNotExist);
        } else {
            // 验证用户权限
            Integer blacklist = accountEntity.getBlacklist();
            if (!blacklist.equals(CommonConstant.ACCOUNT_STATE_NORMAL)) {
                return R.error(ResponseEnum.AccountCannotLogInNormally);
            }
        }
        String accountPassword = accountEntity.getPassword();
        Assert.notBlank(accountPassword, ResponseEnum.SetPassword.getMsg());
        // 加密的密码
        BCryptPasswordEncoder encoder = new BCryptPasswordEncoder();
        // 校验密码 password
        if (!encoder.matches(password, accountPassword)) {
            // 插入错误记录
            accountSupply.addFailNum(servletContext, operateuuidAndPhone);
            return R.error(ResponseEnum.AccountOrPasswordError);
        }
        // 更新用户最近的登录信息
        accountEntity.setRecentLoginIp(IpUtil.getRealIp(request));
        accountEntity.setRecentLoginTime(new Date());
        updateById(accountEntity);

        String accountUuid = accountEntity.getUuid();

        // 登录成功,清空登录失败记录
        accountSupply.cleanFailNum(servletContext, operateuuidAndPhone);
        Map resultMap=new HashMap();
        resultMap.put("accountUuid",accountUuid);
        return R.okData(resultMap);
    }

    /**
     * 通过uuid获取用户信息
     * @param accountUuid
     * @return
     */
    @Override
    public AccountVo getInfoVoByUUIDWithModule(String accountUuid) {
        AccountVo accountVo = getAccountVo(accountUuid);
        return accountVo;
    }




    /**
     * 通过did查询用户信息
     * @param didSymbol
     * @return
     */
    @Override
    public Account queryByDidWithModule(String didSymbol) {
        Account account = accountMapper.queryByDid(didSymbol);
        return account;
    }


    /**
     * 通过uuid查询用户信息
     * @param accountUuid
     * @return
     */
    @Override
    public Account queryByUUIDWithModule(String accountUuid) {
        Account account = accountMapper.queryByUUID(accountUuid);
        return account;
    }

    /**
     * 通过手机号查询用户信息
     * @param phoneNumber
     * @return
     */
    @Override
    public Account queryByPhoneWithModule(String phoneNumber) {
        Account account = accountMapper.queryByPhone(phoneNumber);
        return account;
    }

    /**
     * 修改密码
     * @param accountUuid
     * @param account
     * @param varificationCode
     * @param password
     * @return
     */
    @Override
    public R setPasswordWithModule(String accountUuid, String account, String varificationCode, String password) {
        // 校验手机号
        Account userInfo = accountMapper.queryByUUID(accountUuid);
        Assert.notNull(userInfo,"账户不存在");

        Assert.isTrue(accountSupply.verifyVerificationCode(account, varificationCode),
                ResponseEnum.VerificationCodeError.getMsg());

        Assert.equals(userInfo.getPhoneNumber(), account, ResponseEnum.PhoneNumberMismatch.getMsg());

        // 校验密码格式
        Assert.isTrue(RegexUtil.isPasswordValidWithAlphanumeric(password), ResponseEnum.PasswordRuleError.getMsg());

        BCryptPasswordEncoder encoder = new BCryptPasswordEncoder();
        // 修改密码
        accountMapper.update(null, Wrappers.<Account>lambdaUpdate().eq(Account::getPhoneNumber, account)
                .set(Account::getPassword, encoder.encode(password)));
        return R.ok();
    }


    /**
     *修改手机号
     * @param accountUuid
     * @param account
     * @param varificationCode
     * @return
     */
    @Override
    public R setPhoneNumberWithModule(String accountUuid, String account, String varificationCode) {
        // 校验手机号
        Account userInfo = accountMapper.queryByUUID(accountUuid);
        Assert.notNull(userInfo,"账户不存在");
        Assert.isTrue(accountSupply.verifyVerificationCode(account, varificationCode),
                ResponseEnum.VerificationCodeError.getMsg());
        if (ObjectUtil.equals(account, userInfo.getPhoneNumber())) {
            return R.ok();
        }
        // 判断手机号是否是游客手机号
        String phoneNumber = userInfo.getPhoneNumber();

        boolean mobileSimple = RegexUtil.isMobileSimple(phoneNumber);
        // 校验手机号是否重复

        Boolean b = accountMapper.phoneNumberIsExists(account);

        //不是游客账号
        if (mobileSimple) {
            //手机号不存在
            if (!b) {
                // 修改手机号
                accountMapper.update(null, Wrappers.<Account>lambdaUpdate()
                        .eq(Account::getId, userInfo.getId()).set(Account::getPhoneNumber, account));
            } else {
                Assert.isFalse(b, ResponseEnum.PhoneNumberAlreadyExists.getMsg());
            }

        } else {
            //移除游客设备机器码
            touristDevicePhoneMapper.delete(Wrappers.<TouristDevicePhone>lambdaQuery()
                    .eq(TouristDevicePhone::getPhoneNumber,userInfo.getPhoneNumber()));
            // 不是正常手机号代表是游客 --判断修改的手机号不存在正常修改补全手机号
            if (!b) {
                // 修改手机号
                accountMapper.update(null, Wrappers.<Account>lambdaUpdate()
                        .eq(Account::getId, userInfo.getId()).set(Account::getPhoneNumber, account));
            } else {
                Assert.isFalse(b, ResponseEnum.PhoneNumberAlreadyExists.getMsg());
//                // 判断修改手机号是否有DID
//                Account account1 = accountMapper
//                    .selectOne(Wrappers.<Account>lambdaQuery().eq(Account::getPhoneNumber, account));
//                // 手机号已存在且是游客账号
//                // 判断游客账号是否有DID
//                String didSymbol = userInfo.getDidSymbol();
//                if (StrUtil.isBlank(didSymbol)) {
//                    accountMapper.update(null, Wrappers.<Account>lambdaUpdate()
//                        .eq(Account::getId, userInfo.getId()).set(Account::getBlacklist, 2));
//                    // TODO 判断用户有没有资产 合并用户资产
//                    checkLogout.mergeAssets(userInfo, account1);
//                } else {
//                    String didSymbol1 = account1.getDidSymbol();
//                    if (StrUtil.isBlank(didSymbol1)) {
//                        // 查询用户DID关联表
//                        AccountDid accountDid = accountDidMapper.selectOne(Wrappers.<AccountDid>lambdaQuery()
//                            .eq(AccountDid::getAccountUuid, userInfo.getUuid()));
//                        // 修改游客用户DID关联表为修改用户的
//                        accountDidMapper.update(null,
//                            Wrappers.<AccountDid>lambdaUpdate().eq(AccountDid::getId, accountDid.getId())
//                                .set(AccountDid::getAccountUuid, account1.getUuid())
//                                .set(AccountDid::getOperateUuid, account1.getOperateUuid())
//                        );
//                        // 游客账号有DID且修改手机号已存在
//                        // 禁用游客账号并修改游客账号DID
//                        accountMapper.update(null,
//                            Wrappers.<Account>lambdaUpdate().eq(Account::getId, userInfo.getId())
//                                .set(Account::getBlacklist, 2).set(Account::getDidSymbol, "yk" + didSymbol));
//                        accountMapper.update(null, Wrappers.<Account>lambdaUpdate()
//                            .eq(Account::getPhoneNumber, account).set(Account::getDidSymbol, didSymbol));
//                        // TODO 判断用户有没有资产合并资产
//                        // TODO 判断用户有没有域名资产
//                        // todo 判断用户有没有现金资产
//                        checkLogout.mergeAssets(userInfo, account1);
//                    } else {
//                        return R.error("绑定手机号已经实名");
//                    }
//                }
            }
        }
        return null;
    }


    /**
     * 设置支付密码
     * @param accountUuid
     * @param account
     * @param varificationCode
     * @param payPassword
     * @return
     */
    @Override
    public R setPayPasswordWithModule(String accountUuid, String account, String varificationCode, String payPassword) {
        // 校验手机号
        Account userInfo = accountMapper.queryByUUID(accountUuid);
        Assert.notNull(userInfo,"账户不存在");
        Assert.equals(userInfo.getPhoneNumber(), account, ResponseEnum.PhoneNumberMismatch.getMsg());

        Assert.isTrue(RegexUtil.isNumber(payPassword), ResponseEnum.PayPasswordRuleError.getMsg());

        Assert.isTrue(accountSupply.verifyVerificationCode(account, varificationCode),
                ResponseEnum.VerificationCodeError.getMsg());

        BCryptPasswordEncoder encoder = new BCryptPasswordEncoder();

        String encodePayPassword = encoder.encode(payPassword);
        accountMapper.update(null, Wrappers.<Account>lambdaUpdate().eq(Account::getUuid, userInfo.getUuid())
                .set(Account::getPayPassword, encodePayPassword));
        return R.ok();
    }


    /**
     * 比对原手机号和修改手机号，并校验验证码
     * @param accountUuid
     * @param account
     * @param verificationCode
     * @return
     */
    @Override
    public boolean compareVerificationCodesWithModule(String accountUuid, String account, String verificationCode) {
        // 校验手机号是否是当前登录用户
        // 校验手机号
        Account userInfo = accountMapper.queryByUUID(accountUuid);
        Assert.notNull(userInfo,"账户不存在");
        //判断手机号是否一致
        boolean result = accountSupply.verifyVerificationCode(account, verificationCode);
        Assert.isTrue(result, ResponseEnum.VerificationCodeError.getMsg());
        return result;
    }


    /**
     * 通过用户uuid设置头像信息
     * @param accountUuid uuid
     * @param portrait 图像
     * @param nftId nftId
     * @param type 图像类型 1-普通图像 2-nft图像
     * @return
     */
    @Override
    public R setPortraitWithModule(String accountUuid, String portrait, Long nftId, Integer type) {
        Account userInfo = accountMapper.queryByUUID(accountUuid);
        Assert.notNull(userInfo,"账户不存在");
        // 图像类型 1-普通图像 2-nft图像
        Integer showType = userInfo.getHeadPortraitType();
        // 只修改类型
        if (type != null && !showType.equals(type)) {
            accountMapper.update(null, Wrappers.<Account>lambdaUpdate()
                    .eq(Account::getUuid, userInfo.getUuid()).set(Account::getHeadPortraitType, type));
        }
        // 设置普通图像
        if (StrUtil.isNotBlank(portrait)) {
            String headPortrait = userInfo.getHeadPortrait();
            if (!portrait.equals(headPortrait)) {
                accountMapper.update(null, Wrappers.<Account>lambdaUpdate()
                        .eq(Account::getUuid, userInfo.getUuid()).set(Account::getHeadPortrait, portrait));
            }
        }
        // 设置NFT图像
        if (nftId != null) {
            if (userInfo.getHeadPortraitNftId() != null) {
                Long headPortraitNftId = userInfo.getHeadPortraitNftId();
                if (!headPortraitNftId.equals(nftId)) {
                    accountMapper.update(null, Wrappers.<Account>lambdaUpdate()
                            .eq(Account::getUuid, userInfo.getUuid()).set(Account::getHeadPortraitNftId, nftId));
                }
            } else {
                accountMapper.update(null, Wrappers.<Account>lambdaUpdate()
                        .eq(Account::getUuid, userInfo.getUuid()).set(Account::getHeadPortraitNftId, nftId));
            }
        }
        return R.ok();
    }


    /**
     * 通过uuid设置用户名称
     * @param accountUuid
     * @param nickName
     * @param domainNickName
     * @param type
     * @return
     */
    @Override
    public R setNickNameWithModule(String accountUuid, String nickName, String domainNickName, Integer type) {
        Account userInfo = accountMapper.queryByUUID(accountUuid);
        Assert.notNull(userInfo,"账户不存在");
        // 1-昵称展示 2-域名昵称展示
        Integer showType = userInfo.getShowType();
        // 只修改类型
        if (type != null && !showType.equals(type)) {
            accountMapper.update(null, Wrappers.<Account>lambdaUpdate()
                    .eq(Account::getUuid, userInfo.getUuid()).set(Account::getShowType, type));
        }

        // 设置普通昵称
        if (StrUtil.isNotBlank(nickName)) {
            // 校验昵称的合法性 1-10长度 不能有特殊字符 暂不校验
            // boolean rightNickname = RegexUtil.isRightNickname(nickName);
            int length = nickName.length();
            if (length > 10) {
                return R.error(ResponseEnum.NicknameFormatError);
            }
            String name = userInfo.getNickName();
            if (!nickName.equals(name)) {
                // 校验普通昵称唯一性
                List<Account> accounts = accountMapper
                        .selectList(Wrappers.<Account>lambdaQuery().eq(Account::getNickName, nickName));
                if (accounts.size() > 0) {
                    return R.error(ResponseEnum.NicknameDuplication);
                }
                accountMapper.update(null, Wrappers.<Account>lambdaUpdate()
                        .eq(Account::getUuid, userInfo.getUuid()).set(Account::getNickName, nickName));
            }
        }
        // 设置域名昵称
        if (StrUtil.isNotBlank(domainNickName)) {
            if (userInfo.getDomainNickName() != null) {
                String name = userInfo.getDomainNickName();
                if (!domainNickName.equals(name)) {
                    // 解除原先域名绑定
                    boolean b1 = domainMappingUtilParam.clearBinad(name);
                    // 进行专属昵称绑定 成功之后再进行 更改
                    boolean b = domainMappingUtilParam.setTextNicekName(domainNickName, userInfo.getUuid());
                    if (b) {
                        accountMapper.update(null,
                                Wrappers.<Account>lambdaUpdate().eq(Account::getUuid, userInfo.getUuid())
                                        .set(Account::getDomainNickName, domainNickName));
                    }
                }
            } else {
                // 进行专属昵称绑定 成功之后再进行 更改
                boolean b = domainMappingUtilParam.setTextNicekName(domainNickName, userInfo.getUuid());
                if (b) {
                    accountMapper.update(null,
                            Wrappers.<Account>lambdaUpdate().eq(Account::getUuid, userInfo.getUuid())
                                    .set(Account::getDomainNickName, domainNickName));
                }
            }
        }

        return R.ok();
    }


    /**
     * 同步用户登录信息
     * @param accountUuid
     * @param ip
     * @return {@link Object }
     * <AUTHOR>
     * @date 2025/04/09
     */
    @Override
    public Object logInMessageRecordService(String accountUuid, String ip,String application) {
        // 获取用户信息
        Account userInfo = accountMapper.queryByUUID(accountUuid);
        if (userInfo != null) {
            this.update(null, Wrappers.<Account>lambdaUpdate().eq(Account::getUuid, accountUuid)
                    .set(Account::getRecentLoginIp, ip).set(Account::getRecentLoginTime, new Date()));
        }
        return R.ok();
    }


    /**
     * 通过uuid获取用户信息
     * @param accountUUID
     * @return
     */
    private AccountVo getAccountVo(String accountUUID) {
        Account userInfo =
                accountMapper.selectOne(Wrappers.<Account>lambdaQuery().eq(Account::getUuid, accountUUID));

        AccountVo accountVo = new AccountVo();
        // 获取经销商信息
        Operate operate = operateMapper.selectOne(
                Wrappers.<Operate>lambdaQuery().eq(Operate::getAccountUuid, userInfo.getOperateUuid()));

        // 获取经销商信息
        About about = aboutMapper
                .selectOne(Wrappers.<About>lambdaQuery().eq(About::getOperateUuid, userInfo.getOperateUuid()));
        String browserPortalTitle = about.getBrowserPortalTitle();

        BeanUtils.copyProperties(userInfo, accountVo);
        accountVo.setOperate(operate);
        accountVo.setBrowserPortalTitle(browserPortalTitle);
        if (userInfo.getHeadPortraitNftId() != null) {
            // 查询nft相关信息
            Nft nft = nftMapper
                    .selectOne(Wrappers.<Nft>lambdaQuery().eq(Nft::getId, userInfo.getHeadPortraitNftId())
                            .eq(Nft::getStatus, 1).eq(Nft::getIsDelete, false));
            accountVo.setNft(nft);
        }
        // 针对游客手机号至空
        if (accountVo.getPhoneNumber().contains("yk")) {
            accountVo.setPhoneNumber("");
        }
        accountVo.setDomainNickNameSignImage(globalConfigService.getGlobalConfig(DOMAINNICKNAMESIGNIMAGE));
        accountVo.setExitAmbassadorLevelPicture(globalConfigService.getGlobalConfig(EXITAMBASSADORLEVELPICTURE));
        return accountVo;
    }

}
