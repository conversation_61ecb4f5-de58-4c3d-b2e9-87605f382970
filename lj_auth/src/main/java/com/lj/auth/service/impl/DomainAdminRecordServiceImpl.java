package com.lj.auth.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lj.auth.common.R;
import com.lj.auth.domain.*;
import com.lj.auth.domain.vo.DomainAdminInfoVo;
import com.lj.auth.exception.ResponseEnum;
import com.lj.auth.exception.ServiceException;
import com.lj.auth.mapper.*;
import com.lj.auth.service.AccountService;
import com.lj.auth.service.DomainAdminRecordService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lj.auth.service.DomainApplicationRecordService;
import com.lj.auth.service.GlobalConfigService;
import com.lj.auth.util.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.lj.auth.common.CommonConstant.*;

/**
 * <p>
 * 域名管理记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-07
 */
@Service
@Slf4j
public class DomainAdminRecordServiceImpl extends ServiceImpl<DomainAdminRecordMapper, DomainAdminRecord>
    implements DomainAdminRecordService {
    @Resource
    private AccountService accountService;
    @Resource
    private AccountMapper accountMapper;
    @Resource
    private GlobalConfigService globalConfigService;
    @Resource
    private DomainAdminRecordMapper domainAdminRecordMapper;
    @Resource
    private ChainMapper chainMapper;
    @Resource
    private DomainMappingUtilParam domainMappingUtilParam;
    @Resource
    private AccountDomainAddressMapper accountDomainAddressMapper;
    @Resource
    private DomainApplicationRecordService domainApplicationRecordService;
    @Resource
    private DomainAccountMapper domainAccountMapper;

    @Value("${readImagepath}")
    private String readImagepath;// 图片访问路径,存到数据库

    @Value("${ylChainId}")
    private Integer chainId;

    /**
     * 设置域名所有者
     *
     * @param chainId 链底
     * @param domain 域名
     * @param address 地址
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/07
     */
    @Override
    public R setDomainOwnerService(Integer chainId, String domain, String address) {
        Account userInfo = accountService.getUserInfo();
        Map<String, Object> paramMap = new HashMap<>(5);
        paramMap.put("domain", domain);
        paramMap.put("level", 1);
        paramMap.put("newOwner", address);

        paramMap.put("notifyUrl", globalConfigService.getGlobalConfig(SET_DOMAIN_OWNER_NOTIFYURL));
        long timeStamp = DateUtil.date().getTime();
        paramMap.put("outTradeNo", "YM" + timeStamp);
        String now = DateUtil.now();
        paramMap.put("timestamp", now);
        JSONObject jsonObject =
            JSONObject.parseObject((String)BsnDomainUtil.setOwner(JSON.toJSONString(paramMap)));
        Map<String, Object> result = JsonUtils.toMap(jsonObject);
        // 设置所有者请求成功 等待回调
        if (ObjectUtil.equals(result.get("code"), 0)) {
            // 创建设置管理者记录
            DomainAdminRecord domainAdminRecord = new DomainAdminRecord();
            domainAdminRecord.setSubTime(DateUtil.parse(now));
            domainAdminRecord.setOutTradeNo("YM" + timeStamp);
            domainAdminRecord.setDomain(domain);
            domainAdminRecord.setLevel(1);
            domainAdminRecord.setOwner(address);
            domainAdminRecord.setType(1);
            domainAdminRecord.setChainId(1001);
            domainAdminRecord.setAccountUuid(userInfo.getUuid());
            this.save(domainAdminRecord);
        } else {
            throw new ServiceException(jsonObject.toString());
        }
        return R.ok();
    }

    /**
     * 设置所有这回调通知
     *
     * @param request
     * <AUTHOR>
     * @date 2024/04/07
     */
    @Override
    public void setOwnerNotifyService(HttpServletRequest request) {
        String data = HttpUtil.readData(request);
        log.info("设置所有者回调参数:{}", data);
        Map<String, Object> result = JsonUtils.toMap(data);
        if (result != null) {
            String outTradeNo = (String)result.get("outTradeNo");
            Integer status = Integer.valueOf(result.get("status").toString());
            // 判断回调的状态 2为处理完成
            if (ObjectUtil.equals(status, 2)) {
                DomainAdminRecord one = this.getOne(Wrappers.<DomainAdminRecord>lambdaQuery()
                    .eq(DomainAdminRecord::getOutTradeNo, outTradeNo));
                // 判断数据库的状态是否为未完成
                if (one.getState() == 1 && one != null) {
                    this.update(null,
                        Wrappers.<DomainAdminRecord>lambdaUpdate().eq(DomainAdminRecord::getId, one.getId())
                            .set(DomainAdminRecord::getState, status)
                            .set(DomainAdminRecord::getDomianServeSn, (String)result.get("domainServeSn"))
                            .set(DomainAdminRecord::getTradeCode, (String)result.get("tradeCode")));
                    log.info("设置所有者状态修改完成");
                }
            }
        }
    }

    /**
     * 设置所有者详情
     *
     * @param domain 域名
     * @param page 页
     * @param pageSize 页码
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/07
     */
    @Override
    public R setOwnerDetailService(String domain, int page, int pageSize) {
        Account userInfo = accountService.getUserInfo();
        Map<String, Object> resultData = new HashMap<>();
        Map<String, Object> paramMap = new HashMap<>(5);
        paramMap.put("domain", domain);
        JSONObject jsonObject =
            JSONObject.parseObject((String)BsnDomainUtil.domainSearch(JSON.toJSONString(paramMap)));
        Map<String, Object> result = JsonUtils.toMap(jsonObject);
        if (ObjectUtil.equals(result.get("code"), 0)) {
            Map<String, Object> data = JsonUtils.toMap(result.get("data"));
            if (data != null) {
                resultData.put("owner", data.get("owner") == null ? null : data.get("owner").toString());
            }
        } else {
            throw new ServiceException(jsonObject.toString());
        }
        // 查询设置所有者记录
        Integer totalCount = domainAdminRecordMapper.selectCount(Wrappers.<DomainAdminRecord>lambdaQuery()
            .eq(DomainAdminRecord::getAccountUuid, userInfo.getUuid()).eq(DomainAdminRecord::getType, 1)
            .eq(DomainAdminRecord::getDomain, domain));

        int start = (page - 1) * pageSize;
        List<DomainAdminRecord> noticeList =
            domainAdminRecordMapper.getPage(start, pageSize, userInfo.getUuid(), domain, 1);
        PageUtils pageUtils = new PageUtils(totalCount, pageSize, page, noticeList);

        resultData.put("pageList", pageUtils);
        return R.okData(resultData);
    }

    /**
     * @param domain
     * @param domainAdminInfo
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/07
     */
    @Override
    public R setDomainAdminService(Object domain, Object domainAdminInfo) {
        Account userInfo = accountService.getUserInfo();
        Map<String, Object> paramMap = new HashMap<>(5);
        paramMap.put("domain", domain.toString());
        paramMap.put("level", 1);
        paramMap.put("notifyUrl", globalConfigService.getGlobalConfig(SET_DOMAIN_ADMIN_NOTIFYURL));
        long timeStamp = DateUtil.date().getTime();
        paramMap.put("outTradeNo", "YM" + timeStamp);
        String now = DateUtil.now();
        paramMap.put("timestamp", now);
        paramMap.put("domainAdminInfo", domainAdminInfo);
        JSONObject jsonObject =
            JSONObject.parseObject((String)BsnDomainUtil.setAdmin(JSON.toJSONString(paramMap)));
        Map<String, Object> result = JsonUtils.toMap(jsonObject);
        // 设置所有者请求成功 等待回调
        if (ObjectUtil.equals(result.get("code"), 0)) {
            // 组装下参数
            List<DomainAdminInfoVo> domainAdminInfoVos =
                JSON.parseArray(JSON.toJSONString(domainAdminInfo), DomainAdminInfoVo.class);
            List<DomainAdminInfoVo> domainAdminInfoVos1 = parameterAssembly(domainAdminInfoVos);
            // 创建设置管理者记录
            DomainAdminRecord domainAdminRecord = new DomainAdminRecord();
            domainAdminRecord.setSubTime(DateUtil.parse(now));
            domainAdminRecord.setOutTradeNo("YM" + timeStamp);
            domainAdminRecord.setDomain(domain.toString());
            domainAdminRecord.setLevel(1);
            domainAdminRecord.setManager(JSON.toJSONString(domainAdminInfoVos1));
            domainAdminRecord.setType(2);
            domainAdminRecord.setAccountUuid(userInfo.getUuid());
            this.save(domainAdminRecord);
        } else {
            throw new ServiceException(jsonObject.toString());
        }
        return R.ok();

    }

    /**
     * 设置管理者回调通知
     * 
     * @param request
     * <AUTHOR>
     * @date 2024/04/07
     */
    @Override
    public void setAdminNotifyService(HttpServletRequest request) {
        String data = HttpUtil.readData(request);
        log.info("设置管理者回调参数:{}", data);
        Map<String, Object> result = JsonUtils.toMap(data);
        if (result != null) {
            String outTradeNo = (String)result.get("outTradeNo");
            Integer status = Integer.valueOf(result.get("status").toString());
            // 判断回调的状态 2为处理完成
            if (ObjectUtil.equals(status, 2)) {
                DomainAdminRecord one = this.getOne(Wrappers.<DomainAdminRecord>lambdaQuery()
                    .eq(DomainAdminRecord::getOutTradeNo, outTradeNo));
                // 判断数据库的状态是否为未完成
                if (one.getState() == 1 && one != null) {
                    this.update(null,
                        Wrappers.<DomainAdminRecord>lambdaUpdate().eq(DomainAdminRecord::getId, one.getId())
                            .set(DomainAdminRecord::getState, status)
                            .set(DomainAdminRecord::getDomianServeSn, (String)result.get("domainServeSn")));
                    log.info("设置管理者状态修改完成");
                }
            }
        }
    }

    /**
     * 设置域名管理者详情
     * 
     * @param domain 域名
     * @param page 页
     * @param pageSize 页码
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/07
     */
    @Override
    public R setAdminDetailService(String domain, int page, int pageSize) {
        Account userInfo = accountService.getUserInfo();
        Map<String, Object> resultData = new HashMap<>();
        Map<String, Object> paramMap = new HashMap<>(5);
        paramMap.put("domain", domain);
        JSONObject jsonObject =
            JSONObject.parseObject((String)BsnDomainUtil.searchsAdmin(JSON.toJSONString(paramMap)));
        Map<String, Object> result = JsonUtils.toMap(jsonObject);
        if (ObjectUtil.equals(result.get("code"), 0)) {
            Map<String, Object> data = JsonUtils.toMap(result.get("data"));
            List<DomainAdminInfoVo> domainAdminInfoVos1 = null;
            if (data != null) {
                // 设置链名称
                List<Object> domainAdminInfo = JsonUtils.toList(data.get("domainAdminInfo"));
                List<DomainAdminInfoVo> domainAdminInfoVos =
                    JSON.parseArray(JSON.toJSONString(domainAdminInfo), DomainAdminInfoVo.class);
                domainAdminInfoVos1 = parameterAssembly(domainAdminInfoVos);
            }
            resultData.put("domainAdminInfo", domainAdminInfoVos1);
        } else {
            throw new ServiceException(jsonObject.toString());
        }
        // 查询设置管理者记录
        Integer totalCount = domainAdminRecordMapper.selectCount(Wrappers.<DomainAdminRecord>lambdaQuery()
            .eq(DomainAdminRecord::getAccountUuid, userInfo.getUuid()).eq(DomainAdminRecord::getType, 2)
            .eq(DomainAdminRecord::getDomain, domain));

        int start = (page - 1) * pageSize;
        List<DomainAdminRecord> noticeList =
            domainAdminRecordMapper.getPage(start, pageSize, userInfo.getUuid(), domain, 2);

        // List<DomainAdminRecordVo> domainAdminRecordVos = new ArrayList<>();
        // for (DomainAdminRecord domainAdminRecord : noticeList) {
        // DomainAdminRecordVo domainAdminRecordVo = new DomainAdminRecordVo();
        // BeanUtils.copyProperties(domainAdminRecord, domainAdminRecordVo);
        // List<DomainAdminInfoVo> domainAdminInfoVos =
        // JSON.parseArray(domainAdminRecord.getManager(), DomainAdminInfoVo.class);
        // domainAdminRecordVo.setManager(domainAdminInfoVos);
        // domainAdminRecordVos.add(domainAdminRecordVo);
        // }
        PageUtils pageUtils = new PageUtils(totalCount, pageSize, page, noticeList);
        resultData.put("pageList", pageUtils);
        return R.okData(resultData);
    }

    /**
     * 设置域名解析纪录
     * 
     * @param domain 域名
     * @param resolveInfos 解析参数
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/07
     */
    @Override
    public R setDomainResolveService(Object domain, Object resolveInfos) {
        Account userInfo = accountService.getUserInfo();
        Map<String, Object> paramMap = new HashMap<>(5);
        paramMap.put("domain", domain.toString());
        paramMap.put("level", 1);
        paramMap.put("notifyUrl", globalConfigService.getGlobalConfig(SET_DOMAIN_RESOLVE_NOTIFYURL));
        long timeStamp = DateUtil.date().getTime();
        paramMap.put("outTradeNo", "YM" + timeStamp);
        String now = DateUtil.now();
        paramMap.put("timestamp", now);

        paramMap.put("resolveInfos", resolveInfos);

        JSONObject jsonObject =
            JSONObject.parseObject((String)BsnDomainUtil.setResolveInfo(JSON.toJSONString(paramMap)));
        Map<String, Object> result = JsonUtils.toMap(jsonObject);
        // 设置所有者请求成功 等待回调
        if (ObjectUtil.equals(result.get("code"), 0)) {
            // 创建设置管理者记录
            DomainAdminRecord domainAdminRecord = new DomainAdminRecord();
            domainAdminRecord.setSubTime(DateUtil.parse(now));
            domainAdminRecord.setOutTradeNo("YM" + timeStamp);
            domainAdminRecord.setDomain(domain.toString());
            domainAdminRecord.setLevel(1);
            domainAdminRecord.setType(3);
            domainAdminRecord.setResolveInfos(assembleResolveParam(resolveInfos));
            domainAdminRecord.setAccountUuid(userInfo.getUuid());
            this.save(domainAdminRecord);
        } else {
            throw new ServiceException(jsonObject.toString());
        }
        return R.ok();
    }

    /**
     * 设置解析纪录回调通知
     * 
     * @param request
     * <AUTHOR>
     * @date 2024/04/07
     */
    @Override
    public void setResolveNotifyService(HttpServletRequest request) {
        String data = HttpUtil.readData(request);
        log.info("设置解析记录回调参数:{}", data);
        Map<String, Object> result = JsonUtils.toMap(data);
        if (result != null) {
            String outTradeNo = (String)result.get("outTradeNo");
            Integer status = Integer.valueOf(result.get("status").toString());
            // 判断回调的状态 2为处理完成
            if (ObjectUtil.equals(status, 2)) {
                DomainAdminRecord one = this.getOne(Wrappers.<DomainAdminRecord>lambdaQuery()
                    .eq(DomainAdminRecord::getOutTradeNo, outTradeNo));
                // 判断数据库的状态是否为未完成
                if (one.getState() == 1 && one != null) {
                    this.update(null,
                        Wrappers.<DomainAdminRecord>lambdaUpdate().eq(DomainAdminRecord::getId, one.getId())
                            .set(DomainAdminRecord::getState, status)
                            .set(DomainAdminRecord::getDomianServeSn, (String)result.get("domainServeSn")));
                    log.info("设置解析状态修改完成");
                }
            }
        }

    }

    /**
     * 获取域名解析纪录详情
     * 
     * @param domain 域名
     * @param page 页
     * @param pageSize 页码
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/07
     */
    @Override
    public R setReolveDetailService(String domain, int page, int pageSize) {
        Account userInfo = accountService.getUserInfo();
        Map<String, Object> resultData = new HashMap<>();
        Map<String, Object> paramMap = new HashMap<>(5);
        Map<String, Object> paramMap1 = new HashMap<>(5);
        Map<String, Object> paramMap2 = new HashMap<>(5);
        paramMap1.put("domain", domain);
        paramMap2.put("pageNum", page);
        paramMap2.put("pageSize", pageSize);
        paramMap.put("data", paramMap1);
        paramMap.put("page", paramMap2);

        JSONObject jsonObject =
            JSONObject.parseObject((String)BsnDomainUtil.resolveSearches(JSON.toJSONString(paramMap)));
        Map<String, Object> result = JsonUtils.toMap(jsonObject);

        if (ObjectUtil.equals(result.get("code"), 0)) {
            resultData.put("domainAdminInfo", queryResolveParamEncapsulation(result));
        } else {
            throw new ServiceException(jsonObject.toString());
        }
        // 查询设置管理者记录
        Integer totalCount = domainAdminRecordMapper.selectCount(Wrappers.<DomainAdminRecord>lambdaQuery()
            .eq(DomainAdminRecord::getAccountUuid, userInfo.getUuid()).eq(DomainAdminRecord::getType, 3)
            .eq(DomainAdminRecord::getDomain, domain));

        int start = (page - 1) * pageSize;
        List<DomainAdminRecord> noticeList =
            domainAdminRecordMapper.getPage(start, pageSize, userInfo.getUuid(), domain, 3);
        PageUtils pageUtils = new PageUtils(totalCount, pageSize, page, noticeList);
        resultData.put("pageList", pageUtils);
        return R.okData(resultData);
    }

    /**
     * 获取域名绑定状态
     * 
     * @param domain 域名
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/19
     */
    @Override
    public R getDomainBindStateService(String domain) {
        Map<String, Object> stringObjectMap = domainMappingUtilParam.domainBindState(domain);
        return R.okData(stringObjectMap);
    }

    /**
     * 一键解除域名绑定
     * 
     * @param domain 域名
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/20
     */
    @Override
    @Transactional
    public R getDomainClearService(String domain) {
        // 一键解除后还要移除 数据库域名地址映射
        boolean b = domainMappingUtilParam.clearBinad(domain);
        if (b) {
            Account userInfo = accountService.getUserInfo();

            AccountDomainAddress one = accountDomainAddressMapper.selectOne(
                Wrappers.<AccountDomainAddress>lambdaQuery().eq(AccountDomainAddress::getOpbChainId, chainId)
                    .eq(AccountDomainAddress::getDomain, domain).eq(AccountDomainAddress::getState, true));
            if (one == null) {
                return R.okData(true);
            }
            if (one.getAccountUuid().equals(userInfo.getUuid())) {

                accountDomainAddressMapper.update(null, Wrappers.<AccountDomainAddress>lambdaUpdate()
                    .eq(AccountDomainAddress::getId, one.getId()).set(AccountDomainAddress::getState, false));
                // 修改域名绑定状态
                domainAccountMapper.update(null, Wrappers.<DomainAccount>lambdaUpdate()
                    .eq(DomainAccount::getDomain, domain).set(DomainAccount::getChainAddressBindState, 1));

                domainApplicationRecordService.deleteRecord(userInfo.getUuid(), one.getOpbChainId(),
                    one.getDomain(), 1);
            } else {
                throw new ServiceException(ResponseEnum.IllegalRemoval);
            }
        }
        return R.okData(b);
    }

    @Override
    @Transactional
    public boolean getDomainClearService(String domain, String uuid) {
        // 一键解除后还要移除 数据库域名地址映射
        boolean b = domainMappingUtilParam.clearBinad(domain);
        if (b) {
            AccountDomainAddress one = accountDomainAddressMapper.selectOne(
                Wrappers.<AccountDomainAddress>lambdaQuery().eq(AccountDomainAddress::getOpbChainId, chainId)
                    .eq(AccountDomainAddress::getDomain, domain).eq(AccountDomainAddress::getState, true));
            if (one == null) {
                return true;
            }
            if (one.getAccountUuid().equals(uuid)) {
                Account userInfo = accountMapper.selectOne(Wrappers.<Account>lambdaQuery().eq(Account::getUuid, uuid));
                String domainNickName = userInfo.getDomainNickName();
                if(ObjectUtil.equals(domainNickName, domain)){
                    accountMapper.update(null,
                            Wrappers.<Account>lambdaUpdate().eq(Account::getUuid, userInfo.getUuid())
                                    .set(Account::getShowType, 1)
                                    .set(Account::getDomainNickName, null));
                }
                accountDomainAddressMapper.update(null, Wrappers.<AccountDomainAddress>lambdaUpdate()
                    .eq(AccountDomainAddress::getId, one.getId()).set(AccountDomainAddress::getState, false));
                // 修改域名绑定状态
                domainAccountMapper.update(null, Wrappers.<DomainAccount>lambdaUpdate()
                    .eq(DomainAccount::getDomain, domain).set(DomainAccount::getChainAddressBindState, 1));

                domainApplicationRecordService.deleteRecord(uuid, one.getOpbChainId(), one.getDomain(), 1);
                log.info(domain + "一键解除域名绑定成功");
            } else {
                return false;
            }
        }
        return b;
    }

    // ======================工具方法=======================

    /**
     * 组装参数
     *
     * @param domainAdminInfoVos
     * @return {@link List}<{@link DomainAdminInfoVo}>
     */
    private List<DomainAdminInfoVo> parameterAssembly(List<DomainAdminInfoVo> domainAdminInfoVos) {
        // 获取链信息
        if (domainAdminInfoVos != null) {
            List<Chain> chainList =
                chainMapper.selectList(Wrappers.<Chain>lambdaQuery().isNotNull(Chain::getChainId));
            for (DomainAdminInfoVo domainAdminInfoVo : domainAdminInfoVos) {
                for (Chain chain : chainList) {
                    if (chain.getChainId().equals(domainAdminInfoVo.getChainId())) {
                        domainAdminInfoVo.setChainName(chain.getChainName());
                        domainAdminInfoVo.setChainLogo(readImagepath + chain.getChainLogo());
                    }
                }
            }
        }
        return domainAdminInfoVos;
    }

    /**
     * 组装解析记录参数
     *
     * @param resolveInfos
     * @return {@link Map}<{@link String}, {@link Object}>
     */
    private String assembleResolveParam(Object resolveInfos) {
        Map<String, Object> dataMap = JsonUtils.toMap(resolveInfos);
        Object address = dataMap.get("address");
        if (address != null) {
            List<Object> addressList = JsonUtils.toList(address);
            List<DomainAdminInfoVo> domainAdminInfoVos =
                JSON.parseArray(JSON.toJSONString(addressList), DomainAdminInfoVo.class);
            List<DomainAdminInfoVo> domainAdminInfoVos1 = parameterAssembly(domainAdminInfoVos);
            dataMap.put("address", domainAdminInfoVos1);
        }
        return JSON.toJSONString(dataMap);
    }

    /**
     * 域名解析详情参数封装
     *
     * @param result
     * @return {@link List}<{@link DomainAdminInfoVo}>
     */
    private Map<String, Object> queryResolveParamEncapsulation(Map<String, Object> result) {
        Map<String, Object> paramResult = new HashMap<>();
        List<Object> data = JsonUtils.toList(result.get("data"));
        if (CollectionUtil.isNotEmpty(data)) {
            Object o = data.get(0);
            Map<String, Object> mapParam = JsonUtils.toMap(o);
            if (mapParam != null) {
                Object resolveInfos = mapParam.get("resolveInfos");
                Map<String, Object> resolveMap = JsonUtils.toMap(resolveInfos);
                if (resolveMap != null) {
                    Object address = resolveMap.get("address");
                    Object text = resolveMap.get("text");
                    if (address != null) {
                        List<Object> addressList = JsonUtils.toList(address);
                        List<DomainAdminInfoVo> domainAdminInfoVos =
                            JSON.parseArray(JSON.toJSONString(addressList), DomainAdminInfoVo.class);
                        List<DomainAdminInfoVo> domainAdminInfoVos1 = parameterAssembly(domainAdminInfoVos);
                        paramResult.put("address", domainAdminInfoVos1);
                    }
                    if (text != null) {
                        paramResult.put("text", text);
                    }
                }
            }
        }
        return paramResult;
    }
}
