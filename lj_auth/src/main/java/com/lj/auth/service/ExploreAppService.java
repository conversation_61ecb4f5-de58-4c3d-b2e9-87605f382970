package com.lj.auth.service;

import com.lj.auth.common.R;
import com.lj.auth.domain.ExploreApp;
import com.baomidou.mybatisplus.extension.service.IService;

public interface ExploreAppService extends IService<ExploreApp> {

    /**
     * 获取应用列表
     * 
     * @param page 页
     * @param pageSize 页大小
     * @param appType --0-获取全部 app类别id
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/01
     */
    R getAppListService(int page, int pageSize, Integer appType);

    /**
     * 获取推荐应用列表
     * 
     * @param page 页
     * @param pageSize 页大小
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/01
     */
    R getRecommendationListService(int page, int pageSize);

    /**
     * 应用搜索
     * 
     * @param keyWord 搜索关键字
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/01
     */
    R searchAppService(String keyWord);

    /**
     * 搜索历史纪录
     * 
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/01
     */
    R searchRecordListService();

    /**
     * 删除搜索历史纪录
     * 
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/01
     */
    R removeRecordListService();

    /**
     * 获取官方应用列表
     * 
     * @param page
     * @param pageSize
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/01
     */
    R getOfficialListService(int page, int pageSize);
}
