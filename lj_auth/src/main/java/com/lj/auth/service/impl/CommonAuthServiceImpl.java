package com.lj.auth.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lj.auth.common.R;
import com.lj.auth.domain.Account;
import com.lj.auth.exception.ResponseEnum;
import com.lj.auth.mapper.AccountMapper;
import com.lj.auth.notify.sms.NotifyService;
import com.lj.auth.service.CommonAuthService;
import com.lj.auth.util.CharUtil;
import com.lj.auth.util.RedisUtils;
import com.lj.auth.util.RegexUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.lj.auth.common.CommonConstant.SMS_TYPE_VERIFICATION_CODE;
import static com.lj.auth.common.CommonConstant.USER_BASIC_INFORMATION;

@Service
public class CommonAuthServiceImpl implements CommonAuthService {
    @Resource
    private RedisUtils redisUtils;
    @Resource
    private AccountMapper accountMapper;
    @Value("${readImagepath}")
    private String readImagepath;// 图片访问路径,存到数据库
    @Value("${SMSOpen}")
    private Boolean SMSOpen;
    @Resource
    private NotifyService notifyService;

    /**
     * 缓存用户信息
     * 
     * @param userInfo
     */
    @Override
    @Async
    public void cacheUserInformation(Account userInfo) {
        Map<String, String> map = new HashMap<>();
        String didSymbol = userInfo.getDidSymbol();
        if (StrUtil.isNotEmpty(didSymbol)) {
            String key = USER_BASIC_INFORMATION + ":" + didSymbol;
            map.put("headPortrait", readImagepath + userInfo.getHeadPortrait());
            map.put("nickName", userInfo.getNickName());
            redisUtils.set(key, map);
        }
    }

    @Override
    public R getBaseUserInfoService(String didSymbol) { // 刷新用户信息
        String key = USER_BASIC_INFORMATION + ":" + didSymbol;
        Object o = redisUtils.get(key);
        if (o == null) {
            Account account =
                accountMapper.selectOne(Wrappers.<Account>lambdaQuery().eq(Account::getDidSymbol, didSymbol));
            if (account == null) {
                return R.error("未查询到有效的用户信息");
            }
            // 刷新用户信息
            cacheUserInformation(account);
        }
        return R.okData(redisUtils.get(key));
    }

    /**
     * 批量获取用户基础信息
     * 
     * @param didSymbols
     * @return {@link R }
     */
    @Override
    public R getBatchBaseUserInfoAllService(List<String> didSymbols) {
        List<Map<String, String>> result = new ArrayList<>();
        for (String didSymbol : didSymbols) {
            String key = USER_BASIC_INFORMATION + ":" + didSymbol;
            Object o = redisUtils.get(key);
            if (o == null) {
                Account account = accountMapper
                    .selectOne(Wrappers.<Account>lambdaQuery().eq(Account::getDidSymbol, didSymbol));
                if (account == null) {
                    return R.error("未查询到有效的用户信息");
                }
                // 刷新用户信息
                cacheUserInformation(account);
            }
            Map<String, String> map = JSONUtil.toBean(JSONUtil.toJsonStr(redisUtils.get(key)), Map.class);
            result.add(map);
        }
        return R.okData(result);
    }

    /**
     * 批量获取用户基础信息
     * 
     * @param didSymbols
     * @param type
     * @return {@link R }
     */
    @Override
    public R getBatchBaseUserInfoService(List<String> didSymbols, Integer type) {
        List<String> result = new ArrayList<>();
        for (String didSymbol : didSymbols) {
            String key = USER_BASIC_INFORMATION + ":" + didSymbol;
            Object o = redisUtils.get(key);
            if (o == null) {
                Account account = accountMapper
                    .selectOne(Wrappers.<Account>lambdaQuery().eq(Account::getDidSymbol, didSymbol));
                if (account == null) {
                    return R.error("未查询到有效的用户信息");
                }
                // 刷新用户信息
                cacheUserInformation(account);
            }
            Map<String, String> map = JSONUtil.toBean(JSONUtil.toJsonStr(redisUtils.get(key)), Map.class);
            // 1-昵称
            if (type == 1) {
                String nickName = map.get("nickName");
                result.add(nickName);
                // 2-头像
            } else if (type == 2) {
                String headPortrait = map.get("headPortrait");
                result.add(headPortrait);
            }
        }
        return R.okData(result);
    }

    /**
     * 验证码
     *
     * @param account
     * @param verificationCode
     * @return {@link R }
     */
    @Override
    public R sentCaptcha(String account, String verificationCode) {
        Assert.isTrue(RegexUtil.isMobileSimple(account), ResponseEnum.PhoneNumberFormatError.getMsg());
        boolean validateResult = true;
        if (SMSOpen) {
            validateResult = notifyService.sendSms(SMS_TYPE_VERIFICATION_CODE, account, verificationCode);
        }
        return R.okData(validateResult);
    }
}
