package com.lj.auth.service;

import com.lj.auth.common.R;
import com.lj.auth.domain.GlobalConfig;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lj.auth.domain.ModuleSwitch;

import java.math.BigDecimal;
import java.util.List;

public interface GlobalConfigService extends IService<GlobalConfig> {

    /**
     * 开票信息配置
     * 
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/28
     */
    R getInvoiceConfig();

    /**
     * 获取通用系统配置
     * 
     * @param key key
     * @return {@link String }
     * <AUTHOR>
     * @date 2024/04/01
     */
    String getGlobalConfig(String key);

    /**
     * 获取签到配置
     *
     * @param key key
     * @return {@link List }<{@link Integer }>
     * <AUTHOR>
     * @date 2024/04/02
     */
    List<BigDecimal> getSignGlobalConfig(String key);

    /**
     * 获取提现协议
     * 
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/03
     */
    R getWithdrawalProtocolService();

    /**
     * 获取功能模块开关
     * @param channel
     * @param version
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/04/07
     */
    R getModuleSwitchService(String channel, String version);

    /**
     * 新增
     * @param function
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/04/07
     */
    R addSwitchService(ModuleSwitch function);
}
