package com.lj.auth.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lj.auth.domain.*;
import com.lj.auth.mapper.*;
import com.lj.auth.util.RedisUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

import static com.lj.auth.common.CommonConstant.*;
import static com.lj.auth.common.CommonConstant.AVATAR;

@Component
public class BadgeAndPendantHelper {

    @Autowired
    private BadgeMapper badgeMapper;

    @Autowired
    private UserBadgeMapper userBadgeMapper;

    @Autowired
    private AvatarFrameMapper avatarFrameMapper;

    @Autowired
    private UserAvatarFrameMapper userAvatarFrameMapper;

    @Autowired
    private RedisUtils redisUtils;
    @Resource
    private SkySplitUserMapper  skySplitUserMapper;

    /**
     * 异步刷新徽章
     *
     * @param userInfo
     */
    @Async
    protected void asyncRefreshBadgeAcquisition(Account userInfo) {
        // 判断用户是否获取 域名昵称徽章
        String domainKey = BADGE + ":" + DOMAIN_NAME_BADGE + ":" + userInfo.getUuid();
        // 判断用户是否获取 推广大使徽章
        String promotionKey = BADGE + ":" + PROMOTION_AMBASSADOR_BADGE + ":" + userInfo.getUuid();

        // 判断用户 是否获取 裂空者徽章
        String skySplitKey = BADGE + ":" + SKY_SPLIT_BADGE + ":" + userInfo.getUuid();

        String domainNickName = userInfo.getDomainNickName();
        // 判断是推广大使 1 2 3 9
        Integer promotionLevel = userInfo.getPromotionLevel();

        Integer skySplitLevel = userInfo.getSkySplitLevel();

        // 没有完成域名徽章获取
        if (!redisUtils.hasKey(domainKey) && StrUtil.isNotBlank(domainNickName)) {
            // 判断是否颁发过域名徽章
            Badge badge =
                badgeMapper.selectOne(Wrappers.<Badge>lambdaQuery().eq(Badge::getName, DOMAIN_NAME_BADGE));
            UserBadge userBadge = userBadgeMapper.selectOne(Wrappers.<UserBadge>lambdaQuery()
                .eq(UserBadge::getBadgeId, badge.getId()).eq(UserBadge::getAccountUuid, userInfo.getUuid()));
            if (userBadge == null) {
                // 给用户发送域名徽章
                addBadge(userInfo, badge);
            } else {
                redisUtils.set(domainKey, true);
            }
        }
        // 没有完成推广大使徽章获取
        if (!redisUtils.hasKey(promotionKey)
            && (promotionLevel == 1 || promotionLevel == 2 || promotionLevel == 3 || promotionLevel == 9)) {
            // 判断是否颁发过推广大使徽章
            Badge badge = badgeMapper
                .selectOne(Wrappers.<Badge>lambdaQuery().eq(Badge::getName, PROMOTION_AMBASSADOR_BADGE));
            UserBadge userBadge = userBadgeMapper.selectOne(Wrappers.<UserBadge>lambdaQuery()
                .eq(UserBadge::getBadgeId, badge.getId()).eq(UserBadge::getAccountUuid, userInfo.getUuid()));
            if (userBadge == null) {
                // 给用户发送域名徽章
                addBadge(userInfo, badge);
            } else {
                redisUtils.set(promotionKey, true);
            }
        }
        // 没有完成裂空者徽章获取
        if (!redisUtils.hasKey(skySplitKey) && (skySplitLevel != null && skySplitLevel > 0)) {
            // 判断是否颁发过 裂空者徽章
            Badge badge =
                badgeMapper.selectOne(Wrappers.<Badge>lambdaQuery().eq(Badge::getName, SKY_SPLIT_BADGE));
            UserBadge userBadge = userBadgeMapper.selectOne(Wrappers.<UserBadge>lambdaQuery()
                .eq(UserBadge::getBadgeId, badge.getId()).eq(UserBadge::getAccountUuid, userInfo.getUuid()));
            if (userBadge == null) {
                // 给用户发送域名徽章
                addBadge(userInfo, badge);
            } else {
                redisUtils.set(skySplitKey, true);
            }
        }

    }

    private void addBadge(Account userInfo, Badge badge) {
        Date createTime =new Date();
        String name = badge.getName();
        //判断是列空者 查询用户成为裂空者时间
        if (name.equals(SKY_SPLIT_BADGE)) {
            SkySplitUser skySplitUser =  skySplitUserMapper.selectOne(
                    Wrappers.<SkySplitUser>lambdaQuery().eq(SkySplitUser::getAccountUuid, userInfo.getUuid()));
            if (skySplitUser != null) {
                createTime = skySplitUser.getCreateTime();
            }
        }
        //推广大使
        if (name.equals(PROMOTION_AMBASSADOR_BADGE)){
             createTime=userInfo.getPromotionTime();
             if (createTime == null)  {
                 createTime = new Date();
             }
        }
        UserBadge userBadge = new UserBadge();
        userBadge.setAccountUuid(userInfo.getUuid());
        userBadge.setBadgeId(badge.getId());
        userBadge.setState(1);
        userBadge.setWearFlag(0);
        userBadge.setCreateTime(createTime);
        userBadgeMapper.insert(userBadge);
    }

    /**
     * 异步刷新挂件
     *
     * @param userInfo
     */
    @Async
    protected void asynchronousRefreshPendant(Account userInfo) {
        // 获取裂空者等级
        Integer level = userInfo.getSkySplitLevel();
        Long headPortraitNftId = userInfo.getHeadPortraitNftId();
        if (level != null && level > 0) {
            // 判断用户是否获取 裂空者相应挂件
            String skyKey = AVATAR + ":" + userInfo.getUuid() + ":" + level;
            if (!redisUtils.hasKey(skyKey)) {
                AvatarFrame avatarFrame = avatarFrameMapper.selectById(level);
                UserAvatarFrame userAvatarFrame = userAvatarFrameMapper.selectOne(
                    Wrappers.<UserAvatarFrame>lambdaQuery().eq(UserAvatarFrame::getAvatarFrameId, level)
                        .eq(UserAvatarFrame::getAccountUuid, userInfo.getUuid()));
                if (userAvatarFrame == null) {
                    // 给用户发送域名徽章
                    addAvatar(userInfo, avatarFrame);
                } else {
                    redisUtils.set(skyKey, true);
                }
            }
        }

        if (headPortraitNftId != null) {
            Integer nftLevel = 10;
            String nftKey = AVATAR + ":" + userInfo.getUuid() + ":" + nftLevel;
            if (!redisUtils.hasKey(nftKey)) {
                AvatarFrame avatarFrame = avatarFrameMapper.selectById(nftLevel);
                UserAvatarFrame userAvatarFrame = userAvatarFrameMapper.selectOne(
                    Wrappers.<UserAvatarFrame>lambdaQuery().eq(UserAvatarFrame::getAvatarFrameId, nftLevel)
                        .eq(UserAvatarFrame::getAccountUuid, userInfo.getUuid()));
                if (userAvatarFrame == null) {
                    // 给用户发送域名徽章
                    addAvatar(userInfo, avatarFrame);
                } else {
                    redisUtils.set(nftKey, true);
                }
            }

        }
    }

    private void addAvatar(Account userInfo, AvatarFrame avatarFrame) {
        String name = avatarFrame.getName();
        Date  createTime= new Date();
        //判断是列空者 查询用户成为裂空者时间
        if (name.equals(SKY_SPLIT_BADGE)) {
            SkySplitUser skySplitUser =  skySplitUserMapper.selectOne(
                Wrappers.<SkySplitUser>lambdaQuery().eq(SkySplitUser::getAccountUuid, userInfo.getUuid()));
            if (skySplitUser != null) {
                createTime = skySplitUser.getCreateTime();
            }

        }
        UserAvatarFrame userAvatarFrame = new UserAvatarFrame();
        userAvatarFrame.setAccountUuid(userInfo.getUuid());
        userAvatarFrame.setAvatarFrameId(avatarFrame.getId());
        userAvatarFrame.setState(1);
        userAvatarFrame.setAvatarFrameLevel(Integer.parseInt(avatarFrame.getId().toString()));
        userAvatarFrame.setAvatarFrameImage(avatarFrame.getDefaultImage());
        userAvatarFrame.setCreateTime(createTime);
        userAvatarFrameMapper.insert(userAvatarFrame);
    }
}
