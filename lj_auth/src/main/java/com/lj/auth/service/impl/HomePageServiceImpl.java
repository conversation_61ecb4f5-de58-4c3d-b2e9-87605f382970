package com.lj.auth.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lj.auth.common.R;
import com.lj.auth.domain.HomePage;
import com.lj.auth.domain.HomePageConfig;
import com.lj.auth.domain.vo.HomePageConfigVo;
import com.lj.auth.domain.vo.HomePageVo;
import com.lj.auth.mapper.GlobalConfigMapper;
import com.lj.auth.mapper.HomePageConfigMapper;
import com.lj.auth.mapper.HomePageMapper;
import com.lj.auth.service.HomePageService;
import com.lj.auth.util.RedisUtils;
import com.lj.auth.util.VersionCompareUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * @author: wxm
 * @description:
 * @date: 2024/12/26 11:49
 */
@Service
@Slf4j
public class HomePageServiceImpl implements HomePageService {
    @Resource
    private HomePageMapper homePageMapper;
    @Resource
    private HomePageConfigMapper homePageConfigMapper;
    @Resource
    private GlobalConfigMapper globalConfigMapper;
    @Resource
    private RedisUtils redisUtils;

    @Override
    public R getPart1(String channel) {
        //区域 1-顶部导航栏 2-固定金刚区 3-轮播图 4-功能金刚区 5-占位图 6-场景组件卡片区 7-首页背景图 8-底部导航栏
        //查询顶部导航栏
        HomePageVo homePageVo1 = homePageMapper.getHomePage(channel,"1");
        List<HomePageConfigVo> configList1 = homePageConfigMapper.getHomePageConfigList(channel,"1");
        if(configList1 == null){
            configList1 = new ArrayList<>();
        }
        homePageVo1.setCount(configList1.size());
        homePageVo1.setConfigList(configList1);

        //查询固定金刚区
        HomePageVo homePageVo2 = homePageMapper.getHomePage(channel,"2");
        List<HomePageConfigVo> configList2= homePageConfigMapper.getHomePageConfigList(channel,"2");
        if(configList2 == null){
            configList2 = new ArrayList<>();
        }
        homePageVo2.setCount(configList2.size());
        homePageVo2.setConfigList(configList2);

        //查询底部导航栏
        HomePageVo homePageVo8 = homePageMapper.getHomePage(channel,"8");
        List<HomePageConfigVo> configList8= homePageConfigMapper.getHomePageConfigList(channel,"8");
        if(configList8 == null){
            configList8 = new ArrayList<>();
        }
        homePageVo8.setCount(configList8.size());
        homePageVo8.setConfigList(configList8);
        //组装返回值
        Map<String,Object> resultMap = new HashMap<>();
        resultMap.put("top",homePageVo1);
        resultMap.put("fixedDiamondZone",homePageVo2);
        resultMap.put("menu",homePageVo8);
        return R.ok(resultMap);
    }

    @Override
    public R getPart2(String channel) {
        //区域 1-顶部导航栏 2-固定金刚区 3-轮播图 4-功能金刚区 5-占位图 6-场景组件卡片区 7-首页背景图 8-底部导航栏
        //查询轮播图
        HomePageVo homePageVo3 = homePageMapper.getHomePage(channel,"3");
        List<HomePageConfigVo> configList3 = homePageConfigMapper.getHomePageConfigList(channel,"3");
        if(configList3 == null){
            configList3 = new ArrayList<>();
        }
        homePageVo3.setCount(configList3.size());
        homePageVo3.setConfigList(configList3);

        //查询背景图
        HomePageVo homePageVo7 = homePageMapper.getHomePage(channel,"7");
        List<HomePageConfigVo> configList7= homePageConfigMapper.getHomePageConfigList(channel,"7");
        if(configList7 == null){
            configList7 = new ArrayList<>();
        }
        homePageVo7.setCount(configList7.size());
        homePageVo7.setConfigList(configList7);

        //顶部标题颜色
        String titleColor = "";
        if(redisUtils.hasKey("homepage_title_color")) {
            titleColor = redisUtils.get("homepage_title_color").toString();
        }else{
            titleColor = globalConfigMapper.queryConfig("homepage_title_color");
            redisUtils.set("homepage_title_color",titleColor);
        }
        //顶部背景颜色
        String backColor = "";
        if(redisUtils.hasKey("homepage_back_color")) {
            backColor = redisUtils.get("homepage_back_color").toString();
        }else{
            backColor = globalConfigMapper.queryConfig("homepage_back_color");
            redisUtils.set("homepage_back_color",backColor);
        }

        //组装返回值
        Map<String,Object> resultMap = new HashMap<>();
        resultMap.put("banner",homePageVo3);
        resultMap.put("background",homePageVo7);
        resultMap.put("titleColor",titleColor);
        resultMap.put("backColor",backColor);
        return R.ok(resultMap);
    }

    @Override
    public R getPart3(String channel,String version) {
        //区域 1-顶部导航栏 2-固定金刚区 3-轮播图 4-功能金刚区 5-占位图 6-场景组件卡片区 7-首页背景图 8-底部导航栏
        //查询功能金刚区
        HomePageVo homePageVo4 = homePageMapper.getHomePage(channel,"4");
        List<HomePageConfigVo> configList4 = homePageConfigMapper.getHomePageConfigList(channel,"4");
        if(configList4 == null){
            configList4 = new ArrayList<>();
        }
        homePageVo4.setCount(configList4.size());
        homePageVo4.setConfigList(configList4);

        //查询占位图
        HomePageVo homePageVo5 = homePageMapper.getHomePage(channel,"5");
        List<HomePageConfigVo> configList5= homePageConfigMapper.getHomePageConfigList(channel,"5");
        if(configList5 == null){
            configList5 = new ArrayList<>();
        }
        homePageVo5.setCount(configList5.size());
        homePageVo5.setConfigList(configList5);

        //查询场景组件卡片区
        HomePageVo homePageVo6 = homePageMapper.getHomePage(channel,"6");
        List<Integer> sceneTypeList = new ArrayList<>();
        sceneTypeList.add(1);
        sceneTypeList.add(2);
        sceneTypeList.add(3);
        sceneTypeList.add(4);
        if(StrUtil.isBlank(version)){
            version="2.0.7";
        }
        if(VersionCompareUtil.compareVersion(version,"2.0.7")){
            sceneTypeList.add(18);
            sceneTypeList.add(19);
            sceneTypeList.add(20);
            sceneTypeList.add(21);
            sceneTypeList.add(22);
        }
        List<HomePageConfigVo> configList6 = homePageConfigMapper.getHomePageConfigConditionSceneType(channel,"6",sceneTypeList);
        if(configList6 == null){
            configList6 = new ArrayList<>();
        }
        homePageVo6.setCount(configList6.size());
        homePageVo6.setConfigList(configList6);
        //组装返回值
        Map<String,Object> resultMap = new HashMap<>();
        resultMap.put("functionDiamondZone",homePageVo4);
        resultMap.put("reserve",homePageVo5);
        resultMap.put("scene",homePageVo6);
        return R.ok(resultMap);
    }

    /**
     * 获取话费功能区
     * @param channel
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/01/15
     */
    @Override
    public R getPhonePart(String channel) {
        Map<String, Object> resultMap = new HashMap<>();
        List<HomePageConfig> homePageConfigs = new ArrayList<>();
        try {
            HomePageConfig homePageConfig =
                homePageConfigMapper.selectOne(Wrappers.<HomePageConfig>lambdaQuery()
                    .eq(HomePageConfig::getChannel, channel).eq(HomePageConfig::getArea, 4)
                    .eq(HomePageConfig::getShowFlag, 1).eq(HomePageConfig::getContent, "景点游玩"));
            HomePageConfig homePageConfig1 =
                homePageConfigMapper.selectOne(Wrappers.<HomePageConfig>lambdaQuery()
                    .eq(HomePageConfig::getChannel, channel).eq(HomePageConfig::getArea, 4)
                    .eq(HomePageConfig::getShowFlag, 1).eq(HomePageConfig::getContent, "医疗健康"));
            homePageConfigs.add(homePageConfig);
            homePageConfigs.add(homePageConfig1);
        } catch (Exception e) {
            log.error("获取话费功能区失败", e);
        }
        resultMap.put("data", homePageConfigs);
        return R.ok(resultMap);
    }
}
