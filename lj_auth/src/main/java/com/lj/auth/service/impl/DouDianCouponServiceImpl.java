package com.lj.auth.service.impl;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.List;
import com.lj.auth.domain.DouDianCoupon;
import com.lj.auth.mapper.DouDianCouponMapper;
import com.lj.auth.service.DouDianCouponService;
/**
* <AUTHOR>
* @Description 
* @date 2025/8/13 17:11
*/
@Service
public class DouDianCouponServiceImpl extends ServiceImpl<DouDianCouponMapper, DouDianCoupon> implements DouDianCouponService{

    @Override
    public int updateBatchSelective(List<DouDianCoupon> list) {
        return baseMapper.updateBatchSelective(list);
    }
    @Override
    public int batchInsert(List<DouDianCoupon> list) {
        return baseMapper.batchInsert(list);
    }
}
