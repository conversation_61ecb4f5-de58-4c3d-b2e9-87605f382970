package com.lj.auth.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dtflys.forest.Forest;
import com.dtflys.forest.http.ForestRequest;
import com.lj.auth.common.R;
import com.lj.auth.domain.*;
import com.lj.auth.domain.vo.DidDocumentEntity;
import com.lj.auth.exception.ResponseEnum;
import com.lj.auth.exception.ServiceException;
import com.lj.auth.mapper.*;
import com.lj.auth.service.AccountService;
import com.lj.auth.service.GlobalConfigService;
import com.lj.auth.util.BNBUtil;
import com.lj.auth.util.MovieUtil;
import com.lj.auth.util.RedisUtils;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.asn1.gm.GMNamedCurves;
import org.bouncycastle.asn1.x9.X9ECParameters;
import org.bouncycastle.crypto.params.ECDomainParameters;
import org.bouncycastle.crypto.params.ECPublicKeyParameters;
import org.bouncycastle.crypto.params.ParametersWithID;
import org.bouncycastle.crypto.signers.SM2Signer;
import org.bouncycastle.math.ec.ECPoint;
import org.bouncycastle.util.Strings;
import org.bouncycastle.util.encoders.Hex;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lj.auth.service.DidLoginApplicationRecordService;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.web3j.protocol.core.methods.response.TransactionReceipt;

import static com.lj.auth.common.CommonConstant.*;

@Service
@Slf4j
public class DidLoginApplicationRecordServiceImpl
    extends ServiceImpl<DidLoginApplicationRecordMapper, DidLoginApplicationRecord>
    implements DidLoginApplicationRecordService {
    @Resource
    private DidQrCodeMapper didQrCodeMapper;

    @Resource
    private GlobalConfigService globalConfigService;
    @Resource
    private RedisUtils redisUtils;
    @Resource
    private AccountService accountService;
    @Resource
    private DidLoginApplicationMapper didLoginApplicationMapper;
    @Resource
    private AccountDidMapper accountDidMapper;
    @Resource
    private SystemNoticeMapper systemNoticeMapper;
    @Resource
    private NoticeAccountMapper noticeAccountMapper;
    @Resource
    private DidCheckInAccountMapper didCheckInAccountMapper;
    @Value("${did-check-url}")
    private String didCheckUrl;
    @Resource
    private DidGrantRecordMapper  didGrantRecordMapper;

    /**
     * 设置授权登录信息
     * 
     * @param data
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/07/01
     */
    @Override
    public R setSigDataSnService(Map<String, Object> data) {
        if (MapUtil.isEmpty(data)) {
            return R.error("参数为空");
        }
        if (!(data.containsKey("sn") && data.containsKey("expirationTime") && data.containsKey("noticeUrl")
            && data.containsKey("sign"))) {
            return R.error("缺少参数");
        }
        String sn = data.get("sn").toString();
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("sn", data.get("sn"));
        paramMap.put("expirationTime", data.get("expirationTime"));
        paramMap.put("noticeUrl", data.get("noticeUrl"));
        if (data.containsKey("searchDidIfExistUrl")) {
            paramMap.put("searchDidIfExistUrl", data.get("searchDidIfExistUrl"));
        }
        // 截取平台Id
        String platformId = StrUtil.subBefore(sn, "_", false);
        // 获取平台信息
        DidQrCode didQrCode = didQrCodeMapper.selectOne(Wrappers.<DidQrCode>lambdaQuery()
            .eq(DidQrCode::getApplicationId, Integer.valueOf(platformId)).eq(DidQrCode::getState, 1));
        if (didQrCode == null) {
            return R.error("平台暂未开放，请前往申请");
        }

        // 校验参数
        Map<String, Object> signedParamsMap =
            MovieUtil.getSignedParamsMap(paramMap, didQrCode.getAppSecret());
        if (!data.get("sign").equals(signedParamsMap.get("sign"))) {
            return R.error("签名错误");
        }
        // 缓存过期时间
        String expirationTime = globalConfigService.getGlobalConfig(AUTHORIZATION_EXPIRATION_TIME);
        String key = platformId + ":" + data.get("sn");
        redisUtils.set(key, data, Integer.valueOf(expirationTime));
        return R.ok();
    }

    /**
     * 扫码
     * 
     * @param data
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/07/01
     */
    @Override
    public R scanCodeService(Map<String, Object> data) {
        Account userInfo = accountService.getUserInfo();
        String didSymbol = userInfo.getDidSymbol();
        if (MapUtil.isEmpty(data)) {
            return R.error("参数为空");
        }
        if (StrUtil.isEmpty(didSymbol)) {
            return R.error("DID为空，请前往申领");
        }
        if (!data.containsKey("sn")) {
            return R.error("缺少参数");
        }
        String sn = data.get("sn").toString();
        System.out.println("平台sn参数："+sn);
        // 截取平台Id
        String platformId = StrUtil.subBefore(sn, "_", false);
        // 截取平台Id
        String key = platformId + ":" + sn;
        Map map = redisUtils.get(key, Map.class);
        log.info("缓存信息：",JSONObject.toJSONString(map));
        if (MapUtil.isEmpty(map)) {
            return R.error("二维码已过期");
        }
        if (ObjectUtil.equals(1, Integer.valueOf(platformId))) {
            // 判断用户身份非活动用户扫码无效
            DidCheckInAccount didCheckInAccount = didCheckInAccountMapper.selectOne(Wrappers
                .<DidCheckInAccount>lambdaQuery().eq(DidCheckInAccount::getAccountUuid, userInfo.getUuid()));
            if (didCheckInAccount == null) {
                return R.error("非活动用户不支持扫码");
            }
        } else {
            // 通过传来的url 来判断用户是否允许扫码登录
            String searchDidIfExistUrl = map.get("searchDidIfExistUrl").toString();
            R execute = Forest.post(searchDidIfExistUrl).addBody("didSymbol", didSymbol).execute(R.class);
            if (execute.get("code").equals(200)) {
                Object data1 = execute.get("data");
                if (data1 instanceof Map) {
                    Map<String, Object> map1 = (Map<String, Object>)data1;
                    if (!CollectionUtils.isEmpty(map1)) {
                        Integer count = Integer.valueOf(map1.get("count").toString());
                        if (count != 1) {
                            return R.error("该用户不支持扫码");
                        }
                    } else {
                        return R.error("该用户不支持扫码");
                    }
                } else {
                    return R.error("该用户不支持扫码");
                }
            } else {
                return R.error("该用户不支持扫码");
            }
        }
        // 获取应用信息
        DidLoginApplication didLoginApplication =
            didLoginApplicationMapper.selectOne(Wrappers.<DidLoginApplication>lambdaQuery()
                .eq(DidLoginApplication::getApplicationId, platformId).eq(DidLoginApplication::getState, 1));
        Map<String, Object> result = new HashMap<>();
        result.put("didSymbol", didSymbol);
        result.put("applicationName", didLoginApplication.getApplicationName());
        result.put("website", didLoginApplication.getWebsite());
        // 扫码成功 通知DID授权登录平台

        String noticeUrl = map.get("noticeUrl").toString();
        if (StrUtil.isNotBlank(noticeUrl)) {
            try {
                scanSuccessfulNotification(didSymbol, noticeUrl, data.get("sn").toString());
            } catch (Exception e) {
                log.error(e.getMessage());
            }
        }
        return R.okData(result);
    }

    /**
     * 扫码登录
     * 
     * @param data
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/07/01
     */
    @Override
    public R scanCodeLoginService(Map<String, Object> data) {
        Map<String, Object> result = new HashMap<>();
        Account userInfo = accountService.getUserInfo();
        String didSymbol = userInfo.getDidSymbol();
        if (MapUtil.isEmpty(data)) {
            return R.error("参数为空");
        }
        // 获取参数type 1-登录 0-取消登录
        if (!(data.containsKey("type"))) {
            return R.error("缺少参数");
        }
        if (StrUtil.isEmpty(didSymbol)) {
            return R.error("DID为空，请前往申领");
        }
        if (!(data.containsKey("sn") && data.containsKey("accountSign"))) {
            return R.error("缺少参数");
        }
        // 截取平台Id
        String platformId = StrUtil.subBefore(data.get("sn").toString(), "_", false);
        // 截取平台Id
        String key = platformId + ":" + data.get("sn");
        Map map = redisUtils.get(key, Map.class);
        if (MapUtil.isEmpty(map)) {
            return R.error("二维码已过期");
        }

        if (ObjectUtil.equals(1, Integer.valueOf(platformId))) {
            // 判断用户身份非活动用户扫码无效
            DidCheckInAccount didCheckInAccount = didCheckInAccountMapper.selectOne(Wrappers
                .<DidCheckInAccount>lambdaQuery().eq(DidCheckInAccount::getAccountUuid, userInfo.getUuid()));
            if (didCheckInAccount == null) {
                return R.error("非活动用户不支持扫码");
            }
        } else {
            // 通过传来的url 来判断用户是否允许扫码登录
            String searchDidIfExistUrl = map.get("searchDidIfExistUrl").toString();
            R execute = Forest.post(searchDidIfExistUrl).addBody("didSymbol", didSymbol).execute(R.class);
            if (execute.get("code").equals(200)) {
                Object data1 = execute.get("data");
                if (data1 instanceof Map) {
                    Map<String, Object> map1 = (Map<String, Object>)data1;
                    if (!CollectionUtils.isEmpty(map1)) {
                        Integer count = Integer.valueOf(map1.get("count").toString());
                        if (count != 1) {
                            return R.error("该用户不支持扫码");
                        }
                    } else {
                        return R.error("该用户不支持扫码");
                    }
                } else {
                    return R.error("该用户不支持扫码");
                }
            } else {
                return R.error("该用户不支持扫码");
            }
        }
        String type = data.get("type").toString();
        // 取消登录
        if (ObjectUtil.equals(type, "0")) {
            callbackCancle(didSymbol, String.valueOf(map.get("noticeUrl")), data.get("sn").toString());
        } else {
            // 验证签名
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("sn", map.get("sn"));
            paramMap.put("expirationTime", map.get("expirationTime"));
            paramMap.put("noticeUrl", map.get("noticeUrl"));
            if (map.containsKey("searchDidIfExistUrl")) {
                paramMap.put("searchDidIfExistUrl", map.get("searchDidIfExistUrl"));
            }
            // 获取平台信息
            DidQrCode didQrCode = didQrCodeMapper.selectOne(Wrappers.<DidQrCode>lambdaQuery()
                    .eq(DidQrCode::getApplicationId, Integer.valueOf(platformId)).eq(DidQrCode::getState, 1));
            if (didQrCode == null) {
                return R.error("平台暂未开放，请前往申请");
            }
            // 校验参数
            Map<String, Object> signedParamsMap = MovieUtil.getSignedParamsMap(paramMap,didQrCode.getAppSecret());
            if (!map.get("sign").equals(signedParamsMap.get("sign"))) {
                return R.error("签名错误");
            }
            Object expirationTime = map.get("expirationTime");
            long time = (new Date()).getTime();
            if (time > Long.valueOf(expirationTime.toString())) {
                return R.error("二维码已过期，请重新扫描");
            }
            // 验证DID 签名
            AccountDid accountDid = accountDidMapper.selectOne(
                Wrappers.<AccountDid>lambdaQuery().eq(AccountDid::getAccountUuid, userInfo.getUuid()));
            // 遍历文档公钥验签
            boolean flag = verifyDidSign(accountDid.getDidDocument(), String.valueOf(data.get("accountSign")),
                didSymbol);
            if (!flag) {
                throw new ServiceException("个人签名验签失败");
            }
            // 获取应用信息
            DidLoginApplication didLoginApplication =
                didLoginApplicationMapper.selectOne(Wrappers.<DidLoginApplication>lambdaQuery()
                    .eq(DidLoginApplication::getApplicationId, Integer.valueOf(platformId))
                    .eq(DidLoginApplication::getState, 1));
            // 异步回调DID平台
            try {
                boolean callback = callback(didSymbol, String.valueOf(map.get("noticeUrl")),
                    userInfo.getUuid(), Integer.valueOf(platformId), String.valueOf(data.get("sn")),
                    DateUtil.date(Long.valueOf(expirationTime.toString())), didLoginApplication.getWebsite(),
                    didLoginApplication.getApplicationName());
                result.put("state", callback);
                result.put("message", callback == true ? "授权成功" : "授权失败");
            } catch (Exception e) {
                log.error(e.getMessage());
            }
        }
        return R.okData(result);
    }

    /**
     * DID扫码签到
     * 
     * @param code 码
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/07/31
     */
    @Override
    public R didSignInService(String code) {
        String url = "checkInRecord/mainScanCheckIn";
        Account userInfo = accountService.getUserInfo();
        String didSymbol = userInfo.getDidSymbol();
        if (StrUtil.isEmpty(didSymbol)) {
            return R.error("DID为空，请前往申领");
        }
        R execute = Forest.post(didCheckUrl + url).addBody("mainScanAccount", didSymbol).addBody("code", code)
            .execute(R.class);
        return execute;
    }

    /**
     * 使用系统保存的最新的DID文档校验--从文档里面取只要有符合的就通过
     *
     * @param didDocument 系统内最新的DID文档
     * @param didSign did签名值
     * @param didSymbol did标识
     * @return
     */
    private boolean verifyDidSign(String didDocument, String didSign, String didSymbol) {
        DidDocumentEntity didDocumentEntity = JSONUtil.toBean(didDocument, DidDocumentEntity.class);
        List<DidDocumentEntity.DocumentAuthentication> documentAuthentications =
            didDocumentEntity.getAuthentication();
        List<String> publicKeyHexList = documentAuthentications.stream()
            .map(DidDocumentEntity.DocumentAuthentication::getPublicKeyHex).collect(Collectors.toList());
        for (String publicKeyHex : publicKeyHexList) {
            boolean verify = hexVerify(publicKeyHex, didSymbol, didSign);
            if (verify) {
                return true;
            }
        }
        return false;
    }

    /**
     * 公钥验签
     *
     * @param publicKey 公钥
     * @param content 明文内容
     * @param sign 签名值
     * @return
     */
    public static boolean hexVerify(String publicKey, String content, String sign) {
        byte[] message = content.getBytes(StandardCharsets.UTF_8);
        byte[] signData = Hex.decode(sign);
        X9ECParameters sm2ECParameters = GMNamedCurves.getByName("sm2p256v1");
        ECDomainParameters domainParameters = new ECDomainParameters(sm2ECParameters.getCurve(),
            sm2ECParameters.getG(), sm2ECParameters.getN());
        ECPoint pukPoint = sm2ECParameters.getCurve().decodePoint(Hex.decode(publicKey));
        ECPublicKeyParameters publicKeyParameters = new ECPublicKeyParameters(pukPoint, domainParameters);
        SM2Signer sm2Signer = new SM2Signer();
        ParametersWithID parametersWithID =
            new ParametersWithID(publicKeyParameters, Strings.toByteArray("1234567812345678"));
        sm2Signer.init(false, parametersWithID);
        sm2Signer.update(message, 0, message.length);
        boolean verify = sm2Signer.verifySignature(signData);
        return verify;
    }

    @Async("ljThreadPool")
    public boolean callback(String didSymbol, String url, String uuuid, Integer applicationId, String sn,
        Date expirationTime, String website,String applicationName) {
        boolean flag = false;
        int num = 0;
        while (true) {
            try {
                R execute = Forest.post(url).contentTypeJson().addBody("didSymbol", didSymbol)
                    .addBody("sn", sn).addBody("result", 1).execute(R.class);
                if (execute.get("code").equals(200)) {
                    // 插入授权登录成功记录
                    // this.save(new DidLoginApplicationRecord().setAccountUuid(uuuid)
                    // .setApplicationId(applicationId).setExpirationTime(expirationTime).setSn(sn));
                    DidGrantRecord didGrantRecord = new DidGrantRecord();
                    didGrantRecord.setApplicationId(applicationId);
                    didGrantRecord.setAccountUuid(uuuid);
                    didGrantRecord.setToken(sn);
                    didGrantRecord.setTypeName("授权登录");
                    didGrantRecordMapper.insert(didGrantRecord);
                    // 插入登录成功通知
                    inserNotice(didSymbol, uuuid, website,applicationName);
                    flag = true;
                    break;
                }
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            num++;
            if (num > 10) {
                break;
            }
        }
        return flag;
    }

    @Async("ljThreadPool")
    public void callbackCancle(String didSymbol, String url, String sn) {
        int num = 0;
        while (true) {
            try {
                R execute = Forest.post(url).contentTypeJson().addBody("didSymbol", didSymbol)
                    .addBody("sn", sn).addBody("result", 0).execute(R.class);
                if (execute.get("code").equals(200)) {
                    break;
                }
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            num++;
            if (num > 10) {
                break;
            }
        }
    }

    @Async("ljThreadPool")
    public void scanSuccessfulNotification(String didSymbol, String url, String sn) {
        int num = 0;
        while (true) {
            try {
                R execute = Forest.post(url).contentTypeJson().addBody("didSymbol", didSymbol)
                    .addBody("sn", sn).addBody("result", 2).execute(R.class);
                if (execute.get("code").equals(200)) {
                    break;
                }
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            num++;
            if (num > 10) {
                break;
            }
        }
    }

    public void inserNotice(String didSymbol, String accountUuid, String website,String applicationName) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String title = "DID授权登录操作通知";
        String content = "你的【DID】账号<br>" + didSymbol + "<br>进行了网站登录操作\r\n\n" + "<br><br>登录网页："
            + applicationName+ "\n<br>" + "登录网址：" + website + "\n<br>" + "登录时间：" + sdf.format(new Date())
            + "<br> \r\n\n" + "<br>如有疑问，请联系客服";
        // 插入通知
        SystemNotice systemNotice = new SystemNotice();
        systemNotice.setTitle(title).setDescription("授权登录通知").setContent(content).setSource(0).setType(1);
        systemNoticeMapper.insert(systemNotice);
        // 插入用户通知
        NoticeAccount noticeAccount = new NoticeAccount();
        noticeAccount.setAccountUuid(accountUuid).setNoticeId(systemNotice.getId()).setSource(0);
        noticeAccountMapper.insert(noticeAccount);
    }
}
