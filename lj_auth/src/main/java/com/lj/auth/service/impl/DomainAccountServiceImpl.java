package com.lj.auth.service.impl;

import com.lj.auth.domain.DomainAccount;
import com.lj.auth.mapper.DomainAccountMapper;
import com.lj.auth.service.DomainAccountService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户持有域名表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-07
 */
@Service
public class DomainAccountServiceImpl extends ServiceImpl<DomainAccountMapper, DomainAccount> implements DomainAccountService {

}
