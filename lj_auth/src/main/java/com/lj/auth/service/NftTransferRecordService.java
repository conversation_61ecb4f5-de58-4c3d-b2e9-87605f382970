package com.lj.auth.service;

import com.lj.auth.common.R;
import com.lj.auth.domain.NftTransferRecord;
import com.baomidou.mybatisplus.extension.service.IService;
public interface NftTransferRecordService extends IService<NftTransferRecord>{
    R getNftTransactions(Integer opbChainId, Integer contractId, Integer tokenId, String holderAddress, Integer type);

    R getAllNftTransactions(Integer opbChainId, String chainAccountAddress, Integer type, String time);

}
