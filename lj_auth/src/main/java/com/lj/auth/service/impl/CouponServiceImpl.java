package com.lj.auth.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.util.DateUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.lj.auth.common.CommonConstant;
import com.lj.auth.common.MessageConstant;
import com.lj.auth.common.R;
import com.lj.auth.domain.*;
import com.lj.auth.domain.resp.DomainRenewalActivityListResp;
import com.lj.auth.domain.vo.ActivityVo;
import com.lj.auth.domain.vo.CouponVo;
import com.lj.auth.domain.vo.DouDianCouponVo;
import com.lj.auth.feginClient.IDomainService;
import com.lj.auth.mapper.*;
import com.lj.auth.service.CouponService;
import com.lj.auth.util.DateUtil;
import com.lj.auth.util.HeadUtil;
import com.lj.auth.util.NumberUtil;
import com.lj.auth.util.PageUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: wxm
 * @description:
 * @date: 2024/11/5 9:30
 */
@Service
@Slf4j
public class CouponServiceImpl implements CouponService {
    @Resource
    private CouponBatchMapper couponBatchMapper;
    @Resource
    private CouponMapper couponMapper;
    @Resource
    private CouponAccountMapper couponAccountMapper;
    @Resource
    private AccountMapper accountMapper;
    @Resource
    private GlobalConfigMapper globalConfigMapper;
    @Resource
    private CommunityActivityMapper communityActivityMapper;
    @Resource
    private AccountDidMapper accountDidMapper;
    @Resource
    private AccountInfoVoucherMapper accountInfoVoucherMapper;
    @Resource
    private OutsideChannelApplyConfigMapper outsideChannelApplyConfigMapper;
    @Resource
    private DouDianCouponMapper douDianCouponMapper;
    @Resource
    IDomainService domainService;


    @Override
    public R couponBatchList(JSONObject paramJson) {
        Integer type = paramJson.getInteger("type");
        List<CouponBatch> dataList = couponBatchMapper.couponBatchList(type);
        if (dataList == null) {
            dataList = new ArrayList<>();
        }
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("data", dataList);
        return R.ok(resultMap);
    }

    @Override
    public R couponBatchDetail(JSONObject paramJson) {
        Long couponBatchId = paramJson.getLong("couponBatchId");
        if (couponBatchId == null) {
            return R.error(MessageConstant.PARAMETER_CAN_NOT_BE_NULL);
        }
        CouponBatch couponBatch = couponBatchMapper.selectById(couponBatchId);
        if (couponBatch == null) {
            return R.error(MessageConstant.DATA_NOT_EXIST);
        }
        //查询剩余优惠券数量
        Integer residueNumber = couponMapper.getCouponResidueNumber(couponBatchId);
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("data", couponBatch);
        resultMap.put("residueNumber", residueNumber);
        return R.ok(resultMap);
    }

    @Transactional
    @Override
    public R grabCoupon(JSONObject paramJson) {
        Long couponBatchId = paramJson.getLong("couponBatchId");
        if (couponBatchId == null) {
            return R.error(MessageConstant.PARAMETER_CAN_NOT_BE_NULL);
        }
        String myUuid = StpUtil.getLoginIdAsString();
        if (StringUtils.isEmpty(myUuid)) {
            return R.error(MessageConstant.GET_USER_INFO_FAIL);
        }
        CouponBatch couponBatch = couponBatchMapper.selectById(couponBatchId);
        if (couponBatch == null) {
            return R.error(MessageConstant.DATA_NOT_EXIST);
        }
        Integer getCouponType = couponBatch.getGetCouponType();//领券类型 1-按批次表数量领取  2-按券表数量领取
        Integer getNumber = couponBatch.getGetNumber();//单个用户可领取数量
        QueryWrapper<Account> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("uuid", myUuid);
        Account account = accountMapper.selectOne(queryWrapper);
        if (account == null) {
            return R.error(MessageConstant.DATE_EXCEPTION);
        }
        if (getCouponType == 1) {
            //查询已领取数量
            Integer receivedNumber = couponAccountMapper.getReceivedNumberByBatchId(couponBatchId, myUuid);
            if (receivedNumber >= getNumber) {
                return R.error(MessageConstant.YOU_HAVE_RECEIVED_THE_COUPON);
            }
        } else {
            //TODO 这个情况当前不用处理
            return R.error(MessageConstant.DATE_EXCEPTION);
        }
        //可领取券列表
        List<Coupon> couponList = couponMapper.getCanReceiveCoupon(couponBatchId);
        if (couponList == null || couponList.size() == 0) {
            return R.error(MessageConstant.COUPON_HAVE_BEEN_REDEEMED);
        }
        //先随机出一个券
        Integer randomInt = NumberUtil.randomInt(couponList.size());
        Coupon randomCoupon = couponList.get(randomInt);
        //领券 TODO 若考虑用户领券必得一券的情况，这里要增加逻辑
        Integer reduceNumberCount = couponMapper.reduceResidueNumber(randomCoupon.getId());
        if (reduceNumberCount == 1) {
            //减数量成功，生成用户券记录
            CouponAccount couponAccount = new CouponAccount();
            couponAccount.setAccountUuid(myUuid);
            couponAccount.setCouponBatchId(randomCoupon.getCouponBatchId());
            couponAccount.setCouponId(randomCoupon.getId());
            couponAccount.setOperateUuid(account.getOperateUuid());
            couponAccount.setState(1);//券状态 1-未使用 2-已使用
            couponAccount.setCreateTime(new Date());
            couponAccount.setUpdateTime(new Date());
            Integer count1 = couponAccountMapper.insert(couponAccount);
            if (count1 != 1) {
                throw new RuntimeException("领券时生成用户券信息没有达到预期，执行新增sql失败");
            }
        } else {
            return R.error("网络不佳，请重试");
        }
        //处理折扣显示
        if (randomCoupon.getDiscountRate().compareTo(BigDecimal.ZERO) == 0) {
            randomCoupon.setDiscountRateStr("免费");
        } else {
            randomCoupon.setDiscountRateStr(randomCoupon.getDiscountRate().multiply(new BigDecimal(10)).stripTrailingZeros().toPlainString() + "折");
        }
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("coupon", randomCoupon);
        return R.ok(resultMap);
    }

    @Override
    public R historyCouponPage(JSONObject paramJson) {
        String myUuid = StpUtil.getLoginIdAsString();
        if (StringUtils.isEmpty(myUuid)) {
            return R.error(MessageConstant.GET_USER_INFO_FAIL);
        }
        Integer state = paramJson.getInteger("state");//券状态 1-已使用 2-已失效
        Integer page = paramJson.getInteger("page");
        Integer pageSize = paramJson.getInteger("pageSize");
        if (state == null || (state != 1 && state != 2)) {
            return R.error(MessageConstant.PARAMETER_CAN_NOT_BE_NULL);
        }
        page = page == null ? CommonConstant.DEFAULT_PAGE : page;
        pageSize = pageSize == null ? CommonConstant.DEFAULT_PAGE_SIZE : pageSize;
        Integer dbState = null;
        Integer historyType = null;
        if (state == 1) {
            dbState = 2;
        } else if (state == 2) {
            dbState = 1;
            historyType = 1;
        }
        Integer totalNum = couponAccountMapper.accountCouponListCount(myUuid, dbState, historyType);
        List<CouponVo> dataList = new ArrayList<>();
        if (totalNum > 0) {
            int start = (page - 1) * pageSize;
            dataList = couponAccountMapper.accountCouponList(myUuid, dbState, historyType, start, pageSize);
            if (dataList != null && dataList.size() > 0) {
                for (CouponVo vo : dataList) {
                    //处理折扣显示
                    if (new BigDecimal(vo.getDiscountRate()).compareTo(BigDecimal.ZERO) == 0) {
                        vo.setDiscountRate("免费");
                    } else {
                        vo.setDiscountRate(new BigDecimal(vo.getDiscountRate()).multiply(new BigDecimal(10)).stripTrailingZeros().toPlainString() + "折");
                    }
                }
            }
        }
        PageUtils pageUtils = new PageUtils(totalNum, pageSize, page, dataList);
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("count", totalNum);
        resultMap.put("page", pageUtils);
        return R.ok(resultMap);
    }

    @Override
    public R couponList(JSONObject paramJson) {
        Map<String, Object> resultMap = new HashMap<>();
        String myUuid = StpUtil.getLoginIdAsString();
        if (StringUtils.isEmpty(myUuid)) {
            return R.error(MessageConstant.GET_USER_INFO_FAIL);
        }
        Integer applicationType = null;
        try {
            // 1-域名注册 2-域名续费 3-域名转让 4-域名交易 5-电影 6-话费 7-预约挂号 8-实名DID
            // 9-视听 10-美食 11-生活 12-音频 13-开通裂空者
            applicationType = paramJson.getInteger("applicationType");
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        if (applicationType == null) {
            resultMap.put("activityList",  communityActivityMapper.activityList(null));
            resultMap.put("canUseCouponList", couponAccountMapper.couponList(myUuid, 1));
            resultMap.put("canNotUseCouponList", couponAccountMapper.couponList(myUuid, 0));
        }

        if (ObjectUtil.equals(applicationType,5)) {
            String showId = paramJson.getString("showId");//场次id
            String showTime = paramJson.getString("showTime");//放映时间
            if (StringUtils.isEmpty(showTime)) {
                return R.error(MessageConstant.PARAMETER_CAN_NOT_BE_NULL);
            }
            //根据放映时间判断是走慢票还是快票，慢票最慢出票时间为半小时，所以根据放映时间提前1小时进行判断
            Date showDate = DateUtil.stringToDate(showTime);
            Date curDate = new Date();
            Date limitDate = DateUtil.addDateHours(curDate, 1);
            //查询活动信息
            List<ActivityVo> activityList = new ArrayList<>();
            if (limitDate.after(showDate)) {
                //走快票流程，查询快票活动
                activityList = communityActivityMapper.activityList(1);
            } else {
                //走慢票流程，查询慢票活动
                activityList = communityActivityMapper.activityList(2);
            }
            resultMap.put("activityList", activityList);
            //查询可用券
            List<CouponVo> canUseCouponList = couponAccountMapper.couponList(myUuid, 1);
            if (canUseCouponList != null && canUseCouponList.size() > 0) {
                for (CouponVo vo : canUseCouponList) {
                    vo.setUseType(0);
                    //模式 1-抵扣券 2-折扣券
                    String couponModel = vo.getCouponModel();
                    if("2".equals(couponModel)) {
                        //处理折扣显示
                        if (new BigDecimal(vo.getDiscountRate()).compareTo(BigDecimal.ZERO) == 0) {
                            vo.setDiscountRate("免费");
                        } else {
                            vo.setDiscountRate(new BigDecimal(vo.getDiscountRate()).multiply(new BigDecimal(10)).stripTrailingZeros().toPlainString() + "折");
                        }
                    }else if("1".equals(couponModel)) {
                        vo.setDiscountRate("￥"+vo.getAmount().toPlainString());
                    }
                }
            }
            //查询不可用券
            List<CouponVo> canNotUseCouponList = couponAccountMapper.couponList(myUuid, 0);
            if (canNotUseCouponList != null && canNotUseCouponList.size() > 0) {
                for (CouponVo vo : canNotUseCouponList) {
                    vo.setUseType(0);
                    //模式 1-抵扣券 2-折扣券
                    String couponModel = vo.getCouponModel();
                    if("2".equals(couponModel)) {
                        //处理折扣显示
                        if (new BigDecimal(vo.getDiscountRate()).compareTo(BigDecimal.ZERO) == 0) {
                            vo.setDiscountRate("免费");
                        } else {
                            vo.setDiscountRate(new BigDecimal(vo.getDiscountRate()).multiply(new BigDecimal(10)).stripTrailingZeros().toPlainString() + "折");
                        }
                    }else if("1".equals(couponModel)) {
                        vo.setDiscountRate("￥"+vo.getAmount().toPlainString());
                    }
                    //备注
                    vo.setRemark("暂未到使用时间");
                }
            }
            resultMap.put("canUseCouponList", canUseCouponList);
            resultMap.put("canNotUseCouponList", canNotUseCouponList);
            //编辑活动信息
            if (activityList != null && activityList.size() > 0) {
                for (ActivityVo activityVo : activityList) {
                    Integer activityType = activityVo.getDiscountType();
                    if (activityType == 1) {
                        BigDecimal discountRate = activityVo.getDiscountRate();
                        activityVo.setDiscountRateStr(discountRate.multiply(new BigDecimal("10")).setScale(2, RoundingMode.DOWN).stripTrailingZeros().toPlainString() + "折");
                    } else if (activityType == 2) {
                        BigDecimal discountAmount = activityVo.getDiscountAmount();
                        activityVo.setDiscountRateStr("每座" + discountAmount + "元");
                    }
                }
            }
//            Integer state = Integer.valueOf(globalConfigMapper.queryConfig("movie_to_sp_flag"));
//            if (state == null || state == 0) {
//                resultMap.put("activityList", null);
//            } else {
//                String activityMode = globalConfigMapper.queryConfig("movie_discount_type");
//                List<ActivityVo> activityVoList = new ArrayList<>();
//                ActivityVo activityVo = new ActivityVo();
//                activityVo.setUseType(0);
//                activityVo.setActivityName("平台优惠观影活动");
//                activityVo.setDiscountMode(activityMode);
//                if ("1".equals(activityMode)) {
//                    //按比例优惠
//                    String discountRatio = globalConfigMapper.queryConfig("movie_discount_ratio");
//                    //计算折扣
//                    String discountRate = new BigDecimal("1").subtract(new BigDecimal(discountRatio)).multiply(new BigDecimal("100")).setScale(0, RoundingMode.DOWN).toPlainString() + "折";
//                    activityVo.setDiscountRate(discountRate);
//                    activityVo.setDescription("平台" + discountRate + "观影活动");
//                } else if ("2".equals(activityMode)) {
//                    //按固定金额优惠(每座)
//                    String movieDiscountAmount = globalConfigMapper.queryConfig("movie_discount_amount");
//                    //换算折扣金额
//                    String discountAmount = new BigDecimal(movieDiscountAmount).divide(new BigDecimal("100"), 2, RoundingMode.DOWN).toPlainString() + "元";
//                    activityVo.setDiscountAmount(discountAmount);
//                    activityVo.setDescription("平台优惠观影活动,每座优惠" + discountAmount);
//                }
//                activityVo.setRemark("");
//                activityVoList.add(activityVo);
//                resultMap.put("activityList", activityVoList);
//            }
        }
        //实名DID
        else if (ObjectUtil.equals(applicationType,8)) {
            resultMap.put("activityList", new ArrayList<>());
            resultMap.put("canUseCouponList", new ArrayList<>());
            resultMap.put("canNotUseCouponList", new ArrayList<>());
            
            String idNoHash = paramJson.getString("idNoHash");//身份证号hash
            String idNameHash = paramJson.getString("idNameHash");//姓名hash
            Integer type = paramJson.getInteger("type"); //10-实名DID及个人身份信息凭证申领  12-个人身份信息凭证申领 14-DID申领 16-DID申领(恢复私钥) 17-申领DID(信息补全)
            String appId = paramJson.getString("appId");
            if (Objects.equals(type, 14)) {
                // 1.活动
                //首次申领标识,true:首次,false:非首次
                boolean firstApplyFlag = true;
                //did
                AccountDid accountDidByUser = accountDidMapper.selectOne(new LambdaQueryWrapper<AccountDid>()
                        .eq(AccountDid::getIdNo, idNoHash)
                        .eq(AccountDid::getIdName, idNameHash));
                if (null != accountDidByUser) {
                    firstApplyFlag = false;
                }
                AccountDid accountDidByUuid = accountDidMapper.selectOne(new LambdaQueryWrapper<AccountDid>()
                        .eq(AccountDid::getAccountUuid, myUuid));
                if (null != accountDidByUuid) {
                    firstApplyFlag = false;
                }
    
                //信息凭证
                Integer countByUuid = accountInfoVoucherMapper.selectCount(new LambdaQueryWrapper<AccountInfoVoucher>().eq(AccountInfoVoucher::getAccountUuid, myUuid));
                if (countByUuid > 0) {
                    firstApplyFlag = false;
                }
                Integer countByUser = accountInfoVoucherMapper.selectCount(new LambdaQueryWrapper<AccountInfoVoucher>().eq(AccountInfoVoucher::getIdNo, idNoHash).eq(AccountInfoVoucher::getIdName, idNameHash));
                if (countByUser > 0) {
                    firstApplyFlag = false;
                }
    
    
                //三方渠道用户
                if (StringUtils.isNotBlank(appId)) {
                    OutsideChannelApplyConfig outsideChannelApplyConfig = outsideChannelApplyConfigMapper.queryByAppId(appId);
                    if (null != outsideChannelApplyConfig) {
                        //外部访问免费标识
                        boolean outsideFeeFlag = false;
                        Integer fee = outsideChannelApplyConfig.getFee();
                        if (Objects.equals(fee, 1)) {
                            Integer feeResidueCount = outsideChannelApplyConfig.getFeeResidueCount();
                            if (feeResidueCount > 1) {
                                Integer feeCondition = outsideChannelApplyConfig.getFeeCondition();
                                if (Objects.equals(feeCondition, 1)) {
                                    outsideFeeFlag = true;
                                } else if (Objects.equals(feeCondition, 2) && firstApplyFlag) {
                                    outsideFeeFlag = true;
                                }
                            }
                        }
                        if (outsideFeeFlag){
                            List<ActivityVo> activityList = communityActivityMapper.activityList(6);
                            resultMap.put("activityList", activityList);
                        }
                    }
                }
                //系统用户
                else {
                    String platformSource = HeadUtil.getPlatformSource();
                    if (Objects.equals(platformSource,"WXMINI")){
                        if (firstApplyFlag) {
                            List<ActivityVo> activityVoList = communityActivityMapper.activityList(5);
                            resultMap.put("activityList", activityVoList);
                        }
                    }
                }
                
                // 2.优惠券
                List<CouponVo> canUseCouponList = couponAccountMapper.couponListOfType(myUuid, null,4);// DID抵扣券
                for (CouponVo vo : canUseCouponList) {
                    vo.setUseType(0);
                    //处理折扣显示
                    if (new BigDecimal(vo.getDiscountRate()).compareTo(BigDecimal.ZERO) == 0) {
                        vo.setDiscountRate("免费");
                    } else {
                        vo.setDiscountRate(new BigDecimal(vo.getDiscountRate()).multiply(new BigDecimal(10)).stripTrailingZeros().toPlainString() + "折");
                    }
                }
                
                // 优惠券时间重新赋值
                if (!CollectionUtils.isEmpty(canUseCouponList)) {
                    List<String> couponNumberList = canUseCouponList.stream().map(CouponVo::getCouponNumber).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(couponNumberList)) {
                        List<DouDianCouponVo> douDianCouponVoList = douDianCouponMapper.queryByCouponNumberList(couponNumberList);
                        Map<String, DouDianCouponVo> douDianCouponVoMap = douDianCouponVoList.stream().collect(Collectors.toMap(DouDianCouponVo::getRedeemCode, v -> v));
                        for (CouponVo couponVo : canUseCouponList) {
                            String couponNumber = couponVo.getCouponNumber();
                            DouDianCouponVo douDianCouponVo = douDianCouponVoMap.get(couponNumber);
                            if (null  != douDianCouponVo) {
                                Date createTime = douDianCouponVo.getCreateTime();
                                if (null != createTime) {
                                    couponVo.setUseStartTime(DateUtils.format(createTime,"yyyy-MM-dd HH:mm:ss"));
                                }
                                Date validityTime = douDianCouponVo.getValidityTime();
                                if (null != validityTime) {
                                    couponVo.setUseEndTime(DateUtils.format(validityTime,"yyyy-MM-dd HH:mm:ss"));
                                }
                            }
                        }
                    }
                }
                List<CouponVo> canNotUseCouponList = new ArrayList<>();
                
                resultMap.put("canUseCouponList", canUseCouponList);
                resultMap.put("canNotUseCouponList", canNotUseCouponList);
            }
            else if (Objects.equals(type ,17)) {
                AccountDid accountDid = accountDidMapper.queryByAccountUuid(myUuid);
                if (null != accountDid) {
                    if (StringUtils.isBlank(accountDid.getIdNoEnc())
                            || StringUtils.isBlank(accountDid.getIdNameEnc())
                            || StringUtils.isBlank(accountDid.getPhotoEnc())
                    ){
                        List<ActivityVo> activityList = communityActivityMapper.activityList(7);
                        resultMap.put("activityList", activityList);
                    }
                    
                }
            }
            else if (Objects.equals(type, 18)) {
                // 2.优惠券
                List<CouponVo> canUseCouponList = couponAccountMapper.couponListOfType(myUuid, null,4);// DID抵扣券
                for (CouponVo vo : canUseCouponList) {
                    vo.setUseType(0);
                    //处理折扣显示
                    if (new BigDecimal(vo.getDiscountRate()).compareTo(BigDecimal.ZERO) == 0) {
                        vo.setDiscountRate("免费");
                    } else {
                        vo.setDiscountRate(new BigDecimal(vo.getDiscountRate()).multiply(new BigDecimal(10)).stripTrailingZeros().toPlainString() + "折");
                    }
                }
                List<CouponVo> canNotUseCouponList = new ArrayList<>();
                
                // 优惠券时间重新赋值
                if (!CollectionUtils.isEmpty(canUseCouponList)) {
                    List<String> couponNumberList = canUseCouponList.stream().map(CouponVo::getCouponNumber).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(couponNumberList)) {
                        List<DouDianCouponVo> douDianCouponVoList = douDianCouponMapper.queryByCouponNumberList(couponNumberList);
                        Map<String, DouDianCouponVo> douDianCouponVoMap = douDianCouponVoList.stream().collect(Collectors.toMap(DouDianCouponVo::getRedeemCode, v -> v));
                        for (CouponVo couponVo : canUseCouponList) {
                            String couponNumber = couponVo.getCouponNumber();
                            DouDianCouponVo douDianCouponVo = douDianCouponVoMap.get(couponNumber);
                            if (null  != douDianCouponVo) {
                                Date createTime = douDianCouponVo.getCreateTime();
                                if (null != createTime) {
                                    couponVo.setUseStartTime(DateUtils.format(createTime,"yyyy-MM-dd HH:mm:ss"));
                                }
                                Date validityTime = douDianCouponVo.getValidityTime();
                                if (null != validityTime) {
                                    couponVo.setUseEndTime(DateUtils.format(validityTime,"yyyy-MM-dd HH:mm:ss"));
                                }
                            }
                        }
                    }
                }
                
                resultMap.put("canUseCouponList", canUseCouponList);
                resultMap.put("canNotUseCouponList", canNotUseCouponList);
            }
        }
        // 话费
        else if (ObjectUtil.equals(applicationType,6)) {
            // 查询活动信息
            List<ActivityVo> activityList = communityActivityMapper.activityList(3);

            // 查询可用券
            List<CouponVo> canUseCouponList = couponAccountMapper.couponListOfType(myUuid, 1,3);

            if (canUseCouponList != null && canUseCouponList.size() > 0) {
                for (CouponVo vo : canUseCouponList) {
                    String couponModel = vo.getCouponModel();
//                    抵扣券
                    if(couponModel.equals("1")){
                        vo.setUseType(0);

                    }else if(couponModel.equals("2")){
                        vo.setUseType(0);
                        // 处理折扣显示
                        if (new BigDecimal(vo.getDiscountRate()).compareTo(BigDecimal.ZERO) == 0) {
                            vo.setDiscountRate("免费");
                        } else {
                            vo.setDiscountRate(new BigDecimal(vo.getDiscountRate()).multiply(new BigDecimal(10))
                                    .stripTrailingZeros().toPlainString() + "折");
                        }
                    }
                }
            }
            // 查询不可用券
            List<CouponVo> canNotUseCouponList = couponAccountMapper.couponListOfType(myUuid, 0,3);
            if (canNotUseCouponList != null && canNotUseCouponList.size() > 0) {
                for (CouponVo vo : canNotUseCouponList) {
                    vo.setUseType(0);
                    // 处理折扣显示
                    if (new BigDecimal(vo.getDiscountRate()).compareTo(BigDecimal.ZERO) == 0) {
                        vo.setDiscountRate("免费");
                    } else {
                        vo.setDiscountRate(new BigDecimal(vo.getDiscountRate()).multiply(new BigDecimal(10))
                            .stripTrailingZeros().toPlainString() + "折");
                    }
                    // 备注
                    vo.setRemark("暂未到使用时间");
                }
            }
            // 编辑活动信息
            if (activityList != null && activityList.size() > 0) {
                for (ActivityVo activityVo : activityList) {
                    Integer activityType = activityVo.getDiscountType();
                    if (activityType == 1) {
                        BigDecimal discountRate = activityVo.getDiscountRate();
                        activityVo.setDiscountRateStr(discountRate.multiply(new BigDecimal("10"))
                            .setScale(2, RoundingMode.DOWN).stripTrailingZeros().toPlainString() + "折");
                    } else if (activityType == 2) {
                        BigDecimal discountAmount = activityVo.getDiscountAmount();
                        activityVo.setDiscountRateStr("每单" + discountAmount + "元");
                    }
                }
            }
            resultMap.put("activityList", activityList);
            resultMap.put("canUseCouponList", canUseCouponList);
            resultMap.put("canNotUseCouponList", canNotUseCouponList);

        }

        //  视听
        else if (ObjectUtil.equals(applicationType,9)) {
            // 查询活动信息  活动待定  TODO
            List<ActivityVo> activityList = communityActivityMapper.activityList(8);

            // 查询可用券  券待定TODO
            List<CouponVo> canUseCouponList = couponAccountMapper.couponListOfType(myUuid, 1,8);

            if (canUseCouponList != null && canUseCouponList.size() > 0) {
                for (CouponVo vo : canUseCouponList) {
                    String couponModel = vo.getCouponModel();
//                    抵扣券
                    if(couponModel.equals("1")){
                        vo.setUseType(0);

                    }else if(couponModel.equals("2")){
                        vo.setUseType(0);
                        // 处理折扣显示
                        if (new BigDecimal(vo.getDiscountRate()).compareTo(BigDecimal.ZERO) == 0) {
                            vo.setDiscountRate("免费");
                        } else {
                            vo.setDiscountRate(new BigDecimal(vo.getDiscountRate()).multiply(new BigDecimal(10))
                                    .stripTrailingZeros().toPlainString() + "折");
                        }
                    }
                }
            }
            // 查询不可用券
            List<CouponVo> canNotUseCouponList = couponAccountMapper.couponListOfType(myUuid, 0,8);
            if (canNotUseCouponList != null && canNotUseCouponList.size() > 0) {
                for (CouponVo vo : canNotUseCouponList) {
                    vo.setUseType(0);
                    // 处理折扣显示
                    if (new BigDecimal(vo.getDiscountRate()).compareTo(BigDecimal.ZERO) == 0) {
                        vo.setDiscountRate("免费");
                    } else {
                        vo.setDiscountRate(new BigDecimal(vo.getDiscountRate()).multiply(new BigDecimal(10))
                                .stripTrailingZeros().toPlainString() + "折");
                    }
                    // 备注
                    vo.setRemark("暂未到使用时间");
                }
            }
            // 编辑活动信息
            if (activityList != null && activityList.size() > 0) {
                for (ActivityVo activityVo : activityList) {
                    Integer activityType = activityVo.getDiscountType();
                    if (activityType == 1) {
                        BigDecimal discountRate = activityVo.getDiscountRate();
                        activityVo.setDiscountRateStr(discountRate.multiply(new BigDecimal("10"))
                                .setScale(2, RoundingMode.DOWN).stripTrailingZeros().toPlainString() + "折");
                    } else if (activityType == 2) {
                        BigDecimal discountAmount = activityVo.getDiscountAmount();
                        activityVo.setDiscountRateStr("每单" + discountAmount + "元");
                    }
                }
            }
            resultMap.put("activityList", activityList);
            resultMap.put("canUseCouponList", canUseCouponList);
            resultMap.put("canNotUseCouponList", canNotUseCouponList);

        }
        //  美食
        else if (ObjectUtil.equals(applicationType,10)) {
            // 查询活动信息  TODO
            List<ActivityVo> activityList = communityActivityMapper.activityList(9);

            // 查询可用券  TODO
            List<CouponVo> canUseCouponList = couponAccountMapper.couponListOfType(myUuid, 1,9);

            if (canUseCouponList != null && canUseCouponList.size() > 0) {
                for (CouponVo vo : canUseCouponList) {
                    String couponModel = vo.getCouponModel();
//                    抵扣券
                    if(couponModel.equals("1")){
                        vo.setUseType(0);

                    }else if(couponModel.equals("2")){
                        vo.setUseType(0);
                        // 处理折扣显示
                        if (new BigDecimal(vo.getDiscountRate()).compareTo(BigDecimal.ZERO) == 0) {
                            vo.setDiscountRate("免费");
                        } else {
                            vo.setDiscountRate(new BigDecimal(vo.getDiscountRate()).multiply(new BigDecimal(10))
                                    .stripTrailingZeros().toPlainString() + "折");
                        }
                    }
                }
            }
            // 查询不可用券
            List<CouponVo> canNotUseCouponList = couponAccountMapper.couponListOfType(myUuid, 0,9);
            if (canNotUseCouponList != null && canNotUseCouponList.size() > 0) {
                for (CouponVo vo : canNotUseCouponList) {
                    vo.setUseType(0);
                    // 处理折扣显示
                    if (new BigDecimal(vo.getDiscountRate()).compareTo(BigDecimal.ZERO) == 0) {
                        vo.setDiscountRate("免费");
                    } else {
                        vo.setDiscountRate(new BigDecimal(vo.getDiscountRate()).multiply(new BigDecimal(10))
                                .stripTrailingZeros().toPlainString() + "折");
                    }
                    // 备注
                    vo.setRemark("暂未到使用时间");
                }
            }
            // 编辑活动信息
            if (activityList != null && activityList.size() > 0) {
                for (ActivityVo activityVo : activityList) {
                    Integer activityType = activityVo.getDiscountType();
                    if (activityType == 1) {
                        BigDecimal discountRate = activityVo.getDiscountRate();
                        activityVo.setDiscountRateStr(discountRate.multiply(new BigDecimal("10"))
                                .setScale(2, RoundingMode.DOWN).stripTrailingZeros().toPlainString() + "折");
                    } else if (activityType == 2) {
                        BigDecimal discountAmount = activityVo.getDiscountAmount();
                        activityVo.setDiscountRateStr("每单" + discountAmount + "元");
                    }
                }
            }
            resultMap.put("activityList", activityList);
            resultMap.put("canUseCouponList", canUseCouponList);
            resultMap.put("canNotUseCouponList", canNotUseCouponList);

        }

        //  生活
        else if (ObjectUtil.equals(applicationType,11)) {
            // 查询活动信息   TODO
            List<ActivityVo> activityList = communityActivityMapper.activityList(10);

            // 查询可用券
            List<CouponVo> canUseCouponList = couponAccountMapper.couponListOfType(myUuid, 1,10);

            if (canUseCouponList != null && canUseCouponList.size() > 0) {
                for (CouponVo vo : canUseCouponList) {
                    String couponModel = vo.getCouponModel();
//                    抵扣券
                    if(couponModel.equals("1")){
                        vo.setUseType(0);

                    }else if(couponModel.equals("2")){
                        vo.setUseType(0);
                        // 处理折扣显示
                        if (new BigDecimal(vo.getDiscountRate()).compareTo(BigDecimal.ZERO) == 0) {
                            vo.setDiscountRate("免费");
                        } else {
                            vo.setDiscountRate(new BigDecimal(vo.getDiscountRate()).multiply(new BigDecimal(10))
                                    .stripTrailingZeros().toPlainString() + "折");
                        }
                    }
                }
            }
            // 查询不可用券
            List<CouponVo> canNotUseCouponList = couponAccountMapper.couponListOfType(myUuid, 0,10);
            if (canNotUseCouponList != null && canNotUseCouponList.size() > 0) {
                for (CouponVo vo : canNotUseCouponList) {
                    vo.setUseType(0);
                    // 处理折扣显示
                    if (new BigDecimal(vo.getDiscountRate()).compareTo(BigDecimal.ZERO) == 0) {
                        vo.setDiscountRate("免费");
                    } else {
                        vo.setDiscountRate(new BigDecimal(vo.getDiscountRate()).multiply(new BigDecimal(10))
                                .stripTrailingZeros().toPlainString() + "折");
                    }
                    // 备注
                    vo.setRemark("暂未到使用时间");
                }
            }
            // 编辑活动信息
            if (activityList != null && activityList.size() > 0) {
                for (ActivityVo activityVo : activityList) {
                    Integer activityType = activityVo.getDiscountType();
                    if (activityType == 1) {
                        BigDecimal discountRate = activityVo.getDiscountRate();
                        activityVo.setDiscountRateStr(discountRate.multiply(new BigDecimal("10"))
                                .setScale(2, RoundingMode.DOWN).stripTrailingZeros().toPlainString() + "折");
                    } else if (activityType == 2) {
                        BigDecimal discountAmount = activityVo.getDiscountAmount();
                        activityVo.setDiscountRateStr("每单" + discountAmount + "元");
                    }
                }
            }
            resultMap.put("activityList", activityList);
            resultMap.put("canUseCouponList", canUseCouponList);
            resultMap.put("canNotUseCouponList", canNotUseCouponList);

        }

        //  音频
        else if (ObjectUtil.equals(applicationType,12)) {
            // 查询活动信息
            List<ActivityVo> activityList = communityActivityMapper.activityList(11);

            // 查询可用券
            List<CouponVo> canUseCouponList = couponAccountMapper.couponListOfType(myUuid, 1,11);

            if (canUseCouponList != null && canUseCouponList.size() > 0) {
                for (CouponVo vo : canUseCouponList) {
                    String couponModel = vo.getCouponModel();
//                    抵扣券
                    if(couponModel.equals("1")){
                        vo.setUseType(0);

                    }else if(couponModel.equals("2")){
                        vo.setUseType(0);
                        // 处理折扣显示
                        if (new BigDecimal(vo.getDiscountRate()).compareTo(BigDecimal.ZERO) == 0) {
                            vo.setDiscountRate("免费");
                        } else {
                            vo.setDiscountRate(new BigDecimal(vo.getDiscountRate()).multiply(new BigDecimal(10))
                                    .stripTrailingZeros().toPlainString() + "折");
                        }
                    }
                }
            }
            // 查询不可用券
            List<CouponVo> canNotUseCouponList = couponAccountMapper.couponListOfType(myUuid, 0,11);
            if (canNotUseCouponList != null && canNotUseCouponList.size() > 0) {
                for (CouponVo vo : canNotUseCouponList) {
                    vo.setUseType(0);
                    // 处理折扣显示
                    if (new BigDecimal(vo.getDiscountRate()).compareTo(BigDecimal.ZERO) == 0) {
                        vo.setDiscountRate("免费");
                    } else {
                        vo.setDiscountRate(new BigDecimal(vo.getDiscountRate()).multiply(new BigDecimal(10))
                                .stripTrailingZeros().toPlainString() + "折");
                    }
                    // 备注
                    vo.setRemark("暂未到使用时间");
                }
            }
            // 编辑活动信息
            if (activityList != null && activityList.size() > 0) {
                for (ActivityVo activityVo : activityList) {
                    Integer activityType = activityVo.getDiscountType();
                    if (activityType == 1) {
                        BigDecimal discountRate = activityVo.getDiscountRate();
                        activityVo.setDiscountRateStr(discountRate.multiply(new BigDecimal("10"))
                                .setScale(2, RoundingMode.DOWN).stripTrailingZeros().toPlainString() + "折");
                    } else if (activityType == 2) {
                        BigDecimal discountAmount = activityVo.getDiscountAmount();
                        activityVo.setDiscountRateStr("每单" + discountAmount + "元");
                    }
                }
            }
            resultMap.put("activityList", activityList);
            resultMap.put("canUseCouponList", canUseCouponList);
            resultMap.put("canNotUseCouponList", canNotUseCouponList);

        }

        // 域名续费
        else if (ObjectUtil.equals(applicationType, 2)) {
            resultMap.put("activityList", new ArrayList<>());
            resultMap.put("canUseCouponList", new ArrayList<>());
            resultMap.put("canNotUseCouponList", new ArrayList<>());
            
            R r = domainService.domainRenewalActivityList();
            if (ObjectUtil.equals(r.get("code"), 200)) {
                Object data = r.get("data");
                // DomainRenewalActivityListResp response = JSONObject.parseObject(JSONObject.toJSONString(data), DomainRenewalActivityListResp.class);
                List<DomainRenewalActivityListResp> listResp = JSONArray.parseArray(JSONObject.toJSONString(data), DomainRenewalActivityListResp.class);
                List<ActivityVo> activityList = new ArrayList<>();
                for (DomainRenewalActivityListResp response : listResp) {
                    ActivityVo activityVo = new ActivityVo();
                    activityVo.setActivityId(response.getActivityId());
                    activityVo.setActivityName(response.getDiscountDesc());
                    activityVo.setStartTime(DateUtil.format(response.getStartTime(), "yyyy-MM-dd HH:mm:ss"));
                    activityVo.setEndTime(DateUtil.format(response.getEndTime(), "yyyy-MM-dd HH:mm:ss"));
                    activityVo.setActivitySwitch(1);
                    // activityVo.setDiscountType();
                    activityVo.setDiscountRate(response.getDiscountScale());
                    // activityVo.setDiscountAmount(response.());
                    activityVo.setActivityDesc(response.getActivityDesc());
                    activityVo.setDiscountDesc(response.getDiscountDesc());
                    // activityVo.setPartakeCapitalPool();
                    // activityVo.setCapitalPoolRate();
                    // activityVo.setRemark();
                    activityVo.setDiscountRateStr(response.getDiscountScale().multiply(new BigDecimal("10")).setScale(2, RoundingMode.DOWN).stripTrailingZeros().toPlainString() + "折");
                    activityList.add(activityVo);
                }
                resultMap.put("activityList", activityList);
            }
        }
        
        // 开通裂空者
        else if (ObjectUtil.equals(applicationType, 13)) {
            resultMap.put("activityList", new ArrayList<>());
            resultMap.put("canUseCouponList", new ArrayList<>());
            resultMap.put("canNotUseCouponList", new ArrayList<>());
            
            List<ActivityVo> activityVoList = communityActivityMapper.activityList(12);
            resultMap.put("activityList", activityVoList);
        }
        return R.ok(resultMap);
    }

    @Override
    public R grabCouponTest(JSONObject paramJson) {
        Long couponBatchId = paramJson.getLong("couponBatchId");
        if (couponBatchId == null) {
            return R.error(MessageConstant.PARAMETER_CAN_NOT_BE_NULL);
        }
        String myUuid = paramJson.getString("myUuid");
//        String myUuid = StpUtil.getLoginIdAsString();
//        if (StringUtils.isEmpty(myUuid)) {
//            return R.error(MessageConstant.GET_USER_INFO_FAIL);
//        }
        CouponBatch couponBatch = couponBatchMapper.selectById(couponBatchId);
        if (couponBatch == null) {
            return R.error(MessageConstant.DATA_NOT_EXIST);
        }
        Integer getCouponType = couponBatch.getGetCouponType();//领券类型 1-按批次表数量领取  2-按券表数量领取
        Integer getNumber = couponBatch.getGetNumber();//单个用户可领取数量
        //可领取券列表
        List<Coupon> couponList = couponMapper.getCanReceiveCoupon(couponBatchId);
        if (couponList == null || couponList.size() == 0) {
            return R.error(MessageConstant.COUPON_HAVE_BEEN_REDEEMED);
        }
        QueryWrapper<Account> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("uuid", myUuid);
        Account account = accountMapper.selectOne(queryWrapper);
        if (account == null) {
            return R.error(MessageConstant.DATE_EXCEPTION);
        }
        if (getCouponType == 1) {
            //查询已领取数量
            Integer receivedNumber = couponAccountMapper.getReceivedNumberByBatchId(couponBatchId, myUuid);
            if (receivedNumber >= getNumber) {
                return R.error(MessageConstant.YOU_HAVE_RECEIVED_THE_COUPON);
            }
        } else {
            //TODO 这个情况当前不用处理
            return R.error(MessageConstant.DATE_EXCEPTION);
        }
        //先随机出一个券
        Integer randomInt = NumberUtil.randomInt(couponList.size());
        Coupon randomCoupon = couponList.get(randomInt);
        //领券 TODO 加锁
        Integer reduceNumberCount = couponMapper.reduceResidueNumber(randomCoupon.getId());
        if (reduceNumberCount == 1) {
            //减数量成功，生成用户券记录
            CouponAccount couponAccount = new CouponAccount();
            couponAccount.setAccountUuid(myUuid);
            couponAccount.setCouponBatchId(randomCoupon.getCouponBatchId());
            couponAccount.setCouponId(randomCoupon.getId());
            couponAccount.setOperateUuid(account.getOperateUuid());
            couponAccount.setState(1);//券状态 1-未使用 2-已使用
            couponAccount.setCreateTime(new Date());
            couponAccount.setUpdateTime(new Date());
            Integer count1 = couponAccountMapper.insert(couponAccount);
            if (count1 != 1) {
                throw new RuntimeException("领券时生成用户券信息没有达到预期，执行新增sql失败");
            }
        } else {
            return R.error("网络不佳，请重试");
        }
        //处理折扣显示
        if (randomCoupon.getDiscountRate().compareTo(BigDecimal.ZERO) == 0) {
            randomCoupon.setDiscountRateStr("免费");
        } else {
            randomCoupon.setDiscountRateStr(randomCoupon.getDiscountRate().multiply(new BigDecimal(10)).stripTrailingZeros().toPlainString() + "折");
        }
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("coupon", randomCoupon);
        return R.ok(resultMap);
    }

}
