package com.lj.auth.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lj.auth.exception.ServiceException;
import com.lj.auth.util.RedisUtils;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lj.auth.domain.PlatformAccount;
import com.lj.auth.mapper.PlatformAccountMapper;
import com.lj.auth.service.PlatformAccountService;

import javax.annotation.Resource;

@Service
public class PlatformAccountServiceImpl extends ServiceImpl<PlatformAccountMapper, PlatformAccount>
    implements PlatformAccountService {

    @Resource
    private RedisUtils redisUtils;

    /**
     * 获取云链平台账户私钥
     *
     * @param key key
     * @param type 用处 1-领NFT 2-平台能量充值3-签到赠送 4-公司收款退款账户 5-域名注册解析管理账户
     * @return {@link String }
     * <AUTHOR>
     * @date 2024/04/02
     */
    @Override
    public String getYlAccountPrivate(String key, Integer type) {
        String data = (String)redisUtils.get(key);
        if (StrUtil.isBlank(data)) {
            PlatformAccount one = this.getOne(Wrappers.<PlatformAccount>lambdaQuery()
                .eq(PlatformAccount::getType, type).eq(PlatformAccount::getStatus, 1));
            String value = one.getPrivateKey();
            if (StrUtil.isBlank(value)) {
                throw new ServiceException("缺少配置请前往配置，key:" + key);
            } else {
                redisUtils.set(key, value, 60L);
            }
        } else {
            return data;
        }
        return (String)redisUtils.get(key);
    }

    @Override
    public String getYlAccountAddress(String key, Integer type) {
        String data = (String)redisUtils.get(key);
        if (StrUtil.isBlank(data)) {
            PlatformAccount one = this.getOne(Wrappers.<PlatformAccount>lambdaQuery()
                .eq(PlatformAccount::getType, type).eq(PlatformAccount::getStatus, 1));
            String value = one.getAddress();
            if (StrUtil.isBlank(value)) {
                throw new ServiceException("缺少配置请前往配置，key:" + key);
            } else {
                redisUtils.set(key, value, 60L);
            }
        } else {
            return data;
        }
        return (String)redisUtils.get(key);
    }
}
