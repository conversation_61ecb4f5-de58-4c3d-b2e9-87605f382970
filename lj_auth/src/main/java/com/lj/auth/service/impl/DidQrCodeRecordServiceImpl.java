package com.lj.auth.service.impl;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lj.auth.mapper.DidQrCodeRecordMapper;
import com.lj.auth.domain.DidQrCodeRecord;
import com.lj.auth.service.DidQrCodeRecordService;
@Service
public class DidQrCodeRecordServiceImpl extends ServiceImpl<DidQrCodeRecordMapper, DidQrCodeRecord> implements DidQrCodeRecordService{

}
