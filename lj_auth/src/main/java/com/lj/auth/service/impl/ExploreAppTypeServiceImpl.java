package com.lj.auth.service.impl;

import com.lj.auth.common.R;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lj.auth.domain.ExploreAppType;
import com.lj.auth.mapper.ExploreAppTypeMapper;
import com.lj.auth.service.ExploreAppTypeService;

import javax.annotation.Resource;

@Service
public class ExploreAppTypeServiceImpl extends ServiceImpl<ExploreAppTypeMapper, ExploreAppType>
    implements ExploreAppTypeService {

    @Resource
    private ExploreAppTypeMapper exploreAppTypeMapper;

    /**
     * 探索应用类别
     * 
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/01
     */
    @Override
    public R getAppTypeService() {
        List<ExploreAppType> result = exploreAppTypeMapper.getExploreAppTypeStatistics();
        return R.okData(result);
    }
}
