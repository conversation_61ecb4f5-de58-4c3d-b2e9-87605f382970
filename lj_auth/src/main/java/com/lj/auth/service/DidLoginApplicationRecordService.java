package com.lj.auth.service;

import com.alibaba.fastjson.JSONObject;
import com.lj.auth.common.R;
import com.lj.auth.domain.DidLoginApplicationRecord;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Map;

public interface DidLoginApplicationRecordService extends IService<DidLoginApplicationRecord> {

    /**
     * 设置授权登录信息
     * 
     * @param data
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/07/01
     */
    R setSigDataSnService(Map<String, Object> data);

    /**
     * 扫码
     * 
     * @param data
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/07/01
     */
    R scanCodeService(Map<String, Object> data);

    /**
     * 扫码登录
     * 
     * @param data
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/07/01
     */
    R scanCodeLoginService(Map<String, Object> data);

    /**
     * DID扫码签到
     * 
     * @param code 码
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/07/31
     */
    R didSignInService(String code);

}
