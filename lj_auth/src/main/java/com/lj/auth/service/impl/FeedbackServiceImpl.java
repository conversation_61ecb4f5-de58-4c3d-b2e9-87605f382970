package com.lj.auth.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lj.auth.common.R;
import com.lj.auth.domain.Account;
import com.lj.auth.service.AccountService;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lj.auth.mapper.FeedbackMapper;
import com.lj.auth.domain.Feedback;
import com.lj.auth.service.FeedbackService;

import javax.annotation.Resource;

@Service
public class FeedbackServiceImpl extends ServiceImpl<FeedbackMapper, Feedback> implements FeedbackService {

    @Resource
    private AccountService accountService;

    @Resource
    private FeedbackMapper feedbackMapper;

    /**
     * 新增反馈
     * 
     * @param title 标题
     * @param content 内容
     * @param images 图片
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/29
     */
    @Override
    public R addUserFeedbackService(String title, String content, String images) {
        Account userInfo = accountService.getUserInfo();
        Feedback feedback = new Feedback();
        feedback.setAccountUuid(userInfo.getUuid());
        feedback.setImages(images);
        feedback.setContent(content);
        feedback.setTitle(title);
        this.save(feedback);
        return R.ok();
    }

    /**
     * 获取反馈内容
     * 
     * @param page 页
     * @param pageSize 页大小
     * @param type 1-已回复 0-未回复 2全-部
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/29
     */
    @Override
    public R getUserFeedbackListService(int page, int pageSize, Integer type) {
        Account userInfo = accountService.getUserInfo();
        if (type == null) {
            type = 2;
        }
        Page<Feedback> feedbackPage = null;
        if (type == 2) {
            feedbackPage = feedbackMapper.selectPage(new Page<>(page, pageSize),
                Wrappers.<Feedback>lambdaQuery().eq(Feedback::getAccountUuid, userInfo.getUuid()));
        } else {
            feedbackPage =
                feedbackMapper.selectPage(new Page<>(page, pageSize), Wrappers.<Feedback>lambdaQuery()
                    .eq(Feedback::getAccountUuid, userInfo.getUuid()).eq(Feedback::getState, type));
        }
        return R.okData(feedbackPage);
    }
}
