package com.lj.auth.service;

import com.lj.auth.common.R;
import com.lj.auth.domain.DomainAdminRecord;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.servlet.http.HttpServletRequest;

/**
 * <p>
 * 域名管理记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-07
 */
public interface DomainAdminRecordService extends IService<DomainAdminRecord> {

    /**
     * 设置所有者
     * 
     * @param chainId 链id
     * @param domain 域名
     * @param address 地址
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/07
     */
    R setDomainOwnerService(Integer chainId, String domain, String address);

    /**
     * 设置所有者回调通知
     *
     * @param request
     * <AUTHOR>
     * @date 2024/04/07
     */
    void setOwnerNotifyService(HttpServletRequest request);

    /**
     * 设置所有者详情
     * 
     * @param domain 域名
     * @param page 页
     * @param pageSize 页码
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/07
     */
    R setOwnerDetailService(String domain, int page, int pageSize);

    /**
     * 设置域名管理者
     * 
     * @param domain 域名
     * @param domainAdminInfo 管理者相关参数
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/07
     */
    R setDomainAdminService(Object domain, Object domainAdminInfo);

    /**
     * 设置管理者回调通知
     * 
     * @param request
     * <AUTHOR>
     * @date 2024/04/07
     */
    void setAdminNotifyService(HttpServletRequest request);

    /**
     * 设置域名管理者详情
     * 
     * @param domain 域名
     * @param page 页
     * @param pageSize 页码
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/07
     */
    R setAdminDetailService(String domain, int page, int pageSize);

    /**
     * 设置域名解析纪录
     * 
     * @param domain 域名
     * @param resolveInfos 解析参数
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/07
     */
    R setDomainResolveService(Object domain, Object resolveInfos);

    /**
     * 设置解析纪录回调通知
     * 
     * @param request
     * <AUTHOR>
     * @date 2024/04/07
     */
    void setResolveNotifyService(HttpServletRequest request);

    /**
     * 获取域名解析纪录详情
     * 
     * @param domain 域名
     * @param page 页
     * @param pageSize 页码
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/07
     */
    R setReolveDetailService(String domain, int page, int pageSize);

    /**
     * 获取域名绑定状态
     * 
     * @param domain 域名
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/19
     */
    R getDomainBindStateService(String domain);

    /**
     * 一键解除域名绑定
     * 
     * @param domain 域名
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/19
     */
    R getDomainClearService(String domain);

    /**
     * 内部调用
     * 
     * @param domain
     * @param uuid
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/06/26
     */
    boolean getDomainClearService(String domain, String uuid);
}
