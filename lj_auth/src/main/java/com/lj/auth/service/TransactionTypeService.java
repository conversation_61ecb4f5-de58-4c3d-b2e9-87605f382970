package com.lj.auth.service;

import com.lj.auth.common.R;
import com.lj.auth.domain.TransactionType;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 交易类型表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-07
 */
public interface TransactionTypeService extends IService<TransactionType> {

    /**
     * 获取交易类型
     * 
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/07
     */
    R getTransactionTypeService();
}
