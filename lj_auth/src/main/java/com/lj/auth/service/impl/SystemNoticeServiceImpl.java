package com.lj.auth.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lj.auth.common.R;
import com.lj.auth.common.ResultInfo;
import com.lj.auth.domain.Account;
import com.lj.auth.domain.AccountDid;
import com.lj.auth.domain.NoticeAccount;
import com.lj.auth.domain.vo.DomainExperience;
import com.lj.auth.domain.vo.DomainExperienceVo;
import com.lj.auth.exception.ResponseEnum;
import com.lj.auth.feginClient.IDomainService;
import com.lj.auth.mapper.AccountDidMapper;
import com.lj.auth.mapper.NoticeAccountMapper;
import com.lj.auth.service.AccountService;
import com.lj.auth.util.PageUtils;
import com.lj.auth.util.RedisUtils;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lj.auth.mapper.SystemNoticeMapper;
import com.lj.auth.domain.SystemNotice;
import com.lj.auth.service.SystemNoticeService;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

import static com.lj.auth.common.CommonConstant.*;

@Service
public class SystemNoticeServiceImpl extends ServiceImpl<SystemNoticeMapper, SystemNotice>
    implements SystemNoticeService {
    @Resource
    private AccountService accountService;
    @Resource
    private SystemNoticeMapper systemNoticeMapper;
    @Resource
    private NoticeAccountMapper noticeAccountMapper;
    @Resource
    private AccountDidMapper accountDidMapper;
    @Resource
    private IDomainService iDomainService;
    @Resource
    private RedisUtils redisUtils;

    /**
     * 消息中心分页查询
     *
     * @param page 页
     * @param pageSize 页大小
     * @param source 息来源:0-系统消息 1-系统公告2-转账通知
     * @param edition
     * @param channel
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/29
     */
    @Override
    public R getPage(Integer page, Integer pageSize, Integer source, String edition, String channel) {
        Map<String, Object> result = new HashMap<>();
        Account userInfo = accountService.getUserInfo();
        result.put("count", getUnread(userInfo, edition, channel));
        result.put("page", messageStatistics(source, userInfo, page, pageSize, edition, channel));
        return R.okData(result);
    }

    /**
     * 全部已读
     * 
     * @param accountUuid
     * <AUTHOR>
     * @date 2024/03/29
     */
    @Override
    public void allRead(String accountUuid) {
        // 查询未读数量
        Integer integer = noticeAccountMapper.selectCount(Wrappers.<NoticeAccount>lambdaQuery()
            .eq(NoticeAccount::getAccountUuid, accountUuid).eq(NoticeAccount::getState, 1));
        if (integer > 0) {
            noticeAccountMapper.update(null,
                Wrappers.<NoticeAccount>lambdaUpdate().eq(NoticeAccount::getAccountUuid, accountUuid)
                    .eq(NoticeAccount::getState, 1).set(NoticeAccount::getState, 2));
        }
        // 特殊处理活动公告
        List<SystemNotice> systemNoticeList = systemNoticeMapper
            .selectList(Wrappers.<SystemNotice>lambdaQuery().eq(SystemNotice::getSource, 4));
        if (CollectionUtil.isNotEmpty(systemNoticeList)) {
            // 查询用户是否有未读的活动公告
            for (SystemNotice systemNotice : systemNoticeList) {
                NoticeAccount noticeAccount = noticeAccountMapper.selectOne(
                    Wrappers.<NoticeAccount>lambdaQuery().eq(NoticeAccount::getAccountUuid, accountUuid)
                        .eq(NoticeAccount::getNoticeId, systemNotice.getId()));
                if (noticeAccount == null) {
                    // 插入一条已读记录
                    NoticeAccount noticeAccountNew = new NoticeAccount();
                    noticeAccountNew.setNoticeId(systemNotice.getId());
                    noticeAccountNew.setAccountUuid(accountUuid);
                    noticeAccountNew.setState(2);
                    noticeAccountNew.setSource(4);
                    noticeAccountNew.setReadTime(new Date());
                    noticeAccountNew.setTodayIsRead(0);
                    noticeAccountMapper.insert(noticeAccountNew);
                }
            }
        }
    }

    /**
     * @param id
     * <AUTHOR>
     * @date 2024/03/29
     */
    @Override
    public void isReadService(Integer id, Integer state) {
        Account userInfo = accountService.getUserInfo();
        // 查询该条记录是否已读
        NoticeAccount noticeAccount =
            noticeAccountMapper.selectOne(Wrappers.<NoticeAccount>lambdaQuery().eq(NoticeAccount::getId, id)
                .eq(NoticeAccount::getAccountUuid, userInfo.getUuid()).ne(NoticeAccount::getSource, 4));
        // 正常通知
        if (noticeAccount != null) {
            if (ObjectUtil.equals(noticeAccount.getState(), 1)) {
                noticeAccountMapper.update(null, Wrappers.<NoticeAccount>lambdaUpdate()
                    .eq(NoticeAccount::getId, id).set(NoticeAccount::getState, 2));
            }
            // 为4的活动公告
        } else {
            // 特殊处理活动公告
            SystemNotice systemNotice = systemNoticeMapper.selectOne(Wrappers.<SystemNotice>lambdaQuery()
                .eq(SystemNotice::getId, id).eq(SystemNotice::getSource, 4));
            if (systemNotice != null) {
                // 是否指定用户
                Integer isAppoint = systemNotice.getIsAppoint();
                // 查询是否有活动已读记录
                // 查询是否已读
                NoticeAccount noticeAccount1 = noticeAccountMapper.selectOne(Wrappers
                    .<NoticeAccount>lambdaQuery().eq(NoticeAccount::getAccountUuid, userInfo.getUuid())
                    .eq(NoticeAccount::getNoticeId, id).eq(NoticeAccount::getSource, 4));
                if (noticeAccount1 == null) {
                    NoticeAccount noticeAccountNew = new NoticeAccount();
                    noticeAccountNew.setNoticeId(Long.valueOf(id));
                    noticeAccountNew.setAccountUuid(userInfo.getUuid());
                    noticeAccountNew.setState(2);
                    noticeAccountNew.setSource(4);
                    noticeAccountNew.setReadTime(new Date());
                    noticeAccountNew.setTodayIsRead(state);
                    noticeAccountMapper.insert(noticeAccountNew);
                } else {
                    if (ObjectUtil.equals(state, 1) && ObjectUtil.equals(isAppoint, 0)) {
                        noticeAccountMapper.update(null,
                            Wrappers.<NoticeAccount>lambdaUpdate()
                                .eq(NoticeAccount::getId, noticeAccount1.getId())
                                .set(NoticeAccount::getReadTime, new Date())
                                .set(NoticeAccount::getTodayIsRead, state));
                        // 指定用户活动公告
                    } else if (ObjectUtil.equals(state, 1) && ObjectUtil.equals(isAppoint, 1)) {
                        noticeAccountMapper.update(null,
                            Wrappers.<NoticeAccount>lambdaUpdate()
                                .eq(NoticeAccount::getId, noticeAccount1.getId())
                                .set(NoticeAccount::getReadTime, new Date()).set(NoticeAccount::getState, 2)
                                .set(NoticeAccount::getTodayIsRead, state));
                    } else {
                        noticeAccountMapper.update(null,
                            Wrappers.<NoticeAccount>lambdaUpdate()
                                .eq(NoticeAccount::getId, noticeAccount1.getId())
                                .set(NoticeAccount::getReadTime, new Date()));
                    }

                }
            }
        }
    }

    /**
     * 获取未读数量
     * 
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/29
     */
    @Override
    public R getUreadCountService(String edition, String channel) {
        Account userInfo = accountService.getUserInfo();
        Map<String, Integer> unread = getUnread(userInfo, edition, channel);
        // 暂时不统计 市场消息 不统计 转账消息
        Integer systemMessages = unread.get("systemMessages");
        Integer systemAnnouncement = unread.get("systemAnnouncement");
        unread.put("allUnread", systemMessages + systemAnnouncement);
        return R.okData(unread);
    }

    /**
     * 未读弹窗通知
     * 
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/29
     */
    @Override
    public R getLatestNoticeService(String edition, String channel) {
        Map<String, Object> result = new HashMap<>(1);
        Account userInfo = accountService.getUserInfo();
        // 获取活动弹窗
        // 特殊处理活动公告在有效期内的状态
        List<SystemNotice> systemNoticeList = systemNoticeMapper.selectList(Wrappers
            .<SystemNotice>lambdaQuery().eq(SystemNotice::getSource, 4)
            .le(SystemNotice::getReminderStartTime, new Date())
            .ge(SystemNotice::getReminderEndTime, new Date()).orderByAsc(SystemNotice::getReminderStartTime));

        if (CollectionUtil.isNotEmpty(systemNoticeList)) {
            // 获取最新的一条弹窗活动
            for (SystemNotice systemNotice : systemNoticeList) {
                String edition1 = systemNotice.getEdition();
                String channel1 = systemNotice.getChannel();
                Integer isAppoint = systemNotice.getIsAppoint();
                // 移除掉那些版本和请求头不匹配的通知
                if (StrUtil.isNotBlank(edition1) && StrUtil.isNotBlank(channel1)) {
                    if (!ObjectUtil.equals(edition, edition1) || !ObjectUtil.equals(channel, channel1)) {
                        continue;
                    }
                }
                NoticeAccount noticeAccount = noticeAccountMapper.selectOne(Wrappers
                    .<NoticeAccount>lambdaQuery().eq(NoticeAccount::getAccountUuid, userInfo.getUuid())
                    .eq(NoticeAccount::getNoticeId, systemNotice.getId()).eq(NoticeAccount::getSource, 4));
                if (noticeAccount == null) {
                    // 未读
                    if (isAppoint == 1) {
                        continue;
                    }
                    result.put("id", systemNotice.getId());
                    result.put("title", systemNotice.getTitle());
                    result.put("content", systemNotice.getContent());
                    result.put("description", systemNotice.getDescription());
                    result.put("source", systemNotice.getSource());
                    result.put("createTime", systemNotice.getCreateTime());
                    result.put("jumpPath", systemNotice.getJumpPath());
                    result.put("jumpType", systemNotice.getJumpType());
                    result.put("jumpUrl", systemNotice.getJumpUrl());
                    result.put("isHiddenNav", systemNotice.getIsHiddenNav());
                    result.put("state", 1);
                } else {
                    // 已读
                    Integer frequency = systemNotice.getFrequency();
                    Integer todayIsRead = noticeAccount.getTodayIsRead();
                    // 判断活动公告是否是指定用户，指定用是否和当前用户匹配，不匹配跳过
                    if (ObjectUtil.equals(isAppoint, 1)) {
                        if (!StrUtil.equals(userInfo.getUuid(), noticeAccount.getAccountUuid())) {
                            continue;
                        }
                    }
                    if (ObjectUtil.equals(1, frequency) && ObjectUtil.equals(isAppoint, 0)) {
                        continue;
                    } else {
                        // 判断是否今日已读
                        if (ObjectUtil.equals(1, todayIsRead)) {
                            // 判断今日已读时间是否是昨天
                            Date readTime = noticeAccount.getReadTime();
                            if (DateUtil.isSameDay(readTime, new Date())) {
                                continue;
                            } else {
                                result.put("id", noticeAccount.getNoticeId());
                                result.put("title", systemNotice.getTitle());
                                result.put("content", systemNotice.getContent());
                                result.put("description", systemNotice.getDescription());
                                result.put("source", systemNotice.getSource());
                                result.put("createTime", systemNotice.getCreateTime());
                                result.put("jumpPath", systemNotice.getJumpPath());
                                result.put("jumpType", systemNotice.getJumpType());
                                result.put("jumpUrl", systemNotice.getJumpUrl());
                                result.put("isHiddenNav", systemNotice.getIsHiddenNav());
                                result.put("state", 2);

                            }
                        } else {
                            result.put("id", noticeAccount.getNoticeId());
                            result.put("title", systemNotice.getTitle());
                            result.put("content", systemNotice.getContent());
                            result.put("description", systemNotice.getDescription());
                            result.put("source", systemNotice.getSource());
                            result.put("createTime", systemNotice.getCreateTime());
                            result.put("jumpPath", systemNotice.getJumpPath());
                            result.put("jumpType", systemNotice.getJumpType());
                            result.put("jumpUrl", systemNotice.getJumpUrl());
                            result.put("isHiddenNav", systemNotice.getIsHiddenNav());
                            result.put("state", 2);

                        }
                    }
                }
            }
        } else if (result.isEmpty()) {
            result = noticeAccountMapper.getLatestNotice(userInfo.getUuid());
        }
        return R.okData(result);
    }

    /**
     * 添加授权登录通知
     *
     * @param didSymbol-DID
     * @param title-标题
     * @param content-内容
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/06/27
     */
    @Transactional
    @Override
    public R addloginNotificationService(String didSymbol, String title, String content) {
        // 查询用户uuid
        AccountDid accountDid = accountDidMapper
            .selectOne(Wrappers.<AccountDid>lambdaQuery().eq(AccountDid::getDidSymbol, didSymbol));
        if (accountDid == null) {
            return R.error(ResponseEnum.UserDoesNotExist);
        }
        String accountUuid = accountDid.getAccountUuid();
        // 插入通知
        SystemNotice systemNotice = new SystemNotice();
        systemNotice.setTitle(title).setDescription("授权登录通知").setContent(content).setSource(0).setType(1);
        systemNoticeMapper.insert(systemNotice);
        // 插入用户通知
        NoticeAccount noticeAccount = new NoticeAccount();
        noticeAccount.setAccountUuid(accountUuid).setNoticeId(systemNotice.getId()).setSource(0);
        noticeAccountMapper.insert(noticeAccount);
        return R.ok();
    }

    /**
     * 返回多条弹窗
     * 
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/11/12
     */
    @Override
    public R getLatestNoticeServiceV2(String edition, String channel) {
        List<Map<String, Object>> data = new ArrayList<>();
        Account userInfo = accountService.getUserInfo();
        // 获取活动弹窗
        // 特殊处理活动公告在有效期内的状态 必须是强制弹窗才行
        List<SystemNotice> systemNoticeList = systemNoticeMapper.selectList(Wrappers
            .<SystemNotice>lambdaQuery().eq(SystemNotice::getSource, 4).eq(SystemNotice::getType, 2)
            .le(SystemNotice::getReminderStartTime, new Date())
            .ge(SystemNotice::getReminderEndTime, new Date()).orderByAsc(SystemNotice::getReminderStartTime));

        if (CollectionUtil.isNotEmpty(systemNoticeList)) {
            // 获取最新的一条弹窗活动
            for (SystemNotice systemNotice : systemNoticeList) {
                String edition1 = systemNotice.getEdition();
                String channel1 = systemNotice.getChannel();
                Integer isAppoint = systemNotice.getIsAppoint();
                // 移除掉那些版本和请求头不匹配的通知
                if (StrUtil.isNotBlank(edition1) && StrUtil.isNotBlank(channel1)) {

                    // 修改判断版本号高低====向上兼容
                    // if (!ObjectUtil.equals(edition,edition1) ||!ObjectUtil.equals(channel,channel1)) {
                    // continue;
                    // }
                    if (!isVersionHigher(edition, edition1) || !ObjectUtil.equals(channel, channel1)) {
                        continue;
                    }

                }
                Map<String, Object> result = new HashMap<>(1);
                NoticeAccount noticeAccount = noticeAccountMapper.selectOne(Wrappers
                    .<NoticeAccount>lambdaQuery().eq(NoticeAccount::getAccountUuid, userInfo.getUuid())
                    .eq(NoticeAccount::getNoticeId, systemNotice.getId()).eq(NoticeAccount::getSource, 4));
                if (noticeAccount == null) {
                    // 未读
                    if (isAppoint == 1) {
                        continue;
                    }
                    result.put("id", systemNotice.getId());
                    result.put("title", systemNotice.getTitle());
                    result.put("content", systemNotice.getContent());
                    result.put("description", systemNotice.getDescription());
                    result.put("source", systemNotice.getSource());
                    result.put("createTime",
                        DateUtil.format(systemNotice.getCreateTime(), DatePattern.NORM_DATETIME_PATTERN));
                    result.put("jumpPath", systemNotice.getJumpPath());
                    result.put("jumpType", systemNotice.getJumpType());
                    result.put("jumpUrl", systemNotice.getJumpUrl());
                    result.put("jumpParam", systemNotice.getJumpParam());
                    result.put("isHiddenNav", systemNotice.getIsHiddenNav());
                    result.put("state", 1);
                    data.add(result);
                } else {
                    // 已读
                    Integer frequency = systemNotice.getFrequency();
                    Integer todayIsRead = noticeAccount.getTodayIsRead();
                    // 判断活动公告是否是指定用户，指定用是否和当前用户匹配，不匹配跳过
                    if (ObjectUtil.equals(isAppoint, 1)) {
                        if (!StrUtil.equals(userInfo.getUuid(), noticeAccount.getAccountUuid())) {
                            continue;
                        }
                    }
                    if (ObjectUtil.equals(1, frequency) && ObjectUtil.equals(isAppoint, 0)) {
                        continue;
                    } else {
                        // 判断是否今日已读
                        if (ObjectUtil.equals(1, todayIsRead)) {
                            // 判断今日已读时间是否是昨天
                            Date readTime = noticeAccount.getReadTime();
                            if (DateUtil.isSameDay(readTime, new Date())) {
                                continue;
                            } else {
                                result.put("id", noticeAccount.getNoticeId());
                                result.put("title", systemNotice.getTitle());
                                result.put("content", systemNotice.getContent());
                                result.put("description", systemNotice.getDescription());
                                result.put("source", systemNotice.getSource());
                                result.put("createTime", DateUtil.format(systemNotice.getCreateTime(),
                                    DatePattern.NORM_DATETIME_PATTERN));
                                result.put("jumpPath", systemNotice.getJumpPath());
                                result.put("jumpType", systemNotice.getJumpType());
                                result.put("jumpUrl", systemNotice.getJumpUrl());
                                result.put("jumpParam", systemNotice.getJumpParam());
                                result.put("isHiddenNav", systemNotice.getIsHiddenNav());
                                result.put("state", 1);
                                data.add(result);
                            }
                        } else {
                            result.put("id", noticeAccount.getNoticeId());
                            result.put("title", systemNotice.getTitle());
                            result.put("content", systemNotice.getContent());
                            result.put("description", systemNotice.getDescription());
                            result.put("source", systemNotice.getSource());
                            result.put("createTime", DateUtil.format(systemNotice.getCreateTime(),
                                DatePattern.NORM_DATETIME_PATTERN));
                            result.put("jumpPath", systemNotice.getJumpPath());
                            result.put("jumpType", systemNotice.getJumpType());
                            result.put("jumpUrl", systemNotice.getJumpUrl());
                            result.put("jumpParam", systemNotice.getJumpParam());
                            result.put("isHiddenNav", systemNotice.getIsHiddenNav());
                            result.put("state", 1);
                            data.add(result);
                        }
                    }
                }
            }
        }
        Map<String, Object> result = noticeAccountMapper.getLatestNotice(userInfo.getUuid());
        if (CollectionUtil.isNotEmpty(result)) {
            data.add(result);
        }
        // 根据 createTime 进行倒序排序
        Collections.sort(data, (o1, o2) -> {
            Date createTime1 = DateUtil.parse((String)o1.get("createTime"));
            Date createTime2 = DateUtil.parse((String)o2.get("createTime"));
            return createTime2.compareTo(createTime1);
        });

        return R.okData(data);
    }

    /**
     * 获取域名体验弹窗
     * 
     * @return {@link R }
     */
    @Override
    public R getDomainPopUpService() {
        Map<String, Object> data = new HashMap<>();
        List<Map<String, Object>> result = new ArrayList<>();
        Account userInfo = accountService.getUserInfo();
        ResultInfo accountExperienceState = iDomainService.getAccountExperienceState(userInfo.getUuid());
        Integer code = accountExperienceState.getCode();
        if (code == 200) {
            Object data1 = accountExperienceState.getData();
            if (ObjectUtil.equals(data1, true)) {
                // 添加体验弹窗
                Map<String, Object> experience = new HashMap<String, Object>();
                experience.put("id", 1);
                experience.put("name", "体验弹窗");
                experience.put("time", DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN));
                experience.put("domain",null);
                experience.put("isRead",
                    redisUtils.get(FIRST_EXPERIENCEOF_DOMAINNAME + ":" + userInfo.getUuid()) == null ? false
                        : true);
                result.add(experience);
            }
        }
        // 获取域名体验到期弹窗
        Map map = redisUtils.get(DOMAIN_EXPERIENCE_EXPIRES + ":" + userInfo.getUuid(), Map.class);
        if(map != null){
            result.add(map);
        }
        Map map1 = redisUtils.get(DOMAIN_EXPERIENCE_REGISTERED + ":" + userInfo.getUuid(), Map.class);
        if(map1 != null){
            result.add(map1);
        }
        data.put("domainPopUp", result);
        return R.okData(result);
    }

    /**
     * 域名弹窗今日已读
     *
     * @param id
     * @return {@link R }
     */
    @Override
    public R getDomainPopUpIsReadService(Integer id) {
        Account userInfo = accountService.getUserInfo();
        if (id == 1) {
            // 设置今日结束时间
            Date date = new Date();
            long time1 = date.getTime();
            long time = DateUtil.endOfDay(date).getTime();
            redisUtils.set(FIRST_EXPERIENCEOF_DOMAINNAME + ":" + userInfo.getUuid(), true,
                (time - time1) / 1000);
        } else if (id == 2) {
            redisUtils.del(DOMAIN_EXPERIENCE_EXPIRES + ":" + userInfo.getUuid());

        } else if (id == 3) {
            redisUtils.del(DOMAIN_EXPERIENCE_REGISTERED + ":" + userInfo.getUuid());
        }
        return R.ok();
    }

    /**
     * 域名体验到期
     *
     * @param domainExperience
     * @return {@link R }
     */
    @Override
    public R domainExperienceExpiresService(DomainExperience domainExperience) {
        Integer id = domainExperience.getId();
        if (id == 2) {
            List<DomainExperienceVo> domainExperienceVoList = domainExperience.getDomainExperienceVoList();
            for (DomainExperienceVo domainExperienceVo : domainExperienceVoList) {
                Map<String, Object> map = new HashMap<>();
                map.put("id", 2);
                map.put("name", "体验域名到期");
                map.put("time", DateUtil.format(domainExperienceVo.getEndTime(), DatePattern.NORM_DATETIME_PATTERN));
                map.put("domain", domainExperienceVo.getDomain());
                map.put("isRead", false);
                redisUtils.set(DOMAIN_EXPERIENCE_EXPIRES + ":" + domainExperienceVo.getAccountUuid(), map);
            }

        } else if (id == 3) {
            List<DomainExperienceVo> domainExperienceVoList = domainExperience.getDomainExperienceVoList();
            for (DomainExperienceVo domainExperienceVo : domainExperienceVoList) {
                Map<String, Object> map = new HashMap<>();
                map.put("id", 3);
                map.put("name", "体验域名已注册");
                map.put("time", DateUtil.format(domainExperienceVo.getEndTime(), DatePattern.NORM_DATETIME_PATTERN));
                map.put("domain", domainExperienceVo.getDomain());
                map.put("isRead", false);
                redisUtils.set(DOMAIN_EXPERIENCE_REGISTERED + ":" + domainExperienceVo.getAccountUuid(), map);
            }
        }
        return R.ok();
    }

    // ===============工具方法

    /**
     * 消息来源:0-系统消息 1-系统公告
     *
     * @param source
     * @param account
     * @param page
     * @param pageSize
     * @return {@link PageUtils}
     */
    private PageUtils messageStatistics(int source, Account account, Integer page, Integer pageSize,
        String edition, String channel) {
        // 2.0版本只能看2.0 1.0版本也就是没有版本的所有都能看，有版本的也要区分平台
        List<SystemNotice> systemNoticeList = new ArrayList<>();
        List<SystemNotice> systemNoticeListVersion = new ArrayList<>();
        Integer totalCount = noticeAccountMapper.selectCount(Wrappers.<NoticeAccount>lambdaQuery()
            .eq(NoticeAccount::getAccountUuid, account.getUuid()).eq(NoticeAccount::getSource, source));
        if (ObjectUtil.equals(source, 1)) {
            // 没有版本的通知也就是所有都能看 强制弹窗排除在外不展示
            if (StrUtil.isBlank(edition)) {
                systemNoticeList = systemNoticeMapper
                    .selectList(Wrappers.<SystemNotice>lambdaQuery().eq(SystemNotice::getSource, 4)
                        .eq(SystemNotice::getType, 1).isNull(SystemNotice::getEdition)
                        // .le(SystemNotice::getReminderStartTime, new Date())
                        // .ge(SystemNotice::getReminderEndTime, new Date())
                        .orderByAsc(SystemNotice::getReminderStartTime));
            }
            // 有版本和平台的通知包括指定人员的通知 指定人员必须有版本
            if (StrUtil.isNotBlank(channel) && StrUtil.isNotBlank(edition)) {
                // systemNoticeListVersion = systemNoticeMapper
                // .selectList(Wrappers.<SystemNotice>lambdaQuery().eq(SystemNotice::getSource, 4)
                // .eq(SystemNotice::getEdition, edition).eq(SystemNotice::getChannel, channel)
                // // .le(SystemNotice::getReminderStartTime, new Date())
                // // .ge(SystemNotice::getReminderEndTime, new Date())
                // .orderByAsc(SystemNotice::getReminderStartTime));
                // 查询所有版本小于等于当前版本的通知
                List<SystemNotice> allNotices = systemNoticeMapper
                    .selectList(Wrappers.<SystemNotice>lambdaQuery().eq(SystemNotice::getSource, 4)
                        .eq(SystemNotice::getType, 1).eq(SystemNotice::getChannel, channel)
                        // .le(SystemNotice::getReminderStartTime, new Date())
                        // .ge(SystemNotice::getReminderEndTime, new Date())
                        .orderByAsc(SystemNotice::getReminderStartTime));
                for (SystemNotice notice : allNotices) {
                    String noticeEdition = notice.getEdition();
                    if (noticeEdition != null && isVersionHigher(edition, noticeEdition)) {
                        systemNoticeListVersion.add(notice);
                    }
                }
            }
            // 合并两个集合
            systemNoticeList.addAll(systemNoticeListVersion);
            Integer activityCount = systemNoticeList.size();
            totalCount = totalCount + activityCount;
        }
        int start = (page - 1) * pageSize;
        List<Map<String, String>> noticeList = new ArrayList<>();
        if (start < totalCount) {
            // 系统消息
            if (ObjectUtil.equals(source, 0)) {
                noticeList = noticeAccountMapper.getPage(start, pageSize, account.getUuid(), source);
                // 系统公告
            } else if (ObjectUtil.equals(source, 1)) {
                if (StrUtil.isBlank(edition)) {
                    // 旧版本没有这个字段新版本是2.0
                    edition = "1.0";
                }
                // noticeList = noticeAccountMapper.getPage1(start, pageSize, account.getUuid(), source, edition,
                // channel);

                noticeList =
                    noticeAccountMapper.getPage2(start, pageSize, account.getUuid(), systemNoticeList);

            }
            // 转账通知
            if (source == 2) {
                noticeList = noticeAccountMapper.getTransPage(start, pageSize, account.getUuid(), source);
            }
        }
        PageUtils pageUtils = new PageUtils(totalCount, pageSize, page, noticeList);
        return pageUtils;
    }

    private Map<String, Integer> getUnread(Account account, String edition, String channel) {
        HashMap<String, Integer> result = new HashMap<>();
        // 系统消息 未读数量
        Integer unreadQuantity0 = noticeAccountMapper.selectCount(
            Wrappers.<NoticeAccount>lambdaQuery().eq(NoticeAccount::getAccountUuid, account.getUuid())
                .eq(NoticeAccount::getSource, 0).eq(NoticeAccount::getState, 1));
        // 2.0版本只能看2.0 1.0版本也就是没有版本的所有都能看，有版本的也要区分平台
        List<SystemNotice> systemNoticeList = new ArrayList<>();
        List<SystemNotice> systemNoticeListVersion = new ArrayList<>();

        // 没有版本的通知也就是所有都能看 强制弹窗不展示
        if (StrUtil.isBlank(edition)) {
            systemNoticeList = systemNoticeMapper.selectList(Wrappers.<SystemNotice>lambdaQuery()
                .eq(SystemNotice::getSource, 4).eq(SystemNotice::getType, 1).isNull(SystemNotice::getEdition)
                // .le(SystemNotice::getReminderStartTime, new Date())
                // .ge(SystemNotice::getReminderEndTime, new Date())
                .orderByAsc(SystemNotice::getReminderStartTime));
        }
        // 有版本和平台的通知包括指定人员的通知 指定人员必须有版本
        if (StrUtil.isNotBlank(channel) && StrUtil.isNotBlank(edition)) {

            // systemNoticeListVersion = systemNoticeMapper
            // .selectList(Wrappers.<SystemNotice>lambdaQuery().eq(SystemNotice::getSource, 4)
            // .eq(SystemNotice::getEdition, edition).eq(SystemNotice::getChannel, channel)
            // // .le(SystemNotice::getReminderStartTime, new Date())
            // // .ge(SystemNotice::getReminderEndTime, new Date())
            // .orderByAsc(SystemNotice::getReminderStartTime));

            List<SystemNotice> allNotices = systemNoticeMapper
                .selectList(Wrappers.<SystemNotice>lambdaQuery().eq(SystemNotice::getSource, 4)
                    .eq(SystemNotice::getType, 1).eq(SystemNotice::getChannel, channel)
                    // .le(SystemNotice::getReminderStartTime, new Date())
                    // .ge(SystemNotice::getReminderEndTime, new Date())
                    .orderByAsc(SystemNotice::getReminderStartTime));
            for (SystemNotice notice : allNotices) {
                String noticeEdition = notice.getEdition();
                if (noticeEdition != null && isVersionHigher(edition, noticeEdition)) {
                    systemNoticeListVersion.add(notice);
                }
            }

        }
        // 合并两个集合
        systemNoticeList.addAll(systemNoticeListVersion);
        // 用户收到的所有公告 有指定的公告、有没有版本的公告、有版本的公告 公告只有插入才是已读
        List<NoticeAccount> noticeAccounts =
            noticeAccountMapper.selectList(Wrappers.<NoticeAccount>lambdaQuery()
                .eq(NoticeAccount::getAccountUuid, account.getUuid()).eq(NoticeAccount::getSource, 4));
        // 该请求版本下的公告集合 ==没有版本的 +当前请求版本的公告
        List<NoticeAccount> noticeAccounts1 = new ArrayList<>();
        for (SystemNotice systemNotice : systemNoticeList) {
            for (NoticeAccount noticeAccount : noticeAccounts) {
                Long noticeId = noticeAccount.getNoticeId();
                if (ObjectUtil.equals(systemNotice.getId(), noticeId)) {
                    noticeAccounts1.add(noticeAccount);
                }
            }
        }
        // 获取未读活动公告 公告未插入用户表就是未读，确保最小值是0不能出现负数
        Integer activityCount = Math.max(0, systemNoticeList.size() - noticeAccounts1.size());
        Integer unreadQuantity1 = noticeAccountMapper.selectCount(
            Wrappers.<NoticeAccount>lambdaQuery().eq(NoticeAccount::getAccountUuid, account.getUuid())
                .eq(NoticeAccount::getSource, 1).eq(NoticeAccount::getState, 1));
        Integer unreadQuantity2 = noticeAccountMapper.selectCount(
            Wrappers.<NoticeAccount>lambdaQuery().eq(NoticeAccount::getAccountUuid, account.getUuid())
                .eq(NoticeAccount::getSource, 2).eq(NoticeAccount::getState, 1));
        result.put("systemMessages", unreadQuantity0);
        result.put("systemAnnouncement", unreadQuantity1 + activityCount);
        result.put("transferNotification", unreadQuantity2);
        return result;
    }

    /**
     * 判断版本号是否大于当前版本号 大于 true
     * 
     * @param newVersion 传递
     * @param currentVersion 原有
     * @return boolean
     * <AUTHOR>
     * @date 2025/03/19
     */
    private boolean isVersionHigher(String newVersion, String currentVersion) {
        String[] newParts = newVersion.split("\\.");
        String[] currentParts = currentVersion.split("\\.");

        for (int i = 0; i < Math.min(newParts.length, currentParts.length); i++) {
            int newPart = Integer.parseInt(newParts[i]);
            int currentPart = Integer.parseInt(currentParts[i]);

            if (newPart > currentPart) {
                return true;
            } else if (newPart < currentPart) {
                return false;
            }
        }
        return newParts.length >= currentParts.length;
    }
}