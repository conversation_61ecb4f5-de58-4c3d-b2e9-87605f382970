package com.lj.auth.service.impl;

import static com.lj.auth.common.CommonConstant.YL_GASQUANTITY;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;

import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lj.auth.common.R;
import com.lj.auth.domain.Account;
import com.lj.auth.domain.RechargeAssets;
import com.lj.auth.exception.ResponseEnum;
import com.lj.auth.exception.ServiceException;
import com.lj.auth.mapper.RechargeAssetsMapper;
import com.lj.auth.service.AccountService;
import com.lj.auth.service.GlobalConfigService;
import com.lj.auth.service.RechargeAssetsService;

import cn.hutool.core.util.StrUtil;

@Service
@Slf4j
public class RechargeAssetsServiceImpl extends ServiceImpl<RechargeAssetsMapper, RechargeAssets>
    implements RechargeAssetsService {
    @Resource
    private AccountService accountService;
    @Resource
    private GlobalConfigService globalConfigService;

    /**
     * 校验资产余额
     * 
     * @param amount 消耗金额
     * @return {@link RechargeAssets }
     * <AUTHOR>
     * @date 2024/04/02
     */
    @Override
    public RechargeAssets checkAssets(BigDecimal amount) {
        Account userInfo = accountService.getUserInfo();
        RechargeAssets one = this.getOne(
            Wrappers.<RechargeAssets>lambdaQuery().eq(RechargeAssets::getAccountUuid, userInfo.getUuid()));
        String didSymbol = userInfo.getDidSymbol();
        if (StrUtil.isBlank(didSymbol)) {
            throw new ServiceException(ResponseEnum.NotDIDCertified);
        }

        if (one == null || (amount.compareTo(one.getBalance()) == 1)) {
            throw new ServiceException(ResponseEnum.InsufficientAccountBalance);
        }
        return one;
    }

    /**
     * 扣除资产
     * 
     * @param accountUuid 用户uuid
     * @param amount 金额
     * <AUTHOR>
     * @date 2024/04/02
     */
    @Override
    public void updateAssets(String accountUuid, BigDecimal amount) {
        // 个人资产
        RechargeAssets rechargeAssets = this
            .getOne(Wrappers.<RechargeAssets>lambdaQuery().eq(RechargeAssets::getAccountUuid, accountUuid));

        BigDecimal balance = rechargeAssets.getBalance();
        // 扣除余额
        BigDecimal currentBalance = balance.subtract(amount);
        //校验资产
        checkAssets(amount);
        this.update(null, Wrappers.<RechargeAssets>lambdaUpdate()
            .eq(RechargeAssets::getAccountUuid, accountUuid).set(RechargeAssets::getBalance, currentBalance));
    }

    /**
     * 获取个人资金账户
     * 
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/03
     */
    @Override
    public R getUserAssetService() {
        Account userInfo = accountService.getUserInfo();
        RechargeAssets one = this.getOne(
            Wrappers.<RechargeAssets>lambdaQuery().eq(RechargeAssets::getAccountUuid, userInfo.getUuid()));
        if (one == null) {
            RechargeAssets rechargeAssets = new RechargeAssets();
            rechargeAssets.setAccountUuid(userInfo.getUuid());
            rechargeAssets.setOperateUuid(userInfo.getOperateUuid());
            this.save(rechargeAssets);
            one = this.getOne(Wrappers.<RechargeAssets>lambdaQuery().eq(RechargeAssets::getAccountUuid,
                userInfo.getUuid()));
        }
        one.setTotalAmount( one.getTotalAmount());
        one.setBalance( one.getBalance());
        one.setFreeze( one.getFreeze());
        return R.okData(one);
    }

    /**
     * 增加冻结扣除余额
     * 
     * @param amount 金额
     * @param accountUuid 账户uuid
     * <AUTHOR>
     * @date 2024/04/03
     */
    @Override
    public void addFreeze(BigDecimal amount, String accountUuid) {
        RechargeAssets one = this
            .getOne(Wrappers.<RechargeAssets>lambdaQuery().eq(RechargeAssets::getAccountUuid, accountUuid));
        BigDecimal balance = one.getBalance().subtract(amount);
        BigDecimal add = one.getFreeze().add(amount);
        this.update(Wrappers.<RechargeAssets>lambdaUpdate().eq(RechargeAssets::getAccountUuid, accountUuid)
            .set(RechargeAssets::getBalance, balance).set(RechargeAssets::getFreeze, add));
    }

    /**
     * 云链平台能量换算
     *
     * @param gas
     * @return {@link String}
     */
    private String gasToMoney(BigDecimal gas) {
        // 一元 定价
        String value = globalConfigService.getGlobalConfig(YL_GASQUANTITY);

        BigDecimal fixedPrice = new BigDecimal(value);

        // 保留两位小数
        BigDecimal gasResult = gas.divide(fixedPrice, 2, RoundingMode.HALF_UP);

        return gasResult.toString();
    }
    private BigDecimal decimalConversion(BigDecimal amount) {
        DecimalFormat decimalFormat = new DecimalFormat("#.##");
        return new BigDecimal(decimalFormat.format(amount));
    }
}
