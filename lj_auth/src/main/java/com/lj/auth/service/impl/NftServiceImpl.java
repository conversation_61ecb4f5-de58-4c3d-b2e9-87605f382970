package com.lj.auth.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.HexUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lj.auth.annotation.LogRecord;
import com.lj.auth.common.CommonConstant;
import com.lj.auth.common.R;
import com.lj.auth.config.RabbitConfig;
import com.lj.auth.domain.*;
import com.lj.auth.domain.resp.InsertIPFSResp;
import com.lj.auth.domain.resp.NftGetRecordResp;
import com.lj.auth.domain.vo.*;
import com.lj.auth.exception.ServiceException;
import com.lj.auth.mapper.*;
import com.lj.auth.service.*;
import com.lj.auth.task.CheckSpOverview;
import com.lj.auth.util.BNBUtil;
import com.lj.auth.util.IpfsUtil;
import com.lj.auth.util.PageUtils;
import com.lj.auth.util.UUIdUtil;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.jcajce.provider.symmetric.AES;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.web3j.abi.TypeReference;
import org.web3j.abi.datatypes.*;
import org.web3j.abi.datatypes.generated.Uint256;
import org.web3j.protocol.core.methods.response.*;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.lj.auth.common.CommonConstant.*;
import static com.lj.auth.domain.Contract.*;

import static com.lj.auth.domain.PlatformAccount.STATUS_SHOW;

@Slf4j
@Service
public class NftServiceImpl extends ServiceImpl<NftMapper, Nft> implements NftService {
    // 锁标识
    public static final String LOCK_SYMBOL = "mint";
    @Resource
    private RedisTemplate<String, Integer> redisTemplate;
    @Resource
    private RedissonClient redissonClient;

    @Resource
    private BadgeService badgeService;  // 添加BadgeService注入
    private static final BigInteger GAS_LIMIT = new BigInteger("3000000");

    // 消息的前缀
    private static final String MESSAGE = "message:";
    @Resource
    private NftMapper nftMapper;
    @Resource
    private ChainMapper chainMapper;
    @Resource
    private ContractMapper contractMapper;
    @Resource
    private YlBlockChainServer ylBlockChainServer;
    @Resource
    private GlobalConfigMapper globalConfigMapper;
    @Resource
    private EnergyRechargeFlowMapper ylEnergyRechargeFlowMapper;
    @Resource
    private NftTransferRecordMapper nftTransferRecordMapper;
    @Resource
    private NftGetRecordService nftGetRecordService;
    @Resource
    private RabbitTemplate rabbitTemplate;
    @Resource
    private IpfsImageMapper ipfsImageMapper;
    @Resource
    private NftGetRecordMapper nftGetRecordMapper;
    @Resource
    private NftManualDisposeMapper nftManualDisposeMapper;
    @Resource
    private PlatformAccountMapper platformAccountMapper;

    @Resource
    private CheckSpOverview checkSpOverview;
    @Resource
    private NftGetRecordDomainMapper nftGetRecordDomainMapper;

    // 自研链chainId
    @Value("${ylChainId}")
    private Integer ylChainId;

    @Value("${ipfs.url}")
    private String ipfsUrl;

    @Value("${ipfs.imagePrefix}")
    private String imagePrefix;

    @Resource
    private GlobalConfigService globalConfigService;

    @Resource
    private NftBeforeService nftBeforeService;
    @Resource
    private NftOnChainService nftOnChainService;
    @Resource
    private OrderDomainRenewalNftRefundMapper orderDomainRenewalNftRefundMapper;
    @Resource
    private OrderDomainRenewalNftMapper orderDomainRenewalNftMapper;
    @Resource
    private AccountMapper accountMapper;
    @Resource
    private FixDomainMapper fixDomainMapper;
    @Resource
    private CoverInfoMapper coverInfoMapper;

    /**
     * 自动批量创建NFT
     * 
     * @param chainAccountAddress
     * @param nftCount
     * @return
     */
    @Override
    public R autoBatchCreateNft(String chainAccountAddress, Integer nftCount) {
        // nft 数量大于等于1 且小于等于1
        if (nftCount < 1) {
            return R.error("NFT数量最少1个");
        } else if (nftCount > 1) {
            return R.error("暂不支持批量领取");
        }

        String flag = globalConfigMapper.queryConfig("get_nft_flag");
        if (StrUtil.isEmpty(flag) || "0".equals(flag)) {
            return R.error("NFT领取功能未开启, 敬请期待");
        }
        String uuid = StpUtil.getLoginIdAsString();
        // 查询NFT领取信息
        R r = nftGetRecordService.queryGetNftInfo();
        Integer code = (Integer)r.get("code");
        if (code == 500) {
            return r;
        }
        Map<String, Integer> getNftInfoMap = (Map<String, Integer>)r.get("data");
        log.info("getNftInfoMap:{}", getNftInfoMap);
        Integer noCount = getNftInfoMap.get("noCount");
        if (nftCount > noCount) {
            return R.error("可领取的nft数量不足");
        }
        return ylBlockChainServer.autoBatchCreateNft(chainAccountAddress, nftCount, uuid);
    }

    @Override
    public R autoBatchCreateNftV2(String chainAccountAddress, Integer nftCount) {
        String flag = globalConfigMapper.queryConfig("get_nft_flag");
        if (StrUtil.isEmpty(flag) || "0".equals(flag)) {
            return R.error("NFT领取功能未开启, 敬请期待");
        }
        String uuid = StpUtil.getLoginIdAsString();
        // 查询NFT领取信息
        NftGetRecordResp nftGetRecordResp = nftGetRecordMapper.countByAccountUUID(uuid);
        log.info("getNftInfoMap:{}", nftGetRecordResp);
        Integer noCount = nftGetRecordResp.getNoCount();
        Assert.isTrue(nftCount <= noCount, "可领取的nft数量不足");
        return ylBlockChainServer.autoBatchSingleCreateNft(chainAccountAddress, nftCount, uuid);
    }

    @Override
    public R setNftDescribe(Long id, String describe) {
        // 根据id更新描述
        LambdaUpdateWrapper<Nft> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(Nft::getNftDescribe, describe).eq(Nft::getId, id);
        nftMapper.update(null, updateWrapper);
        return R.ok();
    }

    @Override
    public R getNfts(Integer opbChainId, String chainAccount, String nftName) {
        LambdaQueryWrapper<Nft> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Nft::getHolder, chainAccount).eq(Nft::getStatus, 1).eq(Nft::getNftType, 1)
            .eq(Nft::getOpbChainId, opbChainId).eq(Nft::getIsDelete, false);
        if (StrUtil.isNotEmpty(nftName)) {
            queryWrapper.likeRight(Nft::getNftName, nftName);
        }
        List<Nft> nfts = nftMapper.selectList(queryWrapper);
        if (CollectionUtil.isEmpty(nfts)) {
            return R.ok();
        }
        nfts.sort(Comparator.comparingInt(o -> o.getNftName().toLowerCase().charAt(0)));
        return R.okData(nfts);
    }

    /**
     * 获取NFT列表
     * 
     * @param pageNum 页号
     * @param pageSize 页码
     * @param opbChainId 链id
     * @param chainAccount 链账户
     * @param queryType 查询类型 1：可用 2：不可用
     * @param nftName NFT名称
     * @return
     */
    @Override
    public R getNftsV2(Integer pageNum, Integer pageSize, Integer opbChainId, String chainAccount,
        Integer queryType, String nftName) {
        int start = (pageNum - 1) * pageSize;
        Boolean isApprove = false;
        if (queryType == 1) {
            isApprove = false;
        } else if (queryType == 2) {
            isApprove = true;
        }
        List<String> orderBy = new ArrayList<>();
        List<Nft> nftList = new ArrayList<>();
        orderBy.add("token_id ASC");
        int totalCount = nftMapper.totalPageQueryByAddressAndStatus(opbChainId, Nft.NFT_TYPE_ERC721,
            chainAccount, Nft.NFT_STATUS_NORMAL, isApprove, nftName);
        if (start < totalCount) {
            nftList = nftMapper.pageQueryByAddressAndStatus(opbChainId, Nft.NFT_TYPE_ERC721, chainAccount,
                Nft.NFT_STATUS_NORMAL, isApprove, nftName, orderBy, start, pageSize);
        }
        PageUtils pageUtils = new PageUtils(totalCount, pageSize, pageNum, nftList);
        return R.okData(pageUtils);
    }

    /**
     * 获取NFT详情的NFT列表
     * 
     * @param pageNum 页码
     * @param pageSize 数量
     * @param nftid nftid
     * @return
     */
    @Override
    public R getNftDetailsNFTList(Integer pageNum, Integer pageSize, Long nftid) {
        Nft nft = nftMapper.queryById(nftid);
        Assert.notNull(nft, "参数异常");
        Integer opbChainId = nft.getOpbChainId();
        String contractAddress = nft.getContractAddress();

        int start = (pageNum - 1) * pageSize;
        List<String> orderBy = new ArrayList<>();
        List<Nft> nftList = new ArrayList<>();
        orderBy.add("create_time DESC");
        int totalCount = nftMapper.totalPageQueryNftDetailsNFTList(opbChainId, contractAddress,
            Nft.NFT_TYPE_ERC721, Nft.NFT_STATUS_NORMAL, null);
        if (start < totalCount) {
            nftList = nftMapper.pageQueryNftDetailsNFTList(opbChainId, contractAddress, Nft.NFT_TYPE_ERC721,
                Nft.NFT_STATUS_NORMAL, null, orderBy, start, pageSize);
        }
        PageUtils pageUtils = new PageUtils(totalCount, pageSize, pageNum, nftList);
        return R.okData(pageUtils);
    }

    /**
     * 这里传的 NFT 是 NFT表的id不是tokenid 所以还是 用id查询
     * 
     * @param id
     * @return
     */
    @Override
    public R getNftDetails(Long id) {
        NftVo nftVo = new NftVo();
        String standard = "ERC721";
        // 1.查询nft
        Nft nft = nftMapper.queryById(id);
        if (ObjectUtil.isEmpty(nft)) {
            throw new ServiceException("参数异常");
        }
        nftVo.setNftName(nft.getNftName());
        nftVo.setNftImage(nft.getNftImage());
        nftVo.setNftPrice(nft.getNftPrice());
        // 设置基础信息
        NftVo.NftBaseInfo nftBaseInfo = new NftVo.NftBaseInfo();
        LambdaQueryWrapper<Chain> queryWrapper1 = new LambdaQueryWrapper<>();
        queryWrapper1.eq(Chain::getOpbChainId, nft.getOpbChainId());
        Chain chain = chainMapper.selectOne(queryWrapper1);
        nftBaseInfo.setChainName(chain.getChainName());
        nftBaseInfo.setContractAddress(nft.getContractAddress());
        nftBaseInfo.setCreator(nft.getCreator());
        nftBaseInfo.setHolder(nft.getHolder());
        if (nft.getNftType() == 1) {
            nftBaseInfo.setStandard(standard);
        }
        nftBaseInfo.setTokenId(nft.getTokenId());
        nftVo.setNftBaseInfo(nftBaseInfo);
        // 2.设置交易记录
        LambdaQueryWrapper<NftTransferRecord> queryWrapper2 = new LambdaQueryWrapper<>();
        queryWrapper2.eq(NftTransferRecord::getTokenId, nft.getTokenId())
            .eq(NftTransferRecord::getOpbChainId, nft.getOpbChainId())
            .eq(NftTransferRecord::getContractId, nft.getContractId())
            .eq(NftTransferRecord::getIsDelete, false)
            .and(x -> x.eq(NftTransferRecord::getFromAddress, nft.getHolder()).or()
                .eq(NftTransferRecord::getToAddress, nft.getHolder()))
            .orderByAsc(NftTransferRecord::getCreateTime);
        List<NftTransferRecord> nftTransferRecords = nftTransferRecordMapper.selectList(queryWrapper2);
        List<NftVo.TransferRecord> transferRecords = nftTransferRecords.stream().map(x -> {
            NftVo.TransferRecord transferRecord = new NftVo.TransferRecord();
            BeanUtil.copyProperties(x, transferRecord, true);
            return transferRecord;
        }).collect(Collectors.toList());
        nftVo.setTransferRecords(transferRecords);
        // 3.查询ipfs属性
        String baseUri = null;
        try {
            baseUri = BNBUtil.getBaseUri(nft.getHolder(), nft.getContractAddress());
        } catch (IOException e) {
            e.printStackTrace();
            throw new ServiceException("ipfs路径出错，请联系客服");
        }
        String remoteUrl = baseUri + nft.getTokenId();
        URL url = null;
        try {
            url = new URL(remoteUrl);
        } catch (MalformedURLException e) {
            throw new ServiceException("ipfs访问失败");
        }
        JSONObject jsonObject = JSON.parseObject(url);
        nftVo.setJsonObject(jsonObject);
        // 4.查询所在合约持有的其他NFT
        Contract contract = contractMapper.selectById(nft.getContractId());
        NftVo.ContractInfo contractInfo = new NftVo.ContractInfo();
        contractInfo.setContractId(contract.getId());
        contractInfo.setContractName(contract.getContractName());
        contractInfo.setContractUrl(contract.getContractUrl());
        contractInfo.setContractDescribe(contract.getContractDescribe());
        contractInfo.setContractAddress(contract.getContractAddress());
        contractInfo.setStandard(standard);

        // LambdaQueryWrapper<Nft> queryWrapper3 = new LambdaQueryWrapper<>();
        // queryWrapper3.eq(Nft::getStatus, 1)
        // .eq(Nft::getNftType, 1)
        // .eq(Nft::getOpbChainId, nft.getOpbChainId())
        // .eq(Nft::getContractAddress, nft.getContractAddress())
        // .orderByDesc(Nft::getCreateTime);
        // List<Nft> nfts = nftMapper.selectList(queryWrapper3);
        // if (CollectionUtil.isNotEmpty(nfts)) {
        // contractInfo.setNft(nfts);
        // }

        List<String> orderBy = new ArrayList<>();
        orderBy.add("create_time DESC");
        List<Nft> nftList = nftMapper.pageQueryNftDetailsNFTList(nft.getOpbChainId(),
            nft.getContractAddress(), Nft.NFT_TYPE_ERC721, Nft.NFT_STATUS_NORMAL, null, orderBy, 0, 10);
        if (CollectionUtil.isNotEmpty(nftList)) {
            contractInfo.setNft(nftList);
        }

        BigInteger totalCount = new BigInteger("10000");
        try {
            totalCount = ylBlockChainServer.getTotalCount(nft.getHolder(), nft.getContractAddress());
        } catch (IOException e) {
            e.printStackTrace();
            log.error("web3查询NFT 发行总数失败");
        }
        contractInfo.setItems(totalCount);
        nftVo.setContractInfo(contractInfo);
        return R.okData(nftVo);
    }

    @Override
    public R getNftDetailsv2(Long id) {
        NftVo nftVo = new NftVo();
        // 1.查询nft
        Nft nft = nftMapper.queryById(id);
        Assert.notNull(nft, "参数异常");
        nftVo.setNftName(nft.getNftName());
        nftVo.setNftImage(nft.getNftImage());
        nftVo.setNftPrice(nft.getNftPrice());
        // 设置基础信息
        NftVo.NftBaseInfo nftBaseInfo = new NftVo.NftBaseInfo();
        Chain chain = chainMapper.queryByChainOpbChainId(nft.getOpbChainId());
        nftBaseInfo.setChainName(chain.getChainName());
        nftBaseInfo.setContractAddress(nft.getContractAddress());
        nftBaseInfo.setCreator(nft.getCreator());
        nftBaseInfo.setHolder(nft.getHolder());
        if (nft.getNftType() == 1) {
            nftBaseInfo.setStandard("ERC721");
        }
        nftBaseInfo.setTokenId(nft.getTokenId());
        nftVo.setNftBaseInfo(nftBaseInfo);
        // 2.设置交易记录
        LambdaQueryWrapper<NftTransferRecord> queryWrapper2 = new LambdaQueryWrapper<>();
        queryWrapper2.eq(NftTransferRecord::getTokenId, nft.getTokenId())
            .eq(NftTransferRecord::getOpbChainId, nft.getOpbChainId())
            .eq(NftTransferRecord::getContractId, nft.getContractId())
            .eq(NftTransferRecord::getIsDelete, false)
            .and(x -> x.eq(NftTransferRecord::getFromAddress, nft.getHolder()).or()
                .eq(NftTransferRecord::getToAddress, nft.getHolder()))
            .orderByAsc(NftTransferRecord::getCreateTime);
        List<NftTransferRecord> nftTransferRecords = nftTransferRecordMapper.selectList(queryWrapper2);
        List<NftVo.TransferRecord> transferRecords = nftTransferRecords.stream().map(x -> {
            NftVo.TransferRecord transferRecord = new NftVo.TransferRecord();
            BeanUtil.copyProperties(x, transferRecord, true);
            return transferRecord;
        }).collect(Collectors.toList());
        nftVo.setTransferRecords(transferRecords);
        // 3.查询ipfs属性
        String baseUri = null;
        try {
            baseUri = BNBUtil.getBaseUri(nft.getHolder(), nft.getContractAddress());
        } catch (IOException e) {
            e.printStackTrace();
            throw new ServiceException("ipfs路径出错，请联系客服");
        }
        String remoteUrl = baseUri + nft.getTokenId();
        URL url = null;
        try {
            url = new URL(remoteUrl);
        } catch (MalformedURLException e) {
            throw new ServiceException("ipfs访问失败");
        }
        JSONObject jsonObject = JSON.parseObject(url);
        nftVo.setJsonObject(jsonObject);
        // 4.查询所在合约持有的其他NFT
        Contract contract = contractMapper.selectById(nft.getContractId());
        NftVo.ContractInfo contractInfo = new NftVo.ContractInfo();
        contractInfo.setContractId(contract.getId());
        contractInfo.setContractName(contract.getContractName());
        contractInfo.setContractUrl(contract.getContractUrl());

        LambdaQueryWrapper<Nft> queryWrapper3 = new LambdaQueryWrapper<>();
        queryWrapper3.eq(Nft::getStatus, 1).eq(Nft::getNftType, 1).eq(Nft::getOpbChainId, nft.getOpbChainId())
            .eq(Nft::getContractAddress, nft.getContractAddress()).eq(Nft::getIsDelete, false)
            .orderByDesc(Nft::getCreateTime);
        List<Nft> nfts = nftMapper.selectList(queryWrapper3);
        if (CollectionUtil.isNotEmpty(nfts)) {
            // contractInfo.setNft(nfts);
        }
        BigInteger totalCount = null;
        try {
            totalCount = ylBlockChainServer.getTotalCount(nft.getHolder(), nft.getContractAddress());
        } catch (IOException e) {
            e.printStackTrace();
            throw new ServiceException("获取发行总数失败");
        }
        contractInfo.setItems(totalCount);
        nftVo.setContractInfo(contractInfo);
        return R.okData(nftVo);
    }

    @Override
    public R getNftList(Long id, String nftName) {
        Contract contract = contractMapper.selectById(id);
        if (ObjectUtil.isEmpty(contract)) {
            throw new ServiceException("合约信息不存在");
        }
        NftVo.ContractInfo contractInfo = new NftVo.ContractInfo();
        contractInfo.setContractId(id);
        contractInfo.setContractName(contract.getContractName());
        contractInfo.setContractUrl(contract.getContractUrl());
        contractInfo.setContractDescribe(contract.getContractDescribe());
        contractInfo.setContractAddress(contract.getContractAddress());
        if (contract.getContractType() == 1) {
            contractInfo.setStandard("ERC721");
        }
        LambdaQueryWrapper<Nft> queryWrapper3 = new LambdaQueryWrapper<>();
        queryWrapper3.eq(Nft::getStatus, 1).eq(Nft::getNftType, 1)
            .eq(Nft::getOpbChainId, contract.getOpbChainId()).eq(Nft::getIsDelete, false)
            .eq(Nft::getContractAddress, contract.getContractAddress());
        if (StrUtil.isNotEmpty(nftName)) {
            queryWrapper3.likeRight(Nft::getNftName, nftName);
        }
        queryWrapper3.orderByDesc(Nft::getCreateTime);
        List<Nft> nfts = nftMapper.selectList(queryWrapper3);
        if (CollectionUtil.isNotEmpty(nfts)) {
            contractInfo.setNft(nfts);
        }
        BigInteger totalCount = null;
        try {
            totalCount = ylBlockChainServer.getTotalCount("0x18ac6301b085418d9e4cbbad41ea4b0ecbc0654b",
                contract.getContractAddress());
        } catch (IOException e) {
            e.printStackTrace();
            throw new ServiceException("获取发行总数失败");
        }
        contractInfo.setItems(totalCount);
        return R.okData(contractInfo);
    }

    @Override
    public R getNftListV2(Integer pageNum, Integer pageSize, Long id, String nftName) {
        Map resultMap = new HashMap();

        PlatformAccount platformAccount = platformAccountMapper.queryByTypeAndStatus(
            PlatformAccount.TYPE_DOMAIN_REGISTER_RESOLVE_ACCOUNT, PlatformAccount.STATUS_SHOW);
        Assert.notNull(platformAccount, "平台账户不存在");
        String platformAccountAddress = platformAccount.getAddress();
        Contract contract = contractMapper.selectById(id);
        Assert.notNull(contract, "合约信息不存在");

        Integer opbChainId = contract.getOpbChainId();
        String contractAddress = contract.getContractAddress();
        String contractName = contract.getContractName();
        String contractUrl = contract.getContractUrl();
        String contractDescribe = contract.getContractDescribe();
        String standard = "";
        if (contract.getContractType() == 1) {
            standard = "ERC721";
        }

        resultMap.put("contractId", id);
        resultMap.put("contractName", contractName);
        resultMap.put("contractUrl", contractUrl);
        resultMap.put("contractDescribe", contractDescribe);
        resultMap.put("contractAddress", contractAddress);
        resultMap.put("standard", standard);

        int start = (pageNum - 1) * pageSize;
        List<String> orderBy = new ArrayList<>();
        List<Nft> nftList = new ArrayList<>();
        orderBy.add("create_time DESC");
        int totalCount = nftMapper.totalPageQueryNftDetailsNFTList(opbChainId, contractAddress,
            Nft.NFT_TYPE_ERC721, Nft.NFT_STATUS_NORMAL, nftName);
        if (start < totalCount) {
            nftList = nftMapper.pageQueryNftDetailsNFTList(opbChainId, contractAddress, Nft.NFT_TYPE_ERC721,
                Nft.NFT_STATUS_NORMAL, nftName, orderBy, start, pageSize);
        }

        resultMap.put("totalCount", totalCount);
        resultMap.put("pageSize", pageSize);
        resultMap.put("currPage", pageNum);
        resultMap.put("totalPage", totalCount / pageSize + (totalCount % pageSize != 0 ? 1 : 0));
        resultMap.put("nft", nftList);

        BigInteger items = null;
        try {
            items = ylBlockChainServer.getTotalCount(platformAccountAddress, contractAddress);
        } catch (IOException e) {
            e.printStackTrace();
            throw new ServiceException("获取发行总数失败");
        }
        resultMap.put("items", items);
        return R.okData(resultMap);
    }

    @Override
    public R getTransactionDetail(Long id) {
        NftTransferRecord nftTransferRecord = nftTransferRecordMapper.queryById(id);
        if (ObjectUtil.isEmpty(nftTransferRecord)) {
            throw new ServiceException("交易记录不存在");
        }
        LambdaQueryWrapper<Nft> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Nft::getContractAddress, nftTransferRecord.getContractAddress())
            .eq(Nft::getTokenId, nftTransferRecord.getTokenId())
            .eq(Nft::getOpbChainId, nftTransferRecord.getOpbChainId()).eq(Nft::getIsDelete, false);
        Nft nft = nftMapper.selectOne(queryWrapper);
        if (ObjectUtil.isEmpty(nft)) {
            throw new ServiceException("数据异常，请联系管理员");
        }
        NftTransferRecordVo nftTransferRecordVo = new NftTransferRecordVo();
        BeanUtil.copyProperties(nftTransferRecord, nftTransferRecordVo, true);
        nftTransferRecordVo.setNftUrl(nft.getNftImage());
        nftTransferRecordVo.setNftName(nft.getNftName());
        String transactionHash = nftTransferRecordVo.getTransactionHash();
        EthTransaction ethTransaction = null;
        try {
            ethTransaction = BNBUtil.ethGetTransactionByHash(transactionHash);
        } catch (IOException e) {
            e.printStackTrace();
            throw new ServiceException("交易不存在");
        }
        Optional<Transaction> transaction = ethTransaction.getTransaction();
        if (!transaction.isPresent()) {
            throw new ServiceException("交易不存在");
        }
        Transaction transactionInfo = transaction.get();
        BigInteger value = transactionInfo.getValue();
        BigDecimal fixUsed = new BigDecimal(value).divide(BigDecimal.TEN.pow(18));
        String energyValue = nftTransferRecord.getEnergyValue();
        BigInteger subtract = new BigInteger(energyValue).subtract(value);
        BigDecimal gasUsed = new BigDecimal(subtract).divide(BigDecimal.TEN.pow(18));
        nftTransferRecordVo.setGasPriceUsed(gasUsed.toPlainString());
        nftTransferRecordVo.setFixPriceUsed(fixUsed.toPlainString());
        BigInteger blockNumber = transactionInfo.getBlockNumber();
        EthBlock ethBlock = null;
        try {
            ethBlock = BNBUtil.ethGetBlockByNumber(blockNumber, false);
        } catch (IOException e) {
            e.printStackTrace();
            throw new ServiceException("区块不存在");
        }
        EthBlock.Block block = ethBlock.getBlock();
        BigInteger timestamp = block.getTimestamp();
        String strTime = DateUtil.date(timestamp.multiply(new BigInteger("1000")).longValue())
            .toString("yyyy-MM-dd HH:mm:ss");
        nftTransferRecordVo.setTransactionTime(strTime);
        return R.okData(nftTransferRecordVo);
    }

    @Override
    public R getTransferEstimateGas(Integer opbChainId, String contractAddress, String fromAddress,
        String toAddress, Integer tokenId) {
        if (opbChainId.equals(ylChainId)) {
            String functionName = "transferFrom";
            List<Type> inputParameters = new ArrayList<>();
            List<TypeReference<?>> outputParameters = new ArrayList<>();
            inputParameters.add(new Address(fromAddress));
            inputParameters.add(new Address(toAddress));
            inputParameters.add(new Uint256(tokenId));
            Function function = new Function(functionName, inputParameters, outputParameters);
            try {
                BigDecimal spUsed =
                    ylBlockChainServer.transferEstimateGas(fromAddress, contractAddress, function);
                return R.okData(spUsed.toPlainString());
            } catch (Exception e) {
                e.printStackTrace();
                return R.error("网络异常，请联系管理员");
            }
        }
        return R.error("该链暂未开通，敬请期待");

    }

    /**
     * 获取NFT绑定状态
     * 
     * @param tokenId
     * @param tokenId
     * @return
     */
    @Override
    public R getNFTBindStatus(Long tokenId) {
        List<Map> resultList = new ArrayList<>();
        // 查询NFT头像信息
        boolean headportraitNftIdIsExists = accountMapper.headportraitNftIdIsExists(tokenId);
        if (headportraitNftIdIsExists) {
            Map resultMao = new HashMap();
            resultMao.put("bindType", 1);
            resultList.add(resultMao);
        }
        Contract authContract = contractMapper.queryByChainIDAndType(ylChainId, CONTRACT_TYPE_ERC721,
            STATUS_ENABLE, TO_USE_REGISTER_DOMAIN_NFT);
        Assert.notNull(authContract, "合约不存在");
        String contractAddress = authContract.getContractAddress();
        Nft nft = nftMapper.queryByTokenId(contractAddress, Integer.parseInt(tokenId.toString()), null, null);
        Assert.notNull(nft, "nft不存在");
        String nftImageUrl = nft.getNftImage();
        // 查询NFT封面信息
        boolean coverUrlIsExists = fixDomainMapper.coverUrlIsExists(nftImageUrl);
        if (coverUrlIsExists) {
            Map resultMao = new HashMap();
            resultMao.put("bindType", 2);
            resultList.add(resultMao);
        }
        return R.okData(resultList);
    }

    /**
     * 解绑NFT应用信息系
     * 
     * @param tokenId
     * @param chainAccountAddress
     * @return
     */
    @Override
    public R NFTUnbind(Long tokenId, String chainAccountAddress) {
        List<Map> resultList = new ArrayList<>();
        // 查询NFT头像信息
        Account account = accountMapper.queryByHeadportraitNftId(tokenId);
        if (account != null) {
            // 账户nft头像解绑
            accountMapper.headportraitNftIdUnBind(tokenId);
        }
        Contract authContract = contractMapper.queryByChainIDAndType(ylChainId, CONTRACT_TYPE_ERC721,
            STATUS_ENABLE, TO_USE_REGISTER_DOMAIN_NFT);
        Assert.notNull(authContract, "合约不存在");
        String contractAddress = authContract.getContractAddress();
        Nft nft = nftMapper.queryByTokenId(contractAddress, Integer.parseInt(tokenId.toString()), null, null);
        Assert.notNull(nft, "nft不存在");
        Assert.equals(chainAccountAddress.toLowerCase(), nft.getHolder().toLowerCase(), "链账户地址不匹配");
        String nftImageUrl = nft.getNftImage();
        // 查询NFT封面信息
        List<FixDomain> fixDomains = fixDomainMapper.queryByCoverUrl(nftImageUrl);
        if (CollectionUtil.isNotEmpty(fixDomains)) {
            CoverInfo defaultCover = coverInfoMapper.getDefaultCover();
            Assert.notNull(defaultCover, "默认封面不存在");
            String defaultCoverImage = defaultCover.getImage();
            for (FixDomain fixDomain : fixDomains) {
                fixDomain.setCoverUrl(defaultCoverImage);
                fixDomain.setIsNftCover(0);
                fixDomain.setUpdateTime(new Date());
                fixDomainMapper.updateById(fixDomain);
            }
        }
        return R.okData(resultList);
    }

    /**
     * 保存交易NFT记录 同步灵戒AppNFT 转移信息
     * 
     * @param opbChainId
     * @param transactionHash
     * @return
     */
    @LogRecord(methodName = "保存交易NFT记录")
    @Override
    @Transactional
    public R saveTransferRecord(Integer opbChainId, String transactionHash) {
        String uuid = StpUtil.getLoginIdAsString();
        if (opbChainId.equals(ylChainId)) {
            try {
                TransactionReceipt transactionReceipt =
                    ylBlockChainServer.getTransactionReceipt(transactionHash);
                if (!transactionReceipt.isStatusOK()) {
                    throw new ServiceException("交易未成功");
                }
                BigInteger gasUsed = transactionReceipt.getGasUsed();
                List<Log> logs = transactionReceipt.getLogs();
                Log log = logs.get(0);
                List<String> topics = log.getTopics();
                String fromAddress =
                    "0x" + String.format("%040x", new BigInteger(topics.get(1).substring(2), 16));
                String toAddress =
                    "0x" + String.format("%040x", new BigInteger(topics.get(2).substring(2), 16));
                Integer tokenId = HexUtil.hexToInt(topics.get(3).substring(2).replaceAll("^(0+)", ""));
                BigInteger blockNumber = transactionReceipt.getBlockNumber();
                String contractAddress = transactionReceipt.getTo();
                // 保存到数据库
                EthTransaction ethTransaction = BNBUtil.ethGetTransactionByHash(transactionHash);
                Optional<Transaction> transaction = ethTransaction.getTransaction();
                Transaction transactionInfo = transaction.get();
                BigInteger gasPrice = transactionInfo.getGasPrice();
                BigInteger value = transactionInfo.getValue();
                BigInteger energyUsed = gasUsed.multiply(gasPrice).add(value);
                // gas流水表新增数据
                EnergyRechargeFlow ylEnergyRechargeFlow = EnergyRechargeFlow.builder().accountUuid(uuid)
                    .hash(transactionHash).gas(energyUsed.toString())
                    .reqTransactionSn("YM" + System.currentTimeMillis()).type(2).rechargeState(10).build();
                ylEnergyRechargeFlowMapper.insert(ylEnergyRechargeFlow);
                // nft表修改数据
                LambdaUpdateWrapper<Nft> updateWrapper1 = new LambdaUpdateWrapper<>();
                updateWrapper1.set(Nft::getHolder, toAddress).eq(Nft::getContractAddress, contractAddress)
                    .eq(Nft::getTokenId, tokenId);
                nftMapper.update(null, updateWrapper1);
                // nft交易记录表新增数据
                // 根据合约地址查询合约信息
                LambdaQueryWrapper<Contract> queryWrapper1 = new LambdaQueryWrapper<>();
                queryWrapper1.eq(Contract::getContractAddress, contractAddress);
                Contract contract = contractMapper.selectOne(queryWrapper1);
                Long contractId = contract.getId();
                NftTransferRecord nftTransferRecord =
                    NftTransferRecord.builder().opbChainId(opbChainId).contractId(contractId).tokenId(tokenId)
                        .contractAddress(contractAddress).fromAddress(fromAddress).toAddress(toAddress)
                        .energyValue(energyUsed.toString()).transactionHash(transactionHash)
                        .blockNumber(blockNumber.intValue()).transactionType(2).status(1).build();
                nftTransferRecordMapper.insert(nftTransferRecord);
            } catch (Exception e) {
                e.printStackTrace();
                throw new ServiceException("获取交易回执失败");
            }
            return R.ok();
        }
        return R.error("该链暂未开通，敬请期待");
    }

    @Override
    @Transactional
    public R saveBurnRecord(Integer opbChainId, String transactionHash) {
        String uuid = StpUtil.getLoginIdAsString();
        if (opbChainId.equals(ylChainId)) {
            try {
                TransactionReceipt transactionReceipt =
                    ylBlockChainServer.getTransactionReceipt(transactionHash);
                if (!transactionReceipt.isStatusOK()) {
                    throw new ServiceException("交易未成功");
                }
                BigInteger gasUsed = transactionReceipt.getGasUsed();
                List<Log> logs = transactionReceipt.getLogs();
                Log log = logs.get(0);
                List<String> topics = log.getTopics();
                String fromAddress = "0x" + topics.get(1).substring(2).replaceAll("^(0+)", "");
                String toAddress = "******************************************";
                Integer tokenId = HexUtil.hexToInt(topics.get(3).substring(2).replaceAll("^(0+)", ""));
                BigInteger blockNumber = transactionReceipt.getBlockNumber();
                String contractAddress = transactionReceipt.getTo();
                // 保存到数据库
                EthGasPrice ethGasPrice = BNBUtil.ethGasPrice();
                BigInteger gasPrice = ethGasPrice.getGasPrice();
                BigInteger energyUsed = gasUsed.multiply(gasPrice);
                // gas流水表新增数据
                EnergyRechargeFlow ylEnergyRechargeFlow = EnergyRechargeFlow.builder().accountUuid(uuid)
                    .hash(transactionHash).gas(energyUsed.toString())
                    .reqTransactionSn("YM" + System.currentTimeMillis()).type(2).rechargeState(10).build();
                ylEnergyRechargeFlowMapper.insert(ylEnergyRechargeFlow);
                // nft表修改数据
                LambdaUpdateWrapper<Nft> updateWrapper1 = new LambdaUpdateWrapper<>();
                updateWrapper1.set(Nft::getHolder, toAddress).eq(Nft::getContractAddress, contractAddress)
                    .eq(Nft::getTokenId, tokenId);
                nftMapper.update(null, updateWrapper1);
                // nft交易记录表新增数据
                // 根据合约地址查询合约信息
                LambdaQueryWrapper<Contract> queryWrapper1 = new LambdaQueryWrapper<>();
                queryWrapper1.eq(Contract::getContractAddress, contractAddress);
                Contract contract = contractMapper.selectOne(queryWrapper1);
                Long contractId = contract.getId();
                NftTransferRecord nftTransferRecord =
                    NftTransferRecord.builder().opbChainId(opbChainId).contractId(contractId).tokenId(tokenId)
                        .contractAddress(contractAddress).fromAddress(fromAddress).toAddress(toAddress)
                        .energyValue(energyUsed.toString()).transactionHash(transactionHash)
                        .blockNumber(blockNumber.intValue()).transactionType(3).status(1).build();
                nftTransferRecordMapper.insert(nftTransferRecord);
            } catch (Exception e) {
                e.printStackTrace();
                throw new ServiceException("获取交易回执失败");
            }
            return R.ok();
        }
        return R.error("该链暂未开通，敬请期待");
    }

    /**
     * 获取链上资产信息
     * 
     * @param chainAccountAddress
     * @return
     */
    @Override
    public R getChainAccountAsset(String chainAccountAddress) {
        BigInteger balance = BNBUtil.getBalance(chainAccountAddress);
        BigDecimal ylAsset = new BigDecimal(balance).divide(BigDecimal.TEN.pow(18));
        // 修改为获取实时浮动比率
        BigDecimal rate = checkSpOverview.getBigDecimal();
        BigDecimal ylAssets = ylAsset.multiply(rate).setScale(6, RoundingMode.HALF_UP);
        LambdaQueryWrapper<Nft> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Nft::getHolder, chainAccountAddress).eq(Nft::getStatus, 1).eq(Nft::getIsDelete,
            false);
        List<Nft> nfts = nftMapper.selectList(queryWrapper);
        if (CollectionUtil.isEmpty(nfts)) {
            return R.okData(ylAssets.toPlainString());
        }
        BigDecimal reduce = nfts.stream().map(Nft::getNftPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal totalAssets = ylAssets.add(reduce);
        return R.okData(totalAssets.toPlainString());
    }

    /**
     * 同步NFT上链信息
     * 
     * @return
     */
    @Override
    public R syncNFTUpchainInfo() {
        // 通过地址和状态查询平台地址信息
        List resultList = new ArrayList();
        PlatformAccount platformAccount = platformAccountMapper.queryByTypeAndStatus(
            PlatformAccount.TYPE_DOMAIN_REGISTER_RESOLVE_ACCOUNT, PlatformAccount.STATUS_SHOW);
        Assert.notNull(platformAccount, "平台账户不存在");
        String platformAddress = platformAccount.getAddress();
        Contract authContract = contractMapper.queryByChainIDAndType(ylChainId, CONTRACT_TYPE_ERC721,
            STATUS_ENABLE, TO_USE_REGISTER_DOMAIN_NFT);
        Assert.notNull(authContract, "合约不存在");
        String contractAddress = authContract.getContractAddress();
        Integer opbChainId = authContract.getOpbChainId();
        String price = globalConfigMapper.queryConfig(CommonConstant.NFT_DEFAULT_PRICE);
        // 新增NFT领取记录，处理IPFS相关内容，返回json文档
        int maxTokenId = nftMapper.getMaxTokenId(contractAddress, Nft.NFT_STATUS_NORMAL);
        for (int i = 1; i <= maxTokenId; i++) {
            int tokenId = i;
            String nftOwner = BNBUtil.getNFTOwner(platformAddress, contractAddress, tokenId);
            Nft nft = nftMapper.queryByTokenId(contractAddress, tokenId, null, null);
            if (nft != null) {
                String mintHash = nft.getMintHash();
                if (StrUtil.isNotBlank(mintHash)) {
                    TransactionReceipt transactionReceipt = getTransactionReceipt(mintHash);
                    BigInteger gasUsed = transactionReceipt.getGasUsed();
                    BigInteger blockNumber = transactionReceipt.getBlockNumber();
                    // 判断一下transfer记录是会否存在
                    NftTransferRecord nftTransferRecord =
                        nftTransferRecordMapper.queryByHashAndTransactionType(opbChainId, mintHash, tokenId,
                            NftTransferRecord.TRANSACTION_TYPE_MINT, null);
                    if (nftTransferRecord == null) {
                        nftTransferRecord = NftTransferRecord.builder().opbChainId(opbChainId)
                            .contractId(nft.getContractId()).tokenId(tokenId).contractAddress(contractAddress)
                            .fromAddress("******************************************").toAddress(nftOwner)
                            .energyValue(gasUsed.toString()).transactionHash(mintHash)
                            .blockNumber(blockNumber.intValue()).transactionType(1).status(1).isDelete(true)
                            .build();
                        nftTransferRecordMapper.insert(nftTransferRecord);
                    }
                }
            }
        }
        return R.okData(resultList);
    }

    /**
     * 校验IPFS地址
     * 
     * @param jsonUrl
     * @return
     */
    private static JSONObject validateIPFSUrl(String jsonUrl) {
        JSONObject jsObject;
        try {
            URL url = new URL(jsonUrl);
            jsObject = JSON.parseObject(url);
        } catch (MalformedURLException e) {
            throw new ServiceException("ipfs访问失败");
        }
        return jsObject;
    }

    /**
     * nft交易记录表新增数据
     * 
     * @param opbChainId
     * @param contractId
     * @param tokenId
     * @param contractAddress
     * @param chainAccountAddress
     */
    private NftTransferRecord addNFTTransferRecordV2(Integer opbChainId, Long contractId, Integer tokenId,
        String contractAddress, String chainAccountAddress, BigInteger gasUsed, String transactionHash,
        BigInteger blockNumber) {
        NftTransferRecord nftTransferRecord = NftTransferRecord.builder().opbChainId(opbChainId)
            .contractId(contractId).tokenId(tokenId).contractAddress(contractAddress)
            .fromAddress("******************************************").toAddress(chainAccountAddress)
            // .energyValue(gasUsed.toString())
            // .transactionHash(transactionHash)
            // .blockNumber(blockNumber.intValue())
            .transactionType(1).status(1).energyValue(gasUsed.toString()).transactionHash(transactionHash)
            .blockNumber(blockNumber.intValue()).isDelete(true).build();
        nftTransferRecordMapper.insert(nftTransferRecord);
        return nftTransferRecord;
    }

    /**
     * nft交易记录表新增数据
     * 
     * @param opbChainId
     * @param contractId
     * @param tokenId
     * @param contractAddress
     * @param chainAccountAddress
     * @param nftPrice
     * @return
     */
    private Nft addAuthNftNftRecordV2(Integer opbChainId, Long contractId, Integer tokenId,
        String contractAddress, String chainAccountAddress, BigDecimal nftPrice, String transactionHash) {
        // nft表新增数据
        Nft nft = Nft.builder().opbChainId(opbChainId).contractId(contractId).tokenId(tokenId)
            .contractAddress(contractAddress).creator(chainAccountAddress).holder(chainAccountAddress)
            // .mintHash(transactionHash)
            .mintDate(new Date()).nftType(Nft.NFT_TYPE_ERC721).nftPrice(nftPrice)
            .status(Nft.NFT_STATUS_MINT_IPFS).mintHash(transactionHash).isDelete(true).build();
        nftMapper.insert(nft);
        return nft;
    }

    /**
     * 修改NFT 记录 NFT信息
     * 
     * @param authNftNftRecordList
     * @param nftName
     * @param description
     * @param nftUrl
     */
    private void updateAuthNftNftRecord(List<Nft> authNftNftRecordList, String contractAddress,
        Integer tokenId, String nftName, String description, String nftUrl, String domain) {
        for (Nft nft : authNftNftRecordList) {
            String nftContractAddress = nft.getContractAddress();
            Integer nftTokenId = nft.getTokenId();
            if (Objects.equals(contractAddress, nftContractAddress) && Objects.equals(tokenId, nftTokenId)) {
                // 修改NFT 记录 NFT信息
                nft.setNftName(nftName);
                nft.setNftDescribe(description);
                nft.setNftImage(nftUrl);
                nft.setNftDomain(domain);
                nft.setUpdateTime(new Date());
                nftMapper.updateById(nft);
                break;
            }
        }
    }

    /**
     * 修改NFT 记录 NFT信息
     * 
     * @param authNftNftRecord
     * @param nftName
     * @param description
     * @param nftUrl
     */
    private void updateAuthNftNftRecordV2(Nft authNftNftRecord, NftTransferRecord nftTransferRecord,
        String contractAddress, Integer tokenId, String nftName, String description, String nftUrl,
        String domain) {
        String nftContractAddress = authNftNftRecord.getContractAddress();
        Integer nftTokenId = authNftNftRecord.getTokenId();
        Date nowDate = new Date();
        if (Objects.equals(contractAddress, nftContractAddress) && Objects.equals(tokenId, nftTokenId)) {
            // 修改NFT 记录 NFT信息
            authNftNftRecord.setNftName(nftName);
            authNftNftRecord.setNftDescribe(description);
            authNftNftRecord.setNftImage(nftUrl);
            authNftNftRecord.setNftDomain(domain);
            authNftNftRecord.setStatus(Nft.NFT_STATUS_NORMAL);
            authNftNftRecord.setIsDelete(false);
            authNftNftRecord.setUpdateTime(nowDate);
            nftMapper.updateById(authNftNftRecord);
        }
        nftTransferRecord.setUpdateTime(nowDate);
        nftTransferRecord.setIsDelete(false);
        nftTransferRecordMapper.updateById(nftTransferRecord);

    }

    /**
     * 批量铸币
     * 
     * @param chainAccountAddress
     * @param contractAddress
     * @param platformAccountPrivateKey
     * @param tokenIds
     */
    public TransactionReceipt multipMint(String chainAccountAddress, String contractAddress,
        String platformAccountPrivateKey, List<Uint256> tokenIds) {
        TransactionReceipt transactionReceipt = null;
        try {
            String functionName = "batchMint";
            String platformAccountAddress = BNBUtil.getAddressByPrivateKey(platformAccountPrivateKey);
            Integer tokenNum = getCurrentNum(platformAccountAddress, contractAddress);
            // 获取要铸币的tokenIds的第一个元素 int 类型
            int num = tokenIds.get(0).getValue().intValue();
            Assert.equals(tokenNum + 1, num, "铸币失败，token高度不匹配");
            List<Type> inputParameters = new ArrayList<>();
            List<TypeReference<?>> outputParameters = new ArrayList<>();
            inputParameters.add(new Address(chainAccountAddress));
            inputParameters.add(new DynamicArray(Uint256.class, tokenIds));
            Function function = new Function(functionName, inputParameters, outputParameters);
            String transactionHash =
                BNBUtil.sendTransaction(contractAddress, platformAccountPrivateKey, function, GAS_LIMIT);

            // 判断交易状态
            if (StrUtil.isNotEmpty(transactionHash)) {
                // 判断交易状态
                transactionReceipt = getTransactionReceipt(transactionHash);
                if (!transactionReceipt.isStatusOK()) {
                    throw new ServiceException("hash: " + transactionHash + "铸币失败");
                } else {
                    log.warn("铸币成功 contractAddress:{},chainAccountAddress:{},tokenIds:{}", contractAddress,
                        chainAccountAddress, JSON.toJSONString(tokenIds));
                }
            } else {
                throw new ServiceException("contractAddress " + contractAddress + " chainAccountAddress "
                    + chainAccountAddress + " 铸币失败");
            }

        } catch (Exception ex) {
            throw new RuntimeException(ex);
        }
        return transactionReceipt;
    }

    /**
     * 单个铸币
     * 
     * @param chainAccountAddress
     * @param contractAddress
     * @param platformAccountPrivateKey
     * @param tokenId
     */
    public TransactionReceipt SingleMint(String chainAccountAddress, String contractAddress,
        String platformAccountPrivateKey, Integer tokenId) {
        TransactionReceipt transactionReceipt = null;
        try {
            String functionName = "mint";
            String platformAccountAddress = BNBUtil.getAddressByPrivateKey(platformAccountPrivateKey);
            Integer tokenNum = getCurrentNum(platformAccountAddress, contractAddress);
            // 获取要铸币的tokenIds的第一个元素 int 类型
            Assert.equals(tokenNum + 1, tokenId, "铸币失败，token高度不匹配");
            List<Type> inputParameters = new ArrayList<>();
            List<TypeReference<?>> outputParameters = new ArrayList<>();
            inputParameters.add(new Address(chainAccountAddress));
            inputParameters.add(new Uint256(BigInteger.valueOf(tokenId)));
            Function function = new Function(functionName, inputParameters, outputParameters);
            String transactionHash =
                BNBUtil.sendTransaction(contractAddress, platformAccountPrivateKey, function, GAS_LIMIT);

            // 判断交易状态
            if (StrUtil.isNotEmpty(transactionHash)) {
                // 判断交易状态
                transactionReceipt = getTransactionReceipt(transactionHash);
                if (!transactionReceipt.isStatusOK()) {
                    throw new ServiceException("hash: " + transactionHash + "铸币失败");
                } else {
                    log.warn("单个铸币成功 contractAddress:{},chainAccountAddress:{},tokenIds:{}", contractAddress,
                        chainAccountAddress, tokenId);
                }
            } else {
                throw new ServiceException("contractAddress " + contractAddress + " chainAccountAddress "
                    + chainAccountAddress + " 单个铸币失败");
            }

        } catch (Exception ex) {
            throw new RuntimeException(ex);
        }
        return transactionReceipt;
    }

    /**
     * NFT 补偿发送 从NFT补偿领取记录
     * 
     * @param nftManualDisposeId
     * @param platformAccountAddress
     * @return
     */
    @Transactional
    @Override
    public R compensationFromNFTGetRecord(Long nftManualDisposeId, String platformAccountAddress) {
        try {
            NftManualDispose nftManualDispose = nftManualDisposeMapper.selectById(nftManualDisposeId);
            Assert.notNull(nftManualDispose, "NFT补偿记录不存在");
            // 处理状态 0-未处理 1-处理中 2-已处理
            Integer status = nftManualDispose.getStatus();
            Assert.notEquals(status, NftManualDispose.STATUS_PROCESSED, "NFT补偿记录已处理，或处理中");
            String contractAddress = nftManualDispose.getContractAddress();

            // NFT领取记录id
            Long nftGetRecordId = nftManualDispose.getNftRecordId();
            // 判断NFT领取记录
            NftGetRecord nftGetRecord = nftGetRecordMapper.selectById(nftGetRecordId);
            Assert.notNull(nftGetRecord, "NFT领取记录不存在");
            // 1注册 2领取
            Integer nftGetRecordStatus = nftGetRecord.getStatus();
            Assert.equals(nftGetRecordStatus, NftGetRecord.STATUS_RECEIVE, "NFT领取记录状态异常");

            // 判断NFT领取记录是否已经发送成功
            Integer isSuccess = nftGetRecord.getIsSuccess();
            Assert.notEquals(isSuccess, NftGetRecord.ISSUCCESS_SUCCESS, "NFT已发送成功");

            // NFT领取记录用户地址
            String chainAccountAddress = nftGetRecord.getGetAccountAddress();
            // 用户uuid
            String accountUuid = nftGetRecord.getGetAccountUuid();

            // 通过地址和状态查询平台地址信息
            PlatformAccount platformAccount = platformAccountMapper.queryByAddressAndStatus(
                platformAccountAddress, PlatformAccount.TYPE_DOMAIN_REGISTER_RESOLVE_ACCOUNT, STATUS_SHOW);
            Assert.notNull(platformAccount, "平台账户不存在");

            Integer nftGetRecordGetCount = nftGetRecord.getGetCount();
            // 判断是否有NFTgetRecordDomian数据
            List<NftGetRecordDomain> nftGetRecordDomains =
                nftGetRecordDomainMapper.queryByRecordIdAndStatus(nftGetRecordId, null);
            if (CollectionUtil.isEmpty(nftGetRecordDomains)) {
                nftGetRecordDomains = new ArrayList<>();
                // 插入对应的nftGetRecordDomain表
                for (Integer i = 1; i <= nftGetRecordGetCount; i++) {
                    NftGetRecordDomain nftGetRecordDomain = new NftGetRecordDomain();
                    nftGetRecordDomain.setNftGetRecordId(nftGetRecordId);
                    nftGetRecordDomain.setSort(i);
                    nftGetRecordDomain.setStatus(NftGetRecordDomain.STATUS_UNRECEIVED);
                    nftGetRecordDomainMapper.insert(nftGetRecordDomain);
                    nftGetRecordDomains.add(nftGetRecordDomain);
                }
            }

            for (NftGetRecordDomain nftGetRecordDomain : nftGetRecordDomains) {
                Long nftGetRecordDomainId = nftGetRecordDomain.getId();
                Integer nftGetRecordDomainStatus = nftGetRecordDomain.getStatus();
                // 判断状态是否为未领取
                if (Objects.equals(NftGetRecordDomain.STATUS_UNRECEIVED, nftGetRecordDomainStatus)) {
                    // 构建上链信息
                    UpChainDataVoV3 upChainDataVoV2 = UpChainDataVoV3.builder().opbChainId(ylChainId)
                        .platformAccountAddress(platformAccountAddress).contractAddress(contractAddress)
                        .nftGetRecordId(nftGetRecordId).nftManualDisposeId(nftManualDisposeId)
                        .chainAccountAddress(chainAccountAddress).accountUUID(accountUuid)
                        .nftRecordDomainId(nftGetRecordDomainId).build();
                    String str = JSON.toJSONString(upChainDataVoV2);
                    // 放入rabbitmq
                    rabbitTemplate.convertAndSend(RabbitConfig.COMPENSATION_MINT_UP_CHAIN_QUEUE, (Object)str,
                        message -> {
                            String randomUuid = UUIdUtil.createUUId(15);
                            message.getMessageProperties().setMessageId(randomUuid);
                            return message;
                        });
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException("NFT补偿发送失败");
        }
        return R.ok();
    }

    /**
     * 获取token 高度
     * 
     * @param ylPlatformAccountAddress 平台账户地址
     * @param contractAddress NFT合约地址
     * @return
     * @throws Exception
     */
    public int getCurrentNum(String ylPlatformAccountAddress, String contractAddress) {
        int currentNum = 0;
        try {
            String functionName = "currentNum";
            List<Type> inputParameters = new ArrayList<>();
            List<TypeReference<?>> outputParameters = new ArrayList<>();
            outputParameters.add(new TypeReference<Uint256>() {});
            Function function = new Function(functionName, inputParameters, outputParameters);
            List<Type> types =
                BNBUtil.sendCallTransaction(ylPlatformAccountAddress, contractAddress, function);
            BigInteger tem = (BigInteger)types.get(0).getValue();
            currentNum = tem.intValue();
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException("获取token高度失败");
        }
        return currentNum;
    }

    /**
     * 获取授权地址
     * 
     * @param ylPlatformAccountAddress 平台账户地址
     * @param contractAddress NFT合约地址
     * @return
     * @throws Exception
     */
    public String getApproved(String ylPlatformAccountAddress, String contractAddress, Integer tokenId) {
        String address = null;
        try {
            String functionName = "getApproved";
            List<Type> inputParameters = new ArrayList<>();
            inputParameters.add(new Uint256(BigInteger.valueOf(tokenId)));
            List<TypeReference<?>> outputParameters = new ArrayList<>();
            outputParameters.add(new TypeReference<Uint256>() {});
            Function function = new Function(functionName, inputParameters, outputParameters);
            List<Type> types =
                BNBUtil.sendCallTransaction(ylPlatformAccountAddress, contractAddress, function);
            BigInteger tem = (BigInteger)types.get(0).getValue();
            address = "0x" + tem.toString(16);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException("获取授权地址失败");
        }
        return address;
    }

    /**
     * 交易hash查询交易回执
     * 
     * @param transactionHash
     * @return
     */
    public TransactionReceipt getTransactionReceipt(String transactionHash) {
        try {
            EthGetTransactionReceipt ethGetTransactionReceipt =
                BNBUtil.ethGetTransactionReceipt(transactionHash);
            Optional<TransactionReceipt> optional = ethGetTransactionReceipt.getTransactionReceipt();
            int count = 0;
            if (!optional.isPresent()) {
                while (count < 20) {
                    count++;
                    // 等待1秒
                    log.info("等待" + count + "秒");
                    TimeUnit.SECONDS.sleep(1);
                    ethGetTransactionReceipt = BNBUtil.ethGetTransactionReceipt(transactionHash);
                    optional = ethGetTransactionReceipt.getTransactionReceipt();
                    if (optional.isPresent()) {
                        return optional.get();
                    }
                }
                throw new RuntimeException(transactionHash + " 你太慢了");
            }
            return optional.get();
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException("获取交易回执失败");
        }
    }

    /**
     * 批量铸币
     * 
     * @param contractAddress 合约地址
     * @param chainAccountAddress 链上账户地址
     * @param tokenIds tokenIds
     * @param ylPlatformAccountPrivateKey 平台账户私钥
     * @return
     */
    @Override
    public String batchMint(String contractAddress, String chainAccountAddress, List<Uint256> tokenIds,
        String ylPlatformAccountPrivateKey) {
        String transactionHash = null;
        try {
            String functionName = "batchMint";
            List<Type> inputParameters = new ArrayList<>();
            List<TypeReference<?>> outputParameters = new ArrayList<>();
            inputParameters.add(new Address(chainAccountAddress));
            inputParameters.add(new DynamicArray(Uint256.class, tokenIds));
            Function function = new Function(functionName, inputParameters, outputParameters);
            // 数据上链
            transactionHash =
                BNBUtil.sendTransaction(contractAddress, ylPlatformAccountPrivateKey, function, GAS_LIMIT);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException("铸币失败");
        }
        return transactionHash;
    }

    /**
     * 查询IPFS 属性信息
     * 
     * @param tokenId
     * @param contractAddress
     * @param pathCid
     * @return
     */
    public String queryIPFSTAttribute(String contractAddress, Integer tokenId, String pathCid) {
        IpfsImage ipfsImage = ipfsImageMapper.selectOne(new LambdaQueryWrapper<IpfsImage>()
            .eq(IpfsImage::getTokenId, tokenId).eq(IpfsImage::getContractAddress, contractAddress));
        if (null == ipfsImage) {
            throw new ServiceException("该Token不存在");
        }
        IpfsImageVo ipfsImageVo =
            new IpfsImageVo(ipfsImage.getCid(), ipfsImage.getDomain(), ipfsImage.getTokenId());
        Map<String, Object> returnMap = new HashMap<>(2);
        returnMap.put("imageVos", ipfsImageVo);
        returnMap.put("pathCid", pathCid);
        String returnStr = JSONUtil.toJsonStr(returnMap);
        return returnStr;
    }

    /**
     * 更新baseURi
     * 
     * @param baseUrl
     * @param contractAddress
     * @param privateKey
     */
    public void setBaseUrl(String baseUrl, String contractAddress, String privateKey) {
        // 更新baseURI
        String functionName2 = "setBaseURI";
        List<Type> inputParameters = new ArrayList<>();
        inputParameters.add(new Utf8String(baseUrl));
        List<TypeReference<?>> outputParameters2 = new ArrayList<>();
        Function function = new Function(functionName2, inputParameters, outputParameters2);
        try {
            String transactionHash2 =
                BNBUtil.sendTransaction(contractAddress, privateKey, function, GAS_LIMIT);
            TransactionReceipt transactionReceipt2 = getTransactionReceipt(transactionHash2);
            if (!transactionReceipt2.isStatusOK()) {
                throw new ServiceException("更新baseURI失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException("更新baseURI遇到网络错误");
        }
    }

    /**
     * NFT批量上链处理 旧
     * 
     * @param content
     * @param message
     * @param channel
     * @throws IOException
     */
    @Transactional
    public void upChainHandleV2(String content, Message message, Channel channel) throws IOException {
        channel.basicQos(1);
        MessageProperties messageProperties = message.getMessageProperties();
        long deliveryTag = messageProperties.getDeliveryTag();
        String messageId = messageProperties.getMessageId();
        UpChainDataVo upChainDataVo = JSON.parseObject(content, UpChainDataVo.class);
        Long contractId = upChainDataVo.getContractId();
        String contractAddress = upChainDataVo.getContractAddress();
        String chainAccountAddress = upChainDataVo.getChainAccountAddress();
        PlatformAccount ylPlatformAccount = upChainDataVo.getYlPlatformAccount();
        String platformAccountPrivateKey = ylPlatformAccount.getPrivateKey();
        Integer nftCount = upChainDataVo.getNftCount();
        String uuid = upChainDataVo.getUuid();
        Long nftRecordId = upChainDataVo.getNftRecordId();
        Integer opbChainId = upChainDataVo.getOpbChainId();
        log.info(
            "执行领NFT操作,铸币上链数据：nftRecordId：{}，nftCount：{}，uuid：{}，contractAddress：{}, chainAccountAddress:{}",
            nftRecordId, nftCount, uuid, contractAddress, chainAccountAddress);
        // 加锁
        RLock lock = redissonClient.getLock(LOCK_SYMBOL);
        lock.lock();
        try {
            // 获取token高度
            int currentNum = getCurrentNum(ylPlatformAccount.getAddress(), contractAddress);
            // ipfs表的最大tokenid
            int maxTokenId = ipfsImageMapper.getMaxTokenId(contractAddress, IpfsImage.STATE_USED);
            if (currentNum <= maxTokenId) {
                currentNum = maxTokenId;
            }
            List<Uint256> tokenIds = new ArrayList<>();
            // 计算要铸币的tokenIds
            for (int i = 0; i < nftCount; i++) {
                currentNum++;
                tokenIds.add(new Uint256(BigInteger.valueOf(currentNum)));
            }
            // tokenIds 转成int list
            List<Integer> intTokenids =
                tokenIds.stream().map(x -> x.getValue().intValue()).collect(Collectors.toList());
            // 查询NFT默认价格
            String price = globalConfigMapper.queryConfig(CommonConstant.NFT_DEFAULT_PRICE);
            // 新增NFT领取记录，处理IPFS相关内容，返回json文档
            BigDecimal nftPrice = new BigDecimal(price);
            List<Integer> collect =
                tokenIds.stream().map(x -> x.getValue().intValue()).collect(Collectors.toList());

            // ipfs信息校验
            nftGetRecordService.validateMintInfo(uuid, nftCount, contractAddress, intTokenids);

            // 铸币
            TransactionReceipt transactionReceipt =
                multipMint(chainAccountAddress, contractAddress, platformAccountPrivateKey, tokenIds);
            String transactionHash = transactionReceipt.getTransactionHash();
            BigInteger gasUsed = transactionReceipt.getGasUsed();
            BigInteger blockNumber = transactionReceipt.getBlockNumber();
            List<Nft> authNftNftRecordList = new ArrayList<>();
            // 异步 新增上链记录
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                for (Integer intTokenid : intTokenids) {
                    // nft表新增数据
                    Nft authNftNftRecord = addAuthNftNftRecordV2(opbChainId, contractId, intTokenid,
                        contractAddress, chainAccountAddress, nftPrice, transactionHash);
                    authNftNftRecordList.add(authNftNftRecord);
                    // nft交易记录表新增数据
                    addNFTTransferRecordV2(opbChainId, contractId, intTokenid, contractAddress,
                        chainAccountAddress, gasUsed, transactionHash, blockNumber);
                }
            });

            // 上IPFS
            // 插入nft领取记录，并获得ipfs信息
            String jsonStr = nftGetRecordService.insertNftRecord(uuid, chainAccountAddress, opbChainId,
                nftCount, 1, collect, contractAddress, nftRecordId);
            JSONObject jsonObject = JSON.parseObject(jsonStr);
            String pathCid = jsonObject.getString("pathCid");
            String baseUri = ipfsUrl + pathCid + "/";
            // 新增nft数据
            JSONArray imageVos = jsonObject.getJSONArray("imageVos");
            for (int i = 0; i < imageVos.size(); i++) {
                JSONObject imageVo = imageVos.getJSONObject(i);
                String cid = imageVo.getString("cid");
                Integer tokenId = imageVo.getInteger("tokenId");
                String domain = imageVo.getString("domain");
                String nftUrl = imagePrefix + cid;
                // 获取json数据
                String jsonUrl = baseUri + tokenId;
                JSONObject jsObject = validateIPFSUrl(jsonUrl);

                String nftName = jsObject.getString("name");
                String description = jsObject.getString("description");
                // 同步更新NFT表IPFS信息
                updateAuthNftNftRecord(authNftNftRecordList, contractAddress, tokenId, nftName, description,
                    nftUrl, domain);
            }

            // 更新 合约 baseURI
            setBaseUrl(baseUri, contractAddress, platformAccountPrivateKey);
            channel.basicAck(deliveryTag, false);
        } catch (Exception e) {
            e.printStackTrace();
            redisTemplate.opsForValue().increment(MESSAGE + messageId);
            // 异步添加到补偿记录
            addToManualDispostInfo(channel, messageId, deliveryTag, nftRecordId, chainAccountAddress,
                contractAddress, nftCount, opbChainId, uuid);

            throw new ServiceException("自动批量创建NFT失败");
        } finally {
            log.info("解锁，记录id：{}，账户地址：{}，领取数量：{}", nftRecordId, chainAccountAddress, nftCount);
            lock.unlock();
        }
    }

    /**
     * NFT 单个上链处理
     * 
     * @param content
     * @param message
     * @param channel
     * @throws IOException
     */
    @Transactional
    public void singleUpChainHandle(String content, Message message, Channel channel) throws IOException {
        channel.basicQos(1);
        MessageProperties messageProperties = message.getMessageProperties();
        long deliveryTag = messageProperties.getDeliveryTag();
        String messageId = messageProperties.getMessageId();
        UpChainDataVoV2 upChainDataVo = JSON.parseObject(content, UpChainDataVoV2.class);
        String contractAddress = upChainDataVo.getContractAddress();
        String platformAccountAddress = upChainDataVo.getPlatformAccountAddress();
        Long nftRecorddomainId = upChainDataVo.getNftRecordDomainId();
        Integer opbChainId = upChainDataVo.getOpbChainId();
        Long nftGetRecordId = upChainDataVo.getNftGetRecordId();
        String chainAccountAddress = upChainDataVo.getChainAccountAddress();
        String accountUUID = upChainDataVo.getAccountUUID();
        Integer tokenIdV2 = null;
        log.warn("执行单个领NFT操作,铸币上链数据：，nftRecorddomainId:{},contractAddress：{}, platformAccountAddress:{}",
            nftRecorddomainId, contractAddress, platformAccountAddress);
        // 加锁
        RLock lock = redissonClient.getLock(LOCK_SYMBOL);
        lock.lock();
        try {
            PlatformAccount platformAccount = platformAccountMapper.queryByAddressAndStatus(
                platformAccountAddress, PlatformAccount.TYPE_DOMAIN_REGISTER_RESOLVE_ACCOUNT, STATUS_SHOW);
            Assert.notNull(platformAccount, "平台账户不存在");
            String platformAccountPrivateKey = platformAccount.getPrivateKey();

            NftGetRecordDomain nftGetRecordDomain = nftGetRecordDomainMapper.selectById(nftRecorddomainId);
            Assert.notNull(nftGetRecordDomain, "NFT领取记录不存在");
            Assert.equals(nftGetRecordDomain.getStatus(), NftGetRecordDomain.STATUS_UNRECEIVED,
                "NFT领取记录已经成功");
            Long currentNftGetRecordId = nftGetRecordDomain.getNftGetRecordId();
            Assert.equals(currentNftGetRecordId, nftGetRecordId, "NFT领取记录不匹配");
            Contract contract = contractMapper.queryByChainIDContractAddress(opbChainId, contractAddress,
                CONTRACT_TYPE_ERC721, STATUS_ENABLE, TO_USE_REGISTER_DOMAIN_NFT);
            Assert.notNull(contract, "合约不存在");
            Long contractId = contract.getId();

            // 查询NFT默认价格
            String price = globalConfigMapper.queryConfig(CommonConstant.NFT_DEFAULT_PRICE);
            // 新增NFT领取记录，处理IPFS相关内容，返回json文档
            BigDecimal nftPrice = new BigDecimal(price);

            NftGetRecord nftGetRecord = nftGetRecordMapper.selectById(nftGetRecordId);
            Assert.notNull(nftGetRecord, "NFT领取记录不存在");
            Integer status = nftGetRecord.getStatus();
            Assert.equals(status, NftGetRecord.STATUS_RECEIVE, "NFT领取记录类型异常");
            Integer isSuccess = nftGetRecord.getIsSuccess();
            Assert.notEquals(isSuccess, NftGetRecord.ISSUCCESS_SUCCESS, "NFT领取记录已经成功");

            String currentAccountUuid = nftGetRecord.getGetAccountUuid();
            Assert.equals(currentAccountUuid, accountUUID, "领取账户信息不匹配");
            String currentChainAccountAddress = nftGetRecord.getGetAccountAddress();
            Assert.equals(currentChainAccountAddress, chainAccountAddress, "领取账户信息不匹配");

            // 校验IPFSCID路径
            String jsonPathCid = IpfsUtil.getJsonPathCid(contractAddress);
            Assert.notBlank(jsonPathCid, "IPFS路径CID不存在");

            Integer tokenId = null;
            // 查询是否有铸币成功但是后续失败的数据
            Nft authNftNftRecordInfo = nftMapper.queryOneByContractAndHolder(contractAddress,
                chainAccountAddress, Nft.NFT_STATUS_MINT_IPFS, true);
            NftTransferRecord nftTransferRecord = null;
            if (authNftNftRecordInfo == null) {
                // 单个铸币
                int currentNum = getCurrentNum(platformAccountAddress, contractAddress);
                tokenId = currentNum + 1;

                Integer mintTokenId = tokenId;
                TransactionReceipt transactionReceipt =
                    SingleMint(chainAccountAddress, contractAddress, platformAccountPrivateKey, tokenId);
                String transactionHash = transactionReceipt.getTransactionHash();
                BigInteger gasUsed = transactionReceipt.getGasUsed();
                BigInteger blockNumber = transactionReceipt.getBlockNumber();
                // 异步 新增上链记录
                CompletableFuture<Nft> future = CompletableFuture.supplyAsync(() -> {
                    // nft表新增数据
                    Nft authNftNftRecordResult = addAuthNftNftRecordV2(opbChainId, contractId, mintTokenId,
                        contractAddress, chainAccountAddress, nftPrice, transactionHash);
                    return authNftNftRecordResult;
                });
                authNftNftRecordInfo = future.join();

                CompletableFuture<NftTransferRecord> future2 = CompletableFuture.supplyAsync(() -> {
                    // nft交易记录表新增数据
                    NftTransferRecord nftTransferRecordResult =
                        addNFTTransferRecordV2(opbChainId, contractId, mintTokenId, contractAddress,
                            chainAccountAddress, gasUsed, transactionHash, blockNumber);
                    return nftTransferRecordResult;
                });

                nftTransferRecord = future2.join();

            } else {
                tokenId = authNftNftRecordInfo.getTokenId();
                nftTransferRecord = nftTransferRecordMapper.queryByHashAndTransactionType(opbChainId,
                    authNftNftRecordInfo.getMintHash(), tokenId, NftTransferRecord.TRANSACTION_TYPE_MINT,
                    true);
            }
            Assert.notNull(nftTransferRecord, "交易记录不存在");
            tokenIdV2 = tokenId;
            // ipfs信息校验
            nftGetRecordService.validateMintInfoV2(accountUUID, contractAddress, tokenId);
            // 上IPFS
            // 插入nft领取记录，并获得ipfs信息
            InsertIPFSResp insertIPFSResp =
                nftGetRecordService.insertNftRecordV2(nftGetRecordId, nftGetRecordDomain, accountUUID,
                    chainAccountAddress, opbChainId, 1, tokenId, contractAddress);
            String pathCid = insertIPFSResp.getPathCid();
            String baseUri = ipfsUrl + pathCid + "/";
            // 新增nft数据
            String cid = insertIPFSResp.getCid();
            String domain = insertIPFSResp.getDomain();
            String nftUrl = imagePrefix + cid;
            // 获取json数据
            String jsonUrl = baseUri + tokenId;
            JSONObject jsObject = validateIPFSUrl(jsonUrl);

            String nftName = jsObject.getString("name");
            String description = jsObject.getString("description");
            // 同步更新NFT表IPFS信息
            updateAuthNftNftRecordV2(authNftNftRecordInfo, nftTransferRecord, contractAddress, tokenId,
                nftName, description, nftUrl, domain);

            // 更新 合约 baseURI
            setBaseUrl(baseUri, contractAddress, platformAccountPrivateKey);

            // 异步刷新挂件
            CompletableFuture.runAsync(() -> {
                try {
                    JSONObject paramJson = new JSONObject();
                    paramJson.put("accountUuid", accountUUID);
                    paramJson.put("level", 10);  // NFT挂件等级为10
                    badgeService.refreshPendantService(null, paramJson);
                } catch (Exception e) {
                    log.error("刷新挂件失败:{}", e);
                }
            });

            channel.basicAck(deliveryTag, false);
        } catch (Exception e) {
            e.printStackTrace();
            // 异步添加到补偿记录
            addToManualDispostInfoV2(channel, messageId, deliveryTag, nftGetRecordId, nftRecorddomainId,
                chainAccountAddress, contractAddress, opbChainId, accountUUID);

            throw new ServiceException("自动批量创建NFT失败");
        } finally {
            log.info("解锁，记录id：{}，RecorddomainId:{},账户地址：{}，tokenId：{}", nftGetRecordId, nftRecorddomainId,
                chainAccountAddress, tokenIdV2);
            lock.unlock();
        }
    }

    /**
     * NFT 补偿处理
     * 
     * @param content
     * @param message
     * @param channel
     * @throws IOException
     */
    @Override
    public void compensationMintUpChainHandle(String content, Message message, Channel channel)
        throws IOException {
        channel.basicQos(1);
        MessageProperties messageProperties = message.getMessageProperties();
        long deliveryTag = messageProperties.getDeliveryTag();
        UpChainDataVoV3 upChainDataVo = JSON.parseObject(content, UpChainDataVoV3.class);
        String contractAddress = upChainDataVo.getContractAddress();
        String platformAccountAddress = upChainDataVo.getPlatformAccountAddress();
        Long nftRecorddomainId = upChainDataVo.getNftRecordDomainId();
        Integer opbChainId = upChainDataVo.getOpbChainId();
        Long nftGetRecordId = upChainDataVo.getNftGetRecordId();
        Long nftManualDisposeId = upChainDataVo.getNftManualDisposeId();
        String chainAccountAddress = upChainDataVo.getChainAccountAddress();
        String accountUUID = upChainDataVo.getAccountUUID();
        Integer tokenIdV2 = null;
        log.warn("执行补偿单个领NFT操作,铸币上链数据：，nftRecorddomainId:{},contractAddress：{}, platformAccountAddress:{}",
            nftRecorddomainId, contractAddress, platformAccountAddress);
        // 加锁
        RLock lock = redissonClient.getLock(LOCK_SYMBOL);
        lock.lock();
        try {
            NftManualDispose nftManualDispose = nftManualDisposeMapper.selectById(nftManualDisposeId);
            Assert.notNull(nftManualDispose, "NFT补偿记录不存在");
            // 处理状态 0-未处理 1-处理中 2-已处理
            Integer status = nftManualDispose.getStatus();
            Assert.notEquals(status, NftManualDispose.STATUS_PROCESSED, "NFT补偿记录已处理，或处理中");

            PlatformAccount platformAccount = platformAccountMapper.queryByAddressAndStatus(
                platformAccountAddress, PlatformAccount.TYPE_DOMAIN_REGISTER_RESOLVE_ACCOUNT, STATUS_SHOW);
            Assert.notNull(platformAccount, "平台账户不存在");
            String platformAccountPrivateKey = platformAccount.getPrivateKey();

            NftGetRecordDomain nftGetRecordDomain = nftGetRecordDomainMapper.selectById(nftRecorddomainId);
            Assert.notNull(nftGetRecordDomain, "NFT领取域名记录不存在");
            Assert.equals(nftGetRecordDomain.getStatus(), NftGetRecordDomain.STATUS_UNRECEIVED,
                "NFT领取记录已经成功");
            Long currentNftGetRecordId = nftGetRecordDomain.getNftGetRecordId();
            Assert.equals(currentNftGetRecordId, nftGetRecordId, "NFT领取记录不匹配");
            Contract contract = contractMapper.queryByChainIDContractAddress(opbChainId, contractAddress,
                CONTRACT_TYPE_ERC721, STATUS_ENABLE, TO_USE_REGISTER_DOMAIN_NFT);
            Assert.notNull(contract, "合约不存在");
            Long contractId = contract.getId();

            // 查询NFT默认价格
            String price = globalConfigMapper.queryConfig(CommonConstant.NFT_DEFAULT_PRICE);
            // 新增NFT领取记录，处理IPFS相关内容，返回json文档
            BigDecimal nftPrice = new BigDecimal(price);

            NftGetRecord nftGetRecord = nftGetRecordMapper.selectById(nftGetRecordId);
            Assert.notNull(nftGetRecord, "NFT领取记录不存在");
            Integer nftGetRecordStatus = nftGetRecord.getStatus();
            Assert.equals(nftGetRecordStatus, NftGetRecord.STATUS_RECEIVE, "NFT领取记录类型异常");
            Integer isSuccess = nftGetRecord.getIsSuccess();
            Assert.notEquals(isSuccess, NftGetRecord.ISSUCCESS_SUCCESS, "NFT领取记录已经成功");

            String currentAccountUuid = nftGetRecord.getGetAccountUuid();
            Assert.equals(currentAccountUuid, accountUUID, "领取账户信息不匹配");
            String currentChainAccountAddress = nftGetRecord.getGetAccountAddress();
            Assert.equals(currentChainAccountAddress, chainAccountAddress, "领取账户信息不匹配");

            // 校验IPFSCID路径
            String jsonPathCid = IpfsUtil.getJsonPathCid(contractAddress);
            Assert.notBlank(jsonPathCid, "IPFS路径CID不存在");

            Integer tokenId = null;

            /**
             * 部分数据IPFS数据缺失或IPFSjosn数据格式异常,跳过异常数据 2024-10-21 16:20:51
             */
            // 查询是否有铸币成功但是后续失败的数据,
            // Nft authNftNftRecordInfo = nftMapper.queryOneByContractAndHolder(contractAddress, chainAccountAddress,
            // Nft.NFT_STATUS_MINT_IPFS,true);
            Nft authNftNftRecordInfo = null;
            NftTransferRecord nftTransferRecord = null;
            if (authNftNftRecordInfo == null) {
                // 单个铸币
                int currentNum = getCurrentNum(platformAccountAddress, contractAddress);
                tokenId = currentNum + 1;

                Integer mintTokenId = tokenId;
                TransactionReceipt transactionReceipt =
                    SingleMint(chainAccountAddress, contractAddress, platformAccountPrivateKey, tokenId);
                String transactionHash = transactionReceipt.getTransactionHash();
                BigInteger gasUsed = transactionReceipt.getGasUsed();
                BigInteger blockNumber = transactionReceipt.getBlockNumber();
                // 异步 新增上链记录
                CompletableFuture<Nft> future = CompletableFuture.supplyAsync(() -> {
                    // nft表新增数据
                    Nft authNftNftRecordResult = addAuthNftNftRecordV2(opbChainId, contractId, mintTokenId,
                        contractAddress, chainAccountAddress, nftPrice, transactionHash);
                    return authNftNftRecordResult;
                });
                authNftNftRecordInfo = future.join();

                CompletableFuture<NftTransferRecord> future2 = CompletableFuture.supplyAsync(() -> {
                    // nft交易记录表新增数据
                    NftTransferRecord nftTransferRecordResult =
                        addNFTTransferRecordV2(opbChainId, contractId, mintTokenId, contractAddress,
                            chainAccountAddress, gasUsed, transactionHash, blockNumber);
                    return nftTransferRecordResult;
                });

                nftTransferRecord = future2.join();

            } else {
                tokenId = authNftNftRecordInfo.getTokenId();
                nftTransferRecord = nftTransferRecordMapper.queryByHashAndTransactionType(opbChainId,
                    authNftNftRecordInfo.getMintHash(), tokenId, NftTransferRecord.TRANSACTION_TYPE_MINT,
                    true);
            }
            Assert.notNull(nftTransferRecord, "交易记录不存在");
            tokenIdV2 = tokenId;
            // ipfs信息校验
            nftGetRecordService.validateMintInfoV2(accountUUID, contractAddress, tokenId);
            // 上IPFS
            // 插入nft领取记录，并获得ipfs信息
            InsertIPFSResp insertIPFSResp =
                nftGetRecordService.insertNftRecordV2(nftGetRecordId, nftGetRecordDomain, accountUUID,
                    chainAccountAddress, opbChainId, 1, tokenId, contractAddress);
            String pathCid = insertIPFSResp.getPathCid();
            String baseUri = ipfsUrl + pathCid + "/";
            // 新增nft数据
            String cid = insertIPFSResp.getCid();
            String domain = insertIPFSResp.getDomain();
            String nftUrl = imagePrefix + cid;
            // 获取json数据
            String jsonUrl = baseUri + tokenId;
            JSONObject jsObject = validateIPFSUrl(jsonUrl);

            String nftName = jsObject.getString("name");
            String description = jsObject.getString("description");
            // 同步更新NFT表IPFS信息
            updateAuthNftNftRecordV2(authNftNftRecordInfo, nftTransferRecord, contractAddress, tokenId,
                nftName, description, nftUrl, domain);

            // 更新 合约 baseURI
            setBaseUrl(baseUri, contractAddress, platformAccountPrivateKey);

            // 异步刷新挂件
            CompletableFuture.runAsync(() -> {
                try {
                    JSONObject paramJson = new JSONObject();
                    paramJson.put("accountUuid", accountUUID);
                    paramJson.put("level", 10);  // NFT挂件等级为10
                    badgeService.refreshPendantService(null, paramJson);
                } catch (Exception e) {
                    log.error("刷新挂件失败:{}", e);
                }
            });

            // NFTIDInfo表修改状态 为已补偿

            // 补偿表修改状态 为处理成功
            nftManualDispose.setStatus(NftManualDispose.STATUS_PROCESSED);
            nftManualDisposeMapper.updateById(nftManualDispose);
            // 确认消费
            channel.basicAck(deliveryTag, false);
        } catch (Exception e) {
            e.printStackTrace();
            // 丢弃消息
            channel.basicNack(deliveryTag, false, false);
            throw new ServiceException("补偿NFT信息失败:" + JSON.toJSONString(upChainDataVo));
        } finally {
            log.info("解锁 NFT补偿，记录id：{}，RecorddomainId:{},账户地址：{}，tokenId：{}", nftGetRecordId,
                nftRecorddomainId, chainAccountAddress, tokenIdV2);
            lock.unlock();
        }
    }

    /**
     * 校验同步信息
     * 
     * @param nftIds NFTid
     * @param transactionHash 交易hash
     * @return
     */
    @Override
    public R validApprove(List<Integer> nftIds, String transactionHash) {
        // 异步调用
        List result = new ArrayList();
        // 查询合约信息
        Contract authContract = contractMapper.queryByChainIDAndType(ylChainId, CONTRACT_TYPE_ERC721,
            STATUS_ENABLE, TO_USE_REGISTER_DOMAIN_NFT);
        Assert.notNull(authContract);
        String contractAddress = authContract.getContractAddress();

        // 查询平台地址信息
        PlatformAccount platformAccount = platformAccountMapper.queryByTypeAndStatus(
            PlatformAccount.TYPE_DOMAIN_REGISTER_RESOLVE_ACCOUNT, PlatformAccount.STATUS_SHOW);
        Assert.notNull(platformAccount);
        String platformAccountAddress = platformAccount.getAddress();

        // 校验交易是否成功
        TransactionReceipt transactionReceipt = getTransactionReceipt(transactionHash);
        Assert.isTrue(transactionReceipt.isStatusOK(), "授权交易失败");

        // 异步处理每个NFT的授权验证
        // List<CompletableFuture<Map<String, Object>>> futures = nftIds.stream()
        // .map(nftId -> CompletableFuture.supplyAsync(() -> validateApproveInfo(nftId, platformAccountAddress,
        // contractAddress)))
        // .collect(Collectors.toList());

        // 获取异步处理结果
        // List<Map<String, Object>> resultList = futures.stream()
        // .map(CompletableFuture::join)
        // .collect(Collectors.toList());

        // return R.okData(resultList);
        return R.ok();
    }

    /**
     * NFT续费流程后半段的NFT交易给平台地址
     *
     * @param domainRenewalNFTIds 域名续费NFT表id
     * @return
     */
    @Override
    public R transferToPlatformAddress(List<Long> domainRenewalNFTIds) {
        log.info("NFT续费流程后半段的NFT交易给平台地址 domainRenewalNFTIds:{}", domainRenewalNFTIds);
        for (Long domainRenewalNFTId : domainRenewalNFTIds) {
            OrderDomainRenewalNft orderDomainRenewalNft =
                orderDomainRenewalNftMapper.selectById(domainRenewalNFTId);
            if (orderDomainRenewalNft == null) {
                continue;
            }
            // 单个地址授权转让
            singleTransferToPlatformAddress(orderDomainRenewalNft);
        }
        return R.ok();
    }

    /**
     * NFT续费流程后半段退款，回收地址将NFT返还给用户
     *
     * @param domainRenewalNFTRefundIds 域名续费NFT退款表id
     * @return
     */
    @Override
    public R transferToUserAddress(List<Long> domainRenewalNFTRefundIds) {
        log.info(
            "NFT续费流程后半段退款，回收地址将NFT返还给用户 开始执行=====================================================================================> domainRenewalNFTRefundIds:{}",
            domainRenewalNFTRefundIds);
        for (Long domainRenewalNFTRefundId : domainRenewalNFTRefundIds) {
            OrderDomainRenewalNftRefund orderDomainRenewalNftRefund =
                orderDomainRenewalNftRefundMapper.selectById(domainRenewalNFTRefundId);
            if (orderDomainRenewalNftRefund == null) {
                log.warn("NFT续费流程后半段退款，orderDomainRenewalNftRefund 不存在,domainRenewalNFTRefundId：{}",
                    domainRenewalNFTRefundId);
                continue;
            }
            // 单个地址退还给用户
            singleRefundToNFTOwnerAddress(orderDomainRenewalNftRefund);
        }
        return R.ok();
    }

    @Override
    public R getAddressOverviewService(String address) {
        Map<String, Object> result = new HashMap<>();
        BigDecimal totalAssets = new BigDecimal(0);
        BigInteger balance = BNBUtil.getBalance(address);
        BigDecimal ylAsset = new BigDecimal(balance).divide(BigDecimal.TEN.pow(18));
        // 修改为获取实时浮动比率
        BigDecimal rate = checkSpOverview.getBigDecimal();
        BigDecimal ylAssets = ylAsset.multiply(rate).setScale(3, RoundingMode.HALF_UP);
        totalAssets = ylAssets;
        LambdaQueryWrapper<Nft> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Nft::getHolder, address).eq(Nft::getStatus, 1).eq(Nft::getIsDelete, false);
        List<Nft> nfts = nftMapper.selectList(queryWrapper);
        if (CollectionUtil.isNotEmpty(nfts)) {
            BigDecimal reduce = nfts.stream().map(Nft::getNftPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
            totalAssets = ylAssets.add(reduce);
        }
        result.put("totalAssets", totalAssets);
        result.put("balance", ylAsset.setScale(3, RoundingMode.HALF_UP));
        result.put("address",address);
        return R.okData(result);
    }

    /**
     * 合约列表
     *
     * @param page
     * @param pageSize
     * @return {@link R }
     */
    @Override
    public R getContractListService(Integer page, Integer pageSize) {
        // 查询合约
        Page<Contract> contractPage = contractMapper.selectPage(new Page<>(page, pageSize),
            new QueryWrapper<Contract>().lambda().eq(Contract::getStatus, 1).eq(Contract::getToUse, 1));
        List<Contract> records = contractPage.getRecords();
        // 封装NFT数量 持仓地址数 交易总数量
        List<ContractVo> contractVoList = new ArrayList<>();
        for (Contract contract : records) {
            ContractVo contractVo = new ContractVo();
            BeanUtils.copyProperties(contract, contractVo);
            contractVo.setNftNum(contractMapper.selecMaxTokenId());
            contractVo.setNftAddressNum(contractMapper.selectAddressNum(contract.getId()));
            contractVo.setNftTransferNum(contractMapper.selectTransferNum(contract.getId()));
            contractVoList.add(contractVo);
        }
        PageUtils pageUtils =
            new PageUtils(contractPage.getTotal(), pageSize, contractPage.getCurrent(), contractVoList);
        return R.okData(pageUtils);
    }

    /**
     * NFT交易记录列表
     *
     * @param page
     * @param pageSize
     * @param address
     * @return {@link R }
     */
    @Override
    public R nftTrRecordService(Integer page, Integer pageSize, String address, Integer tokenId) {
        Integer start = (page - 1) * pageSize;
        List<NftTransferRecordVo> nftTransferRecordPage =
            nftTransferRecordMapper.selectNftTrRecord(start, pageSize, address, tokenId);
        Long total = nftTransferRecordMapper.selectNftTrRecordCount(address, tokenId);

        return R.okData(new PageUtils(total, pageSize, page, nftTransferRecordPage));
    }

    /**
     * NFT资产
     *
     * @param page
     * @param pageSize
     * @param address
     * @return {@link R }
     */
    @Override
    public R nftPropertyService(Integer page, Integer pageSize, String address) {
        Page<Nft> nftPage;
        if (StrUtil.isNotBlank(address)) {
            nftPage = nftMapper.selectPage(new Page<>(page, pageSize), new QueryWrapper<Nft>().lambda()
                .eq(Nft::getIsDelete, 0).eq(Nft::getHolder, address).eq(Nft::getStatus, 1));
        } else {
            nftPage = nftMapper.selectPage(new Page<>(page, pageSize),
                new QueryWrapper<Nft>().lambda().eq(Nft::getIsDelete, 0).eq(Nft::getStatus, 1));
        }
        return R.okData(new PageUtils(nftPage));
    }

    /**
     * nft持有列表
     *
     * @param page
     * @param pageSize
     * @return {@link R }
     */
    @Override
    public R nftHoldRecordService(Integer page, Integer pageSize) {
        Page<NftHolderVo> nftHolderVoPage = nftMapper.selectNftHolderVoPage(new Page<>(page, pageSize));
        // 封装交易数量和占比
        nftHolderVoPage.getRecords().forEach(nftHolderVo -> {
            nftHolderVo.setTrNum(
                Long.valueOf(nftTransferRecordMapper.selectCount(Wrappers.<NftTransferRecord>lambdaQuery()
                    .eq(NftTransferRecord::getFromAddress, nftHolderVo.getAddress()).or()
                    .eq(NftTransferRecord::getToAddress, nftHolderVo.getAddress())
                )
                ));
        });
        return R.okData(new PageUtils(nftHolderVoPage));
    }

    /**
     * 合约详情
     * @param id
     * @return {@link R }
     */
    @Override
    public R contractDetailService(Integer id) {
        String creatorAddress = "0x7d03671464bd363528cb58a04f6a993dd90f8b7d";
        String creatorAddressHash = "0x8bd615e4f158c346ccdf65de24dfd009aa0005308cbd18e9b281c70080c6bbd5";
        Contract contract = contractMapper.selectById(id);
        ContractVo contractVo = new ContractVo();
        BeanUtil.copyProperties(contract, contractVo);
        contractVo.setNftNum(contractMapper.selecMaxTokenId());
        contractVo.setNftAddressNum(contractMapper.selectAddressNum(contract.getId()));
        contractVo.setNftTransferNum(contractMapper.selectTransferNum(contract.getId()));
        contractVo.setCreatorAddress(creatorAddress);
        contractVo.setCreatorAddressHash(creatorAddressHash);
        return R.okData(contractVo);
    }

    /**
     * nft详情
     *
     * @param tokenId
     * @return {@link R }
     */
    @Override
    public R nftDetailService(Integer tokenId) {
        Nft nft = nftMapper.selectOne(Wrappers.<Nft>lambdaQuery().eq(Nft::getTokenId, tokenId));
        return R.okData(nft);
    }

    /**
     * 处理单个NFT续费信息
     * 
     * @param orderDomainRenewalNft
     * @return
     */
    public Boolean singleTransferToPlatformAddress(OrderDomainRenewalNft orderDomainRenewalNft) {
        try {
            String recycleAddress = globalConfigService.getGlobalConfig(NFT_RENEWAL_DOMAIN_RECYCLE_ADDRESS);
            Assert.notBlank(recycleAddress, "回收地址不存在");
            // 上链前处理
            Boolean beforeTransferNFT =
                nftBeforeService.beforeTransferNFT(orderDomainRenewalNft, recycleAddress);
            // 上链处理以及后续操作
            if (beforeTransferNFT) {
                nftOnChainService.transferNFTProcess(orderDomainRenewalNft.getId(), recycleAddress);
            }
        } catch (Exception e) {
            log.error("NFT授权后转移失败:{}", e);
            // 修改状态为处理失败
            orderDomainRenewalNft.setTranState(OrderDomainRenewalNft.TRAN_STATE_FAIL);
            orderDomainRenewalNft.setUpdateTime(new Date());
            orderDomainRenewalNftMapper.updateById(orderDomainRenewalNft);
            return false;
        }
        return true;
    }

    /**
     * 处理单个NFT退还到NFT续费地址
     * 
     * @param orderDomainRenewalNftRefund
     * @return
     */
    public Boolean singleRefundToNFTOwnerAddress(OrderDomainRenewalNftRefund orderDomainRenewalNftRefund) {
        try {
            // NFT退款前处理
            Boolean beforeTransferNFT =
                nftBeforeService.beforeRefundToNFTOwnerAddress(orderDomainRenewalNftRefund);
            // 上链处理以及后续操作
            if (beforeTransferNFT) {
                nftOnChainService.refundNFTProcess(orderDomainRenewalNftRefund.getId());
            }
        } catch (Exception e) {
            log.error("NFT退款转移失败:{}", e);
            // 修改状态为处理失败
            orderDomainRenewalNftRefund.setTranState(OrderDomainRenewalNft.TRAN_STATE_FAIL);
            orderDomainRenewalNftRefund.setUpdateTime(new Date());
            orderDomainRenewalNftRefundMapper.updateById(orderDomainRenewalNftRefund);
            return false;
        }
        return true;
    }

    /**
     * 添加到补偿记录表信息
     * 
     * @param channel
     * @param messageId
     * @param deliveryTag
     * @param nftRecordId
     * @param chainAccountAddress
     * @param contractAddress
     * @param nftCount
     * @param opbChainId
     * @param uuid
     * @throws IOException
     */
    private void addToManualDispostInfo(Channel channel, String messageId, long deliveryTag, Long nftRecordId,
        String chainAccountAddress, String contractAddress, Integer nftCount, Integer opbChainId, String uuid)
        throws IOException {
        redisTemplate.delete(MESSAGE + messageId);
        // 不签收，人工处理
        channel.basicNack(deliveryTag, false, false);
        CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
            NftManualDispose nftManualDispose = NftManualDispose.builder().nftRecordId(nftRecordId)
                .chainAccountAddress(chainAccountAddress).contractAddress(contractAddress).nftCount(nftCount)
                .opbChainId(opbChainId).uuid(uuid).status(0).build();
            nftManualDisposeMapper.insert(nftManualDispose);
        });
    }

    /**
     * 添加到补偿记录表信息
     * 
     * @param channel
     * @param messageId
     * @param deliveryTag
     * @param nftRecordId
     * @param chainAccountAddress
     * @param contractAddress
     * @param opbChainId
     * @param uuid
     * @throws IOException
     */
    private void addToManualDispostInfoV2(Channel channel, String messageId, long deliveryTag,
        Long nftRecordId, Long nftRecordDomainId, String chainAccountAddress, String contractAddress,
        Integer opbChainId, String uuid) throws IOException {
        redisTemplate.delete(MESSAGE + messageId);
        // 不签收，人工处理
        channel.basicNack(deliveryTag, false, false);
        CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
            NftManualDispose nftManualDispose =
                NftManualDispose.builder().nftRecordId(nftRecordId).nftRecordDomainId(nftRecordDomainId)
                    .chainAccountAddress(chainAccountAddress).contractAddress(contractAddress).nftCount(1)
                    .opbChainId(opbChainId).uuid(uuid).status(0).build();
            nftManualDisposeMapper.insert(nftManualDispose);
        });
    }
}
