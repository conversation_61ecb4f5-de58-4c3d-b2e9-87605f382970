package com.lj.auth.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lj.auth.common.R;
import com.lj.auth.domain.Account;
import com.lj.auth.domain.vo.AddressBookVo;
import com.lj.auth.service.AccountService;
import com.lj.auth.util.PageUtils;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lj.auth.domain.AddressBook;
import com.lj.auth.mapper.AddressBookMapper;
import com.lj.auth.service.AddressBookService;

import javax.annotation.Resource;

@Service
public class AddressBookServiceImpl extends ServiceImpl<AddressBookMapper, AddressBook>
    implements AddressBookService {
    @Resource
    private AccountService accountService;
    @Resource
    private AddressBookMapper addressBookMapper;

    /**
     * 新增联系人
     * 
     * @param chainId
     * @param address
     * @param describe
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/29
     */
    @Override
    public R addAddressBookService(Integer chainId, String address, String describe) {

        Account userInfo = accountService.getUserInfo();
        AddressBook addressBook = new AddressBook();
        addressBook.setAcountUuid(userInfo.getUuid());
        addressBook.setChainId(chainId);
        addressBook.setChainAddress(address);
        if (StrUtil.isNotBlank(describe)) {
            addressBook.setContactDescription(describe);
        }
        this.save(addressBook);
        return R.ok();
    }

    /**
     * 删除联系人
     * 
     * @param id id
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/29
     */
    @Override
    public R deleteAddressBookService(Long id) {
        Account userInfo = accountService.getUserInfo();
        List<AddressBook> list = this.list(Wrappers.<AddressBook>lambdaQuery()
            .eq(AddressBook::getAcountUuid, userInfo.getUuid()).eq(AddressBook::getState, true));
        List<Long> ids = list.stream().map(AddressBook::getId).collect(Collectors.toList());
        if (ids.contains(id)) {
            this.update(Wrappers.<AddressBook>lambdaUpdate().eq(AddressBook::getId, id)
                .set(AddressBook::getState, false));
        }
        return R.ok();
    }

    /**
     * 修改联系人
     * 
     * @param id id
     * @param chainId 链id
     * @param address 地址
     * @param describe 描述
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/29
     */
    @Override
    public R updateAddressBookService(Long id, Integer chainId, String address, String describe) {
        Account userInfo = accountService.getUserInfo();
        List<AddressBook> list = this.list(Wrappers.<AddressBook>lambdaQuery()
            .eq(AddressBook::getAcountUuid, userInfo.getUuid()).eq(AddressBook::getState, true));
        List<Long> ids = list.stream().map(AddressBook::getId).collect(Collectors.toList());
        if (ids.contains(id)) {
            this.update(Wrappers.<AddressBook>lambdaUpdate().eq(AddressBook::getId, id)
                .set(AddressBook::getChainId, chainId).set(AddressBook::getChainAddress, address));
            if (StrUtil.isNotBlank(describe)) {
                this.update(Wrappers.<AddressBook>lambdaUpdate().eq(AddressBook::getId, id)
                    .set(AddressBook::getContactDescription, describe));
            }
        }
        return R.ok();
    }

    /**
     * 查询联系人
     * 
     * @param page 页
     * @param pageSize 页大小
     * @param chainId 链ID
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/29
     */
    @Override
    public R queryAddressBookService(int page, int pageSize, Integer chainId) {
        Account userInfo = accountService.getUserInfo();
        Integer totalCount = 0;
        if (chainId == null) {
            // 查询设置所有者记录
            totalCount = addressBookMapper.selectCount(Wrappers.<AddressBook>lambdaQuery()
                .eq(AddressBook::getAcountUuid, userInfo.getUuid()).eq(AddressBook::getState, true));
        } else {
            // 查询设置所有者记录
            totalCount = addressBookMapper.selectCount(
                Wrappers.<AddressBook>lambdaQuery().eq(AddressBook::getAcountUuid, userInfo.getUuid())
                    .eq(AddressBook::getChainId, chainId).eq(AddressBook::getState, true));
        }
        int start = (page - 1) * pageSize;
        List<AddressBookVo> addressBookVoList =
            addressBookMapper.getPage(start, pageSize, userInfo.getUuid(), chainId);
        PageUtils pageUtils = new PageUtils(totalCount, pageSize, page, addressBookVoList);
        return R.okData(pageUtils);
    }
}
