package com.lj.auth.service;

import com.lj.auth.common.R;
import com.lj.auth.domain.AddressBook;
import com.baomidou.mybatisplus.extension.service.IService;

public interface AddressBookService extends IService<AddressBook> {

    /**
     * 新增联系人
     * 
     * @param chainId 链id
     * @param address 地址
     * @param describe 描述
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/28
     */
    R addAddressBookService(Integer chainId, String address, String describe);

    /**
     * 删除联系人
     * 
     * @param id id
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/29
     */
    R deleteAddressBookService(Long id);

    /**
     * 修改联系人额
     * 
     * @param id id
     * @param chainId 链id
     * @param address 地址
     * @param describe 描述
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/29
     */
    R updateAddressBookService(Long id, Integer chainId, String address, String describe);

    /**
     * 查询联系人
     * 
     * @param page 页
     * @param pageSize 页大小
     * @param chainId 链ID
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/29
     */
    R queryAddressBookService(int page, int pageSize, Integer chainId);
}
