package com.lj.auth.service;

import com.lj.auth.common.R;
import com.lj.auth.domain.AccountDomainAddress;
import com.baomidou.mybatisplus.extension.service.IService;

public interface AccountDomainAddressService extends IService<AccountDomainAddress> {

    /**
     * 设置 域名地址映射
     * 
     * @param opbChainId 链框架Id
     * @param domain 域名
     * @param address 地址
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/07
     */
    R domainNameMappingService(Integer opbChainId, String domain, String address);

    /**
     * 修改域名地址映射
     * 
     * @param id id
     * @param opbChainId 链框架Id
     * @param address 地址
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/07
     */
    R updateDomainNameMappingService(Integer id, Integer opbChainId, String address);

    /**
     * 查询域名映射列表
     * 
     * @param domain 域名
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/07
     */
    R domainNameMappingListService(String domain);

    /**
     * 移除域名地址映射
     * 
     * @param id
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/07
     */
    R domainNameMappingDeleteService(Integer id);

    /**
     * 域名上链列表
     * 
     * @param domain 域名
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/07
     */
    R searchDomianDetailService(String domain);

    /**
     * 获取域名应用数量
     * 
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/07
     */
    R queryDomainApplicationService();

    /**
     * 获取域名应用
     * 
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/07
     */
    R queryDomainApplicationManageService();

    /**
     * 获取应用列表
     * 
     * @param page 页
     * @param pageSize 页码
     * @param type 1-已经应用 2-未应用
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/07
     */
    R queryDomainApplicationListService(Integer page, Integer pageSize, Integer type);

    /**
     * 获取域名应用详情
     * 
     * @param domain 域名
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/07
     */
    R queryDomainApplicationDetailService(String domain);

    /**
     * 域名解析地址
     * @param domain
     * @return {@link R }
     * <AUTHOR>
     * @date 2025/02/26
     */
    R domainResolveAddressService(String domain);

    R domainNameMappingDeleteService1(Integer id, String uuid);

    /**
     * 地址解析域名
     *
     * @param address
     * @return {@link R }
     */
    R addressDomainNameResolutionService(String address);
}
