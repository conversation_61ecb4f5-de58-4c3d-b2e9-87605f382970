package com.lj.auth.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lj.auth.common.R;
import com.lj.auth.domain.Account;
import com.lj.auth.service.AccountService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lj.auth.domain.AddresseeMessage;
import com.lj.auth.mapper.AddresseeMessageMapper;
import com.lj.auth.service.AddresseeMessageService;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

@Service
public class AddresseeMessageServiceImpl extends ServiceImpl<AddresseeMessageMapper, AddresseeMessage>
    implements AddresseeMessageService {
    @Resource
    private AccountService accountService;
    @Resource
    private AddresseeMessageMapper addresseeMessageMapper;

    /**
     * 收件信息分页查询
     *
     * @param page
     * @param pageSize
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/27
     */
    @Override
    public R getPage(Integer page, Integer pageSize) {
        Account userInfo = accountService.getUserInfo();
        Page<AddresseeMessage> reesult = this.page(new Page<>(page, pageSize), Wrappers
            .<AddresseeMessage>lambdaQuery().eq(AddresseeMessage::getAccountUuid, userInfo.getUuid()));
        return R.okData(reesult);
    }

    /**
     * 新增收件信息
     *
     * @param name -姓名
     * @param phone -手机号
     * @param email -邮箱
     * @param address -地址
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/27
     */
    @Override
    public R add(String name, String phone, String email, String address) {
        Account userInfo = accountService.getUserInfo();
        AddresseeMessage message = new AddresseeMessage().setName(name).setPhone(phone).setEmail(email)
            .setAddress(address).setAccountUuid(userInfo.getUuid());
        this.save(message);
        return R.ok();
    }

    /**
     * 修改收件信息
     *
     * @param id id
     * @param name 姓名
     * @param phone 手机号
     * @param email 邮箱
     * @param address 地址
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/27
     */
    @Override
    public R updateService(Long id, String name, String phone, String email, String address) {
        addresseeMessageMapper.updateAddressMessage(id, name, phone, email, address);
        return R.ok();
    }

    /**
     * @param id id
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/27
     */
    @Override
    public R del(Long id) {
        this.removeById(id);
        return R.ok();
    }

    /**
     * 设置默认收件信息
     *
     * @param id id
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/27
     */
    @Override
    @Transactional
    public R setDefault(Long id) {
        Account userInfo = accountService.getUserInfo();
        // 先设置所有地址为非默认
        boolean update = this.update(Wrappers.<AddresseeMessage>lambdaUpdate()
            .eq(AddresseeMessage::getAccountUuid, userInfo.getUuid())
            .set(AddresseeMessage::getState, 2));
        this.update(Wrappers.<AddresseeMessage>lambdaUpdate().eq(AddresseeMessage::getId, id)
            .set(AddresseeMessage::getState, 1));
        return R.ok();
    }
}
