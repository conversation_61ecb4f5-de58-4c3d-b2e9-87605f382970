package com.lj.auth.service;

import com.lj.auth.common.R;
import com.lj.auth.domain.ExploreAppRecently;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

public interface ExploreAppRecentlyService extends IService<ExploreAppRecently> {

    /**
     * 获取最近访问纪录
     * 
     * @param page 页
     * @param pageSize 页大小
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/01
     */
    R getRecentlyListService(int page, int pageSize);

    /**
     * 删除访问纪录
     * 
     * @param ids id集合
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/01
     */
    R unRecentlyListService(List<Integer> ids);

    /**
     * 获取探索背景图
     * 
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/01
     */
    R getBackgroundListService();
}
