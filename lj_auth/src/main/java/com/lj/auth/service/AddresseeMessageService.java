package com.lj.auth.service;

import com.lj.auth.common.R;
import com.lj.auth.domain.AddresseeMessage;
import com.baomidou.mybatisplus.extension.service.IService;
public interface AddresseeMessageService extends IService<AddresseeMessage>{


    /**
     *
     *  收件信息分页查询
     *
     * @param page
     * @param pageSize
     * @return {@link R}
     */
    R getPage(Integer page, Integer pageSize);

    /**
     * 新增收件信息
     *
     * @param name-姓名
     * @param phone-手机号
     * @param email-邮箱
     * @param address-地址
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/27
     */
    R add(String name, String phone, String email, String address);

    /**
     * 修改收件信息
     *
     * @param id      id
     * @param name    姓名
     * @param phone   手机号
     * @param email   邮箱
     * @param address 地址
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/27
     */
    R updateService(Long id, String name, String phone, String email, String address);

    /**
     * 删除收件人信息
     *
     * @param id id
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/27
     */
    R del(Long id);

    /**
     * 设置默认收件信息
     *
     * @param id id
     * @return {@link R}
     */
    R setDefault(Long id);
}
