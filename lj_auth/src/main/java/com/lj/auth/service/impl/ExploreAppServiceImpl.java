package com.lj.auth.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lj.auth.common.R;
import com.lj.auth.controller.ExploreController;
import com.lj.auth.domain.Account;
import com.lj.auth.domain.ExploreAppCollection;
import com.lj.auth.domain.ExploreAppSearchRecord;
import com.lj.auth.mapper.ExploreAppCollectionMapper;
import com.lj.auth.mapper.ExploreAppSearchRecordMapper;
import com.lj.auth.service.AccountService;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lj.auth.domain.ExploreApp;
import com.lj.auth.mapper.ExploreAppMapper;
import com.lj.auth.service.ExploreAppService;

import javax.annotation.Resource;

@Service
public class ExploreAppServiceImpl extends ServiceImpl<ExploreAppMapper, ExploreApp>
    implements ExploreAppService {
    @Resource
    private ExploreAppMapper exploreAppMapper;
    @Resource
    private AccountService accountService;
    @Resource
    private ExploreAppCollectionMapper exploreAppCollectionMapper;
    @Resource
    private ExploreAppSearchRecordMapper exploreAppSearchRecordMapper;

    /**
     * 获取应用列表
     * 
     * @param page 页
     * @param pageSize 页大小
     * @param appType --0-获取全部 app类别id
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/01
     */
    @Override
    public R getAppListService(int page, int pageSize, Integer appType) {
        Page<ExploreApp> exploreAppPage = new Page<>();
        if (appType == 0) {
            exploreAppPage = exploreAppMapper.selectPage(new Page<>(page, pageSize), Wrappers
                .<ExploreApp>lambdaQuery().eq(ExploreApp::getState, true).orderByAsc(ExploreApp::getSort));

        } else {
            exploreAppPage = exploreAppMapper.selectPage(new Page<>(page, pageSize),
                Wrappers.<ExploreApp>lambdaQuery().eq(ExploreApp::getAppTypeId, appType)
                    .eq(ExploreApp::getState, true).orderByAsc(ExploreApp::getSort));

        }
        // 查询个人收藏列表
        Account userInfo = accountService.getUserInfo();

        List<ExploreAppCollection> exploreAppCollections =
            exploreAppCollectionMapper.selectList(Wrappers.<ExploreAppCollection>lambdaQuery()
                .eq(ExploreAppCollection::getAccountUuid, userInfo.getUuid()));

        Map<Integer, ExploreAppCollection> collectionMap = new HashMap<>();
        exploreAppCollections.forEach(collection -> collectionMap.put(collection.getAppId(), collection));

        List<ExploreApp> records = exploreAppPage.getRecords();
        for (ExploreApp record : records) {
            ExploreAppCollection collection = collectionMap.get(record.getId());
            if (collection != null) {
                record.setIsCollection(true);
            }
        }
        return R.okData(exploreAppPage);
    }

    /**
     * 获取推荐应用列表
     * 
     * @param page 页
     * @param pageSize 页大小
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/01
     */
    @Override
    public R getRecommendationListService(int page, int pageSize) {
        Page<ExploreApp> exploreAppPage = exploreAppMapper.selectPage(new Page<>(page, pageSize),
            Wrappers.<ExploreApp>lambdaQuery().eq(ExploreApp::getIsRecommendation, true)
                .eq(ExploreApp::getState, true).orderByAsc(ExploreApp::getSort));
        return R.okData(exploreAppPage);
    }

    /**
     * 应用搜索
     * 
     * @param keyWord 搜索关键字
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/01
     */
    @Override
    public R searchAppService(String keyWord) {
        Account userInfo = accountService.getUserInfo();

        List<ExploreApp> exploreAppList =
            exploreAppMapper.selectList(Wrappers.<ExploreApp>lambdaQuery().eq(ExploreApp::getState, true)
                .like(ExploreApp::getAppName, keyWord).or().like(ExploreApp::getAppLink, keyWord));
        // 插入搜索记录
        if (StrUtil.isNotBlank(keyWord)) {
            ExploreAppSearchRecord exploreAppSearchRecord =
                new ExploreAppSearchRecord().setAccountUuid(userInfo.getUuid()).setContent(keyWord);
            exploreAppSearchRecordMapper.insert(exploreAppSearchRecord);
        }
        return R.okData(exploreAppList);
    }

    /**
     * 搜索历史纪录
     * 
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/01
     */
    @Override
    public R searchRecordListService() {
        Account userInfo = accountService.getUserInfo();
        List<ExploreAppSearchRecord> exploreAppSearchRecords =
            exploreAppSearchRecordMapper.selectList(Wrappers.<ExploreAppSearchRecord>lambdaQuery()
                .eq(ExploreAppSearchRecord::getAccountUuid, userInfo.getUuid())
                .orderByDesc(ExploreAppSearchRecord::getCreateTime).last("limit 5"));
        List<ExploreApp> list = this.list();
        Map<Integer, String> idToLogoMap =
            list.stream().collect(Collectors.toMap(ExploreApp::getId, ExploreApp::getLogo));
        exploreAppSearchRecords.forEach(exploreAppSearchRecord -> {
            Integer appId = exploreAppSearchRecord.getAppId();
            idToLogoMap.getOrDefault(appId, null);
            exploreAppSearchRecord.setLogo(idToLogoMap.get(appId));
        });
        return R.okData(exploreAppSearchRecords);
    }

    /**
     * 删除搜索历史纪录
     * 
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/01
     */
    @Override
    public R removeRecordListService() {
        Account userInfo = accountService.getUserInfo();
        int delete = exploreAppSearchRecordMapper.delete(Wrappers.<ExploreAppSearchRecord>lambdaQuery()
            .eq(ExploreAppSearchRecord::getAccountUuid, userInfo.getUuid()));
        return R.ok();
    }

    /**
     * 获取官方应用列表
     * 
     * @param page 页
     * @param pageSize 页大小
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/01
     */
    @Override
    public R getOfficialListService(int page, int pageSize) {
        Page<ExploreApp> exploreAppPage = exploreAppMapper.selectPage(new Page<>(page, pageSize),
            Wrappers.<ExploreApp>lambdaQuery().eq(ExploreApp::getIsOfficial, true)
                .eq(ExploreApp::getState, true).orderByAsc(ExploreApp::getSort));
        return R.okData(exploreAppPage);
    }
}
