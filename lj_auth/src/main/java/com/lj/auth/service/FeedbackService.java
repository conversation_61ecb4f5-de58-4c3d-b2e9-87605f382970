package com.lj.auth.service;

import com.lj.auth.common.R;
import com.lj.auth.domain.Feedback;
import com.baomidou.mybatisplus.extension.service.IService;

public interface FeedbackService extends IService<Feedback> {

    /**
     * 新增反馈
     * 
     * @param title 标题
     * @param content 内容
     * @param images 图片
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/29
     */
    R addUserFeedbackService(String title, String content, String images);

    /**
     * 获取反馈内容列表
     * 
     * @param page 页
     * @param pageSize 页大小
     * @param type 1-已回复 0-未回复 2全-部
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/29
     */
    R getUserFeedbackListService(int page, int pageSize, Integer type);
}
