package com.lj.auth.service;

import com.lj.auth.domain.Version;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <AUTHOR>
 * @date 2024/10/17
 */
public interface VersionService extends IService<Version>{


    /**
     * 获取APP版本信息
     *
     * @param version    版本
     * @param systemType 系统类型 1-安卓 2-IOS
     * @param build      构建次数
     * @param platform
     * @param isShop
     * @return {@link Version }
     */
    Version getLatestVersionService(String version, Integer systemType,String build,String platform,Integer isShop);
}
