package com.lj.auth.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.lj.auth.common.R;
import com.lj.auth.domain.Account;
import com.lj.auth.domain.vo.ExploreAppRecentlyVo;
import com.lj.auth.domain.vo.ImageVo;
import com.lj.auth.service.AccountService;
import com.lj.auth.service.GlobalConfigService;
import com.lj.auth.util.PageUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lj.auth.domain.ExploreAppRecently;
import com.lj.auth.mapper.ExploreAppRecentlyMapper;
import com.lj.auth.service.ExploreAppRecentlyService;

import javax.annotation.Resource;

import static com.lj.auth.common.CommonConstant.*;

@Service
public class ExploreAppRecentlyServiceImpl extends ServiceImpl<ExploreAppRecentlyMapper, ExploreAppRecently>
    implements ExploreAppRecentlyService {
    @Resource
    private AccountService accountService;
    @Resource
    private ExploreAppRecentlyMapper exploreAppRecentlyMapper;

    @Resource
    private GlobalConfigService globalConfigService;

    /**
     * 获取最近访问纪录
     * 
     * @param page 页
     * @param pageSize 页大小
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/01
     */
    @Override
    public R getRecentlyListService(int page, int pageSize) {
        Account userInfo = accountService.getUserInfo();
        Integer totalCount = exploreAppRecentlyMapper.appRecentlyCount(userInfo.getUuid());

        int start = (page - 1) * pageSize;

        List<ExploreAppRecentlyVo> appRecentlyVoList = new ArrayList<>();
        if (start < totalCount) {
            appRecentlyVoList =
                exploreAppRecentlyMapper.getAppRecentlyPage(start, pageSize, userInfo.getUuid());
        }
        PageUtils pageUtils = new PageUtils(totalCount, pageSize, page, appRecentlyVoList);

        return R.okData(pageUtils);
    }

    /**
     * 删除最近访问纪录
     * 
     * @param ids id集合
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/01
     */
    @Override
    public R unRecentlyListService(List<Integer> ids) {
        List<ExploreAppRecently> exploreAppRecentlies = this.listByIds(ids);
        if (CollectionUtil.isNotEmpty(exploreAppRecentlies)) {
            boolean b = this.removeByIds(ids);
        }
        return R.ok();
    }

    /**
     * 获取探索背景图
     * 
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/04/01
     */
    @Override
    public R getBackgroundListService() {
        String exploreBackgroundList = globalConfigService.getGlobalConfig(EXPLORE_BACKGROUND);
        List<ImageVo> result = new ArrayList<>();
        if (exploreBackgroundList.contains(",")) {
            result = Arrays.stream(exploreBackgroundList.split(",")).map(url -> {
                ImageVo imageVo = new ImageVo();
                imageVo.setUrl(url);
                return imageVo;
            }).collect(Collectors.toList());
        } else {
            ImageVo imageVo = new ImageVo();
            imageVo.setUrl(exploreBackgroundList);
            result.add(imageVo);
        }
        return R.okData(result);
    }
}
