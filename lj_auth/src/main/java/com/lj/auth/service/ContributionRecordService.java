package com.lj.auth.service;

import com.lj.auth.common.R;
import com.lj.auth.domain.ContributionRecord;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lj.auth.domain.vo.OrderNotifyRequest;
import com.lj.auth.domain.vo.PayNotifyVo;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;

public interface ContributionRecordService extends IService<ContributionRecord> {

    /**
     * 贡献支付配置
     * 
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/07/02
     */
    R getPayConfigService();

    /**
     * 排行榜
     * 
     * @param page 页码
     * @param pageSize 页大小
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/07/02
     */
    R rankingListService(int page, int pageSize);

    /**
     * 贡献记录
     * 
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/07/03
     */
    R contributionRecordsService();

    /**
     * 我的贡献
     * 
     * @param page 页
     * @param pageSize 页码
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/07/04
     */
    R myContributionService(int page, int pageSize);

    /**
     * 贡献充值
     * 
     * @param amount 金额
     * @param payType 支付方式：1-微信2-支付宝
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/07/08
     */
    R getGXWeiXinLinkService(BigDecimal amount, int payType);

    /**
     * 我的贡献排行
     * 
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/07/08
     */
    R myRankingService();

    /**
     * app下单支付
     *
     * @param merOrderId 订单号
     * @param jsCode 临时登录凭证
     * @param returnUrl 订单展示页面
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/07/08
     */
    R appPay(String merOrderId, String jsCode, String returnUrl);

    /**
     * @param data
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/07/08
     */
    R appNotify(PayNotifyVo data);

    /**
     * 贡献订单交易查询
     * 
     * @param merOrderId
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/07/09
     */
    R appQuery(String merOrderId);

    /**
     * 创建订单
     *
     * @param request
     * @param amount
     * @param remark
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/12/03
     */
    R createOrderService(HttpServletRequest request,BigDecimal amount, String remark);


    R appNotify1(OrderNotifyRequest data);
}
