package com.lj.auth.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.lj.auth.exception.ServiceException;
import com.lj.auth.util.IpfsUtil;
import io.ipfs.api.IPFS;
import lombok.extern.slf4j.Slf4j;

import java.net.MalformedURLException;
import java.net.URL;

/**
 * <AUTHOR>
 * @describe
 */
@Slf4j
public class IPFSTest {

    public static void main(String[] args) {
        IPFS ipfs = new IPFS("/ip4/*************/tcp/5002");
//        IPFS ipfs = new IPFS("/ip4/**************/tcp/5002");
        String contractAddress="0xc8468072Fb453Df2d69540159e54Aa66EB4493A2";
        String pathCid = IpfsUtil.getJsonPathCid(contractAddress,ipfs);
        log.info("pathCid:{}",pathCid);
        System.out.println(pathCid);


//        String pathCid = insertIPFSResp.getPathCid();
        String ipfsUrl ="http://*************:8088/ipfs/";
//        String ipfsUrl ="http://**************:8088/ipfs/";
        String baseUri = ipfsUrl + pathCid + "/";
        //新增nft数据
//        String domain = insertIPFSResp.getDomain();
//        String nftUrl = imagePrefix + cid;
        //获取json数据
        String jsonUrl = baseUri + 10;
        log.info("jsonUrl:{}",jsonUrl);
        JSONObject jsObject = validateIPFSUrl(jsonUrl);
        System.out.println(jsObject);
    }

    /**
     * 校验IPFS地址
     * @param jsonUrl
     * @return
     */
    private static JSONObject validateIPFSUrl(String jsonUrl) {
        JSONObject jsObject;
        try {
            URL url = new URL(jsonUrl);
            jsObject = JSON.parseObject(url);
        } catch (MalformedURLException e) {
            throw new ServiceException("ipfs访问失败");
        }
        return jsObject;
    }
}
