package com.lj.auth.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lj.auth.common.R;
import com.lj.auth.domain.*;
import com.lj.auth.domain.resp.InsertIPFSResp;
import com.lj.auth.domain.resp.InsertNftRecordPreResp;
import com.lj.auth.domain.resp.NftGetRecordResp;
import com.lj.auth.domain.vo.IpfsImageVo;
import com.lj.auth.domain.vo.NftGetRecordVo;
import com.lj.auth.exception.ServiceException;
import com.lj.auth.mapper.*;
import com.lj.auth.service.GlobalConfigService;
import com.lj.auth.util.IpfsUtil;
import com.lj.auth.util.PageUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lj.auth.service.NftGetRecordService;
import org.springframework.transaction.annotation.Transactional;

import static com.lj.auth.common.CommonConstant.NFT_CLAIM_FAILURE_REASONS;

@Slf4j
@Service
public class NftGetRecordServiceImpl extends ServiceImpl<NftGetRecordMapper, NftGetRecord> implements NftGetRecordService{

    @Resource
    private NftGetRecordMapper nftGetRecordMapper;
    @Resource
    private DomainAccountMapper domainAccountMapper;
    @Resource
    private IpfsImageMapper ipfsImageMapper;
    @Resource
    private IpfsImageAttributeMapper ipfsImageAttributeMapper;
    @Resource
    private NftGetRecordDomainMapper nftGetRecordDomainMapper;

    @Resource
    private GlobalConfigService configService;

    @Override
    public R queryGetNftInfo() {
        String uuid = StpUtil.getLoginIdAsString();
        Map<String, Integer> map = new HashMap<>();
        //查询用户NFT总数
        LambdaQueryWrapper<NftGetRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(NftGetRecord::getGetAccountUuid, uuid)
                .eq(NftGetRecord::getStatus, 1)
                .eq(NftGetRecord::getGetStatus, 1);
        List<NftGetRecord> nftGetRecords = nftGetRecordMapper.selectList(queryWrapper);
        if (CollectionUtil.isEmpty(nftGetRecords)) {
            map.put("totalCount", 0);
            map.put("hasCount", 0);
            map.put("noCount", 0);
            return R.okData(map);
        }
        map.put("totalCount", nftGetRecords.size());
        //查询用户已领取的nft数量
        Integer hasCount = nftGetRecordMapper.selectGetCount(uuid);
        map.put("hasCount", hasCount);
        //计算用户未领取的nft数量
        if (hasCount > nftGetRecords.size()) {
            return R.error("数据异常，请联系管理员");
        }
        Integer noCount = nftGetRecords.size() - hasCount;
        map.put("noCount", noCount);
        return R.okData(map);
    }

    /**
     * 查询用户领取nft的信息
     * @return
     */
    @Override
    public NftGetRecordResp queryGetNftInfoV2() {
        String uuid = StpUtil.getLoginIdAsString();
        NftGetRecordResp nftGetRecordResp = nftGetRecordMapper.countByAccountUUID(uuid);
        return nftGetRecordResp;
    }


    /**
     * 查询nft记录
     * @return
     */
    @Override
    public R queryGetNftRecord() {
        String uuid = StpUtil.getLoginIdAsString();
        LambdaQueryWrapper<NftGetRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(NftGetRecord::getGetAccountUuid, uuid)
                .orderByDesc(NftGetRecord::getCreateTime);
        List<NftGetRecord> nftGetRecords = nftGetRecordMapper.selectList(queryWrapper);
        if (CollectionUtil.isEmpty(nftGetRecords)) {
            return R.ok();
        }
        return R.okData(nftGetRecords);
    }

    @Override
    public R queryGetNftRecordV2(Integer pageNum, Integer pageSize) {
        String nftClaimFailureReasons = configService.getGlobalConfig(NFT_CLAIM_FAILURE_REASONS);
        String uuid = StpUtil.getLoginIdAsString();
        int start = (pageNum - 1) * pageSize;
        List<NftGetRecordVo> nftGetRecordList= new ArrayList<>();

        int totalCount = nftGetRecordMapper.totalPageQueryByAccountUUID(uuid);
        if (start < totalCount) {
            nftGetRecordList = nftGetRecordMapper.pageQueryByAccountUUID(uuid, start, pageSize);
        }
        //修改返回值里面的nft领取失败原因
        for (NftGetRecordVo nftGetRecordVo : nftGetRecordList) {
            Integer status = nftGetRecordVo.getStatus();
            Integer getStatus = nftGetRecordVo.getGetStatus();
            if(status.equals(NftGetRecord.STATUS_REGISTER) && getStatus.equals(NftGetRecord.GET_STATUS_FAILURE)){
                nftGetRecordVo.setClaimFailureReasons(nftClaimFailureReasons);
            }
        }
        PageUtils pageUtils = new PageUtils(totalCount, pageSize, pageNum, nftGetRecordList);
        return R.okData(pageUtils);
    }

    @Override
    public Long insertNftRecordPre(String uuid, String chainAccountAddress, Integer opbChainId, Integer count, String contractAddress) {
        NftGetRecord nftGetRecord = NftGetRecord.builder().title("领取NFT")
                .getAccountUuid(uuid)
                .getAccountAddress(chainAccountAddress)
                .opbChainId(opbChainId)
                .status(NftGetRecord.STATUS_RECEIVE)
                .getCount(count)
                .isSuccess(NftGetRecord.issuccess_PENDING)
                .build();
        nftGetRecordMapper.insert(nftGetRecord);
        return nftGetRecord.getId();
    }

    /**
     * 在nftgettrecord表插入数据 domian表也插入数据
     * @param uuid
     * @param chainAccountAddress
     * @param opbChainId
     * @param count
     * @param contractAddress
     * @return
     */
    @Override
    public InsertNftRecordPreResp insertNftRecordPreV2(String uuid, String chainAccountAddress, Integer opbChainId, Integer count, String contractAddress) {
        InsertNftRecordPreResp insertNftRecordResp = new InsertNftRecordPreResp();
        //插入nftgetrecord表
        NftGetRecord nftGetRecord = NftGetRecord.builder()
                .title("领取NFT")
                .getAccountUuid(uuid)
                .getAccountAddress(chainAccountAddress)
                .opbChainId(opbChainId)
                .status(NftGetRecord.STATUS_RECEIVE)
                .getCount(count)
                .isSuccess(NftGetRecord.issuccess_PENDING)
                .build();
        nftGetRecordMapper.insert(nftGetRecord);
        Long nftGetRecordId = nftGetRecord.getId();
        Assert.notNull(nftGetRecordId, "插入nftgetrecord数据失败");
        List<Long> nftGetRecordDomainIds= new ArrayList<>();
        //插入对应的nftGetRecordDomain表
        for (Integer i = 1; i <= count; i++) {
            NftGetRecordDomain nftGetRecordDomain = new NftGetRecordDomain();
            nftGetRecordDomain.setNftGetRecordId(nftGetRecordId);
            nftGetRecordDomain.setSort(i);
            nftGetRecordDomainMapper.insert(nftGetRecordDomain);
            Long nftGetRecordDomainId = nftGetRecordDomain.getId();
            Assert.notNull(nftGetRecordId, "插入nftGetRecordDomain数据失败");
            nftGetRecordDomainIds.add(nftGetRecordDomainId);
        }
        insertNftRecordResp.setNftGetRecordDomainIds(nftGetRecordDomainIds);
        insertNftRecordResp.setNftGetRecordId(nftGetRecordId);
        return insertNftRecordResp;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String insertNftRecord(String uuid, String chainAccountAddress, Integer opbChainId, Integer count, Integer isSuccess, List<Integer> tokenIds, String contractAddress,Long nftRecordId) {
        NftGetRecord record = nftGetRecordMapper.selectById(nftRecordId);
        if (null == record){
            throw new ServiceException("记录不存在");
        }
        if (!Objects.equals(record.getGetAccountUuid(),uuid)){
            throw new ServiceException("领取账户信息不匹配");
        }

        //失败
        if (!Objects.equals(isSuccess, NftGetRecord.ISSUCCESS_SUCCESS)) {
            record.setIsSuccess(isSuccess);
            nftGetRecordMapper.updateById(record);
            return null;
        }

        if (!Objects.equals(count, tokenIds.size())) {
            throw new ServiceException("铸币数量和token数量不一致");
        }
        //成功
        //1.查询可领取NFT的域名信息
        //2.查询未使用的图片,组装ipfs-json信息上传
        //3.查询最新目录CID
        //4.修改域名领取NFT状态
        //5.新增领取NFT记录

        //1
        // LambdaQueryWrapper<DomainAccount> domainAccountWrapper = new LambdaQueryWrapper<>();
        // domainAccountWrapper.eq(DomainAccount::getAccountUuid, uuid)
        //         .eq(DomainAccount::getPayState, DomainAccount.payState1)
        //         .eq(DomainAccount::getNftGetState, DomainAccount.nftGetState1)
        //         .last("LIMIT " + count);
        // List<DomainAccount> domainAccounts = domainAccountMapper.selectList(domainAccountWrapper);
        List<DomainAccount> domainAccounts = domainAccountMapper.selectGetNftList(uuid,count);
        if (domainAccounts.size() < count) {
            throw new ServiceException("铸币数量非法,没有足够的域名可领取NFT");
        }
        //2
        Integer tokenIdsCount = ipfsImageMapper.selectCount(new LambdaQueryWrapper<IpfsImage>()
                .eq(IpfsImage::getContractAddress, contractAddress)
                .in(IpfsImage::getTokenId, tokenIds));
        if (tokenIdsCount > 0) {
            log.error("存在重复铸币的TOKEN,contractAddress:{},tokenIds:{}",contractAddress,tokenIds);
            throw new ServiceException("存在重复铸币的TOKEN");
        }
        List<IpfsImage> ipfsImages = ipfsImageMapper.selectList(new LambdaQueryWrapper<IpfsImage>()
                .eq(IpfsImage::getState, IpfsImage.STATE_UNUSED)
                .eq(IpfsImage::getContractAddress, contractAddress)
                .last("LIMIT " + count));
        if (ipfsImages.size() < count) {
            throw new ServiceException("铸币数量非法,没有足够的NFT-IPFS文件可使用,contractAddress:" + contractAddress);
        }
        List<Integer> indexList = ipfsImages.stream().map(IpfsImage::getIndex).collect(Collectors.toList());
        //图片属性信息
        List<IpfsImageAttribute> ipfsImageAttributes = ipfsImageAttributeMapper.selectList(new LambdaQueryWrapper<IpfsImageAttribute>()
                .eq(IpfsImageAttribute::getState, IpfsImageAttribute.state1)
                .in(IpfsImageAttribute::getIndex,indexList));
        if (ipfsImages.size() != ipfsImageAttributes.size()){
            log.error("铸币失败,NFT属性值数据异常.图片数量和属性数量不匹配{}",indexList);
            throw new ServiceException("NFT属性值数据异常");
        }
        Map<Integer, IpfsImageAttribute> imageAttributeMap = ipfsImageAttributes.stream().collect(Collectors.toMap(IpfsImageAttribute::getIndex, item -> item));
    
        List<IpfsImageVo> imageVos = new ArrayList<>(count);
        for (int i = 0; i < count; i++) {
            IpfsImage ipfsImage = ipfsImages.get(i);
            String cid = ipfsImage.getCid();
            Integer index = ipfsImage.getIndex();
            String domain = domainAccounts.get(i).getDomain();
            Integer tokenId = tokenIds.get(i);
            IpfsImageVo ipfsImageVo = new IpfsImageVo(cid, domain, tokenId);
            // IpfsUtil.fileWrite(contractAddress, tokenId.toString(), domain, cid);
            imageVos.add(ipfsImageVo);
    
            IpfsImageAttribute ipfsImageAttribute = imageAttributeMap.get(index);
            
            String headwear = ipfsImageAttribute.getHeadwear();//头饰
            String glasses = ipfsImageAttribute.getGlasses();//饰品
            String emote = ipfsImageAttribute.getEmote();//表情
            String head = ipfsImageAttribute.getHead();//头套
            String clothes = ipfsImageAttribute.getClothes();//衣服
            String body = ipfsImageAttribute.getBody();//身体
            String bg = ipfsImageAttribute.getBg();//背景
            //组装上传到IPFS的json文件
            Map<String, Object> map = new HashMap<>(4);
            map.put("name", "云链IP人物#" + tokenId);
            map.put("image", cid);
            map.put("description", "云链首发IP人物NFT数字藏品");
            
            List<Map<String,String>> attributes = new ArrayList<>(8);
            Map<String,String> sourceMap = new HashMap<>(2);
            sourceMap.put("type","来源");
            sourceMap.put("value",domain);
            attributes.add(sourceMap);
            
            Map<String,String> headwearMap = new HashMap<>(2);
            headwearMap.put("type","头饰");
            headwearMap.put("value",headwear);
            attributes.add(headwearMap);
    
            Map<String,String> glassesMap = new HashMap<>(2);
            glassesMap.put("type","饰品");
            glassesMap.put("value",glasses);
            attributes.add(glassesMap);
    
            Map<String,String> emoteMap = new HashMap<>(2);
            emoteMap.put("type","表情");
            emoteMap.put("value",emote);
            attributes.add(emoteMap);
    
            Map<String,String> headMap = new HashMap<>(2);
            headMap.put("type","头套");
            headMap.put("value",head);
            attributes.add(headMap);
    
            Map<String,String> clothesMap = new HashMap<>(2);
            clothesMap.put("type","衣服");
            clothesMap.put("value",clothes);
            attributes.add(clothesMap);
    
            Map<String,String> bodyMap = new HashMap<>(2);
            bodyMap.put("type","身体");
            bodyMap.put("value",body);
            attributes.add(bodyMap);
    
            Map<String,String> bgMap = new HashMap<>(2);
            bgMap.put("type","背景");
            bgMap.put("value",bg);
            attributes.add(bgMap);
            
            map.put("attributes", attributes);
    
            //写入IPFS
            IpfsUtil.fileWriteNFT(contractAddress,tokenId.toString(),JSONUtil.toJsonStr(map));
    
            ipfsImage.setDomain(domain);
            ipfsImage.setTokenId(tokenId);
            ipfsImage.setUpdateTime(new Date());
            ipfsImage.setState(IpfsImage.STATE_USED);
    
            ipfsImageAttribute.setState(IpfsImageAttribute.state2);
            ipfsImageAttributeMapper.updateById(ipfsImageAttribute);
        }
        //3
        String pathCid = IpfsUtil.getJsonPathCid(contractAddress);
        Map<String, Object> returnMap = new HashMap<>(2);
        returnMap.put("imageVos", imageVos);
        returnMap.put("pathCid", pathCid);
        String returnStr = JSONUtil.toJsonStr(returnMap);
        //4
        List<DomainAccount> updateDomainAccounts = domainAccounts.stream().peek(item -> item.setNftGetState(DomainAccount.NFT_GET_STATE_RECEIVED)).collect(Collectors.toList());
        domainAccountMapper.updateBatchSelective(updateDomainAccounts);
        ipfsImageMapper.updateBatchSelective(ipfsImages);

        //5
        List<String> domains = imageVos.stream().map(IpfsImageVo::getDomain).collect(Collectors.toList());
        // NftGetRecord nftGetRecord = NftGetRecord.builder().title("领取NFT")
        //         .getAccountUuid(uuid)
        //         .getAccountAddress(chainAccountAddress)
        //         .opbChainId(opbChainId)
        //         .status(NftGetRecord.status2)
        //         .getCount(count)
        //         .isSuccess(isSuccess)
        //         .domain(domains.toString())
        //         .build();
        // nftGetRecordMapper.insert(nftGetRecord);

        record.setIsSuccess(isSuccess);
        record.setDomain(domains.toString());
        nftGetRecordMapper.updateById(record);
        return returnStr;
    }

    @Override
    public InsertIPFSResp insertNftRecordV2(Long nftRecordId, NftGetRecordDomain nftGetRecordDomain , String uuid, String chainAccountAddress, Integer opbChainId, Integer isSuccess, Integer tokenId, String contractAddress) {
        InsertIPFSResp insertIPFSResp= new InsertIPFSResp();
        NftGetRecord record = nftGetRecordMapper.selectById(nftRecordId);
        Assert.notNull(record, "记录不存在");

        Assert.equals(record.getGetAccountUuid(),uuid,"领取账户信息不匹配");
        
        //失败
        if (!Objects.equals(isSuccess, NftGetRecord.ISSUCCESS_SUCCESS)) {
            record.setIsSuccess(isSuccess);
            nftGetRecordMapper.updateById(record);
            return null;
        }


        //1.查询可领取NFT的域名信息
        DomainAccount domainAccount = domainAccountMapper.queryOneByNFTByStatus(uuid, DomainAccount.NFT_GET_STATE_UNRECEIVED);
        Assert.notNull(domainAccount, "IPFS校验 铸币数量非法,没有足够的域名可领取NFT");
        String domain = domainAccount.getDomain();
        //2
        Boolean isTokenExist = ipfsImageMapper.countByContractAndTokenId(contractAddress, tokenId);
        if (isTokenExist) {
            log.error("存在重复铸币的TOKEN,contractAddress:{},tokenIds:{}",contractAddress,tokenId);
            throw new ServiceException("IPFS校验 存在重复铸币的TOKEN");
        }
        //2.查询未使用的图片,组装ipfs-json信息上传
        IpfsImage ipfsImage = ipfsImageMapper.queryOneByStateAndContract(contractAddress, IpfsImage.STATE_UNUSED);
        Assert.notNull(ipfsImage, "IPFS校验 铸币数量非法,没有足够的NFT-IPFS文件可使用,contractAddress:" + contractAddress);

        Integer index = ipfsImage.getIndex();
        //图片属性信息
        IpfsImageAttribute ipfsImageAttribute = ipfsImageAttributeMapper.queryByStateAndIndex(IpfsImageAttribute.state1, index);
        Assert.notNull(ipfsImageAttribute, "IPFS校验 铸币失败,NFT属性值数据异常.图片数量和属性数量不匹配 "+index);

        Date noewDate = new Date();

        //组装参数上IPFS  上传json文件  修改 ipfs表和attribute表的状态
        assembleIpfsAttributeInfo(contractAddress, tokenId, ipfsImage,domain, ipfsImageAttribute);
        //3.查询最新目录CID
        String pathCid = IpfsUtil.getJsonPathCid(contractAddress);
        insertIPFSResp.setTokenId(tokenId);
        insertIPFSResp.setCid(ipfsImage.getCid());
        insertIPFSResp.setDomain(domain);
        insertIPFSResp.setPathCid(pathCid);
        //4.修改域名领取NFT状态
        domainAccount.setNftGetState(DomainAccount.NFT_GET_STATE_RECEIVED);
        domainAccountMapper.updateById(domainAccount);

        //5.修改领取NFT记录状态

        //修改nftGetRecordDomain的数据
        nftGetRecordDomain.setDomian(domain);
        nftGetRecordDomain.setTokenId(tokenId);
        nftGetRecordDomain.setStatus(NftGetRecordDomain.STATUS_RECEIVED);
        nftGetRecordDomain.setUpdateTime(noewDate);
        nftGetRecordDomainMapper.updateById(nftGetRecordDomain);

        Integer getCount = record.getGetCount();
        Integer receivedCount = nftGetRecordDomainMapper.countByRecordIdAndStatus(nftRecordId, NftGetRecordDomain.STATUS_RECEIVED);
        if(getCount.equals(receivedCount)){
            record.setIsSuccess(isSuccess);
            nftGetRecordMapper.updateById(record);

        }
        return insertIPFSResp;
    }

    private void assembleIpfsAttributeInfo(String contractAddress, Integer tokenId, IpfsImage ipfsImage, String domain,IpfsImageAttribute ipfsImageAttribute) {
        String cid = ipfsImage.getCid();

        String headwear = ipfsImageAttribute.getHeadwear();//头饰
        String glasses = ipfsImageAttribute.getGlasses();//饰品
        String emote = ipfsImageAttribute.getEmote();//表情
        String head = ipfsImageAttribute.getHead();//头套
        String clothes = ipfsImageAttribute.getClothes();//衣服
        String body = ipfsImageAttribute.getBody();//身体
        String bg = ipfsImageAttribute.getBg();//背景
        //组装上传到IPFS的json文件
        Map<String, Object> map = new HashMap<>(4);
        map.put("name", "云链IP人物#" + tokenId);
        map.put("image", cid);
        map.put("description", "云链首发IP人物NFT数字藏品");

        List<Map<String,String>> attributes = new ArrayList<>(8);
        Map<String,String> sourceMap = new HashMap<>(2);
        sourceMap.put("type","来源");
        sourceMap.put("value",domain);
        attributes.add(sourceMap);

        Map<String,String> headwearMap = new HashMap<>(2);
        headwearMap.put("type","头饰");
        headwearMap.put("value",headwear);
        attributes.add(headwearMap);

        Map<String,String> glassesMap = new HashMap<>(2);
        glassesMap.put("type","饰品");
        glassesMap.put("value",glasses);
        attributes.add(glassesMap);

        Map<String,String> emoteMap = new HashMap<>(2);
        emoteMap.put("type","表情");
        emoteMap.put("value",emote);
        attributes.add(emoteMap);

        Map<String,String> headMap = new HashMap<>(2);
        headMap.put("type","头套");
        headMap.put("value",head);
        attributes.add(headMap);

        Map<String,String> clothesMap = new HashMap<>(2);
        clothesMap.put("type","衣服");
        clothesMap.put("value",clothes);
        attributes.add(clothesMap);

        Map<String,String> bodyMap = new HashMap<>(2);
        bodyMap.put("type","身体");
        bodyMap.put("value",body);
        attributes.add(bodyMap);

        Map<String,String> bgMap = new HashMap<>(2);
        bgMap.put("type","背景");
        bgMap.put("value",bg);
        attributes.add(bgMap);

        map.put("attributes", attributes);

        //写入IPFS
        IpfsUtil.fileWriteNFT(contractAddress,tokenId.toString(),JSONUtil.toJsonStr(map));

        ipfsImage.setDomain(domain);
        ipfsImage.setTokenId(tokenId);
        ipfsImage.setUpdateTime(new Date());
        ipfsImage.setState(IpfsImage.STATE_USED);
        ipfsImageMapper.updateById(ipfsImage);

        ipfsImageAttribute.setState(IpfsImageAttribute.state2);
        ipfsImageAttributeMapper.updateById(ipfsImageAttribute);
    }



    @Override
    public void validateMintInfo(String uuid, Integer count, String contractAddress, List<Integer> tokenIds) {
        List<DomainAccount> domainAccounts = domainAccountMapper.selectGetNftList(uuid,count);
        if (domainAccounts.size() < count) {
            throw new ServiceException("IPFS校验 铸币数量非法,没有足够的域名可领取NFT");
        }
        //2

        Integer tokenIdsCount = ipfsImageMapper.countByContractAndTokenIds(contractAddress, tokenIds);
        if (tokenIdsCount > 0) {
            log.error("存在重复铸币的TOKEN,contractAddress:{},tokenIds:{}",contractAddress,tokenIds);
            throw new ServiceException("IPFS校验 存在重复铸币的TOKEN");
        }
        List<IpfsImage> ipfsImages = ipfsImageMapper.selectList(new LambdaQueryWrapper<IpfsImage>()
                .eq(IpfsImage::getState, IpfsImage.STATE_UNUSED)
                .eq(IpfsImage::getContractAddress, contractAddress)
                .last("LIMIT " + count));
        if (ipfsImages.size() < count) {
            throw new ServiceException("IPFS校验 铸币数量非法,没有足够的NFT-IPFS文件可使用,contractAddress:" + contractAddress);
        }
        List<Integer> indexList = ipfsImages.stream().map(IpfsImage::getIndex).collect(Collectors.toList());
        //图片属性信息
        List<IpfsImageAttribute> ipfsImageAttributes = ipfsImageAttributeMapper.selectList(new LambdaQueryWrapper<IpfsImageAttribute>()
                .eq(IpfsImageAttribute::getState, IpfsImageAttribute.state1)
                .in(IpfsImageAttribute::getIndex,indexList));
        if (ipfsImages.size() != ipfsImageAttributes.size()){
            log.error("IPFS校验 铸币失败,NFT属性值数据异常.图片数量和属性数量不匹配{}",indexList);
            throw new ServiceException("IPFS校验 NFT属性值数据异常");
        }
    }

    public static void main(String[] args) {
        List<String> test=new ArrayList<>();
        Assert.notEmpty(test,"test不能为空");
    }

    @Override
    public void validateMintInfoV2(String uuid,  String contractAddress, Integer tokenId) {
        DomainAccount domainAccount = domainAccountMapper.queryOneByNFTByStatus(uuid, DomainAccount.NFT_GET_STATE_UNRECEIVED);
        Assert.notNull(domainAccount, "IPFS校验 铸币数量非法,没有足够的域名可领取NFT");
        //2
        Boolean isTokenExist = ipfsImageMapper.countByContractAndTokenId(contractAddress, tokenId);
        if (isTokenExist) {
            log.error("存在重复铸币的TOKEN,contractAddress:{},tokenIds:{}",contractAddress,tokenId);
            throw new ServiceException("IPFS校验 存在重复铸币的TOKEN");
        }

        IpfsImage ipfsImage = ipfsImageMapper.queryOneByStateAndContract(contractAddress, IpfsImage.STATE_UNUSED);
        Assert.notNull(ipfsImage, "IPFS校验 铸币数量非法,没有足够的NFT-IPFS文件可使用,contractAddress:" + contractAddress);

        Integer index = ipfsImage.getIndex();
        //图片属性信息
        IpfsImageAttribute ipfsImageAttribute = ipfsImageAttributeMapper.queryByStateAndIndex(IpfsImageAttribute.state1, index);
        Assert.notNull(ipfsImageAttribute, "IPFS校验 铸币失败,NFT属性值数据异常.图片数量和属性数量不匹配 "+index);

    }




}

