package com.lj.auth.common.filter;

import java.io.IOException;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;



/**
 * @author: wxm
 * @description:
 * @date: 2025/6/5 17:29
 */
@Component
public class CachingRequestFilter extends OncePerRequestFilter {

    @Override
    protected void doFilterInternal(HttpServletRequest request,
                                    HttpServletResponse response,
                                    Filter<PERSON>hain filterChain) throws ServletException, IOException {
        if (request.getMethod().equalsIgnoreCase("GET")) {
            filterChain.doFilter(request, response);
            return;
        }
        if (request.getContentType() == null || !request.getContentType().contains("application/json")) {
            filterChain.doFilter(request, response);
            return;
        }
        //如果是文件上传，则不缓存
        if (request.getContentType().contains("multipart/form-data")) {
            filterChain.doFilter(request, response);
            return;
        }
        //如果是表单提交，则使用CachedParamsHttpServletRequest包装request
//        if (request.getContentType().contains("application/x-www-form-urlencoded")) {
//            CachedParamsHttpServletRequest wrappedRequest = new CachedParamsHttpServletRequest(request);
//            filterChain.doFilter(wrappedRequest, response);
//            return;
//        }
        if(request.getContentType().contains("application/json")){
            // 处理application/json类型的请求，对json请求做缓存处理
            CachedBodyHttpServletRequest wrappedRequest = new CachedBodyHttpServletRequest(request);
            filterChain.doFilter(wrappedRequest, response);
        }else if(request.getContentType().contains("multipart/form-data")){
            // 处理表单提交
//            CachedParamsHttpServletRequest wrappedRequest = new CachedParamsHttpServletRequest(request);
            filterChain.doFilter(request, response);
        }else{
            filterChain.doFilter(request, response);
        }
    }
}
