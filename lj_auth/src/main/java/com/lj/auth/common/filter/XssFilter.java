package com.lj.auth.common.filter;

import java.io.IOException;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;


import com.lj.auth.util.PropertiesRead;
import lombok.extern.slf4j.Slf4j;

/**
 * XSS过滤
 *
 * <AUTHOR> sunlight<PERSON>@gmail.com
 */
@Slf4j
public class XssFilter implements Filter {
    static  String  flag;
    static {
       flag= PropertiesRead.getYmlStringForActive("filterFlag");
        // flag= "true";
    }

    @Override
    public void init(FilterConfig config) throws ServletException {
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        XssHttpServletRequestWrapper xssRequest = new XssHttpServletRequestWrapper(
                (HttpServletRequest) request);
        HttpServletResponse httpServletResponse = (HttpServletResponse) response;
        //转换成代理类
        ResponseWrapper wrapperResponse = new ResponseWrapper(httpServletResponse);
        if(flag.equals("true")) {
            chain.doFilter(xssRequest, wrapperResponse);
            //一下是对结果加密
            byte[] bytes = wrapperResponse.getContent();
//            log.error(new String(bytes));
//            log.error(Des3Util.encryptThreeDESECB(new String(bytes), "666666666666666666666666"));
            //此處的bytes為response返回來的數據，根據自身需求就response数据进行压缩，或者是进行加密等等一系列操作
            wrapperResponse.setContentLength(-1);
            ServletOutputStream out = response.getOutputStream();
            out.write(bytes);
            out.flush();
        }else  {
            chain.doFilter(request, response);
        }
    }

    @Override
    public void destroy() {
    }

}
