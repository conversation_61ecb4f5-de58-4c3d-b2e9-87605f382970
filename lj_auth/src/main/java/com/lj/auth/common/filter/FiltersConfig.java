package com.lj.auth.common.filter;

import javax.servlet.DispatcherType;

import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Filter配置
 *
 * <AUTHOR>
 */
@Configuration
public class FiltersConfig {
    
    @Bean
    public FilterRegistrationBean xssFilterRegistration() {
        FilterRegistrationBean registration = new FilterRegistrationBean();
        //该值缺省为false，表示生命周期由SpringApplicationContext管理，设置为true则表示由ServletContainer管理
        registration.addInitParameter("targetFilterLifecycle", "true");
        registration.setDispatcherTypes(DispatcherType.REQUEST);
        registration.setFilter(new XssFilter());
        registration.setEnabled(true);
        registration.addUrlPatterns("/about/*",
                "/accountAccredit/*",
                "/account/*", "/bank/*",
                "/domainAccount/*",
                "/domainIntention/*",
                "/domainLeakage/*",
                "/domainMarket/*",
                "/domainRecommend/*",
                "/promotion/*",
                "/file/*",
                "/orderBsnInfo/*",
                "/payment/*",
                "/rechargeAssets/*",
                "/rechargeFlow/*",
                "/withdraw/*",
                "/ym/*",
                "/aggregatePay/h5Pay",
                "/aggregatePay/h5Close",
                "/aggregatePay/h5Query",
                "/aggregatePay/h5Search",
                "/aggregatePay/pcPay",
                "/aggregatePay/pcUpdateQrCode",
                "/aggregatePay/pcCloseQrCode",
                "/aggregatePay/pcQuery",
                "/aggregatePay/pcQueryQrCodeInfo",
                "/aggregatePay/appPay",
                "/aggregatePay/appClose",
                "/aggregatePay/appQuery",
                "/cart/*",
                "/addresseeMessage/*",
                "/invoiceAccount/*",
                "/invoiceMessage/*",
                "/message/*",
                "/appVersion/*",
                "/attention/*",
                "/notice/*",
                "/api/wx-pay/miniAPP",
                "/douYinPay/createOrder",
                "/douYinPay/outOrderNo",
                "/douYinPay/pushOrder",
                "/customerService/*",
                "/promotionInterest/*",
                "/operateService/*",
                "/applyOperate/*",
                "/coupon/*",
                "/couponAccount/*",
                "/globalConfig/*",
                "/api/mini-pay/*",
                "/accountDid/*",
                "/accountInfoVoucher/applySuccess"
                ,"/accountInfoVoucher/list"
                ,"/did/applyFee"
                ,"/did/applyFeeByType"
                ,"/accountDidDevice/*"
                ,"/accountDidDomain/*"
                ,"/didIllustrate/*"
                ,"/fusui/uploadPeople"
                ,"/fusui/queryPeople"
                ,"/fusui/queryAmount"
                ,"/fusui/queryPlan"
                ,"/voucherAccredit/*"
                ,"/ecology/*"
                ,"/nftGetRecord/*"
                ,"/order/*"
                
        );
        registration.setName("xssFilter");
        registration.setOrder(Integer.MAX_VALUE);
        return registration;
    }
}
