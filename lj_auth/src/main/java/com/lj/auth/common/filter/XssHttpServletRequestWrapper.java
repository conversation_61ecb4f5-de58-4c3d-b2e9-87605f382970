package com.lj.auth.common.filter;


import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.LinkedHashMap;
import java.util.Map;

import javax.servlet.ReadListener;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;

import cn.hutool.core.util.StrUtil;
import com.lj.auth.util.Des3Util;
import org.apache.commons.io.IOUtils;

import org.springframework.http.HttpHeaders;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;


import lombok.extern.slf4j.Slf4j;


/**
 * XSS过滤处理
 *
 * <AUTHOR> <PERSON>@gmail.com
 */
@Slf4j
public class XssHttpServletRequestWrapper extends HttpServletRequestWrapper {
    //没被包装过的HttpServletRequest（特殊场景，需要自己过滤）
    HttpServletRequest orgRequest;
    //html过滤
    private final static HTMLFilter htmlFilter = new HTMLFilter();

    public XssHttpServletRequestWrapper(HttpServletRequest request) {
        super(request);
        orgRequest = request;
        String type = super.getHeader(HttpHeaders.CONTENT_TYPE);
        if (StrUtil.isNotBlank(type) && type.contains("application/json")){
            try {
                super.getInputStream();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public ServletInputStream getInputStream() throws IOException {
        //非json类型，直接返回
        // if (!MediaType.APPLICATION_JSON_VALUE.equalsIgnoreCase(super.getHeader(HttpHeaders.CONTENT_TYPE))) {
        //     return super.getInputStream();
        // }
        if (!super.getHeader(HttpHeaders.CONTENT_TYPE).contains("application/json")){
            return super.getInputStream();
        }

        //为空，直接返回
        String json = IOUtils.toString(super.getInputStream(), "utf-8");
        if (StrUtil.isBlank(json)) {
            return super.getInputStream();
        }

        //xss过滤
        json = xssEncode(json);
        String s ;
        try {
            JSONObject jsonObject = JSONObject.parseObject(json);
            JSONObject dataObject = new JSONObject();
            for (String key : jsonObject.keySet()) {
                String value = (String) jsonObject.get(key);
                String str  = Des3Util.decryptThreeDESECB(value);
                dataObject.put(key,str);
            }
            s = dataObject.toString();
        }catch (Exception exception){
            JSONArray dataArray = new JSONArray();
            JSONArray jsonArray = JSONArray.parseArray(json);
            for (int i = 0; i < jsonArray.size(); i++) {
                Object obj = jsonArray.get(i);
                if (obj instanceof JSONArray){
            
                }else if (obj instanceof JSONObject){
                    JSONObject jsonObject = jsonArray.getJSONObject(i);
                    JSONObject dataObject = new JSONObject();
                    for (String key : jsonObject.keySet()) {
                        String value = (String) jsonObject.get(key);
                        String str  = Des3Util.decryptThreeDESECB(value);
                        dataObject.put(key,str);
                        try {
                            Integer i1 = Integer.parseInt(str);
                            dataObject.put(key,i1);
                        }catch (NumberFormatException e){
                            try {
                                Long i1 = Long.valueOf(str);
                                dataArray.add(i1);
                            }catch (NumberFormatException e1){
                        
                            }
                        }
                    }
                    dataArray.add(dataObject);
                }else{
                    String value = (String) jsonArray.get(i);
                    String str  = Des3Util.decryptThreeDESECB(value);
                    try {
                        Integer i1 = Integer.parseInt(str);
                        dataArray.add(i1);
                    }catch (NumberFormatException e){
                        try {
                            Long i1 = Long.valueOf(str);
                            dataArray.add(i1);
                        }catch (NumberFormatException e1){
                            dataArray.add(str);
                        }
                    }
                }
        
            }
            s = dataArray.toString();
        }
        
        final ByteArrayInputStream bis = new ByteArrayInputStream(s.getBytes("utf-8"));
        return new ServletInputStream() {
            @Override
            public boolean isFinished() {
                return true;
            }

            @Override
            public boolean isReady() {
                return true;
            }

            @Override
            public void setReadListener(ReadListener readListener) {

            }

            @Override
            public int read() throws IOException {
                return bis.read();
            }
        };
    }

    @Override
    public String getParameter(String name) {
        String value = super.getParameter(xssEncode(name));
        if (StrUtil.isNotBlank(value)) {
            //参数解密
            value = Des3Util.decryptThreeDESECB(value);
            value = xssEncode(value);
        }
        return value;
    }

    @Override
    public String[] getParameterValues(String name) {
        String[] parameters = super.getParameterValues(name);
        if (parameters == null || parameters.length == 0) {
            return null;
        }

        for (int i = 0; i < parameters.length; i++) {
            //参数解密
            parameters[i] = Des3Util.decryptThreeDESECB(parameters[i]);
            parameters[i] = xssEncode(parameters[i]);
        }
        return parameters;
    }

    @Override
    public Map<String, String[]> getParameterMap() {
        Map<String, String[]> map = new LinkedHashMap<>();
        Map<String, String[]> parameters = super.getParameterMap();
        for (String key : parameters.keySet()) {
            String[] values = parameters.get(key);
            for (int i = 0; i < values.length; i++) {
                if("lang".equals(key)){
                    continue;
                }
                //参数解密
                values[i] = Des3Util.decryptThreeDESECB(values[i]);
                values[i] = xssEncode(values[i]);
            }
            map.put(key, values);
        }
        return map;
    }

    @Override
    public String getHeader(String name) {
        String value = super.getHeader(xssEncode(name));
        if (StrUtil.isNotBlank(value)) {
            value = xssEncode(value);
        }
        return value;
    }

    private String xssEncode(String input) {
        return htmlFilter.filter(input);
    }

    /**
     * 获取最原始的request
     */
    public HttpServletRequest getOrgRequest() {
        return orgRequest;
    }

    /**
     * 获取最原始的request
     */
    public static HttpServletRequest getOrgRequest(HttpServletRequest request) {
        if (request instanceof XssHttpServletRequestWrapper) {
            return ((XssHttpServletRequestWrapper) request).getOrgRequest();
        }

        return request;
    }

}
