package com.lj.auth.common;

import java.io.Serializable;

public class ResultInfo<T> implements Serializable {
    private Integer code;
    private String msg;
    private T data;

    public ResultInfo() {
    }

    public Integer getCode() {
        return this.code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }
    
    public String getMsg() {
        return msg;
    }
    
    public void setMsg(String msg) {
        this.msg = msg;
    }
    
  
    public T getData() {
        return this.data;
    }

    public void setData(T data) {
        this.data = data;
    }
    
    public static ResultInfo<String> success() {
        ResultInfo<String> result = new ResultInfo();
        result.setCode(CommonConstant.SUCCESS);
        result.setMsg("SUCCESS");
        result.setData("");
        return result;
    }
    
    public static <T> ResultInfo<T> success(T data) {
        ResultInfo<T> result = new ResultInfo();
        result.setCode(CommonConstant.SUCCESS);
        result.setMsg("SUCCESS");
        result.setData(data);
        return result;
    }
    
    public static <T> ResultInfo<T> error(String message) {
        ResultInfo<T> result = new ResultInfo();
        result.setCode(CommonConstant.ERROR);
        result.setMsg(message);
        return result;
    }
    
    public static <T> ResultInfo<T> error(Integer code, String message) {
        ResultInfo<T> result = new ResultInfo();
        result.setCode(code);
        result.setMsg(message);
        return result;
    }

    public static <T> ResultInfo<T> error(Integer code, String message, Class<T> classes) {
        ResultInfo<T> result = new ResultInfo();
        result.setCode(code);
        result.setMsg(message);
        return result;
    }
}
