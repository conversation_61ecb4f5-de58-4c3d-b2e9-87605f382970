package com.lj.auth.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
    * 黑名单地址表
    */
@Data
@TableName(value = "sp_address_blacklist")
public class SpAddressBlacklist implements Serializable {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 黑名单地址
     */
    @TableField(value = "address")
    private String address;

    /**
     * 加入黑名单原因
     */
    @TableField(value = "reason")
    private String reason;

    /**
     * 0:未启用 1:启用
     */
    @TableField(value = "avtive")
    private Integer avtive;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}