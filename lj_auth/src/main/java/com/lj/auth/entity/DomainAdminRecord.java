package com.lj.auth.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 域名管理记录
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("lj_auth_domain_admin_record")
public class DomainAdminRecord implements Serializable {

    private static final long serialVersionUID=1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 1-处理中2-处理完成3-处理失败
     */
    private Integer state;

    /**
     * 账户uuid
     */
    private String accountUuid;

    /**
     * 域名设置所有者流水号
     */
    private String domianServeSn;

    /**
     * 交易流水号
     */
    private String tradeCode;

    /**
     * 商户订单号
     */
    private String outTradeNo;

    /**
     * 链id
     */
    private Integer chainId;

    /**
     * 所有者地址
     */
    private String owner;

    /**
     * 管理者地址
     */
    private String manager;

    /**
     * 解析信息
     */
    private String resolveInfos;

    /**
     * 提交时间
     */
    private LocalDateTime subTime;

    /**
     * 域名
     */
    private String domain;

    /**
     * 域名等级 1:一级域名 2:子域名
     */
    private Integer level;

    /**
     * 所有者地址是否上链true-上链false-未上链
     */
    private Boolean isUpchain;

    /**
     * 1=设置所有者2=设置管理者3=设置解析记录
     */
    private Integer type;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;


}
