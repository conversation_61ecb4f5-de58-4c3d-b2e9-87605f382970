package com.lj.auth.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 链上交易记录表
 */
@Data
@TableName(value = "sp_transaction_records")
public class SpTransactionRecords implements Serializable {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 链ID，对应不同区块链
     */
    @TableField(value = "chain_id")
    private Long chainId;

    /**
     * 交易哈希，链上唯一
     */
    @TableField(value = "tx_hash")
    private String txHash;

    /**
     * 发送方地址
     */
    @TableField(value = "from_address")
    private String fromAddress;

    /**
     * 接收方地址
     */
    @TableField(value = "to_address")
    private String toAddress;

    /**
     * 币种符号，例如 ETH、USDT
     */
    @TableField(value = "symbol")
    private String symbol;

    /**
     * 代币合约地址（主币为NULL）
     */
    @TableField(value = "contract_address")
    private String contractAddress;

    /**
     * 转账数量
     */
    @TableField(value = "amount")
    private BigDecimal amount;

    /**
     * 交易类型：1=转主币，2=代币转账，3=合约交互
     */
    @TableField(value = "tx_type")
    private Integer txType;

    /**
     * 实际消耗的Gas费
     */
    @TableField(value = "gas_fee")
    private BigDecimal gasFee;

    /**
     * 状态：0=待确认，1=成功，2=失败
     */
    @TableField(value = "`status`")
    private Integer status;

    /**
     * 所在区块号
     */
    @TableField(value = "block_number")
    private Long blockNumber;

    /**
     * 交易时间
     */
    @TableField(value = "transaction_time")
    private String transactionTime;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}