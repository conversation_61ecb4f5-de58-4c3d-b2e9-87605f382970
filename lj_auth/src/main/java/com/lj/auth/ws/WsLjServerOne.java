package com.lj.auth.ws;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArraySet;

import javax.websocket.OnClose;
import javax.websocket.OnError;
import javax.websocket.OnMessage;
import javax.websocket.OnOpen;
import javax.websocket.Session;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;

import com.lj.auth.exception.ServiceException;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;

import lombok.extern.slf4j.Slf4j;

/**
 * websocket 浏览器服务端
 */
@Slf4j
@Component
@ServerEndpoint(value = "/WS/DIDSign/{userId}", subprotocols = {"protocol"})
public class WsLjServerOne {

    private static int onlineCount = 0;

    private static CopyOnWriteArraySet<WsLjServerOne> clientWsSet = new CopyOnWriteArraySet<>();

    private static ConcurrentHashMap<String, WsLjServerOne> webSocketMap = new ConcurrentHashMap<>();

    private String userId;

    private Session session;

    @OnOpen
    public void onOpen(Session session, @PathParam("userId") String userId) {
        this.userId = userId;
        this.session = session;
        clientWsSet.add(this);
        webSocketMap.put(userId, this);
        addOnlineCount();
        log.info("新窗口开始监听: {}, 当前在线人数为 {}", userId, getOnlineCount());
        try {
            Map<String, Object> result = new HashMap<>();
            result.put("code", 200);
            result.put("message", "连接成功");
            sendMessage(JSON.toJSONString(result));
        } catch (IOException e) {
            throw new ServiceException("websocket IO异常!!!");
        }
    }

    @OnClose
    public void onClose() {
        log.info("{} 连接关闭", session.getId());
        clientWsSet.remove(this);
        webSocketMap.remove(userId);
        subOnlineCount();
        log.info("当前在线人数为: {}", getOnlineCount());
    }

    @OnMessage
    public void onMessage(String message, Session session) {
        log.info("{} 说: {}", userId, message);
        // 可以考虑只广播给特定用户，或者处理消息
    }

    @OnError
    public void onError(Session session, Throwable error) {
        log.error("Error in connection: {}", error.getMessage());
        error.printStackTrace();
    }

    public void sendMessage(String message) throws IOException {
        synchronized (session) {
            session.getBasicRemote().sendText(message);
        }
    }

    public static void sendToUser(String userId, String message) throws IOException {
        WsLjServerOne wsClient = webSocketMap.get(userId);
        if (wsClient != null) {
            wsClient.sendMessage(message);
        } else {
            log.warn("尝试向未连接的用户 {} 发送消息", userId);
        }
    }

    private static synchronized int getOnlineCount() {
        return onlineCount;
    }

    private static synchronized void addOnlineCount() {
        onlineCount++;
    }

    private static synchronized void subOnlineCount() {
        onlineCount--;
    }
}