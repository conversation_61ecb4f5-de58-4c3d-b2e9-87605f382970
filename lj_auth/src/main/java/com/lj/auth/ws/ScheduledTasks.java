package com.lj.auth.ws;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class ScheduledTasks {

    @Resource
    private PushInfo pushInfo;


    /**
     * 推送中移链首页信息 2秒推送一次
     */
//    @Scheduled(cron = "*/2 * * * * ?")
    public void pushXsuperInfo() {
        Map<String, Object> message = new HashMap<>();
        message.put("failReason", "failReason");
        pushInfo.pushBrowserEthInfo("",message);
    }



}
