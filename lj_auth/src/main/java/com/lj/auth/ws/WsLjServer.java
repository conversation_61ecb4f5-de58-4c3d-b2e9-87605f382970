package com.lj.auth.ws;

import java.io.IOException;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArraySet;

import javax.websocket.*;
import javax.websocket.server.ServerEndpoint;

import com.lj.auth.exception.ServiceException;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;


import lombok.extern.slf4j.Slf4j;

/**
 * websocket 浏览器服务端
 * 
 * <AUTHOR>
@Slf4j
@Component
// @ServerEndpoint(value = "/server/{userId}")
@ServerEndpoint(value = "/WS/DIDSign", subprotocols = {"protocol"})
public class WsLjServer {

    /**
     * 静态变量，用来记录当前在线连接数。应该把它设计成线程安全的
     */
    private static int onlineCount = 0;

    // 用来存放每个客户端对应的WsScanServer对象
    private static CopyOnWriteArraySet<WsLjServer> clientWsSet =
        new CopyOnWriteArraySet<WsLjServer>();

    /**
     * concurrent 包的线程安全Set，用来存放每个客户端对应的 myWebSocket对象 根据userId来获取对应的 WebSocket
     */
    private static ConcurrentHashMap<String, WsLjServer> webSocketMap = new ConcurrentHashMap<>();

    /**
     * 接收 sid
     */
    private String userId = "";

    // 与某个客户端的连接会话，需要通过它来给客户端发送数据
    private Session session;

    /**
     * 收到客户端消息后调用的方法
     *
     * @param message 客户端发送过来的消息
     * @param session
     */
    @OnMessage
    public void onMessage(String message, Session session) throws IOException {
        // 群发消息
        for (WsLjServer clientWebSocket : clientWsSet) {
            clientWebSocket.session.getBasicRemote().sendText(session.getId() + "说：" + message);
            // myWebSocket.session.getBasicRemote().sendText("<img src=''/>");
        }
    }

    /**
     * 连接建立成功调用的方法
     * 
     * @param session
     */
    @OnOpen
    public void onOpen(Session session) {
        System.out.println(session.getId() + " open...");

        addOnlineCount(); // 在线数 +1
        log.info("有新窗口开始监听:" + userId + ",当前在线人数为" + getOnlineCount());
        this.session = session;
        clientWsSet.add(this);
        try {
             sendMessage(JSON.toJSONString("连接成功"));
        } catch (IOException e) {
            e.printStackTrace();
            throw new ServiceException("websocket IO异常！！！！");
        }

    }

    /**
     * 关闭连接
     */
    @OnClose
    public void onClose() {
        log.info(this.session.getId() + "连接关闭");
        clientWsSet.remove(this);
        subOnlineCount(); // 人数 -1
        log.info("当前在线人数为：" + getOnlineCount());
    }

    /**
     * 实现服务器主动推送消息到客户端
     * 
     * @param message
     * @throws IOException
     */
    public void sendMessage(String message) throws IOException {
        synchronized (session) {
            // session.getAsyncRemote().sendText(message);
            session.getBasicRemote().sendText(message);
        }
        // this.session.getBasicRemote().sendText(message);
    }

    /**
     * 群发自定义消息
     *
     * @param message
     * @throws IOException
     */
    public static void sendInfo(String message) throws IOException {

        // 遍历集合，可设置为推送给指定sid，为 null 时发送给所有人
        // 群发消息
        for (WsLjServer clientWebSocket : clientWsSet) {
            // myWebSocket.session.getBasicRemote().sendText("<img src=''/>");
            clientWebSocket.sendMessage(message);
        }
    }

    /**
     * @param session
     * @param error
     */
    @OnError
    public void onError(Session session, Throwable error) {
        log.info(this.session.getId() + " error..." + error.getMessage());
        error.printStackTrace();
    }

    private static synchronized int getOnlineCount() {
        return onlineCount;
    }

    private static synchronized void addOnlineCount() {
        WsLjServer.onlineCount++;
    }

    private static synchronized void subOnlineCount() {
        WsLjServer.onlineCount--;
    }

}
