package com.lj.auth.ws;

import java.io.IOException;
import java.net.URI;

import javax.websocket.*;

import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;


@Slf4j
@Component
public class WsClient {


    private Session session;

    @OnOpen
    public void onOpen(Session session) throws IOException {
        this.session = session;
    }


    @OnMessage
    public void onMessage(String message) {
    }

    @OnError
    public void onError(Throwable t) {
        t.printStackTrace();
        log.error(t.getMessage());
    }
    /**
     * 连接关闭调用的方法
     * @throws Exception
     */
    @OnClose
    public void onClose() throws Exception{
    }

    /**
     * 关闭链接方法
     * @throws IOException
     */
    public void closeSocket() throws IOException{
        this.session.close();
    }

    /**
     * 发送消息方法。
     * @param message
     * @throws IOException
     */
    public void sendMessage(String message) throws IOException{
        this.session.getBasicRemote().sendText(message);
    }
    //启动客户端并建立链接
    public void start(String uri) {
        WebSocketContainer container = ContainerProvider.getWebSocketContainer();
        try {
            this.session = container.connectToServer(WsClient.class, URI.create(uri));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
