package com.lj.auth.ws;

import java.io.IOException;

import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;


import com.alibaba.fastjson.JSONObject;


import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component // 被Spring容器管理
@Order(1) // 如果多个自定义ApplicationRunner，用来表明执行顺序

/**
 * @ClassName PushAlarm
 * @Description
 **/
@Service
public class PushInfo {

    /**
     * 推浏览器学生信息
     */
    public void pushBrowserEthInfo(String userId,Object map) {
        String message = "";
        message = JSONObject.toJSONString(map);
        try {
            // 推送
//            WsLjServer.sendInfo(message);
            WsLjServerOne.sendToUser(userId,message);
        } catch (IOException e) {
            e.printStackTrace();
            log.error(e.getMessage());
        }
    }


    public void pushBrowserEthInfo1(String userId,String message) {
        try {
            // 推送
//            WsLjServer.sendInfo(message);
            WsLjServerOne.sendToUser(userId,message);
        } catch (IOException e) {
            e.printStackTrace();
            log.error(e.getMessage());
        }
    }

}
