package com.lj.auth.task;

import static com.lj.auth.common.CommonConstant.*;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.lj.auth.domain.RechargeWithdrawRecord;
import com.lj.auth.mapper.RechargeWithdrawRecordMapper;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.web3j.protocol.core.methods.response.TransactionReceipt;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lj.auth.domain.EnergyRechargeFlow;
import com.lj.auth.domain.SignIn;
import com.lj.auth.domain.SignInHist;
import com.lj.auth.mapper.EnergyRechargeFlowMapper;
import com.lj.auth.mapper.SignInHistMapper;
import com.lj.auth.mapper.SignInMapper;
import com.lj.auth.service.EnergyRechargeFlowService;
import com.lj.auth.util.BNBUtil;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 描述：校验修改终端用户充提订单表 待支付超时状态 创建人: CFJ 创建时间: 2024/04/27
 */
@Component
@Slf4j
public class CheckRechargeWithdrawState {
    @Resource
    private RechargeWithdrawRecordMapper rechargeWithdrawRecordMapper;

    /**
     * 每24小时执行一次
     *
     * <AUTHOR>
     * @date 2024/04/27
     */
    @Scheduled(cron = "0 0 0/24 * * ? ")
    // @Scheduled(cron = "0/10 * * * * ? ")
    public void checkEnergyState() {
        List<RechargeWithdrawRecord> result = rechargeWithdrawRecordMapper.getOver24Hours();
        if (CollectionUtil.isNotEmpty(result)) {
            List<Long> ids = result.stream().map(RechargeWithdrawRecord::getId).collect(Collectors.toList());
            rechargeWithdrawRecordMapper.update(null, Wrappers.<RechargeWithdrawRecord>lambdaUpdate()
                .in(RechargeWithdrawRecord::getId, ids).set(RechargeWithdrawRecord::getState, 5));
            log.info("修改待支付定时任务：{}", ids.stream().map(Object::toString).collect(Collectors.joining(",")));
        }
    }

}
