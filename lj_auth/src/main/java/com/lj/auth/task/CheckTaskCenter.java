package com.lj.auth.task;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lj.auth.domain.RechargeWithdrawRecord;
import com.lj.auth.domain.TaskCenter;
import com.lj.auth.mapper.RechargeWithdrawRecordMapper;
import com.lj.auth.mapper.TaskCenterMapper;
import com.lj.auth.service.GlobalConfigService;
import com.lj.auth.service.TaskCenterService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.weaver.ast.Var;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.lj.auth.common.CommonConstant.*;


/**
 * 描述：任务中心任务任务重置
 * 创建人: CFJ
 * 创建时间: 2024/05/30
 */
@Component
@Slf4j
public class CheckTaskCenter {
    @Resource
    private TaskCenterMapper taskCenterMapper;

    @Resource
    private TaskCenterService taskCenterService;
    @Resource
    private GlobalConfigService globalConfigService;


    /**
     * 日常任务每日凌晨重置
     *
     * <AUTHOR>
     * @date 2024/05/30
     */
    @Scheduled(cron = "0 0 0 * * ? ")
//    @Scheduled(cron = "0/5 * * * * ? ")
    public void dailyTasksReset() {
        log.info("日常任务修改开始");
        Date date = new Date();
        //统计未关闭的日常任务 且任务开始时间不等于今天
        List<TaskCenter> taskCenters = taskCenterMapper.selectList(Wrappers.<TaskCenter>lambdaQuery()
                .eq(TaskCenter::getTaskTypeId, 1)
                .eq(TaskCenter::getTaskState, 1)
                .ne(TaskCenter::getOpeningTime, DateUtil.beginOfDay(date))
        );
        if (CollectionUtil.isNotEmpty(taskCenters)) {

            //关闭之前的任务
            List<Integer> taskCenterIds = taskCenters.stream().map(TaskCenter::getId).collect(Collectors.toList());
            taskCenterMapper.update(null, Wrappers.<TaskCenter>lambdaUpdate().in(TaskCenter::getId, taskCenterIds)
                    .set(TaskCenter::getTaskState, 0)
            );
            LocalDateTime endOfDay = DateUtil.endOfDay(date).toLocalDateTime();
            endOfDay = endOfDay.withNano(0); // 移除纳秒部分，确保时间到秒级别
            Date dateWithoutMillis = Date.from(endOfDay.atZone(ZoneId.systemDefault()).toInstant());
            DateTime starTime = DateUtil.beginOfDay(date);
            //重新在插入任务 =>更改任务时间
            for (TaskCenter taskCenter : taskCenters) {
                taskCenter.setOpeningTime(starTime)
                        .setCompletionTime(dateWithoutMillis)
                ;
            }
            taskCenterService.saveBatch(taskCenters);
            log.info("日常任务修改完毕");
        }
    }


    /**
     * 周期任务每周一凌晨重置
     *
     * <AUTHOR>
     * @date 2024/05/30
     */
    @Scheduled(cron = "0 0 0 * * 1")
//    @Scheduled(cron = "0/5 * * * * ? ")
    public void cycleTasksReset() {
        log.info("周期任务修改开始");
        Date date = new Date();
        // 获取本周周一的日期，即本周开始时间
        Date weekStartDate = DateUtil.beginOfWeek(date);
        String weekStart = DateUtil.formatDateTime(weekStartDate);
        //统计未关闭的周期任务 且任务开始时间不等  于本周周一
        List<TaskCenter> taskCenters = taskCenterMapper.selectList(Wrappers.<TaskCenter>lambdaQuery()
                .eq(TaskCenter::getTaskTypeId, 2)
                .eq(TaskCenter::getTaskState, 1)
                .ne(TaskCenter::getOpeningTime, weekStart)
        );
        if (CollectionUtil.isNotEmpty(taskCenters)) {
            //关闭之前的任务
            List<Integer> taskCenterIds = taskCenters.stream().map(TaskCenter::getId).collect(Collectors.toList());
            taskCenterMapper.update(null, Wrappers.<TaskCenter>lambdaUpdate().in(TaskCenter::getId, taskCenterIds)
                    .set(TaskCenter::getTaskState, 0)
            );
            LocalDateTime endOfDay = DateUtil.endOfWeek(date).toLocalDateTime();
            endOfDay = endOfDay.withNano(0); // 移除纳秒部分，确保时间到秒级别
            Date dateWithoutMillis = Date.from(endOfDay.atZone(ZoneId.systemDefault()).toInstant());
            DateTime starTime = DateUtil.beginOfWeek(date);
            //重新在插入任务 =>更改任务时间
            for (TaskCenter taskCenter : taskCenters) {
                taskCenter.setOpeningTime(starTime)
                        .setCompletionTime(dateWithoutMillis)
                ;
            }
            taskCenterService.saveBatch(taskCenters);
            log.info("周期任务修改完毕");
        }
    }


    /**
     * 限时任务每周一凌晨重置
     *
     * <AUTHOR>
     * @date 2024/05/31
     */
    @Scheduled(cron = "0 0 0 * * 1")
//    @Scheduled(cron = "0/5 * * * * ? ")
    public void limitedTimeTasksReset() {
        log.info("限时任务修改开始");
        Date date = new Date();
        // 获取本周周一的日期，即本周开始时间
        Date weekStartDate = DateUtil.beginOfWeek(date);
        String weekStart = DateUtil.formatDateTime(weekStartDate);
        DateTime weekEndDate = DateUtil.beginOfWeek(date);

        //获取限时任务周期
        String cycle = globalConfigService.getGlobalConfig(TIME_LIMITED_TASKCYCLE);
        //限时任务周期：d代表天  m代表分钟  天最大数是7，最小1，分钟最大60，最小1
        if (cycle.contains("d")) {
            // 向后偏移6天，到达下周一
            weekEndDate = DateUtil.offsetDay(weekEndDate, Integer.valueOf(cycle.replaceAll("d", "")));

        } else if (cycle.contains("m")) {
            weekEndDate = DateUtil.offsetMinute(weekEndDate, Integer.valueOf(cycle.replaceAll("m", "")));
        }
        //统计未关闭的 限时 任务 且任务开始时间不等  于本周周一
        List<TaskCenter> taskCenters = taskCenterMapper.selectList(Wrappers.<TaskCenter>lambdaQuery()
                .eq(TaskCenter::getTaskTypeId, 4)
                .eq(TaskCenter::getTaskState, 1)
                .ne(TaskCenter::getOpeningTime, weekStart)
        );
        if (CollectionUtil.isNotEmpty(taskCenters)) {
            //关闭之前的任务
            List<Integer> taskCenterIds = taskCenters.stream().map(TaskCenter::getId).collect(Collectors.toList());
            taskCenterMapper.update(null, Wrappers.<TaskCenter>lambdaUpdate().in(TaskCenter::getId, taskCenterIds)
                    .set(TaskCenter::getTaskState, 0)
            );
            LocalDateTime endOfDay = weekEndDate.toLocalDateTime();
            endOfDay = endOfDay.withNano(0); // 移除纳秒部分，确保时间到秒级别
            Date dateWithoutMillis = Date.from(endOfDay.atZone(ZoneId.systemDefault()).toInstant());

            //重新在插入任务 =>更改任务时间
            for (TaskCenter taskCenter : taskCenters) {
                taskCenter.setOpeningTime(weekStartDate)
                        .setCompletionTime(dateWithoutMillis)
                ;
            }
            taskCenterService.saveBatch(taskCenters);
            log.info("限时任务修改完毕");
        }
    }
}
