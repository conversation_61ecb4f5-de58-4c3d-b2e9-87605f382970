package com.lj.auth.task;

import static com.lj.auth.common.CommonConstant.*;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;

import com.lj.auth.domain.*;
import com.lj.auth.exception.ServiceException;
import com.lj.auth.mapper.*;
import com.lj.auth.util.RegexUtil;
import org.apache.commons.collections4.ListUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.web3j.protocol.core.methods.response.TransactionReceipt;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lj.auth.domain.vo.ChainAssetsVO;
import com.lj.auth.service.EnergyRechargeFlowService;
import com.lj.auth.util.BNBUtil;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 描述：校验链上交易是否成功 创建人: CFJ 创建时间: 2024/04/24
 */
@Component
@Slf4j
public class CheckTransactionState {
    @Resource
    private EnergyRechargeFlowMapper energyRechargeFlowMapper;
    @Resource
    private SignInMapper signInMapper;
    @Resource
    private SignInHistMapper signInHistMapper;
    @Resource
    private EnergyRechargeFlowService energyRechargeFlowService;
    @Resource
    private TaskCenterUserMapper taskCenterUserMapper;
    @Resource
    private PlatformAccountMapper platformAccountMapper;
    @Resource
    private SpRecordBatchMapper spamRecordBatchMapper;

    /**
     * 每10秒检查能量充值状态--判断是否有未充值成功的
     *
     * <AUTHOR>
     * @date 2024/04/24
     */
    // @Scheduled(cron = "0/10 * * * * ? ")
    public void checkEnergyState() {
        List<EnergyRechargeFlow> energyRechargeFlows = energyRechargeFlowMapper
            .selectList(Wrappers.<EnergyRechargeFlow>lambdaQuery().eq(EnergyRechargeFlow::getState, 0));
        for (EnergyRechargeFlow energyRechargeFlow : energyRechargeFlows) {
            String hash = energyRechargeFlow.getHash();
            if (StrUtil.isNotBlank(hash)) {
                // hash 不为空说明充值成功 资金扣除完成
                // 判断hash在链上的交易状态
                try {
                    TransactionReceipt transactionReceipt = getTransactionReceipt(hash);

                    boolean statusOK = transactionReceipt == null ? false : transactionReceipt.isStatusOK();
                    // 交易成功更改状态
                    if (statusOK) {
                        energyRechargeFlowMapper.update(null,
                            Wrappers.<EnergyRechargeFlow>lambdaUpdate()
                                .eq(EnergyRechargeFlow::getId, energyRechargeFlow.getId())
                                .set(EnergyRechargeFlow::getState, 1));
                        log.info("充值成功更改上链状态:{} ", energyRechargeFlow.getId());
                        // 阻塞重新上链
                    } else {
                        log.info("充值阻塞重新上链记录id: " + energyRechargeFlow.getId(),
                            "重新上链之前hash: " + energyRechargeFlow.getHash());
                        log.info("充值阻塞重新上链记录id: " + energyRechargeFlow.getId(),
                            "重新上链之前hash: " + energyRechargeFlow.getHash());
                        // 重新给用户充值
                        String gas =
                            energyRechargeFlowService.moneyToGas1(energyRechargeFlow.getBusinessMoney());

                        String newHash = energyRechargeFlowService.transactionMainCurrency1(
                            YL_ENERGY_RECHARGE, energyRechargeFlow.getAddress(), new BigDecimal(gas), 2,
                            ENERGY_RECHARGE_HEX_REMARKS);
                        // 更新hash
                        energyRechargeFlowMapper.update(null,
                            Wrappers.<EnergyRechargeFlow>lambdaUpdate()
                                .eq(EnergyRechargeFlow::getId, energyRechargeFlow.getId())
                                .set(EnergyRechargeFlow::getHash, newHash));

                        log.info("充值阻塞重新上链记录id: " + energyRechargeFlow.getId(), "重新上链之后hash: " + newHash);
                        log.info("充值阻塞重新上链记录id: " + energyRechargeFlow.getId(), "重新上链之后hash: " + newHash);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 每10秒检查签到能量赠送状态--判断是否有未充值成功的
     *
     * <AUTHOR>
     * @date 2024/04/24
     */
    @Scheduled(cron = "0/10 * * * * ? ")
    public void checkSignState() {
        // 耗时统计
        long startTime = System.currentTimeMillis();
        List<SignIn> signIns = signInMapper.selectList(Wrappers.<SignIn>lambdaQuery().eq(SignIn::getState, 0)
            .orderByAsc(SignIn::getId).last("limit 10"));
        // 获取任务中心打币地址
        List<PlatformAccount> platformAccounts = platformAccountMapper.selectList(
            Wrappers.<PlatformAccount>lambdaQuery().like(PlatformAccount::getChainAccountName, "签到"));
        int size = platformAccounts.size();
        if (CollectionUtil.isNotEmpty(signIns)) {
            ExecutorService executor = Executors.newFixedThreadPool(size);
            // 拆分集合
            // 动态计算每个子列表的大小
            int partitionSize = (int)Math.ceil((double)signIns.size() / size);
            List<List<SignIn>> signInLists = ListUtils.partition(signIns, partitionSize);
            for (int i = 0; i < signInLists.size(); i++) {
                int finalI = i;
                executor.submit(() -> signInAndDepositCoins(signInLists.get(finalI),
                    platformAccounts.get(finalI % size).getType()));
            }
            // 关闭线程池
            executor.shutdown();
            try {
                if (!executor.awaitTermination(60, TimeUnit.SECONDS)) {
                    executor.shutdownNow();
                }
            } catch (InterruptedException e) {
                executor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        log.error("签到打币耗时:{}", System.currentTimeMillis() - startTime);
    }

    /**
     * 签到打币
     * 
     * @param signIns
     * @param type
     * <AUTHOR>
     * @date 2024/12/19
     */
    private void signInAndDepositCoins(List<SignIn> signIns, Integer type) {
        for (SignIn signIn : signIns) {
            String hash = signIn.getHash();
            if (StrUtil.isNotBlank(hash)) {
                try {
                    TransactionReceipt transactionReceipt = getTransactionReceipt(hash);
                    boolean statusOK = transactionReceipt == null ? false : transactionReceipt.isStatusOK();
                    // 交易成功更改状态
                    if (statusOK) {
                        signInMapper.update(null, Wrappers.<SignIn>lambdaUpdate()
                            .eq(SignIn::getId, signIn.getId()).set(SignIn::getState, 1));
                        log.info("签到成功更改上链状态:{} ", signIn.getId());
                        // 阻塞重新上链
                    } else {
                        log.info("签到阻塞重新上链记录id: " + signIn.getId(), "重新上链之前hash: " + signIn.getHash());
                        log.info("签到阻塞重新上链记录id: " + signIn.getId(), "重新上链之前hash: " + signIn.getHash());

                        // 签到能量赠送
                        String newHash = energyRechargeFlowService.transactionMainCurrency1(
                            YL_ENERGY_SIGNIN + type, signIn.getAddress(), signIn.getRewardMoney(), type,
                            YL_ENERGY_SIGNIN_HEX_REMARKS);

                        signInHistMapper.update(null, Wrappers.<SignInHist>lambdaUpdate()
                            .eq(SignInHist::getHash, hash).set(SignInHist::getHash, newHash));

                        // 更新hash
                        signInMapper.update(null, Wrappers.<SignIn>lambdaUpdate()
                            .eq(SignIn::getId, signIn.getId()).set(SignIn::getHash, newHash));
                        System.out.println("签到hash:" + newHash);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            } else {
                try {
                    log.info("哈希为null上链记录id: " + signIn.getId());
                    log.info("哈希为null上链记录id: " + signIn.getId());
                    // 判断是否已经补发
                    SignInHist signInHist = signInHistMapper.selectOne(
                        Wrappers.<SignInHist>lambdaQuery().eq(SignInHist::getAddress, signIn.getAddress())
                            .eq(SignInHist::getSignInDate, signIn.getSignInDate())
                            .eq(SignInHist::getAccountUuid, signIn.getAccountUuid())
                            .eq(SignInHist::getRewardMoney, signIn.getRewardMoney()));
                    if (signInHist != null) {
                        if (StrUtil.isNotBlank(signInHist.getHash())) {
                            // 更新hash
                            signInMapper.update(null,
                                Wrappers.<SignIn>lambdaUpdate().eq(SignIn::getId, signIn.getId())
                                    .set(SignIn::getHash, signInHist.getHash()));
                        } else {
                            // 签到能量赠送
                            String newHash = energyRechargeFlowService.transactionMainCurrency1(
                                YL_ENERGY_SIGNIN + type, signIn.getAddress(), signIn.getRewardMoney(), type,
                                YL_ENERGY_SIGNIN_HEX_REMARKS);

                            TransactionReceipt transactionReceipt = getTransactionReceipt(newHash);
                            boolean statusOK =
                                transactionReceipt == null ? false : transactionReceipt.isStatusOK();
                            if (statusOK) {
                                signInHistMapper.update(null,
                                    Wrappers.<SignInHist>lambdaUpdate()
                                        .eq(SignInHist::getAddress, signIn.getAddress())
                                        .eq(SignInHist::getAccountUuid, signIn.getAccountUuid())
                                        .eq(SignInHist::getSignInDate, signIn.getSignInDate())
                                        .eq(SignInHist::getRewardMoney, signIn.getRewardMoney())
                                        .set(SignInHist::getHash, newHash));
                                // 更新hash
                                signInMapper.update(null, Wrappers.<SignIn>lambdaUpdate()
                                    .eq(SignIn::getId, signIn.getId()).set(SignIn::getHash, newHash));
                                System.out.println("签到hash:" + newHash);
                            }
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 每10秒检查任务完成能量领取状态--判断是否有未充值成功的
     *
     * <AUTHOR>
     * @date 2024/05/31
     */
    @Scheduled(cron = "0/10 * * * * ? ")
    public void checkTaskCenterIsReceiveState() {
        // 耗时统计
        long startTime = System.currentTimeMillis();
        // 查询出已经领取sp的任务 且未上链成功的任务记录
        List<TaskCenterUser> taskCenterUserList = taskCenterUserMapper
            .selectList(Wrappers.<TaskCenterUser>lambdaQuery().eq(TaskCenterUser::getIsReceive, 1)
                .eq(TaskCenterUser::getState, 0).orderByAsc(TaskCenterUser::getId).last("limit 20"));
        // 获取任务中心打币地址
        List<PlatformAccount> platformAccounts = platformAccountMapper.selectList(
            Wrappers.<PlatformAccount>lambdaQuery().like(PlatformAccount::getChainAccountName, "任务中心"));
        int size = platformAccounts.size();
        if (CollectionUtil.isNotEmpty(taskCenterUserList)) {
            ExecutorService executor = Executors.newFixedThreadPool(size);
            // 拆分集合
            // 动态计算每个子列表的大小
            int partitionSize = (int)Math.ceil((double)taskCenterUserList.size() / size);
            List<List<TaskCenterUser>> taskCenterUserLists =
                ListUtils.partition(taskCenterUserList, partitionSize);
            for (int i = 0; i < taskCenterUserLists.size(); i++) {
                int finalI = i;
                executor.submit(() -> taskCenterCoining(taskCenterUserLists.get(finalI),
                    platformAccounts.get(finalI % size).getType()));
            }
            // 关闭线程池
            executor.shutdown();
            try {
                if (!executor.awaitTermination(60, TimeUnit.SECONDS)) {
                    executor.shutdownNow();
                }
            } catch (InterruptedException e) {
                executor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        log.error("任务中心打币耗时:{}", System.currentTimeMillis() - startTime);
    }

    /**
     * 任务中心打币
     * 
     * <AUTHOR>
     * @date 2024/12/19
     */
    public void taskCenterCoining(List<TaskCenterUser> taskCenterUserList, Integer type) {
        for (TaskCenterUser taskCenterUser : taskCenterUserList) {
            String hash = taskCenterUser.getHash();
            Integer state = taskCenterUser.getState();
            if (StrUtil.isNotBlank(hash) && state == 0) {
                try {
                    TransactionReceipt transactionReceipt = getTransactionReceipt(hash);
                    boolean statusOK = transactionReceipt == null ? false : transactionReceipt.isStatusOK();
                    // 交易成功更改状态
                    if (statusOK) {
                        taskCenterUserMapper.update(null,
                            Wrappers.<TaskCenterUser>lambdaUpdate()
                                .eq(TaskCenterUser::getId, taskCenterUser.getId())
                                .set(TaskCenterUser::getState, 1));
                        log.info("任务奖励发送成功更改上链状态:{} ", taskCenterUser.getId());
                        // 阻塞重新上链
                    } else {
                        log.info("任务奖励发送失败重新上链记录id: " + taskCenterUser.getId(),
                            "重新上链之前hash: " + taskCenterUser.getHash());
                        log.info("任务奖励发送失败重新上链记录id: " + taskCenterUser.getId(),
                            "重新上链之前hash: " + taskCenterUser.getHash());

                        // 签到能量赠送
                        String newHash = energyRechargeFlowService.transactionMainCurrency1(
                            YL_ENERGY_SIGNIN + type, taskCenterUser.getAddress(), taskCenterUser.getReward(),
                            type, TASK_REWARDS_HEX_REMARKS);
                        taskCenterUserMapper.update(null,
                            Wrappers.<TaskCenterUser>lambdaUpdate()
                                .eq(TaskCenterUser::getId, taskCenterUser.getId())
                                .set(TaskCenterUser::getHash, newHash));
                        System.out.println("任务中心hash:" + newHash);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            } else {
                try {
                    log.info("哈希为null任务id: " + taskCenterUser.getId());
                    log.info("哈希为null任务id: " + taskCenterUser.getId());

                    // 签到能量赠送
                    String newHash = energyRechargeFlowService.transactionMainCurrency1(
                        YL_ENERGY_SIGNIN + type, taskCenterUser.getAddress(), taskCenterUser.getReward(),
                        type, TASK_REWARDS_HEX_REMARKS);

                    TransactionReceipt transactionReceipt = getTransactionReceipt(newHash);
                    boolean statusOK = transactionReceipt == null ? false : transactionReceipt.isStatusOK();
                    if (statusOK) {
                        // 更新hash
                        taskCenterUserMapper.update(null,
                            Wrappers.<TaskCenterUser>lambdaUpdate()
                                .eq(TaskCenterUser::getId, taskCenterUser.getId())
                                .set(TaskCenterUser::getHash, newHash));
                        System.out.println("任务中心hash:" + newHash);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 每10秒检查批量打币是否有未完成的记录
     */
    @Scheduled(cron = "0/10 * * * * ? ")
    public void checkSpRecordBatchState() {
        // 耗时统计
        long startTime = System.currentTimeMillis();
        // 查询未打币完成的记录
        SpRecordBatch spRecordBatchOn = spamRecordBatchMapper
            .selectOne(Wrappers.<SpRecordBatch>lambdaQuery().eq(SpRecordBatch::getIsCheck, 1)
                .eq(SpRecordBatch::getState, 0).orderByAsc(SpRecordBatch::getCreateTime).last("limit 1"));
        if (spRecordBatchOn != null) {
            Integer state = spRecordBatchOn.getState();
            String hash = spRecordBatchOn.getHash();
            if (state == 0) {
                try {
                    log.info("接收地址: " + spRecordBatchOn.getAddress());
                    log.info("接收地址: " + spRecordBatchOn.getAddress());

                    // 校验打币地址
                    if (!RegexUtil.checkEthAddress(spRecordBatchOn.getAddress())) {
                        log.error("地址格式错误,地址：" + spRecordBatchOn.getAddress());
                        return;
                    }
                    // 批量打币
                    String newHash = energyRechargeFlowService.transactionMainCurrency1(YL_ENERGY_SIGNIN,
                        spRecordBatchOn.getAddress(), spRecordBatchOn.getReward(), 4,
                        remarkTOhex(spRecordBatchOn.getRemark()));
                    TransactionReceipt transactionReceipt = getTransactionReceipt(newHash);
                    boolean statusOK = transactionReceipt == null ? false : transactionReceipt.isStatusOK();
                    if (statusOK) {
                        // 更新hash
                        spamRecordBatchMapper.update(null,
                            Wrappers.<SpRecordBatch>lambdaUpdate()
                                .eq(SpRecordBatch::getId, spRecordBatchOn.getId())
                                .set(SpRecordBatch::getHash, newHash).set(SpRecordBatch::getState, 1)
                                .set(SpRecordBatch::getDistributionTime, new Date()));
                        System.out.println("更新批量打币Hash:" + newHash);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            log.error("批量打币:{}", System.currentTimeMillis() - startTime);
        }
    }

    public void checkSignHistState1() {
        List<ChainAssetsVO> chainAssetsVOS = signInHistMapper.getChainAsses();
        for (ChainAssetsVO chainAssetsVO : chainAssetsVOS) {
            BigInteger balance = BNBUtil.getBalance(chainAssetsVO.getAddress());
            BigDecimal balance1 = new BigDecimal(balance);
            // wei ==>gas 链上资产
            BigDecimal gas = balance1.divide(new BigDecimal(BigInteger.TEN.pow(18)));
            BigDecimal dataGas = chainAssetsVO.getGas();
            // 补齐差额 链上资产小于 数据库资产
            if (gas.compareTo(dataGas) < 0) {
                // 需要补充的值
                BigDecimal subtractGas = dataGas.subtract(gas);
                try {
                    // 签到能量赠送
                    String newHash = energyRechargeFlowService.transactionMainCurrency1(YL_ENERGY_SIGNIN,
                        chainAssetsVO.getAddress(), subtractGas, 3, YL_ENERGY_SIGNIN_Reissue_HEX_REMARKS);
                    // 校验是否补发成功
                    TransactionReceipt transactionReceipt = getTransactionReceipt(newHash);
                    boolean statusOK = transactionReceipt == null ? false : transactionReceipt.isStatusOK();
                    if (statusOK) {
                        log.info("补发成功记录: {} ", chainAssetsVO.toString());
                        log.info("补发成功记录: {} ", chainAssetsVO.toString());
                        log.info("补发成功记录: {} ", chainAssetsVO.toString());

                    } else {
                        log.info("补发失败记录: {} ", chainAssetsVO.toString());
                    }

                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

        }
    }

    /**
     * 补发签到sp
     * 
     * <AUTHOR>
     * @date 2024/12/09
     */
    // @Scheduled(cron = "0/10 * * * * ? ")
    public void reissueSignInSp() {
        List<SignInHist> signInHists = signInHistMapper.selectList(
            Wrappers.<SignInHist>lambdaQuery().lt(SignInHist::getSignInDate, DateUtil.beginOfDay(new Date()))
                .isNull(SignInHist::getHash).orderByDesc(SignInHist::getSignInDate));
        System.out.println("补发条数："+signInHists.size());
        if (signInHists.size() > 0) {
            for (SignInHist signInHist : signInHists) {
                try {
                    log.error("哈希为null上链记录id: " + signInHist.getId());
                    log.error("哈希为null上链记录id: " + signInHist.getId());
                    // 签到能量赠送
                    // 耗时统计
                    long startTime = System.currentTimeMillis();
                    String newHash = energyRechargeFlowService.transactionMainCurrency2(
                        YL_ENERGY_RECHARGE + 15, signInHist.getAddress(), signInHist.getRewardMoney(), 15,
                        YL_ENERGY_SIGNIN_HEX_REMARKS);
                    long endTime = System.currentTimeMillis();
                    System.out.println("补发耗时：" + (endTime - startTime) + "ms");
                    TransactionReceipt transactionReceipt = getTransactionReceipt(newHash);
                    boolean statusOK = transactionReceipt == null ? false : transactionReceipt.isStatusOK();
                    if (statusOK) {
                        signInHistMapper.update(null, Wrappers.<SignInHist>lambdaUpdate()
                            .eq(SignInHist::getId, signInHist.getId()).set(SignInHist::getHash, newHash));
                        // 查询今天签到是否有记录
                        // 判断是否有hash
                        SignIn signIn = signInMapper.selectOne(
                            Wrappers.<SignIn>lambdaQuery().eq(SignIn::getAddress, signInHist.getAddress())
                                .eq(SignIn::getSignInDate, signInHist.getSignInDate())
                                .eq(SignIn::getAccountUuid, signInHist.getAccountUuid())
                                .eq(SignIn::getRewardMoney, signInHist.getRewardMoney()));
                        if (signIn != null) {
                            if (StrUtil.isBlank(signIn.getHash())) {
                                signInMapper.update(null, Wrappers.<SignIn>lambdaUpdate()
                                    .eq(SignIn::getId, signIn.getId()).set(SignIn::getHash, newHash));
                            }
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        System.out.println("补发打币完成!!!!!");
    }

    /**
     * // TODO 校验链资产补发是否对---以数据库的充值数据为准--对比链上实际资产 校验链上资产
     *
     * <AUTHOR>
     * @date 2024/05/08
     */
    public void checkChainAssets() {
        // 统计个人签到获得总gas
        List<ChainAssetsVO> chainAssetsVOS = signInHistMapper.getChainAsses();
        for (ChainAssetsVO chainAssetsVO : chainAssetsVOS) {
            BigInteger balance = BNBUtil.getBalance(chainAssetsVO.getAddress());
            BigDecimal balance1 = new BigDecimal(balance);
            // wei ==>gas
            BigDecimal gas = balance1.divide(new BigDecimal(BigInteger.TEN.pow(18)));
            // 资产不相等
            if (gas.compareTo(chainAssetsVO.getGas()) > 0) {
                log.error("链上资产 大于 数据库 资产");
                log.error("链上资产 大于 数据库 资产");
                log.error("链上资产：{}", gas);
                log.error("数据库资产：{}", chainAssetsVO.getGas());
                log.error("数据记录：{}", chainAssetsVO.toString());
                log.error("================================");
                log.error("================================");
            }
            // else if (gas.compareTo(chainAssetsVO.getGas()) < 0) {
            // log.error("链上资产 小于 数据库 资产");
            // log.error("链上资产：{}", gas);
            // log.error("数据库资产：{}", chainAssetsVO.getGas());
            // log.error("数据记录：{}", chainAssetsVO.toString());
            // log.error("================================");
            // log.error("================================");
            // }
            // else if (gas.compareTo(chainAssetsVO.getGas()) == 0) {
            // log.info("链上和数据库资产相等");
            // log.info("链上和数据库资产相等");
            // log.info("链上资产：{}", gas);
            // log.info("数据库资产：{}", chainAssetsVO.getGas());
            // log.info("数据记录：{}", chainAssetsVO.toString());
            // log.info("================================");
            // log.info("================================");
            // }
        }
    }

    @Async("ljThreadPool")
    public TransactionReceipt getTransactionReceipt(String transactionHash) throws Exception {
        int num = 0;
        while (true) {
            Optional<TransactionReceipt> transactionReceipt =
                BNBUtil.ethGetTransactionReceipt(transactionHash).getTransactionReceipt();
            if (transactionReceipt.isPresent()) {
                return transactionReceipt.get();
            }
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                // 重新设置中断状态
                Thread.currentThread().interrupt();
                // 记录日志或执行其他中断处理逻辑
                log.error("Thread was interrupted while waiting for transaction receipt", e);
                return null; // 或者根据需要返回其他值
            }
            num++;
            if (num > 10) {
                return null;
            }
        }
    }

    public String remarkTOhex(String message) {
        try {
            StringBuilder hex = new StringBuilder();
            byte[] bytes = message.getBytes("UTF-8");
            for (byte b : bytes) {
                hex.append(String.format("%02x", b));
            }
            return hex.toString();
        } catch (Exception e) {
        }
        return message;
    }

}
