package com.lj.auth.task;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.util.DateUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lj.auth.domain.*;
import com.lj.auth.exception.ServiceException;
import com.lj.auth.feginClient.IDidService;
import com.lj.auth.feginClient.IDomainService;
import com.lj.auth.feginClient.ITransferService;
import com.lj.auth.mapper.*;
import com.lj.auth.service.DomainAdminRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 描述：检测注销账户数据处理 创建人: CFJ 创建时间: 2024/06/21
 */

@Component
@Slf4j
public class CheckLogout {
    static final List<String> tableNames = new ArrayList<>(Arrays.asList
            ("lj_auth_account_domain_address",
                    "lj_auth_contribution_record",
                    "lj_auth_domain_admin_record",
                    "lj_auth_domain_application_record",
                    "lj_auth_feedback",
                    "lj_auth_recharge_record",
                    "lj_movie_order",
                    "lj_payment_transaction_record",
                    "lj_phone_recharge_record",
                    "lj_transfer_fix_bid_record",
                    "lj_transfer_fix_domain",
                    "lj_transfer_fix_domain_order",
                    "mix_order",
                    "mix_order_view",
                    "mix_payment_refund",
                    "ym_domain_account_cancel_record",
                    "ym_order_domain_renewal",
                    "ym_order_domain_renewal_info",
                    "mix_payment_transaction_record"
                    ));
    static final List<String> YMtableNames =new ArrayList<>(Arrays.asList
            (
             "ym_domain_account",
             "ym_order_bsn_info",
             "ym_order_domain",
                    "ym_order",
                    "ym_recharge_flow",
                    "ym_recharge_withdraw_record"
            ));


    static final List<String> YMOperatetableNames =new ArrayList<>(Arrays.asList
            (
                    "ym_voucher_accredit",
                    "ym_record",
                    "ym_recharge_withdraw_record",
                    "ym_recharge_supplement",
                    "ym_recharge_flow",
                    "ym_recharge_assets",
                    "ym_order_domain",
                    "ym_order_bsn_info",
                    "ym_order",
                    "ym_operate_apply_order",
                    "ym_operate_apply",
                    "ym_invoice_message",
                    "ym_invoice_account",
                    "ym_domain_intention",
                    "ym_domain_account",
                    "ym_addressee_message",
                    "ym_account_info_voucher",
                    "ym_account_did_history",
                    "ym_account_did_domain",
                    "ym_account_did_device",
                    "ym_account_did",
                    "ym_account_card",
                    "ym_account_accredit",
                    "mix_order",
                    "lj_transfer_fix_domain_order",
                    "lj_phone_recharge_record"
            ));


    @Resource
    private AccountLogoutConditionRecordMapper accountLogoutConditionRecordMapper;
    @Resource
    private AccountLogoutMapper accountLogoutMapper;
    @Resource
    private FixDomainMapper fixDomainMapper;
    @Resource
    private ITransferService iTransferService;
    @Resource
    private AccountMapper accountMapper;
    @Resource
    private AccountDidDomainMapper accountDidDomainMapperMapper;
    @Resource
    private IDidService iDidService;
    @Resource
    private DomainAccountMapper domainAccountMapper;
    @Resource
    private DomainApplicationRecordMapper domainApplicationRecordMapper;
    @Resource
    private DomainAdminRecordService domainAdminRecordService;
    @Resource
    private IDomainService iDomainService;
    @Resource
    private RechargeFlowMapper rechargeFlowMapper;
    @Resource
    private RechargeAssetsMapper    rechargeAssetsMapper;
    @Resource
    private RechargeWithdrawRecordMapper rechargeWithdrawRecordMapper;

    @Resource
    private AccountMergeAssetsMapper accountMergeAssetsMapper;
    @Resource
    private ContributionRecordMapper  contributionsRecordMapper;

    @Resource
    private ContributionMapper  contributionsMapper;


    /**
     * 每日凌晨清除已注销用户数据
     * 
     * <AUTHOR>
     * @date 2024/06/21
     */
     @Scheduled(cron = "0 0 0 * * ? ")
//    @Scheduled(cron = "0/5 * * * * ? ")
    public void clearUserData() {
        log.info("开始执行注销用户数据处理");
        // 查询出完成注销条件的注销用户
        List<String> users = accountLogoutConditionRecordMapper.selectLogOutUser();
        // 查询注销用户表是否有注销记录
        if (CollectionUtil.isNotEmpty(users)) {
            List<AccountLogout> accountLogouts = accountLogoutMapper
                .selectList(Wrappers.<AccountLogout>lambdaQuery().in(AccountLogout::getUuid, users));
            if (CollectionUtil.isNotEmpty(accountLogouts)) {
                // 不为空处理用户数据
                for (AccountLogout accountLogout : accountLogouts) {
                    boolean b = userDataRelease(accountLogout.getUuid());
                    // 修该注销用户数据处理记录
                    if (b) {
                        accountLogoutConditionRecordMapper.update(null,
                            Wrappers.<AccountLogoutConditionRecord>lambdaUpdate()
                                .eq(AccountLogoutConditionRecord::getAccountUuid, accountLogout.getUuid())
                                .set(AccountLogoutConditionRecord::getIsIgnore, 3));
                    }
                }
            }
        }
    }

    public boolean userDataRelease(String accountUUID) {
        boolean flag = false;
        // 一口价格 和竞拍全部下架
        flag = iTransferService.deactivateDomains(accountUUID, new ArrayList<>());
        if (!flag) {
            return flag;
        }
        List<AccountDidDomain> accountDidDomains = accountDidDomainMapperMapper.selectList(
            Wrappers.<AccountDidDomain>lambdaQuery().eq(AccountDidDomain::getAccountUuid, accountUUID));
        if (CollectionUtil.isNotEmpty(accountDidDomains)) {
            List<String> domains =
                accountDidDomains.stream().map(AccountDidDomain::getDomain).collect(Collectors.toList());
            // 解绑域名绑定DID
            for (String domain : domains) {
                flag = iDidService.unbindDidDomainV2(accountUUID, domain);
            }
        }
        if (!flag) {
            return flag;
        }
        // 转移所有域名到运营账号
        // 获取用户的所有域名
        List<DomainAccount> domainAccounts = domainAccountMapper
            .selectList(Wrappers.<DomainAccount>lambdaQuery().eq(DomainAccount::getAccountUuid, accountUUID));
        // 获取用户绑定地址的域名
        List<String> result = domainApplicationRecordMapper.getApplicationList(accountUUID);

        // 域名转让前需要解绑
        if (CollectionUtil.isNotEmpty(result)) {
            for (String domain : result) {
                flag = domainAdminRecordService.getDomainClearService(domain, accountUUID);
            }
        }
        if (!flag) {
            return flag;
        }

        // 调用域名转让接口
        if (CollectionUtil.isNotEmpty(domainAccounts)) {
            for (DomainAccount domainAccount : domainAccounts) {
                log.info("开始转让域名：{}"+domainAccount.getDomain());
                flag =
                 iDomainService.transferDomainV2(domainAccount.getDomain(), accountUUID, "CDYY51fce0a98b");

                try {
                    //同步持有域名信息表数据 transType 1:交易 2：转让 3：注销
                    iDomainService.addHistoricalHoldDomainInfo(domainAccount.getDomain(),accountUUID,"CDYY51fce0a98b",2);
             } catch (Exception e) {
                    log.error("Auth同步持有域名信息失败:domain:{},accountId:{},toUUID:{},transType:{},TransactionDate:{}", domainAccount.getDomain(), accountUUID, "CDYY51fce0a98b", 2, DateUtils.format(new Date()));
                }
            }
        }

        if (!flag) {
            return flag;
        }
        // 解除用户上下级关系
        Account account =
            accountMapper.selectOne(Wrappers.<Account>lambdaQuery().eq(Account::getUuid, accountUUID));
        // 修改用户的手机号
        accountMapper.update(null, Wrappers.<Account>lambdaUpdate().eq(Account::getUuid, accountUUID)
            .set(Account::getPhoneNumber, "zx" + (new Date()).getTime()));
        // 修改用户的DIDsymbol
        String didSymbol = account.getDidSymbol();
        if (StrUtil.isNotBlank(didSymbol)) {
            accountMapper.update(null, Wrappers.<Account>lambdaUpdate().eq(Account::getId, account.getId())
                .set(Account::getDidSymbol, "zx" + didSymbol));
        }
        String operateUuid = account.getOperateUuid();
        String operateAccountUUID = accountLogoutMapper.selectOperateAccountUUID(operateUuid);
        // 查询该用户的下级
        List<Account> accounts = accountMapper
            .selectList(Wrappers.<Account>lambdaQuery().eq(Account::getParentUuid, account.getUuid()));
        if (CollectionUtil.isNotEmpty(accounts)) {
            List<Long> accountIds = accounts.stream().map(Account::getId).collect(Collectors.toList());
            // 解除下级上下级关系 把该用户的下级设置为该经销商
            accountMapper.update(null, Wrappers.<Account>lambdaUpdate().in(Account::getId, accountIds)
                .set(Account::getParentUuid, operateAccountUUID));
        }
        return flag;
    }


    /**
     * 合并账号资产合并
     * @param account
     * @param account1
     * <AUTHOR>
     * @date 2024/11/19
     */
    public void mergeAssets(Account account, Account account1) {
        // TODO 判断用户有没有域名资产
        // 查询账号是否持有域名
        List<DomainAccount> domainAccounts = domainAccountMapper.selectList(
                Wrappers.<DomainAccount>lambdaQuery().eq(DomainAccount::getAccountUuid, account.getUuid())
                        .eq(DomainAccount::getTransferState, 1).eq(DomainAccount::getStatus, 0));
        // 获取用户绑定地址的域名
        List<String> result = domainApplicationRecordMapper.getApplicationList(account.getUuid());
        // 域名转让前需要解绑
        if (CollectionUtil.isNotEmpty(result)) {
            for (String domain : result) {
                boolean flag = domainAdminRecordService.getDomainClearService(domain, account.getUuid());
            }
        }
        // 调用域名转让接口
        if (CollectionUtil.isNotEmpty(domainAccounts)) {
            for (DomainAccount domainAccount : domainAccounts) {
                log.info("开始转让域名：{}" + domainAccount.getDomain());
                boolean flag = iDomainService.transferDomainV2(domainAccount.getDomain(), account.getUuid(),
                        account1.getUuid());
                try {
                    // 同步持有域名信息表数据 transType 1:交易 2：转让 3：注销
                    iDomainService.addHistoricalHoldDomainInfo(domainAccount.getDomain(), account.getUuid(),
                            account1.getUuid(), 2);
                } catch (Exception e) {
                    log.error(
                            "Auth同步持有域名信息失败:domain:{},accountId:{},toUUID:{},transType:{},TransactionDate:{}",
                            domainAccount.getDomain(), account.getUuid(), account1.getUuid(), 2,
                            DateUtils.format(new Date()));
                }
            }
        }
        // todo 判断用户有没有现金资产
        // 查询账号是否有余额和冻结
        RechargeAssets one = rechargeAssetsMapper.selectOne(
                Wrappers.<RechargeAssets>lambdaQuery().eq(RechargeAssets::getAccountUuid, account.getUuid()));
        BigDecimal balance = one.getBalance();
        BigDecimal freeze = one.getFreeze();
        if(freeze.compareTo(new BigDecimal("0")) > 0){
             throw new ServiceException("存在冻结资产,请先处理");
        }
        if (balance.compareTo(new BigDecimal("0")) > 0) {
            RechargeAssets one1 = rechargeAssetsMapper.selectOne(Wrappers.<RechargeAssets>lambdaQuery()
                    .eq(RechargeAssets::getAccountUuid, account1.getUuid()));
            // 合并用户资产
            rechargeAssetsMapper.update(null,
                    Wrappers.<RechargeAssets>lambdaUpdate().eq(RechargeAssets::getAccountUuid, account1.getUuid())
                            .set(RechargeAssets::getBalance, one1.getBalance().add(balance))
                            .set(RechargeAssets::getTotalAmount, one1.getTotalAmount().add(balance))
            );
            rechargeAssetsMapper.update(null,
                    Wrappers.<RechargeAssets>lambdaUpdate().eq(RechargeAssets::getAccountUuid, account.getUuid())
                            .set(RechargeAssets::getBalance, new BigDecimal("0")));
            //添加划转流水记录
            // 4.2 门户账户资金增加
            // 手续费
            BigDecimal rate = new BigDecimal("0");
            BigDecimal serviceCharge = new BigDecimal("0");
            // 4.3 进行数据库更新操作
            // 增加资金订单记录
            // 生成随机数
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMddHHmmssSSSS");
            String timeFormat = simpleDateFormat.format(new Date());
            String realRate =
                rate.multiply(new BigDecimal("100"))
                        .setScale(0, RoundingMode.HALF_UP).toPlainString() + "%";

            RechargeWithdrawRecord rechargeWithdrawRecord =
                RechargeWithdrawRecord.builder().operateUuid(account1.getOperateUuid()).accountUuid(account1.getUuid())
                    .amount(balance).realAmount(balance).rate(realRate).balance(one1.getBalance()).state(3)
                    .createTime(new Date()).type(5).tranNumber("hz" + timeFormat).build();
            rechargeWithdrawRecordMapper.insert(rechargeWithdrawRecord);
            // 增加资金流水记录
            RechargeFlow rechargeFlow = RechargeFlow.builder().operateUuid(account1.getOperateUuid())
                .accountUuid(account1.getUuid()).createTime(new Date()).orderId(rechargeWithdrawRecord.getId())
                .amount(balance).type(5).build();
            rechargeFlowMapper.insert(rechargeFlow);
        }
    }

    /**
     * 定时任务合并账号资产 每10秒执行一次 合并账号资产合并 把手机号账户合并到DID账户
     *
     * @param account 手机号账户
     * @param account1 DID账户
     * <AUTHOR>
     * @date 2024/11/19
     */
    @Scheduled(cron = "0/10 * * * * ? ")
    @Transactional
    public void mergeAssets2() {
        AccountMergeAssets accountMergeAssets = accountMergeAssetsMapper
            .selectOne(Wrappers.<AccountMergeAssets>lambdaQuery().eq(AccountMergeAssets::getIsAll, 0)
                .orderByAsc(AccountMergeAssets::getCreateTime).last("limit 1"));
        if (accountMergeAssets == null) {
            return;
        }
        String fromUuid = accountMergeAssets.getFromUuid();
        String fOperateUuid = accountMergeAssets.getFOperateUuid();
        String toUuid = accountMergeAssets.getToUuid();
        String tOperateUuid = accountMergeAssets.getTOperateUuid();
        // 判断用户有没有现金资产
        // 查询账号是否有余额和冻结
        RechargeAssets one = rechargeAssetsMapper
            .selectOne(Wrappers.<RechargeAssets>lambdaQuery().eq(RechargeAssets::getAccountUuid, fromUuid));

        RechargeAssets one1 = rechargeAssetsMapper
            .selectOne(Wrappers.<RechargeAssets>lambdaQuery().eq(RechargeAssets::getAccountUuid, toUuid));

        BigDecimal balance = one.getBalance();
        BigDecimal freeze = one.getFreeze();

        if (freeze.compareTo(new BigDecimal("0")) > 0) {
            throw new ServiceException("存在冻结资产,请先处理");
        }
        if (balance.compareTo(new BigDecimal("0")) > 0) {
            // 合并用户资产
            rechargeAssetsMapper.update(null,
                Wrappers.<RechargeAssets>lambdaUpdate().eq(RechargeAssets::getAccountUuid, toUuid)
                    .set(RechargeAssets::getBalance, one1.getBalance().add(balance))
                    .set(RechargeAssets::getOperateUuid, fOperateUuid)
                    .set(RechargeAssets::getTotalAmount, one1.getTotalAmount().add(balance)));
            rechargeAssetsMapper.update(null,
                Wrappers.<RechargeAssets>lambdaUpdate().eq(RechargeAssets::getAccountUuid, fromUuid)
                        .set(RechargeAssets::getOperateUuid, tOperateUuid)
                    .set(RechargeAssets::getBalance, new BigDecimal("0")));
        }
        // 合并贡献资产
        Contribution contribution = contributionsMapper
            .selectOne(Wrappers.<Contribution>lambdaQuery().eq(Contribution::getAccountUuid, fromUuid));
        // 合并贡献资产
        if (contribution != null) {
            BigDecimal amount = contribution.getAmount();
            Contribution contributionTo = contributionsMapper
                .selectOne(Wrappers.<Contribution>lambdaQuery().eq(Contribution::getAccountUuid, toUuid));
            if (contributionTo == null) {
                // 直接修改uuid为 toUUID
                contributionsMapper.update(null, Wrappers.<Contribution>lambdaUpdate()
                    .eq(Contribution::getId, contribution.getId()).set(Contribution::getAccountUuid, toUuid));
            } else {
                contributionsMapper.update(null,
                    Wrappers.<Contribution>lambdaUpdate().eq(Contribution::getId, contributionTo.getId())
                        .set(Contribution::getAmount, contributionTo.getAmount().add(amount)));
            }
        }
        // 查询该用户的下级
        List<Account> accounts = accountMapper
                .selectList(Wrappers.<Account>lambdaQuery().eq(Account::getParentUuid,toUuid));

        if (CollectionUtil.isNotEmpty(accounts)) {
            List<Long> accountIds = accounts.stream().map(Account::getId).collect(Collectors.toList());
            // 解除下级上下级关系 把该用户的下级设置为该经销商
            accountMapper.update(null, Wrappers.<Account>lambdaUpdate().in(Account::getId, accountIds)
                    .set(Account::getOperateUuid, fOperateUuid)
            );
        }
        //改变原先手机号账户推广关系的parent_uuid
        // 查询该用户的下级
        List<Account> accountPhone = accountMapper
                .selectList(Wrappers.<Account>lambdaQuery().eq(Account::getParentUuid,fromUuid));

        if (CollectionUtil.isNotEmpty(accountPhone)) {
            List<Long> accountIds = accountPhone.stream().map(Account::getId).collect(Collectors.toList());
            // 解除下级上下级关系 把该用户的下级设置为该经销商
            accountMapper.update(null, Wrappers.<Account>lambdaUpdate().in(Account::getId, accountIds)
                    .set(Account::getParentUuid, toUuid)
            );
        }
        // 更新合并记录
        accountMergeAssetsMapper.update(null,
            Wrappers.<AccountMergeAssets>lambdaUpdate()
                .eq(AccountMergeAssets::getId, accountMergeAssets.getId())
                .set(AccountMergeAssets::getFBalance, balance)
                .set(AccountMergeAssets::getTBalance, one1.getTotalAmount())
                .set(AccountMergeAssets::getIsAssets, 1));
    }

    

    /**
     * 合并资产记录
     * 
     * <AUTHOR>
     * @date 2025/02/07
     */
    @Scheduled(cron = "0/10 * * * * ? ")
    @Transactional
    public void mergeAssetRecords() {
        AccountMergeAssets accountMergeAssets = accountMergeAssetsMapper
            .selectOne(Wrappers.<AccountMergeAssets>lambdaQuery().eq(AccountMergeAssets::getIsAll, 0)
                .orderByAsc(AccountMergeAssets::getCreateTime).last("limit 1"));
        // 处理合并记录
        // 遍历每个表，执行更新操作
        if (accountMergeAssets == null) {
            return;
        }
        if (accountMergeAssets.getIsAssets() == 1) {
            //手机号账号的uuid数据都要改成DID UUID的数据
            for (String tableName : tableNames) {
                accountMergeAssetsMapper.updateAccountUuid(tableName, accountMergeAssets.getToUuid(),
                    accountMergeAssets.getFromUuid());
            }
            // 修改ym相关表
            //所有手机号相关的表的uuid修改为DID UUID
            for (String tableName : YMtableNames) {
                accountMergeAssetsMapper.updateAccountUuidAndOperateUuid(tableName,
                    accountMergeAssets.getToUuid(),
                    accountMergeAssets.getFromUuid());
            }
            //修改所有DID uuid相关联的operate_uuid  为手机号的operate_uuid
            for (String tableName : YMOperatetableNames) {
                accountMergeAssetsMapper.updateAccountUuidOperateUuid(tableName,
                    accountMergeAssets.getFOperateUuid(), accountMergeAssets.getToUuid());
            }
            // 更新合并记录
            accountMergeAssetsMapper.update(null,
                    Wrappers.<AccountMergeAssets>lambdaUpdate()
                            .eq(AccountMergeAssets::getId, accountMergeAssets.getId())
                            .set(AccountMergeAssets::getIsAll, 1));
            System.out.println("合并资产记录完成");
        }

    }
    
}
