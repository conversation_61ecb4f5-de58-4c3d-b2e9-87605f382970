package com.lj.auth.task;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.convert.impl.DateConverter;
import cn.hutool.core.date.DatePattern;
import com.lj.auth.domain.*;
import com.lj.auth.mapper.*;
import com.lj.auth.service.EnergyRechargeFlowService;
import com.lj.auth.service.PlatformAccountService;
import com.lj.auth.util.BNBUtil;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lj.auth.service.GlobalConfigService;
import com.lj.auth.service.TaskCenterService;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;

import static com.lj.auth.common.CommonConstant.*;

/**
 * 描述：任务中心任务任务重置 创建人: CFJ 创建时间: 2024/05/30
 */
@Component
@Slf4j
public class CheckSpOverview {
    @Resource
    private GlobalConfigService globalConfigService;
    @Resource
    private MovieOrderMapper movieOrderMapper;
    @Resource
    private Sp24hoursMapper sp24hoursMapper;
    @Resource
    private TaskCenterUserMapper taskCenterUserMapper;
    @Resource
    private EnergyRechargeFlowService energyRechargeFlowService;
    @Resource
    private DayKLineMapper dayKLineMapper;
    @Resource
    private WeekKLineMapper weekKLineMapper;
    @Resource
    private MonthKLineMapper monthKLineMapper;

    /**
     * SP单价趋势 凌晨刷新
     *
     * <AUTHOR>
     * @date 2024/05/30
     */
    @Scheduled(cron = "0 0 0 * * ? ")
    // @Scheduled(cron = "0/5 * * * * ? ")
    public void dailyTasksReset() {
        log.info("SP单价趋势修改开始");
        // 总量 SP
        BigDecimal unitPriceSp = getBigDecimal();

        Sp24hours sp24hours1 = sp24hoursMapper.selectOne(Wrappers.<Sp24hours>lambdaQuery()
            .eq(Sp24hours::getCreateTime, DateUtil.beginOfDay(DateUtil.yesterday())));
        if (sp24hours1 == null) {
            Sp24hours sp24hours = new Sp24hours();
            sp24hours.setSpPrice(String.valueOf(unitPriceSp));
            sp24hours.setCreateTime(DateUtil.beginOfDay(DateUtil.yesterday()));
            sp24hoursMapper.insert(sp24hours);
            log.info("SP单价趋势修改完成");
        }
    }

    /**
     * SP单价趋势 凌晨刷新
     *
     * <AUTHOR>
     * @date 2024/05/30
     */
    // @Scheduled(cron = "0 0 0 * * ? ")
    // 没五秒刷新一次sp单价
    @Scheduled(cron = "0/5 * * * * ? ")
    @Transactional
    public void dailyTasksReset1() {
        Date date = new Date();
//        log.info("SP单价趋势修改开始");
        // 单价 SP
        BigDecimal unitPriceSp = getBigDecimal();
        Sp24hours sp24hours = new Sp24hours();
        sp24hours.setSpPrice(String.valueOf(unitPriceSp));
        sp24hours.setCreateTime(new Date());
        sp24hoursMapper.insert(sp24hours);
//        log.info("SP单价趋势修改完成");
        // 实时修改
        toUpdateDayKLine(date, sp24hours);
        toUpdateWeekKLine(date, sp24hours);
        toUpdateMonthKLine(date, sp24hours);
    }

    /**
     * 更新 日 K线
     * 
     * <AUTHOR>
     * @date 2024/07/18
     */
    public void toUpdateDayKLine(Date date, Sp24hours sp24hours) {
//        log.info("更新 日 k 线开始");
        // 获取当前时间
        DateTime createTime = DateUtil.beginOfDay(date);
        // 获取昨天凌晨日期
        DateTime yesterday = DateUtil.beginOfDay(DateUtil.yesterday());

        // 判断 是否存在当前时间的日 k线记录
        DayKLine dayKLine = dayKLineMapper
            .selectOne(Wrappers.<DayKLine>lambdaQuery().eq(DayKLine::getCreateTime, createTime));
        // 查询昨天 的 k 线记录
        DayKLine yesterdayKLine =
            dayKLineMapper.selectOne(Wrappers.<DayKLine>lambdaQuery().eq(DayKLine::getCreateTime, yesterday));
        // 获取今天最新一条记录sp价格
        BigDecimal spPrice = new BigDecimal(sp24hours.getSpPrice());
        if (dayKLine == null) {
            DayKLine dayKLine1 = new DayKLine();
            dayKLine1.setCreateTime(createTime);
            // 设置开盘价
            dayKLine1.setStart(spPrice);
            // 最低价=开盘价
            dayKLine1.setLow(spPrice);
            // 最高价=开盘价
            dayKLine1.setMax(spPrice);
            // 实时单价=开盘价
            dayKLine1.setSpPrice(spPrice);
            if (yesterdayKLine != null) {
                BigDecimal end = yesterdayKLine.getEnd();
                String s = riseAndFallRange(end, spPrice);
                dayKLine1.setRiseAndFall(s);
            } else {
                String s = riseAndFallRange(spPrice, spPrice);
                dayKLine1.setRiseAndFall(s);
            }
            // 收盘价格=开盘价
            dayKLine1.setEnd(spPrice);
            dayKLineMapper.insert(dayKLine1);
        } else {
            // 不为空 实时更新 最低价 最高价格 实时价格 涨跌幅 收盘价格
            Map<String, BigDecimal> result = sp24hoursMapper.selectMaxAndLow();
            BigDecimal end = dayKLine.getStart();
            if (yesterdayKLine != null) {
                end = yesterdayKLine.getEnd();
            }
            // 涨跌幅
            String riseAndFallRange = riseAndFallRange(end, spPrice);
            dayKLineMapper.update(null,
                Wrappers.<DayKLine>lambdaUpdate().eq(DayKLine::getCreateTime, createTime)
                    .set(DayKLine::getLow, result.get("low")).set(DayKLine::getMax, result.get("max"))
                    .set(DayKLine::getSpPrice, spPrice).set(DayKLine::getRiseAndFall, riseAndFallRange)
                    .set(DayKLine::getEnd, spPrice));
        }

    }

    /**
     * 更新 周 K线
     *
     * @param date
     * @param sp24hours
     * <AUTHOR>
     * @date 2024/07/22
     */
    public void toUpdateWeekKLine(Date date, Sp24hours sp24hours) {
//        log.info("更新 周 k 线开始");

        // 获取当前时间
        DateTime createTime = DateUtil.beginOfDay(date);
        // 获取昨天时间
        DateTime yesterday = DateUtil.beginOfDay(DateUtil.yesterday());

        // 获取当前周结束时间
        DateTime weekTime = DateUtil.beginOfDay(DateUtil.endOfWeek(date));

        // 获取上周结束时间
        DateTime lastWeekTime = DateUtil.beginOfDay(DateUtil.endOfWeek(DateUtil.lastWeek(), true));

        // 判断 是否存在当前时间的周 k线记录
        WeekKLine weekKLine = weekKLineMapper
            .selectOne(Wrappers.<WeekKLine>lambdaQuery().eq(WeekKLine::getCreateTime, createTime));

        // 判断 是否存在昨天 周 k 线记录
        WeekKLine yesterdayWeekKLine = weekKLineMapper
            .selectOne(Wrappers.<WeekKLine>lambdaQuery().eq(WeekKLine::getCreateTime, yesterday));

        // 查询上周 的 k 线记录
        WeekKLine lastWeekKLine = weekKLineMapper
            .selectOne(Wrappers.<WeekKLine>lambdaQuery().eq(WeekKLine::getCreateTime, lastWeekTime));
        // 获取今天最新一条记录sp价格
        BigDecimal spPrice = new BigDecimal(sp24hours.getSpPrice());

        // 昨天和今天周K线为空，说明没有记录 ，需要新插入
        if (weekKLine == null && yesterdayWeekKLine == null) {
            WeekKLine weekKLine1 = new WeekKLine();
            weekKLine1.setCreateTime(createTime);
            // 设置开盘价
            weekKLine1.setStart(spPrice);
            // 最低价=开盘价
            weekKLine1.setLow(spPrice);
            // 最高价=开盘价
            weekKLine1.setMax(spPrice);
            // 实时单价=开盘价
            weekKLine1.setSpPrice(spPrice);
            if (lastWeekKLine != null) {
                BigDecimal end = lastWeekKLine.getEnd();
                String s = riseAndFallRange(end, spPrice);
                weekKLine1.setRiseAndFall(s);
            } else {
                String s = riseAndFallRange(spPrice, spPrice);
                weekKLine1.setRiseAndFall(s);
            }
            // 收盘价格=开盘价
            weekKLine1.setEnd(spPrice);
            weekKLineMapper.insert(weekKLine1);

            // 今天周K为null 昨天周 k不为空
        } else if (weekKLine == null && yesterdayWeekKLine != null) {
            // 判断昨天周k是否是 上周结束日期
            Date createTime1 = yesterdayWeekKLine.getCreateTime();
            if (DateUtil.compare(lastWeekTime, createTime1) == 0) {
                WeekKLine weekKLine1 = new WeekKLine();
                weekKLine1.setCreateTime(createTime);
                // 设置开盘价
                weekKLine1.setStart(spPrice);
                // 最低价=开盘价
                weekKLine1.setLow(spPrice);
                // 最高价=开盘价
                weekKLine1.setMax(spPrice);
                // 实时单价=开盘价
                weekKLine1.setSpPrice(spPrice);
                BigDecimal end = yesterdayWeekKLine.getEnd();
                String s = riseAndFallRange(end, spPrice);
                weekKLine1.setRiseAndFall(s);
                // 收盘价格=开盘价
                weekKLine1.setEnd(spPrice);
                weekKLineMapper.insert(weekKLine1);
            } else {
                // 不是上周 k 线 则需要更新
                // 实时更新 时间 最低价 最高价格 实时价格 涨跌幅 收盘价格
                Map<String, BigDecimal> result = sp24hoursMapper.selectWeekMaxAndLow();

                BigDecimal end = yesterdayWeekKLine.getStart();
                if (lastWeekKLine != null) {
                    end = lastWeekKLine.getEnd();
                }
                // 涨跌幅
                String riseAndFallRange = riseAndFallRange(end, spPrice);
                weekKLineMapper.update(null,
                    Wrappers.<WeekKLine>lambdaUpdate().eq(WeekKLine::getId, yesterdayWeekKLine.getId())
                        .set(WeekKLine::getCreateTime, createTime).set(WeekKLine::getLow, result.get("low"))
                        .set(WeekKLine::getMax, result.get("max")).set(WeekKLine::getSpPrice, spPrice)
                        .set(WeekKLine::getRiseAndFall, riseAndFallRange).set(WeekKLine::getEnd, spPrice));
            }
        } else if (weekKLine != null) {
            // 本周 K 线不为空 则更新 最大值 最小值 涨跌幅 实时价格 收盘价格
            Map<String, BigDecimal> result = sp24hoursMapper.selectWeekMaxAndLow();
            // 判断 上周 k线是否为空
            if (yesterdayWeekKLine != null) {
                BigDecimal end = yesterdayWeekKLine.getStart();
                if (lastWeekKLine != null) {
                    end = lastWeekKLine.getEnd();
                }
                // 涨跌幅
                String riseAndFallRange = riseAndFallRange(end, spPrice);
                weekKLineMapper.update(null,
                    Wrappers.<WeekKLine>lambdaUpdate().eq(WeekKLine::getId, weekKLine.getId())
                        .set(WeekKLine::getLow, result.get("low")).set(WeekKLine::getMax, result.get("max"))
                        .set(WeekKLine::getSpPrice, spPrice).set(WeekKLine::getRiseAndFall, riseAndFallRange)
                        .set(WeekKLine::getEnd, spPrice));
            } else {
                // 涨跌幅
                String riseAndFallRange = riseAndFallRange(weekKLine.getStart(), spPrice);
                weekKLineMapper.update(null,
                    Wrappers.<WeekKLine>lambdaUpdate().eq(WeekKLine::getId, weekKLine.getId())
                        .set(WeekKLine::getLow, result.get("low")).set(WeekKLine::getMax, result.get("max"))
                        .set(WeekKLine::getSpPrice, spPrice).set(WeekKLine::getRiseAndFall, riseAndFallRange)
                        .set(WeekKLine::getEnd, spPrice));
            }
        }

    }

    /**
     * 更新 月 K线
     *
     * @param date
     * @param sp24hours
     * <AUTHOR>
     * @date 2024/07/29
     */
    public void toUpdateMonthKLine(Date date, Sp24hours sp24hours) {
//        log.info("更新 月 k 线开始");

        // 获取当前时间
        DateTime createTime = DateUtil.beginOfDay(date);

        // 获取昨天时间
        DateTime yesterday = DateUtil.beginOfDay(DateUtil.yesterday());

        // 获取当前月结束时间
        DateTime monthTime = DateUtil.beginOfDay(DateUtil.endOfMonth(date));

        // 获取上月结束时间
        DateTime lastMonthTime = DateUtil.beginOfDay(DateUtil.endOfMonth(DateUtil.lastMonth()));

        // 判断 是否存在当前时间的月 k线记录
        MonthKLine monthKLine = monthKLineMapper
            .selectOne(Wrappers.<MonthKLine>lambdaQuery().eq(MonthKLine::getCreateTime, createTime));

        // 判断 是否存在昨天 月 k 线记录
        MonthKLine yesterdayMonthKLine = monthKLineMapper
            .selectOne(Wrappers.<MonthKLine>lambdaQuery().eq(MonthKLine::getCreateTime, yesterday));

        // 查询上月 的 k 线记录
        MonthKLine lastMonthKLine = monthKLineMapper
            .selectOne(Wrappers.<MonthKLine>lambdaQuery().eq(MonthKLine::getCreateTime, lastMonthTime));

        // 获取今天最新一条记录sp价格
        BigDecimal spPrice = new BigDecimal(sp24hours.getSpPrice());

        // 昨天和今天月K线为空，说明没有记录 ，需要新插入
        if (monthKLine == null && yesterdayMonthKLine == null) {
            MonthKLine monthKLine1 = new MonthKLine();
            monthKLine1.setCreateTime(createTime);
            // 设置开盘价
            monthKLine1.setStart(spPrice);
            // 最低价=开盘价
            monthKLine1.setLow(spPrice);
            // 最高价=开盘价
            monthKLine1.setMax(spPrice);
            // 实时单价=开盘价
            monthKLine1.setSpPrice(spPrice);
            if (lastMonthKLine != null) {
                BigDecimal end = lastMonthKLine.getEnd();
                String s = riseAndFallRange(end, spPrice);
                monthKLine1.setRiseAndFall(s);
            } else {
                String s = riseAndFallRange(spPrice, spPrice);
                monthKLine1.setRiseAndFall(s);
            }
            // 收盘价格=开盘价
            monthKLine1.setEnd(spPrice);
            monthKLineMapper.insert(monthKLine1);
            // 今天月K为null 昨天月 k不为空
        } else if (monthKLine == null && yesterdayMonthKLine != null) {
            // 判断昨天月k是否是 上月结束日期
            Date createTime1 = yesterdayMonthKLine.getCreateTime();
            if (DateUtil.compare(lastMonthTime, createTime1) == 0) {
                MonthKLine monthKLine1 = new MonthKLine();
                monthKLine1.setCreateTime(createTime);
                // 设置开盘价
                monthKLine1.setStart(spPrice);
                // 最低价=开盘价
                monthKLine1.setLow(spPrice);
                // 最高价=开盘价
                monthKLine1.setMax(spPrice);
                // 实时单价=开盘价
                monthKLine1.setSpPrice(spPrice);
                BigDecimal end = yesterdayMonthKLine.getEnd();
                String s = riseAndFallRange(end, spPrice);
                monthKLine1.setRiseAndFall(s);
                // 收盘价格=开盘价
                monthKLine1.setEnd(spPrice);
                monthKLineMapper.insert(monthKLine1);
            } else {
                // 不是上月 k 线 则需要更新
                // 实时更新 时间 最低价 最高价格 实时价格 涨跌幅 收盘价格
                Map<String, BigDecimal> result = sp24hoursMapper.selectMonthMaxAndLow();
                BigDecimal end = yesterdayMonthKLine.getStart();
                if (lastMonthKLine != null) {
                    end = lastMonthKLine.getEnd();
                }
                // 涨跌幅
                String riseAndFallRange = riseAndFallRange(end, spPrice);
                monthKLineMapper.update(null,
                    Wrappers.<MonthKLine>lambdaUpdate().eq(MonthKLine::getId, yesterdayMonthKLine.getId())
                        .set(MonthKLine::getCreateTime, createTime).set(MonthKLine::getLow, result.get("low"))
                        .set(MonthKLine::getMax, result.get("max")).set(MonthKLine::getSpPrice, spPrice)
                        .set(MonthKLine::getRiseAndFall, riseAndFallRange).set(MonthKLine::getEnd, spPrice));
            }

        } else if (monthKLine != null) {
            // 本周 K 线不为空 则更新 最大值 最小值 涨跌幅 实时价格 收盘价格
            Map<String, BigDecimal> result = sp24hoursMapper.selectMonthMaxAndLow();

            // 判断 上月 k线是否为空
            if (yesterdayMonthKLine != null) {
                BigDecimal end = yesterdayMonthKLine.getStart();
                if (lastMonthKLine != null) {
                    end = lastMonthKLine.getEnd();
                }
                // 涨跌幅
                String riseAndFallRange = riseAndFallRange(end, spPrice);
                monthKLineMapper.update(null,
                    Wrappers.<MonthKLine>lambdaUpdate().eq(MonthKLine::getId, monthKLine.getId())
                        .set(MonthKLine::getLow, result.get("low")).set(MonthKLine::getMax, result.get("max"))
                        .set(MonthKLine::getSpPrice, spPrice).set(MonthKLine::getRiseAndFall, riseAndFallRange)
                        .set(MonthKLine::getEnd, spPrice));
            } else {
                // 涨跌幅
                String riseAndFallRange = riseAndFallRange(monthKLine.getStart(), spPrice);
                monthKLineMapper.update(null,
                    Wrappers.<MonthKLine>lambdaUpdate().eq(MonthKLine::getId, monthKLine.getId())
                        .set(MonthKLine::getLow, result.get("low")).set(MonthKLine::getMax, result.get("max"))
                        .set(MonthKLine::getSpPrice, spPrice).set(MonthKLine::getRiseAndFall, riseAndFallRange)
                        .set(MonthKLine::getEnd, spPrice));
            }
        }

    }

    public BigDecimal getBigDecimal() {
        BigDecimal circulatingMarketValue = energyRechargeFlowService.obtainCirculatingMarketValue();
        // 单价
        BigDecimal unitPriceSp = circulatingMarketValue.divide(circulationSp(), 18, RoundingMode.HALF_UP);
        return unitPriceSp;
    }

    /**
     * 流通SP 计算
     *
     * @param totalSp
     * @return {@link BigDecimal }
     * <AUTHOR>
     * @date 2024/06/04
     */
    private BigDecimal circulationSp() {
        BigDecimal result = taskCenterUserMapper.circulationVolumeValue();
        return result;
    }

    public static void main(String[] args) {
        DateTime lastWeekTime = DateUtil.beginOfDay(DateUtil.endOfMonth(DateUtil.lastMonth()));
        System.out.println(lastWeekTime);

    }

    /**
     * 判断是否是凌晨
     * 
     * @return boolean
     * <AUTHOR>
     * @date 2024/07/18
     */
    public boolean checkIfItsEarlyMorning(Date date) {
        LocalDateTime localDateTime = DateUtil.toLocalDateTime(date);
        int hour = localDateTime.getHour();
        if (hour == 0) {
            return true;
        }
        return false;
    }

    public String riseAndFallRange(BigDecimal yesterDay, BigDecimal day) {
        BigDecimal difference = day.subtract(yesterDay);
        BigDecimal divide = difference.divide(yesterDay, 4, RoundingMode.HALF_UP);
        BigDecimal bigDecimal = divide.setScale(4, RoundingMode.HALF_UP);
        DecimalFormat decimalFormat = new DecimalFormat("#.##%");
        String format = decimalFormat.format(bigDecimal.doubleValue());
        return format;
    }
}