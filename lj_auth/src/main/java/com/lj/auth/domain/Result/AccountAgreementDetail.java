package com.lj.auth.domain.Result;

import lombok.Data;

import java.util.Date;


/**
 * <AUTHOR>
 * @describe
 */

@Data
public class AccountAgreementDetail {

    /**
     * 用户协议已读且确认的最新一条版本号
     */
   private String accountAgreementReadAndConfirmVersion;

    /**
     * 用户协议最新版本号
     */
    private String accountAgreementLatestVersion;


    /**
     * 用户协议已读时间
     */
    private Date accountAgreementReadAndConfirmTime;



}
