package com.lj.auth.domain.vo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 描述：邀请用户任务展示Vo 创建人: CFJ 创建时间: 2024/05/29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class TaskUserVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户uuid
     */
    private String accountUuid;

    /**
     * 任务中心id
     */
    private Integer taskCenterId;

    /**
     * 完成任务id
     */
    private String taskIdCompleted;

    /**
     * 任务奖励
     */
    private BigDecimal reward;

    /**
     * 0-未完成任务 1-完成任务
     */
    private Integer isComplete;

    /**
     * 是否领取奖励1-已领取 0-未领取
     */
    private Integer isReceive;

    /**
     * 任务上链奖励hash
     */
    private String hash;

    /**
     * 上链状态1-成功0-失败
     */
    private Integer state;

    /**
     * 实际任务完成时间
     */
    private Date completionTime;

    /**
     * 实际任务完成数量
     */
    private Integer completionCount;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;
    // ===================================

    /**
     *
     * 普通昵称
     */
    private String nickName;

    /**
     * 域名昵称
     */
    private String domainNickName;

    /**
     * 1-昵称展示 2-域名昵称展示
     */
    private Integer showType;

    /**
     * 普通图像
     */
    private String headPortrait;

    /**
     * nft图像Id
     */
    private Integer headPortraitNftId;

    /**
     * 图像类型1-普通图像 2-nft图像
     */
    private Integer headPortraitType;

    /**
     * 注册时间
     */
    private Date registerTime;

    /**
     * nft图像URl
     */
    private String nftImage;

}
