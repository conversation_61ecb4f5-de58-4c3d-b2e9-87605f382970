package com.lj.auth.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.*;
import lombok.experimental.Accessors;

/**
 * <p>
 * 终端用户充提订单表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-11
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("ym_recharge_withdraw_record")
public class RechargeWithdrawRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 经销商uuid
     */
    private String operateUuid;

    /**
     * 终端用户uuid
     */
    private String accountUuid;

    /**
     * 银行卡号
     */
    private String cardNumber;

    /**
     * 开户行
     */
    private String bank;

    /**
     * 开户姓名
     */
    private String cardName;

    /**
     * 充值/提现金额
     */
    private BigDecimal amount;

    /**
     * 充值/提现时余额
     */
    private BigDecimal balance;

    /**
     * 状态( 1-审核中，2-审核失败，3-提现/充值成功 4-用户取消 5-超时 6-待支付)
     */
    private Integer state;

    /**
     * 审核失败原因
     */
    private String reason;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 审核时间
     */
    private Date updateTime;

    /**
     * 类型 1:充值 2:提现 3-购买域名消耗 4-购买域名退款 5-划转 6-下级注册推广返佣 7-自己注册域名返佣 8-推广大使升级消耗 9-申请经销商消耗 10-实名DID及个人身份信息凭证申领(消耗)
     * 11-实名DID及个人身份信息凭证申领(退款) 12-信息凭证申领(消耗) 13-信息凭证申领(退款) 14-DID申领 15-DID申领(退款) 16-恢复私钥 17-恢复私钥(退款)18-域名转让
     */
    private Integer type;

    /**
     * 回执姓名(充值用)
     */
    private String receiptName;

    /**
     * 回执手机号(充值用)
     */
    private String receiptPhone;

    /**
     * 回执图片(充值用)
     */
    private String receiptPicture;

    /**
     * 充值转账备注码(充值用)
     */
    private String rechargeCode;

    /**
     * 钉钉消息推送次数
     */
    private Integer robotPushMessage;

    /**
     * 订单表ID(ym_order)
     */
    private Long orderId;

    /**
     * 交易流水号
     */
    private String tranNumber;

    /**
     * 支付方式 1:微信 2:支付宝 3:余额 4:银行卡 5:pc聚合支付  6:抖音支付
     */
    private Integer payType;

    /**
     * 二维码ID
     */
    private String qrCodeId;

    /**
     * 平台：1:H5 2:pc 3:app 4:微信小程序 5:抖音小程序
     */
    private Integer platform;

    /**
     * 费率
     */
    private String rate;

    /**
     * 实际提现金额
     */
    private BigDecimal realAmount;

    /**
     * 抖音充值抖音侧唯一订单号
     */
    private String douyinOrderId;

    /**
     * 签名后的订单信息
     */
    private String douyinOrderToken;

    /**
     * 分账状态 0:未分账 1:分账已完成
     */
    private Integer settleStatus;

    /**
     * 退款状态 0-未退款 1-已退款 2-退款中 3-退款失败 5-已完成(不能退款)
     */
    private Integer refundState;

    /**
     * 税率
     */
    private BigDecimal taxRate;

    /**
     * 税额
     */
    private BigDecimal taxAmount;

    /**
     * 提现服务费
     */
    private BigDecimal withdrawServiceCharge;

    private String remark;

    /**
     * 有效期
     */
    private Date validityTime;

    /**
     * 状态：1-福穗平台待打款
     */
    private Integer status;

    /**
     * 1-域名门户2-灵戒APP
     */
    @TableField("source_Funds")
    private Integer sourceFunds;

}
