package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
    * NFT退款表
    */
@Data
@TableName(value = "ym_order_domain_renewal_nft_refund")
public class OrderDomainRenewalNftRefund implements Serializable {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * ym_order_domain_renewal_info主键
     */
    @TableField(value = "order_info_id")
    private Long orderInfoId;

    /**
     * ym_order_domain_renewal_nft主键
     */
    @TableField(value = "order_nft_id")
    private Long orderNftId;

    /**
     * 合约地址
     */
    @TableField(value = "contract_address")
    private String contractAddress;

    /**
     * TOKENID
     */
    @TableField(value = "token")
    private Integer token;

    /**
     * 交易状态 1-待交易 2-交易中(已提交上链) 3-交易完成/成功 4-交易失败
     */
    @TableField(value = "tran_state")
    private Integer tranState;

    /**
     * 交易HASH
     */
    @TableField(value = "hash")
    private String hash;

    /**
     * 发送地址
     */
    @TableField(value = "from_address")
    private String fromAddress;

    /**
     * 接收地址
     */
    @TableField(value = "to_address")
    private String toAddress;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    private static final long serialVersionUID = 1L;

    /**
     * 交易状态 待交易
     */
    public static final Integer TRAN_STATE_WAIT = 1;

    /**
     * 交易状态 交易中
     */
    public static final Integer TRAN_STATE_PROCESSING = 2;

    /**
     * 交易状态 交易完成
     */
    public static final  Integer TRAN_STATE_SUCCESS = 3;

    /**
     * 交易状态 交易失败
     */
    public static final Integer TRAN_STATE_FAIL = 4;
}