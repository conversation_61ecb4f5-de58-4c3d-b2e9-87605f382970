package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "lj_auth_nft_transfer_record")
public class NftTransferRecord implements Serializable {
    public static final Integer STATUS_FAIL = 0;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 链框架id
     */
    @TableField(value = "opb_chain_id")
    private Integer opbChainId;

    /**
     * 合约id
     */
    @TableField(value = "contract_id")
    private Long contractId;

    /**
     * tokenId
     */
    @TableField(value = "token_id")
    private Integer tokenId;

    /**
     * 合约地址
     */
    @TableField(value = "contract_address")
    private String contractAddress;

    /**
     * 发送者地址
     */
    @TableField(value = "from_address")
    private String fromAddress;

    /**
     * 接收者地址
     */
    @TableField(value = "to_address")
    private String toAddress;

    /**
     * 能量值
     */
    @TableField(value = "energy_value")
    private String energyValue;

    /**
     * 交易hash
     */
    @TableField(value = "transaction_hash")
    private String transactionHash;

    /**
     * 区块高度
     */
    @TableField(value = "block_number")
    private Integer blockNumber;

    /**
     * 交易类型  1-铸币 2-转账  3-销毁  4-单个token授权  5-批量授权
     */
    @TableField(value = "transaction_type")
    private Integer transactionType;

    /**
     * 状态  0-交易失败   1-交易成功
     */
    @TableField(value = "`status`")
    private Integer status;

    /**
     * 逻辑删除 1:是 0:否(默认)
     */
    @TableField(value = "is_delete")
    private Boolean isDelete;


    /**
     * 交易类型 铸币
     *
     */
    public static final Integer TRANSACTION_TYPE_MINT=1;

    private static final long serialVersionUID = 1L;
}