package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

/**
    * DID返佣关联表
    */
@Data
@Accessors(chain = true)
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "ym_did_rebate")
public class DidRebate {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 申领DID类型 1-流水 2-订单
     */
    @TableField(value = "`type`")
    private Integer type;

    /**
     * 申领DID订单表主键，type=1[ym_recharge_flow主键],type=2[]
     */
    @TableField(value = "order_id")
    private Long orderId;

    /**
     * 账户uuid
     */
    @TableField(value = "account_uuid")
    private String accountUuid;

    /**
     * 实付金额
     */
    @TableField(value = "amount")
    private BigDecimal amount;

    /**
     * 当前账户返佣比例
     */
    @TableField(value = "this_account_scale")
    private BigDecimal thisAccountScale;

    /**
     * 当前账户返佣金额
     */
    @TableField(value = "this_account_amount")
    private BigDecimal thisAccountAmount;

    /**
     * 上级账户uuid
     */
    @TableField(value = "parent_uuid")
    private String parentUuid;

    /**
     * 上级账户返佣比例
     */
    @TableField(value = "parent_account_scale")
    private BigDecimal parentAccountScale;

    /**
     * 上级账户返佣金额
     */
    @TableField(value = "parent_account_amount")
    private BigDecimal parentAccountAmount;

    /**
     * 本级经销商uuid
     */
    @TableField(value = "this_operate_uuid")
    private String thisOperateUuid;

    /**
     * 本级经销商返佣比例
     */
    @TableField(value = "this_operate_scale")
    private BigDecimal thisOperateScale;

    /**
     * 本地经销商返佣金额
     */
    @TableField(value = "this_operate_amount")
    private BigDecimal thisOperateAmount;

    /**
     * 上级经销商uuid
     */
    @TableField(value = "parent_operate_uuid")
    private String parentOperateUuid;

    /**
     * 上级经销商返佣比例
     */
    @TableField(value = "parent_operate_scale")
    private BigDecimal parentOperateScale;

    /**
     * 上级经销商返佣金额
     */
    @TableField(value = "parent_operate_amount")
    private BigDecimal parentOperateAmount;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 返佣状态 1-待发放 2-发放中 3-已发放 4-订单失效(不返佣)
     */
    @TableField(value = "rebate_state")
    private Integer rebateState;
}