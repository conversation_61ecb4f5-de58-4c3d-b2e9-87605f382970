package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 广场关注
 */
@Data
@TableName(value = "lj_square_follow")
public class SquareFollow implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户uuid(如张三关注李四，此为李四)
     */
    @TableField(value = "account_uuid")
    private String accountUuid;

    /**
     * 关注用户的uuid(如张三关注李四，此为张三)
     */
    @TableField(value = "follow_uuid")
    private String followUuid;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 已读状态 0-未读 1-已读
     */
    @TableField(value = "read_flag")
    private Integer readFlag;

    /**
     * 0-未删除 1-已删除
     */
    @TableField(value = "remove_flag")
    private Integer removeFlag;

    private static final long serialVersionUID = 1L;
}