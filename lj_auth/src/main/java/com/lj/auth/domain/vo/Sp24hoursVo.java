package com.lj.auth.domain.vo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 源力24小时趋势
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04
 */
@Data
public class Sp24hoursVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Integer id;

    /**
     * 日期
     */
    private Date createTime;

    /**
     * sp单价
     */
    private BigDecimal spPrice;

}
