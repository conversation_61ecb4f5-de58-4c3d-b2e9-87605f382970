package com.lj.auth.domain;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 账号注销条件记录
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("lj_auth_account_logout_condition_record")
public class AccountLogoutConditionRecord implements Serializable {

    private static final long serialVersionUID=1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 注销条件id
     */
    private Integer conditionId;

    /**
     * 用户uuid
     */
    private String accountUuid;

    /**
     * 是否忽略:0-未处理 1-已忽略 2-已处理3-后台数据已清除
     */
    private Integer isIgnore;



}
