package com.lj.auth.domain.vo.orderDetail;

import lombok.Data;

/**
 * @describe：订单行
 * @author: cfj
 * @date: 2024/12/09
 */
@Data
public class OrderRows {
    private String  title;
    private String  value;
    private String  valueIcon;
    private StyleTitleVo valueStyle;
    /**
     * 1-能复制 0-不能复制
     */
    private  Integer copy;

    /**
     * 复制内容
     */
    private String  copyContent;


    /**
     * 描述
     */
    private String  desc;

    /**
     * 1-有详情 0-没有
     */
    private Integer hasDetail;
    private String detailTitle;
    private StyleTitleVo detailTitleStyle;
}
