package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
    * 市场封面配置表
    */
@Data
@TableName(value = "lj_transfer_cover_info")
public class CoverInfo implements Serializable {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 封面图片
     */
    @TableField(value = "image")
    private String image;

    /**
     * 状态   0；关闭  1：正常(默认） 
     */
    @TableField(value = "`status`")
    private String status;

    /**
     * 类型：1-一口价封面 2-拍卖封面
     */
    @TableField(value = "`type`")
    private Integer type;

    /**
     * 描述
     */
    @TableField(value = "describes")
    private String describes;

    /**
     * 排序 从小到大
     */
    @TableField(value = "sort")
    private Integer sort;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}