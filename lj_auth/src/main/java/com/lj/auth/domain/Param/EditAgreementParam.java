package com.lj.auth.domain.Param;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @describe
 */
@Data
public class EditAgreementParam {

    /**
     * 修改的时候传 versionId
     */
    private Integer versionId;

    /**
     * 1:用户协议 2:隐私协议
     */
    @NotNull
    private Integer agreementType;

    /**
     * 版本标题
     */
    @NotBlank
    private String versionTitle;

    /**
     * 版本号 数字
     */
    @NotBlank
    private String versionNumber;


    /**
     * 更新内容
     */
    @NotBlank
    private String updateContent;



    /**
     * 协议内容
     */
    @NotBlank
    private String agreementContent;


}
