package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;

import com.lj.auth.annotation.PrefixPath;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

/**
 * 贡献
 */
@Data
@Accessors(chain = true)
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "lj_auth_contribution")
public class Contribution {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户uuid
     */
    @TableField(value = "account_uuid")
    private String accountUuid;

    /**
     * 贡献金额（单位：元）
     */
    @TableField(value = "amount")
    private BigDecimal amount;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    @TableField(exist = false)
    private Integer ranking;

    @TableField(exist = false)
    private String nickName;

    @TableField(exist = false)
    private String domainNickName;

    @TableField(exist = false)
    private Integer showType;

    @TableField(exist = false)
    @PrefixPath
    private String headPortrait;

    @TableField(exist = false)
    private Integer headPortraitNftId;

    @TableField(exist = false)
    private Integer headPortraitType;

    @TableField(exist = false)
    private String nftImage;

    @TableField(exist = false)
    private String didSymbol;

    /**
     * im好友关系默认不是好友0
     */
    @TableField(exist = false)
    private Integer imFriend =0;

    /**
     * 关注关系默认0-不是
     */
    @TableField(exist = false)
    private Integer isFollow =0;


    /**
     * 佩戴徽章图片
     */
    @TableField(exist = false)
    @PrefixPath
    private String badgeImage;

    /**
     * 佩戴挂件图片
     */
    @TableField(exist = false)
    @PrefixPath
    private String avatarFrameImage;

}