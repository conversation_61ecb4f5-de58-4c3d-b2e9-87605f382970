package com.lj.auth.domain.vo;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.TableField;
import com.lj.auth.annotation.PrefixPath;
import com.lj.auth.domain.Nft;
import com.lj.auth.domain.Operate;

import lombok.Data;

@Data
public class AccountVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 手机号
     */
    private String phoneNumber;

    /**
     * uuid
     */
    private String uuid;


    /**
     * 支付密码
     */
    private String payPassword;

    /**
     * 头像
     */
    @PrefixPath
    private String headPortrait;

    /**
     * 个人主页背景图片
     */
    @PrefixPath
    private String backgroundImg;

    /**
     * 昵称
     */
    private String nickName;




    /**
     * 是否实名认证 0:未认证(默认) 1:认证中 2:认证通过 3:认证失败
     */
    private Integer isRealName;

    /**
     * 用户状态 1:正常 2:黑名单 3:已注销
     */
    private Integer blacklist;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 经销商uuid
     */
    private String operateUuid;


    /**
     * 用户标识 1普通用户 2经销商 3渠道商
     */
    private Integer identity;

    /**
     * uuid后缀
     */
    private String uuidSuffix;

    /**
     * 上级uuid 注册时填入
     */
    private String parentUuid;

    /**
     * 是否禁言 1:禁言 0:否(默认)
     */
    private Boolean isBanned;


    /**
     * 推广等级 0-lv1推广大使(待激活) 1-lv1推广大使2-lv2推广大使 3-lv3推广大使 4-默认(不满足条件) 9-推广大使(新等级)
     */
    private Integer promotionLevel;

    /**
     * 推广返佣状态 1-参与返佣(默认) 2-不参与返佣
     */
    private Integer promotionRebateState;

    /**
     * 活跃标识 1:活跃 0:非活跃
     */
    private Boolean activeFlag;

    /**
     * DID标识
     */
    private String didSymbol;

    /**
     * 福穗平台人员id
     */
    private String peopleId;

    /**
     * 福穗平台签约状态(0-待处理 1-校验失败 2-待签约 3-签约成功 4-签约校验错误)
     */
    private Integer signStatus;

    /**
     * 签约地址
     */
    private String signUrl;


    /**
     * 邀请码
     */
    private String inviteCode;

    // 经销商信息
    private Operate operate;
    // nft信息
    private Nft nft;

    /**
     * 1-昵称展示 2-域名昵称展示
     */
    private Integer showType;

    /**
     * 域名昵称
     */
    private String domainNickName;

    /**
     * 注册来源：1-灵戒App 2-域名门户
     */
    private Integer registrationSource;

    /**
     * 图像类型 1-普通图像 2-nft图像
     */
    private Integer headPortraitType;

    /**
     * nft表(lj_auth_nft)的id
     */
    private Long headPortraitNftId;


    private String browserPortalTitle;

    /**
     * 是否可添加好友 1:可添加  0:不可添加 2:未导入im
     */
    private Integer avaliableAddFriend=2;

    /**
     * 关注关系默认-不是
     */
    private Integer isFollow =0;

    /**
     * 域名昵称标识图片
     */
    @PrefixPath
    private String domainNickNameSignImage;
    /**
     * 推广大使等级图片
     */
    @PrefixPath
    private String exitAmbassadorLevelPicture;


    /**
     * 推广DID标识图片
     */
    @PrefixPath
    private String tgDidSignImage;

    /**
     * 推广裂空者图片
     */
    @PrefixPath
    private String tgLkzSignImage;

    /**
     *裂空者等级
     */
    private Integer skySplitLevel;


    /**
     * 佩戴徽章图片
     */
    @PrefixPath
    private String badgeImage;

    /**
     * 佩戴挂件图片
     */
    @PrefixPath
    private String avatarFrameImage;

    /**
     * 裂空者图片 1-有效0-无效
     */
    private Integer lkzImageState=1;

    /**
     * 裂空者开通时间
     */
    private Date  lkzCreateTime;

    /**
     * 无效裂空者提示
     */
    private String lkzPrompt;

    /**
     * 徽章列表
     */
    private List<BadgeVo> badgeVos;
}
