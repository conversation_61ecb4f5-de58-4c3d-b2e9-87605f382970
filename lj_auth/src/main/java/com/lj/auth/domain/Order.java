package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 域名-订单表
 */
@Data
@TableName(value = "ym_order")
public class Order implements Serializable {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 域名
     */
    @TableField(value = "domain_name")
    private String domainName;

    /**
     * 所有者地址
     */
    @TableField(value = "owner_address")
    private String ownerAddress;

    /**
     * 注册时长
     */
    @TableField(value = "duration")
    private Integer duration;

    /**
     * 总金额(售卖价格)
     */
    @TableField(value = "total_amount")
    private BigDecimal totalAmount;

    /**
     * 底价(首年的底价)
     */
    @TableField(value = "floor_price")
    private BigDecimal floorPrice;

    /**
     * 域名等级 1:一级域名 2:子域名
     */
    @TableField(value = "`level`")
    private Integer level;

    /**
     * 支付状态 1:待支付/交易创建 2:支付完成/支付成功 3:支付失败 4:支付超时/交易超时/交易关闭 5:交易结束/不可退款
     */
    @TableField(value = "pay_status")
    private Integer payStatus;

    /**
     * 交易类型 1:域名注册 2:域名注销 3:设置所有者 4:设置管理者 10-域名交易, 11-域名转让
     */
    @TableField(value = "tran_type")
    private Integer tranType;

    /**
     * 域名服务流水号
     */
    @TableField(value = "domain_server_sn")
    private String domainServerSn;

    /**
     * 用户交易流水号
     */
    @TableField(value = "trade_code")
    private String tradeCode;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 支付方式  1:微信 2:支付宝 3:余额 4:银行卡 5:pc聚合支付 6:抖音支付
     */
    @TableField(value = "pay_type")
    private Integer payType;

    /**
     * 支付(商户订单号)，域名(分销商订单号)
     */
    @TableField(value = "out_trade_no")
    private String outTradeNo;

    /**
     * 经销商uuid
     */
    @TableField(value = "operate_uuid")
    private String operateUuid;

    /**
     * 终端用户uuid
     */
    @TableField(value = "account_uuid")
    private String accountUuid;

    /**
     * 域名类型 1-普通域名 2-星级域名 3-星级域名(到期未续费释放,无需购买申请权,直接调用注册接口)
     */
    @TableField(value = "`type`")
    private Integer type;

    /**
     * 上级账户uuid
     */
    @TableField(value = "parent_uuid")
    private String parentUuid;

    /**
     * 订单交易流水号(微信支付、抖音支付)
     */
    @TableField(value = "tran_number")
    private String tranNumber;

    /**
     * 退款订单号(微信支付、抖音支付)
     */
    @TableField(value = "refund_number")
    private String refundNumber;

    /**
     * 退款状态 1-未退款 2-已退款 3-退款中 4-退款失败
     */
    @TableField(value = "refund_state")
    private Integer refundState;

    /**
     * 平台：1:H5 2:pc 3:app 4:微信小程序 5:抖音小程序
     */
    @TableField(value = "platform")
    private Integer platform;

    /**
     * 原始价格(未使用优惠券的金额)
     */
    @TableField(value = "primitive_amount")
    private BigDecimal primitiveAmount;

    /**
     * 优惠金额
     */
    @TableField(value = "discount_amount")
    private BigDecimal discountAmount;

    /**
     * 优惠券ID
     */
    @TableField(value = "coupon_id")
    private Long couponId;

    /**
     * 优惠券码
     */
    @TableField(value = "coupon_number")
    private String couponNumber;

    /**
     * 使用优惠券手机号
     */
    @TableField(value = "coupon_phone")
    private String couponPhone;

    /**
     * 域名交易状态 1-售出 2-购入
     */
    @TableField(value = "transaction_state")
    private Integer transactionState;

    /**
     * 域名转让状态 1-转让 2-转入
     */
    @TableField(value = "transfer_state")
    private Integer transferState;

    /**
     * 交易hash(域名转让)
     */
    @TableField(value = "transaction_hash")
    private String transactionHash;

    /**
     * 平台来源 1-门户 2-灵戒
     */
    @TableField(value = "platform_source")
    private Integer platformSource;

    /**
     * 订单编号(关联汇总订单表)
     */
    @TableField(value = "order_number")
    private String orderNumber;

    /**
     * 订单标记 1-原订单 2-合并订单改造
     */
    @TableField(value = "order_mark")
    private Integer orderMark;

    private static final long serialVersionUID = 1L;
}