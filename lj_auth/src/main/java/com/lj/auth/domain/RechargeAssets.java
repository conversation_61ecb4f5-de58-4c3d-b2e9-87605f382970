package com.lj.auth.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 终端用户充值资产表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("ym_recharge_assets")
public class RechargeAssets implements Serializable {

    private static final long serialVersionUID=1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 经销商uuid
     */
    private String operateUuid;

    /**
     * 终端用户uuid
     */
    private String accountUuid;

    /**
     * 总充值金额(只会增加，不会减少)
     */
    private BigDecimal totalAmount;

    /**
     * 当前余额
     */
    private BigDecimal balance;

    /**
     * 冻结量
     */
    private BigDecimal freeze;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;


}
