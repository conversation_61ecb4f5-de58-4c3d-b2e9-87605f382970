package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * @author: wxm
 * @description:
 * @date: 2024/12/26 11:34
 */
@Data
@TableName(value = "lj_home_page")
public class HomePage {

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 渠道 JIEWAI,XIAOMI,HUAWEI,OPPO,VIVO,HONOUR,APPSTORE,WXMP
     */
    @TableField("channel")
    private String channel;
    /**
     * 区域 1-顶部导航栏 2-固定金刚区 3-轮播图 4-功能金刚区 5-站位图 6-场景组件卡片区 7-首页背景图 8-底部导航栏
     */
    @TableField("area")
    private String area;
    /**
     * 单行最大数量
     */
    @TableField("single_row_max")
    private String singleRowMax;
    /**
     * 最大行数
     */
    @TableField("max_row_number")
    private String maxRowNumber;
    /**
     * 是否展示 0-关闭 1-开启
     */
    @TableField("show_flag")
    private String showFlag;
    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;
    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

}
