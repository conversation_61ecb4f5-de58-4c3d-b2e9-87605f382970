package com.lj.auth.domain.vo;

import com.alibaba.fastjson2.JSONObject;
import com.lj.auth.domain.Nft;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class NftVo {

    /**
     * nft名称
     */
    private String nftName;

    /**
     * nft图像
     */
    private String nftImage;

    /**
     * nft价格
     */
    private BigDecimal nftPrice;

    /**
     * nft基础信息
     */
    private NftBaseInfo nftBaseInfo;

    /**
     * nft交易记录
     */
    private List<TransferRecord> transferRecords;

    /**
     * nft属性
     */
    private JSONObject jsonObject;

    /**
     * nft所在合约信息
     */
    private ContractInfo contractInfo;

    @Data
    public static class NftBaseInfo {

        /**
         * 链名称
         */
        private String chainName;

        /**
         * 合约地址
         */
        private String contractAddress;

        /**
         * 创建者
         */
        private String creator;

        /**
         * 持有者
         */
        private String holder;

        /**
         * 标准
         */
        private String standard;

        /**
         * token编号
         */
        private Integer tokenId;
    }

    @Data
    public static class TransferRecord {

        /**
         * 主键
         */
        private Long id;
        /**
         * 创建时间
         */
        private Date createTime;
        /**
         * 合约地址
         */
        private String contractAddress;

        /**
         * 发送者地址
         */
        private String fromAddress;

        /**
         * 接收者地址
         */
        private String toAddress;

        /**
         * 交易类型
         */
        private Integer transactionType;
    }

    @Data
    public static class ContractInfo {

        private Long contractId;

        private String contractName;

        private String contractUrl;

        private BigInteger items;

        private String contractDescribe;

        private String contractAddress;

        private String standard;

        private List<Nft> nft;
    }
}
