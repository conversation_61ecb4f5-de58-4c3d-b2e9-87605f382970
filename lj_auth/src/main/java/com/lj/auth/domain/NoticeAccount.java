package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.util.Date;

/**
* <AUTHOR>
* @Description 
* @date 2025/3/20 14:52
*/

/**
 * 系统通知账户关联表
 */
@Data
@Accessors(chain = true)
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "lj_auth_notice_account")
public class NoticeAccount {
    private static final long serialVersionUID = 1L;
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 系统通知id
     */
    @TableField(value = "notice_id")
    private Long noticeId;
    
    /**
     * 账户UUID
     */
    @TableField(value = "account_uuid")
    private String accountUuid;
    
    /**
     * 状态 1-未读 2-已读
     */
    @TableField(value = "`state`")
    private Integer state;
    
    /**
     * 消息来源:0-系统消息 1-系统公告2-转账通知3-市场通知
     */
    @TableField(value = "`source`")
    private Integer source;
    
    /**
     * 已读时间
     */
    @TableField(value = "read_time")
    private Date readTime;
    
    /**
     * 是否今日已读1-今日已读
     */
    @TableField(value = "today_is_read")
    private Integer todayIsRead;
}