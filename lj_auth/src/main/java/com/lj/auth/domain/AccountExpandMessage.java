package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
    * 账户扩展信息表
    */
@Data
@TableName(value = "account_expand_message")
public class AccountExpandMessage implements Serializable {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * account表主键id
     */
    @TableField(value = "account_id")
    private Long accountId;

    /**
     * 手机号
     */
    @TableField(value = "phone_number")
    private String phoneNumber;

    /**
     * uuid
     */
    @TableField(value = "uuid")
    private String uuid;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 性别：1-男0-女2-保密
     */
    @TableField(value = "gender")
    private Integer gender;

    /**
     * 生日
     */
    @TableField(value = "birthday")
    private String birthday;

    /**
     * 生日0-不展示1-展示
     */
    @TableField(value = "bir_display_flag")
    private Integer birDisplayFlag;

    /**
     * 星座
     */
    @TableField(value = "constellation")
    private String constellation;

    /**
     * 星座0-不展示1-展示
     */
    @TableField(value = "cons_display_flag")
    private Integer consDisplayFlag;

    /**
     * 地区
     */
    @TableField(value = "region_name")
    private String regionName;

    /**
     * 经纬度
     */
    @TableField(value = "center")
    private String center;

    /**
     * 地区0-不展示1-展示
     */
    @TableField(value = "reg_display_flag")
    private Integer regDisplayFlag;

    /**
     * 最近登录地区
     */
    @TableField(value = "recently_region")
    private String recentlyRegion;

    /**
     * 简介
     */
    @TableField(value = "introduction")
    private String introduction;

    /**
     * 兴趣标签0-不展示1-展示
     */
    @TableField(value = "tag_display_flag")
    private Integer tagDisplayFlag;

    private static final long serialVersionUID = 1L;
}