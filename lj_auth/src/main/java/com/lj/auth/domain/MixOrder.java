package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 聚合订单表
 */
@Data
@TableName(value = "mix_order")
public class MixOrder implements Serializable {
    /**
     * id 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 订单编号
     */
    @TableField(value = "order_number")
    private String orderNumber;

    /**
     * 应用id
     */
    @TableField(value = "applicaton_id")
    private Integer applicatonId;

    /**
     * 应用名称
     */
    @TableField(value = "application_name")
    private String applicationName;

    /**
     * 账户uuid
     */
    @TableField(value = "account_uuid")
    private String accountUuid;

    /**
     * 用户did标识
     */
    @TableField(value = "did_symbol")
    private String didSymbol;

    /**
     * 订单金额
     */
    @TableField(value = "order_amount")
    private BigDecimal orderAmount;

    /**
     * 实际金额
     */
    @TableField(value = "actual_amount")
    private BigDecimal actualAmount;

    /**
     * 订单状态 0:待支付
     */
    @TableField(value = "order_status")
    private Integer orderStatus;

    /**
     * 订单简易状态
     */
    @TableField(value = "simple_status")
    private Integer simpleStatus;

    /**
     * 支付状态 0:待支付 1:已支付 2:支付失败 3:支付超时关闭的交易 4:未支付关闭的订单
5:支付成功全额退款成功关闭的交易
     */
    @TableField(value = "pay_status")
    private Integer payStatus;

    /**
     * 三方支付 商户订单号
     */
    @TableField(value = "pay_out_trade_no")
    private String payOutTradeNo;

    /**
     * 支付方式  1:微信  2：支付宝  3:余额 4:业务方支付
     */
    @TableField(value = "pay_type")
    private Integer payType;

    /**
     * 支付金额
     */
    @TableField(value = "pay_amount")
    private BigDecimal payAmount;

    /**
     * 支付过期时间
     */
    @TableField(value = "pay_expired_time")
    private Date payExpiredTime;

    /**
     * 支付时间
     */
    @TableField(value = "pay_time")
    private Date payTime;

    /**
     * 支付描述
     */
    @TableField(value = "pay_desc")
    private String payDesc;

    /**
     * 经销商uuid
     */
    @TableField(value = "operate_uuid")
    private String operateUuid;

    /**
     * 订单类型
     */
    @TableField(value = "order_type")
    private Integer orderType;

    /**
     * 业务订单号 实际唯一的主键
     */
    @TableField(value = "bussiness_order_number")
    private String bussinessOrderNumber;

    /**
     * 业务订单展示流水号 不保证唯一，业务端展示用
     */
    @TableField(value = "bussiness_show_order_number")
    private String bussinessShowOrderNumber;

    /**
     * 1-支付宝APP 2-支付宝H5 3-支付宝PC 4-实名DID小程序 5-余额支付 6-sp支付
     */
    @TableField(value = "pay_tool")
    private Integer payTool;

    /**
     * 退款订单号,多个退款单号逗号分隔
     */
    @TableField(value = "refund_number")
    private String refundNumber;

    /**
     * 退款金额
     */
    @TableField(value = "refund_amount")
    private BigDecimal refundAmount;

    /**
     * 退款状态 0-未退款 1-已退款 2-部分退款 3-退款中 4:退款失败
     */
    @TableField(value = "refund_state")
    private Integer refundState;

    /**
     * 原始价格
     */
    @TableField(value = "original_amount")
    private BigDecimal originalAmount;

    /**
     * 优惠金额
     */
    @TableField(value = "discount_amount")
    private BigDecimal discountAmount;

    /**
     * 平台来源 1-门户 2-灵戒
     */
    @TableField(value = "platform_source")
    private Integer platformSource;

    /**
     * 支付配置id
     */
    @TableField(value = "base_config_id")
    private String baseConfigId;

    /**
     * 订单状态描述
     */
    @TableField(value = "status_desc")
    private String statusDesc;

    /**
     * 订单标题
     */
    @TableField(value = "order_title")
    private String orderTitle;

    /**
     * 同步时间
     */
    @TableField(value = "sync_time")
    private Date syncTime;

    /**
     * 业务方支付回调地址
     */
    @TableField(value = "pay_callback_notify_address")
    private String payCallbackNotifyAddress;

    /**
     * 创建时间 业务订单表的创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 是否展示
     */
    @TableField(value = "is_display")
    private Boolean isDisplay;

    /**
     * 修改时间 业务订单表的修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 订单关闭时间
     */
    @TableField(value = "order_close_time")
    private Date orderCloseTime;

    /**
     * 订单完成时间
     */
    @TableField(value = "order_finish_time")
    private Date orderFinishTime;

    /**
     * 余额支付预设参数
     */
    @TableField(value = "balance_param")
    private String balanceParam;

    /**
     * 订单类目id
     */
    @TableField(value = "category_id")
    private Integer categoryId;

    /**
     * 可退款金额
     */
    @TableField(value = "avaliable_refund_amount")
    private BigDecimal avaliableRefundAmount;

    /**
     * 支付回调地址
     */
    @TableField(value = "notify_pay_url")
    private String notifyPayUrl;

    /**
     * 退款回调地址
     */
    @TableField(value = "notify_refund_url")
    private String notifyRefundUrl;

    /**
     * 渠道appid
     */
    @TableField(value = "channel_appId")
    private String channelAppid;

    /**
     * ip地址
     */
    @TableField(value = "` ip_addr`")
    private String ipAddr;

    private static final long serialVersionUID = 1L;
}