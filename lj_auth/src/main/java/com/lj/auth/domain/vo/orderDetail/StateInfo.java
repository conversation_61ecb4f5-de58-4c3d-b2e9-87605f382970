package com.lj.auth.domain.vo.orderDetail;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/** 业务状态
 * @describe：
 * @author: cfj
 * @date: 2024/12/09
 */
@Data
public class StateInfo {

    /**
     * 1-展示过期时间 0-不展示
     */
    private Integer showExpirationTime;
    /**
     * 过期时间
     */
    private Date expirationTime;
    /**
     * 支付状态
     */
    private Integer payState;
    /**
     * 支付状态名称
     */
    private String payStateName;

    /**
     * 业务类型
     */
    private Integer applicationType;
    /**
     * 业务类型名称
     */
    private String applicationTypeName;


    /**
     * 退款状态
     */
    private Integer refundState;
    private String refundStateName;


    /**
     * 支付金额
     */
    private BigDecimal  amount;

    /**
     * 订单号
     */
    private String orderNumber;

    /**
     *订单状态
     */
    private Integer orderState;
    /**
     * 订单状态名称
     */
    private String orderStateName;

    /**
     * 用户uuid
     */
    private String accountUUID;

}
