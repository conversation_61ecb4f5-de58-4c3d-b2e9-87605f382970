package com.lj.auth.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 域名续费订单详情表(子表)
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("ym_order_domain_renewal_info")
public class OrderDomainRenewalInfo implements Serializable {

    private static final long serialVersionUID=1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 续费订单id(关联表:ym_order_domain_renewal)
     */
    private Long orderId;

    /**
     * 账户uuid
     */
    private String accountUuid;

    /**
     * 子订单交易流水号
     */
    private String tranNumber;

    /**
     * 域名
     */
    private String domain;

    /**
     * 注册时长
     */
    private Integer duration;

    /**
     * 实付金额
     */
    private BigDecimal amount;

    /**
     * 优惠金额
     */
    private BigDecimal discountAmount;

    /**
     * 原价=实付+优惠
     */
    private BigDecimal originalAmount;

    /**
     * 单价(每个/每年的续费价格)
     */
    private BigDecimal unitPrice;

    /**
     * 支付方式 1-余额支付 2-NFT续费 3-组合支付(余额+NFT)
     */
    private Integer payType;

    /**
     * 退款状态 1-未退款 2-已退款 3-退款中
     */
    private Integer refundState;

    /**
     * 续费状态 1-续费处理中(请求未抵达BSN) 2-续费中(请求已抵达,等待BSN处理) 3-续费完成(BSN响应处理完成)
     */
    private Integer renewalState;

    /**
     * 交易hash,多个逗号拼隔(NFT支付)
     */
    private String tranHash;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    private String oldExpirationTime;

    /**
     * 域名服务流水号(BSN侧)
     */
    private String domainServerSn;

    /**
     * 到期时间(BSN侧)
     */
    private String expirationTime;

    /**
     * 失败原因(BSN侧)
     */
    @TableField("failReason")
    private String failReason;

    /**
     * 域名等级(BSN侧)
     */
    private Integer level;

    /**
     * 处理状态(BSN侧) 0=待处理，1=处理中， 2=成功， 3=失败
     */
    private Integer status;


}
