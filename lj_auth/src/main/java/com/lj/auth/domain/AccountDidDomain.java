package com.lj.auth.domain;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * did域名绑定记录
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("ym_account_did_domain")
public class AccountDidDomain implements Serializable {

    private static final long serialVersionUID=1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 域名
     */
    private String domain;

    /**
     * did标识
     */
    private String did;

    /**
     * 账户uuid
     */
    private String accountUuid;

    /**
     * 经销商uuid
     */
    private String operateUuid;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 域名持有状态 1-持有 2-未持有
     */
    private Integer domainHoldState;

    /**
     * 域名登录状态 1-可登录 2-不可登录
     */
    private Integer domainLoginState;


}
