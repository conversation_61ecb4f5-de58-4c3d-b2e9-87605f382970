package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
    * 用户头像边框挂件表
    */
@Data
@TableName(value = "lj_user_avatar_frame")
public class UserAvatarFrame implements Serializable {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户uuid
     */
    @TableField(value = "account_uuid")
    private String accountUuid;

    /**
     * 头像挂件表id
     */
    @TableField(value = "avatar_frame_id")
    private Long avatarFrameId;

    /**
     * 状态  0-未激活 1-已激活
     */
    @TableField(value = "`state`")
    private Integer state;

    /**
     * 挂件级别(如裂空者级别)
     */
    @TableField(value = "avatar_frame_level")
    private Integer avatarFrameLevel;

    /**
     * 挂件图片
     */
    @TableField(value = "avatar_frame_image")
    private String avatarFrameImage;

    /**
     * 佩戴标识 0-未佩戴 1-已佩戴
     */
    @TableField(value = "wear_flag")
    private Integer wearFlag;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}