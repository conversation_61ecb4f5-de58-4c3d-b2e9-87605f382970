package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 操作记录表
 */
@Data
@Accessors(chain = true)
@TableName(value = "lj_auth_operation_records")
public class OperationRecords implements Serializable {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    @NotNull(message = "id不能为null")
    private Long id;

    /**
     * 基础账号
     */
    @TableField(value = "account")
    @Size(max = 200,message = "基础账号最大长度要小于 200")
    @NotBlank(message = "基础账号不能为空")
    private String account;

    /**
     * 最近登录ip
     */
    @TableField(value = "login_ip")
    @Size(max = 30,message = "最近登录ip最大长度要小于 30")
    @NotBlank(message = "最近登录ip不能为空")
    private String loginIp;

    /**
     * 登录时间
     */
    @TableField(value = "login_time")
    @NotNull(message = "登录时间不能为null")
    private Date loginTime;

    /**
     * 操作状态 0-失败 1-成功
     */
    @TableField(value = "`state`")
    @NotNull(message = "操作状态 0-失败 1-成功不能为null")
    private Integer state;

    /**
     * 请求参数
     */
    @TableField(value = "request_param")
    private String requestParam;

    /**
     * 请求接口
     */
    @TableField(value = "request_interface")
    @Size(max = 100,message = "请求接口最大长度要小于 100")
    private String requestInterface;

    private static final long serialVersionUID = 1L;
}