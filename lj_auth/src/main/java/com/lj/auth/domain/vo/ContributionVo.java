package com.lj.auth.domain.vo;

import java.math.BigDecimal;
import java.util.List;

import com.lj.auth.annotation.PrefixPath;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 贡献
 */
@Data
@Accessors(chain = true)
public class ContributionVo {
    private Integer id;

    /**
     * 用户uuid
     */
    private String accountUuid;

    /**
     * 贡献金额（单位：元）
     */
    private BigDecimal amount;

    private Integer ranking;

    private Integer count;

    private String nickName;

    private String domainNickName;

    private Integer showType;

    @PrefixPath
    private String headPortrait;

    private Integer headPortraitNftId;

    private Integer headPortraitType;

    private String nftImage;

}