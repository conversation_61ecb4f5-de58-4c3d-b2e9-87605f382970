package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
    * nft领取记录表
    */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "ym_nft_get_record")
public class NftGetRecord {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 记录标题
     */
    @TableField(value = "title")
    private String title;

    /**
     * 域名
     */
    @TableField(value = "`domain`")
    private String domain;

    /**
     * 领取人
     */
    @TableField(value = "get_account_uuid")
    private String getAccountUuid;

    /**
     * 领取地址
     */
    @TableField(value = "get_account_address")
    private String getAccountAddress;

    /**
     * 链框架id
     */
    @TableField(value = "opb_chain_id")
    private Integer opbChainId;

    /**
     * 1注册   2领取
     */
    @TableField(value = "`status`")
    private Integer status;

    /**
     * 0-不满足  1-满足
     */
    @TableField(value = "get_status")
    private Integer getStatus;

    /**
     * 领取数量/注册数量
     */
    @TableField(value = "get_count")
    private Integer getCount;

    /**
     * 是否成功  0-失败  1-成功 2-处理中
     */
    @TableField(value = "is_success")
    private Integer isSuccess;


    /**
     * 领取NFT状态  失败
     */
    public static Integer ISSUCCESS_FAILA = 0;
    /**
     * 领取NFT状态  成功
     */
    public static Integer ISSUCCESS_SUCCESS = 1;
    /**
     * 领取NFT状态  处理中
     */
    public static Integer issuccess_PENDING = 2;

    /**
     *  状态 1注册
     */
    public static Integer STATUS_REGISTER = 1;
    /**
     * 状态  2领取
     */
    public static Integer STATUS_RECEIVE = 2;

    /**
     * 注册的域名是否满足领取NFT的条件 0：不满足
     */
    public static Integer GET_STATUS_FAILURE = 0;
}
