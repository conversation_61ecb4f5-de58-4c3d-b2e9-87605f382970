package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
* <AUTHOR>
* @Description 
* @date 2024/12/10 15:59
*/
/**
    * 外部渠道申领DID配置
    */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "lj_outside_channel_apply_config")
public class OutsideChannelApplyConfig {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 三方渠道标识appid
     */
    @TableField(value = "app_id")
    private String appId;

    /**
     * 认证模式 M68:三要素 M66:二要素
     */
    @TableField(value = "auth_mode")
    private String authMode;

    /**
     * 是否免费 1-免费 2-不免费
     */
    @TableField(value = "fee")
    private Integer fee;

    /**
     * 免费次数(总量)
     */
    @TableField(value = "fee_total_count")
    private Integer feeTotalCount;

    /**
     * 免费条件 1-不限制 2-限首次申领新用户
     */
    @TableField(value = "fee_condition")
    private Integer feeCondition;

    /**
     * 已使用次数
     */
    @TableField(value = "fee_use_count")
    private Integer feeUseCount;

    /**
     * 剩余次数
     */
    @TableField(value = "fee_residue_count")
    private Integer feeResidueCount;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;
}