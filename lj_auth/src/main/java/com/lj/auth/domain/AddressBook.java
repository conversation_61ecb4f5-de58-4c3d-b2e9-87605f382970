package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.util.Date;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 通讯录
 */
@Data
@Accessors(chain = true)
@TableName(value = "lj_auth_address_book")
public class AddressBook implements Serializable {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    @NotNull(message = "id不能为null")
    private Long id;

    /**
     * 用户uuid
     */
    @TableField(value = "acount_uuid")
    @Size(max = 50,message = "用户uuid最大长度要小于 50")
    @NotBlank(message = "用户uuid不能为空")
    private String acountUuid;

    /**
     * 链id
     */
    @TableField(value = "chain_id")
    private Integer chainId;

    /**
     * 链地址
     */
    @TableField(value = "chain_address")
    @Size(max = 100,message = "链地址最大长度要小于 100")
    @NotBlank(message = "链地址不能为空")
    private String chainAddress;

    /**
     * 联系人描述
     */
    @TableField(value = "contact_description",updateStrategy = FieldStrategy.IGNORED)
    @Size(max = 500,message = "联系人描述最大长度要小于 500")
    private String contactDescription;

    /**
     * true=未删除false=删除
     */
    @TableField(value = "`state`")
    @NotNull(message = "true=未删除false=删除不能为null")
    private Boolean state;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @NotNull(message = "创建时间不能为null")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    @NotNull(message = "修改时间不能为null")
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}