package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 探索应用
 */
@Data
@Accessors(chain = true)
@TableName(value = "lj_auth_explore_app")
public class ExploreApp implements Serializable {
    /**
     * 应用:1-域名门户 2-钱包 3-交易市场
     */
    @TableId(value = "id", type = IdType.AUTO)
    @NotNull(message = "应用:1-域名门户 2-钱包 3-交易市场不能为null")
    private Integer id;

    /**
     * 应用名称
     */
    @TableField(value = "app_name")
    @Size(max = 100, message = "应用名称最大长度要小于 100")
    @NotBlank(message = "应用名称不能为空")
    private String appName;

    /**
     * 排序：升序
     */
    @TableField(value = "sort")
    @NotNull(message = "排序：升序不能为null")
    private Integer sort;

    /**
     * 1-开启 0-关闭
     */
    @TableField(value = "`state`")
    @NotNull(message = "1-开启 0-关闭不能为null")
    private Boolean state;

    /**
     * 探索应用类型id
     */
    @TableField(value = "app_type_id")
    @NotNull(message = "探索应用类型id不能为null")
    private Integer appTypeId;

    /**
     * 应用描述
     */
    @TableField(value = "`describe`")
    @Size(max = 500, message = "应用描述最大长度要小于 500")
    private String describe;

    /**
     * 应用logo
     */
    @TableField(value = "logo")
    @Size(max = 100, message = "应用logo最大长度要小于 100")
    private String logo;

    /**
     * 应用链接
     */
    @TableField(value = "app_link")
    @Size(max = 100, message = "应用链接最大长度要小于 100")
    private String appLink;

    /**
     * 1-推荐 0-不推荐
     */
    @TableField(value = "is_recommendation")
    @NotNull(message = "1-推荐 0-不推荐不能为null")
    private Boolean isRecommendation;

    /**
     * 1-官方0-非官方
     */
    @TableField(value = "is_official")
    @NotNull(message = "1-官方0-非官方不能为null")
    private Boolean isOfficial;

    /**
     * 跳转方式
     */
    @TableField(value = "jump_type")
    private Integer jumpType;

    /**
     * 是否收藏
     */
    @TableField(exist = false)
    private Boolean isCollection = false;

    private static final long serialVersionUID = 1L;
}