package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * @author: wxm
 * @description:
 * @date: 2024/12/26 11:38
 */
@Data
@TableName(value = "lj_home_page_config")
public class HomePageConfig {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 渠道 JIEWAI,XIAOMI,HUAWEI,OPPO,VIVO,HONOUR,APPSTORE,WXMP
     */
    @TableField("channel")
    private String channel;
    /**
     * 区域 1-顶部导航栏 2-固定金刚区 3-轮播图 4-功能金刚区 5-站位图 6-场景组件卡片区 7-首页背景图 8-底部导航栏
     */
    @TableField("area")
    private String area;
    /**
     * logo（顶部导航栏独有）
     */
    @TableField("logo")
    private String logo;
    /**
     * icon(或图片链接)
     */
    @TableField("icon")
    private String icon;
    /**
     * 背景图类型 1-长图  2-短图 (背景图独有)
     */
    @TableField("background_type")
    private String backgroundType;
    /**
     * 选中时的icon
     */
    @TableField("selected_icon")
    private String selectedIcon;
    /**
     * 内容(文本提示语或文案)
     */
    @TableField("content")
    private String content;
    /**
     * 跳转类型 1-原生 2-内部webview打开 3-外部浏览器打开
     */
    @TableField("jump_type")
    private String jumpType;
    /**
     * 内部路径
     */
    @TableField("jump_path")
    private String jumpPath;
    /**
     * 外部链接
     */
    @TableField("jump_url")
    private String jumpUrl;
    /**
     * 是否隐藏原生导航栏 1-隐藏
     */
    @TableField("is_hidden_nav")
    private String isHiddenNav;
    /**
     * 排序字段，值小在前
     */
    @TableField("sort")
    private String sort;
    /**
     * 是否展示 0-关闭 1-开启
     */
    @TableField("show_flag")
    private String showFlag;
    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;
    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

}
