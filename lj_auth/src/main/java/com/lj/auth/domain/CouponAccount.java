package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * @author: wxm
 * @description:
 * @date: 2024/11/5 9:22
 */
@Data
@TableName(value = "lj_coupon_account")
public class CouponAccount {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 券批次id
     */
    @TableField("coupon_batch_id")
    private Long couponBatchId;
    /**
     * 券id
     */
    @TableField("coupon_id")
    private Long couponId;
    /**
     * 优惠券码
     */
    @TableField("coupon_number")
    private String coupon_number;
    /**
     * 券状态 1-未使用 2-已使用
     */
    @TableField("state")
    private Integer state;
    /**
     * 账户uuid
     */
    @TableField("account_uuid")
    private String accountUuid;
    /**
     * 经销商uuid
     */
    @TableField("operate_uuid")
    private String operateUuid;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField("create_time")
    private Date createTime;
    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField("update_time")
    private Date updateTime;
    /**
     * 发放短信通知 1-未发 2-已发
     */
    @TableField("notice_state")
    private Integer noticeState;


}
