package com.lj.auth.domain.vo;

import com.lj.auth.annotation.PrefixPath;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigInteger;
import java.util.Date;

/**
 * 描述： 签到详情 创建人: CFJ 创建时间: 2024/04/10
 */

@Accessors(chain = true)
@Data
public class SignInHistVo extends RechargeMessageVo {

    /**
     * 签到时间
     */
    private Date time;

    /**
     * 签到状态 1-成功 0-失败
     */
    private Integer state;

}
