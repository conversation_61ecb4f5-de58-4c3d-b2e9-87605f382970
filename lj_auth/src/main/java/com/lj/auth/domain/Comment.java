package com.lj.auth.domain;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 广场评论
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("lj_square_comment")
public class Comment implements Serializable {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户uuid
     */
    private String accountUuid;

    /**
     * 动态id
     */
    private String trendsId;

    private String content;

    /**
     * 点赞数量
     */
    private Integer likesNum;

    /**
     * 转发数量
     */
    private Integer forwardNum;

    /**
     * 删除标签 0-未删除 1-主动删除 2-举报核实后删除 3-因上级动态删除而删除
     */
    private Integer removeFlag;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;


}
