package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import com.lj.auth.annotation.PrefixPath;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 用户域名地址映射表
 */
@Data
@Accessors(chain = true)
@TableName(value = "lj_auth_account_domain_address")
public class AccountDomainAddress implements Serializable {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    @NotNull(message = "id不能为null")
    private Long id;

    /**
     * 链框架id
     */
    @TableField(value = "opb_chain_id")
    @NotNull(message = "链框架id不能为null")
    private Integer opbChainId;

    /**
     * 用户uuid
     */
    @TableField(value = "account_uuid")
    @Size(max = 50,message = "用户uuid最大长度要小于 50")
    @NotBlank(message = "用户uuid不能为空")
    private String accountUuid;

    /**
     * 链地址
     */
    @TableField(value = "address")
    @Size(max = 100,message = "链地址最大长度要小于 100")
    @NotBlank(message = "链地址不能为空")
    private String address;

    /**
     * 域名
     */
    @TableField(value = "`domain`")
    @Size(max = 100,message = "域名最大长度要小于 100")
    @NotBlank(message = "域名不能为空")
    private String domain;

    /**
     * true-不隐藏false-隐藏
     */
    @TableField(value = "`state`")
    @NotNull(message = "true-不隐藏false-隐藏不能为null")
    private Boolean state;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @NotNull(message = "创建时间不能为null")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    @NotNull(message = "修改时间不能为null")
    private Date updateTime;


    // =========================================
    @TableField(exist = false)
    private Integer chainId;
    @TableField(exist = false)
    private String chainName;
    @TableField(exist = false)
    @PrefixPath
    private String chainLogo;


    private static final long serialVersionUID = 1L;
}