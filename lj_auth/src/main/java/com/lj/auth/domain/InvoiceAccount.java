package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 用户发票
 */
@Data
@Accessors(chain = true)
@TableName(value = "lj_auth_invoice_account")
public class InvoiceAccount implements Serializable {
    @TableId(value = "id", type = IdType.AUTO)
    @NotNull(message = "不能为null")
    private Long id;

    /**
     * 账户UUID
     */
    @TableField(value = "account_uuid")
    @Size(max = 20, message = "账户UUID最大长度要小于 20")
    private String accountUuid;

    /**
     * 主体类型 1-个人 2-企业 3-组织
     */
    @TableField(value = "`type`")
    private Integer type;

    /**
     * 发票内容
     */
    @TableField(value = "ticket_content")
    @Size(max = 255, message = "发票内容最大长度要小于 255")
    private String ticketContent;

    /**
     * 税率
     */
    @TableField(value = "tax_rate")
    @Size(max = 255, message = "税率最大长度要小于 255")
    private String taxRate;

    /**
     * 开票类型 1-增值税普通发票 2-增值税专用发票
     */
    @TableField(value = "ticket_type")
    private Integer ticketType;

    /**
     * 开票介质 1-纸质发票 2-电子发票
     */
    @TableField(value = "ticket_medium")
    private Integer ticketMedium;

    /**
     * 审核状态 1-待审核 2-审核通过 3-审核驳回
     */
    @TableField(value = "examine_state")
    private Integer examineState;

    /**
     * 审核意见
     */
    @TableField(value = "examine_opinion")
    @Size(max = 255, message = "审核意见最大长度要小于 255")
    private String examineOpinion;

    /**
     * 发票金额
     */
    @TableField(value = "ticket_amount")
    private BigDecimal ticketAmount;

    /**
     * 创建时间/申请时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 申请单号
     */
    @TableField(value = "ticket_uuid")
    @Size(max = 20, message = "申请单号最大长度要小于 20")
    private String ticketUuid;

    /**
     * (lj_auth_recharge_records主键ID)
     */
    @TableField(value = "recharge_record_ids")
    @Size(max = 255, message = "(lj_auth_recharge_records主键ID)最大长度要小于 255")
    private String rechargeRecordIds;

    /**
     * 发票抬头
     */
    @TableField(value = "invoice_title")
    @Size(max = 255, message = "发票抬头最大长度要小于 255")
    private String invoiceTitle;

    /**
     * 纳税人证件号/纳税人识别号/组织机构代码
     */
    @TableField(value = "id_number")
    @Size(max = 30, message = "纳税人证件号/纳税人识别号/组织机构代码最大长度要小于 30")
    private String idNumber;

    /**
     * 发票打印电话
     */
    @TableField(value = "print_phone")
    @Size(max = 255, message = "发票打印电话最大长度要小于 255")
    private String printPhone;

    /**
     * 注册地址
     */
    @TableField(value = "register_address")
    @Size(max = 255, message = "注册地址最大长度要小于 255")
    private String registerAddress;

    /**
     * 基本户开户行
     */
    @TableField(value = "bank")
    @Size(max = 255, message = "基本户开户行最大长度要小于 255")
    private String bank;

    /**
     * 基本户开户支行
     */
    @TableField(value = "bank_branch")
    @Size(max = 255, message = "基本户开户支行最大长度要小于 255")
    private String bankBranch;

    /**
     * 基本户银行账号
     */
    @TableField(value = "bank_account")
    @Size(max = 255, message = "基本户银行账号最大长度要小于 255")
    private String bankAccount;

    /**
     * 备注
     */
    @TableField(value = "remark")
    @Size(max = 255, message = "备注最大长度要小于 255")
    private String remark;

    /**
     * 收件人姓名
     */
    @TableField(value = "`name`")
    @Size(max = 255, message = "收件人姓名最大长度要小于 255")
    private String name;

    /**
     * 收件人电话
     */
    @TableField(value = "phone")
    @Size(max = 255, message = "收件人电话最大长度要小于 255")
    private String phone;

    /**
     * 发票接收邮箱
     */
    @TableField(value = "email")
    @Size(max = 255, message = "发票接收邮箱最大长度要小于 255")
    private String email;

    /**
     * 接收地址
     */
    @TableField(value = "address")
    @Size(max = 255, message = "接收地址最大长度要小于 255")
    private String address;

    private static final long serialVersionUID = 1L;
}