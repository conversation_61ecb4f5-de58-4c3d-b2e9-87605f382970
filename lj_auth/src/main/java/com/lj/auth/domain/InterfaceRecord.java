package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
    * 接口请求记录
    */
@Data
@TableName(value = "lj_auth_interface_record")
public class InterfaceRecord implements Serializable {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 基础账号
     */
    @TableField(value = "account")
    private String account;

    /**
     * 请求ip
     */
    @TableField(value = "request_ip")
    private String requestIp;

    /**
     * 请求时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 请求路径
     */
    @TableField(value = "`path`")
    private String path;

    /**
     * 请求参数
     */
    @TableField(value = "request_param")
    private String requestParam;

    /**
     * 平台
     */
    @TableField(value = "channel")
    private String channel;

    /**
     * 版本
     */
    @TableField(value = "edition")
    private String edition;

    private static final long serialVersionUID = 1L;
}