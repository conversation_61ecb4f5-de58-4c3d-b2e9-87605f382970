package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import com.lj.auth.annotation.PrefixPath;
import lombok.Data;

/**
 * 联系客服
 */
@Data
@TableName(value = "lj_auth_customerservice")
public class Customerservice implements Serializable {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 客服微信
     */
    @TableField(value = "weixin")
    private String weixin;

    /**
     * 客服qq
     */
    @TableField(value = "qq")
    private String qq;

    /**
     * 客服邮箱
     */
    @TableField(value = "email")
    private String email;

    /**
     * 客服电话
     */
    @TableField(value = "phone")
    private String phone;

    /**
     * 二维码
     */
    @TableField(value = "qrCode")
    @PrefixPath
    private String qrCode;

    /**
     * 二维码1
     */
    @TableField(value = "qrCode1")
    @PrefixPath
    private String qrCode1;

    /**
     * 在线时间
     */
    @TableField(value = "`time`")
    private String time;

    private static final long serialVersionUID = 1L;
}