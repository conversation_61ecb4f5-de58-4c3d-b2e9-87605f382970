package com.lj.auth.domain.Param;

import lombok.Data;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @describe
 */

@Data
public class AccountAgreementReadedRecordParam {

    /**
     *用户id
     */
    @NotBlank
    private String accountUUID;

    /**
     * 协议类型 1:用户协议 2:隐私协议
     */
    private Integer agreementType;

    /**
     * 协议id
     */
    private Integer agreementVersionId;


    @NotNull
    @Min(value = 1, message = "页码最小为1")
    int page=1;

    @NotNull
    @Min(value = 1, message = "数量最小为1")
    int pageSize=10;

}
