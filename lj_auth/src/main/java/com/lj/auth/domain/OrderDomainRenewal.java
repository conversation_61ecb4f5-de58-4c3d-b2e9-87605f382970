package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 域名续费订单表(总表)
 */
@Data
@TableName(value = "ym_order_domain_renewal")
public class OrderDomainRenewal {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 账户uuid
     */
    @TableField(value = "account_uuid")
    private String accountUuid;

    /**
     * 总金额(实付/应付金额)
     */
    @TableField(value = "total_amount")
    private BigDecimal totalAmount;

    /**
     * NFT数量
     */
    @TableField(value = "nft_number")
    private Integer nftNumber;

    /**
     * 交易流水号
     */
    @TableField(value = "tran_number")
    private String tranNumber;

    /**
     * 支付方式 1-余额支付 2-NFT续费 3-微信 4-支付宝
     */
    @TableField(value = "pay_type")
    private Integer payType;

    /**
     * 支付状态 1-待支付 2-支付完成 3-支付失败 4-支付超时/交易
     */
    @TableField(value = "pay_state")
    private Integer payState;

    /**
     * 退款状态 1-未退款 2-部分退款 3-全部退款
     */
    @TableField(value = "refund_state")
    private Integer refundState;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 域名服务流水号(BSN侧)
     */
    @TableField(value = "domain_server_sn")
    private String domainServerSn;

    /**
     * 用户交易流水号((BSN侧)
     */
    @TableField(value = "trade_code")
    private String tradeCode;

    /**
     * 平台来源 1-门户 2-灵界
     */
    @TableField(value = "platform_source")
    private Integer platformSource;

    /**
     * 订单状态 1-待支付 2-已取消 3-已关闭 4-续费中 5-续费成功 6-续费失败
     */
    @TableField(value = "order_state")
    private Integer orderState;

    /**
     * 支付时间
     */
    @TableField(value = "pay_time")
    private Date payTime;

    /**
     * 取消时间
     */
    @TableField(value = "cancel_time")
    private Date cancelTime;

    /**
     * 关闭时间
     */
    @TableField(value = "close_time")
    private Date closeTime;

    /**
     * 支付交易流水号
     */
    @TableField(value = "pay_out_trade_no")
    private String payOutTradeNo;

    /**
     * 退款时间
     */
    @TableField(value = "refund_time")
    private Date refundTime;

    /**
     * 退款订单号
     */
    @TableField(value = "refund_number")
    private String refundNumber;

    /**
     * 订单有效期,过期未支付自动关闭
     */
    @TableField(value = "order_expiration_time")
    private Date orderExpirationTime;

    /**
     * 业务有效期 支付完成,未使用 自动退款
     */
    @TableField(value = "validity_time")
    private Date validityTime;

    /**
     * 优惠金额
     */
    @TableField(value = "discount_amount")
    private BigDecimal discountAmount;

    /**
     * 原价=实付+优惠
     */
    @TableField(value = "original_amount")
    private BigDecimal originalAmount;

    /**
     * 订单编号(关联汇总订单表)
     */
    @TableField(value = "order_number")
    private String orderNumber;

    /**
     * 订单标记 1-原订单 2-合并订单改造
     */
    @TableField(value = "order_mark")
    private Integer orderMark;

    /**
     * 退款金额(此订单累计退款金额)
     */
    @TableField(value = "refund_amount")
    private BigDecimal refundAmount;

    /**
     * 续费活动id
     */
    @TableField(value = "renewal_activity_id")
    private Long renewalActivityId;

    /**
     * 参与活动域名数量
     */
    @TableField(value = "renewal_activity_number")
    private Integer renewalActivityNumber;

    /**
     * 活动折扣金额
     */
    @TableField(value = "renewal_activity_amount")
    private BigDecimal renewalActivityAmount;
}