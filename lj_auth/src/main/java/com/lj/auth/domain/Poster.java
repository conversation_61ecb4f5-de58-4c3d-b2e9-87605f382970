package com.lj.auth.domain;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import com.lj.auth.annotation.PrefixPath;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 灵戒邀请海报
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("lj_auth_poster")
public class Poster implements Serializable {

    private static final long serialVersionUID=1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 顶部logo
     */
    private String topLogo;

    /**
     * 底部logo
     */
    private String bottomLogo;

    /**
     * 海报背景
     */
    private String posterBackground;

    /**
     * app名称
     */
    private String appName;

    /**
     * 描述
     */
    private String describes;

    /**
     * 二维码
     */
    @TableField("qrCode")
    private String qrCode;

    /**
     * app链接
     */
    private String appLink;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;


}
