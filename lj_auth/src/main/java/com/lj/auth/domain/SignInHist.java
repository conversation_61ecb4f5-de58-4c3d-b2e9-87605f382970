package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 用户签到历史表
 */
@Data
@Accessors(chain = true)
@TableName(value = "lj_auth_sign_in_hist")
public class SignInHist implements Serializable {
    @TableId(value = "id", type = IdType.AUTO)
    @NotNull(message = "不能为null")
    private Long id;

    /**
     * 链地址
     */
    @TableField(value = "address")
    @Size(max = 100,message = "链地址最大长度要小于 100")
    private String address;

    /**
     * 用户uuid
     */
    @TableField(value = "account_uuid")
    @Size(max = 32,message = "用户uuid最大长度要小于 32")
    private String accountUuid;

    /**
     * 签到日期(单位精确到日)
     */
    @TableField(value = "sign_in_date")
    private Date signInDate;

    /**
     * 本次签到奖励
     */
    @TableField(value = "reward_money")
    private BigDecimal rewardMoney;

    /**
     * 连续签到天数（A:7天内如果有断签从0开始 B:7天签满从0开始）
     */
    @TableField(value = "continuite_day")
    private Integer continuiteDay;

    /**
     * 上链hash
     */
    @TableField(value = "hash")
    @Size(max = 100,message = "上链hash最大长度要小于 100")
    private String hash;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}