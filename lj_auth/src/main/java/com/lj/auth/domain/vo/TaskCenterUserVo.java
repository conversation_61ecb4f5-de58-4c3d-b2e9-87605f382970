package com.lj.auth.domain.vo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * 任务中心个人完成情况
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-28
 */
@Data
@Accessors(chain = true)
public class TaskCenterUserVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Integer id;

    /**
     * 用户uuid
     */
    private String accountUuid;

    /**
     * 任务中心id
     */
    private Integer taskCenterId;

    /**
     * 完成任务id
     */
    private String taskIdCompleted;

    /**
     * 任务奖励
     */
    private BigDecimal reward;

    /**
     * 0-未完成任务 1-完成任务
     */
    private Integer isComplete;

    /**
     * 是否领取奖励1-已领取 0-未领取
     */
    private Integer isReceive;

    /**
     * 任务上链奖励hash
     */
    private String hash;

    /**
     * 上链状态1-成功0-失败
     */
    private Integer state;

    /**
     * 实际任务完成时间
     */
    private Date actualTaskCompletionTime;

    /**
     * 实际任务完成数量
     */
    private Integer actualCompletedQuantity;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    // ==============================

    /**
     * 1-日常任务2-周期任务3-新手任务4-限时任务5-长期任务
     */
    private Integer taskTypeId;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 任务介绍
     */
    private String introduce;

    /**
     * 任务完成奖励
     */
    private BigDecimal completeReward;

    /**
     * 任务完成数量
     */
    private Integer completionCount;

    /**
     * 任务开启时间
     */
    private Date openingTime;

    /**
     * 任务完成时间
     */
    private Date completionTime;

}
