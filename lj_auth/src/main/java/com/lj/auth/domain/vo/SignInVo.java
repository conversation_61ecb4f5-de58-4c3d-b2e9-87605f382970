package com.lj.auth.domain.vo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import lombok.Data;

/**
 * 用户签到表
 */
@Data
public class SignInVo implements Serializable {
    /**
     * 链地址
     */
    private String address;
    /**
     * 天数
     */
    private Integer days;

    /**
     * 周期日期
     */
    private Date cycleDate;

    /**
     * 是否签到
     */
    private Boolean isSign;

    /**
     * 用户uuid
     */
    private String accountUuid;

    /**
     * 签到日期(单位精确到日)
     */
    private Date signInDate;

    /**
     * 本次签到奖励
     */
    private BigDecimal rewardMoney;

    /**
     * 连续签到天数（A:7天内如果有断签从0开始 B:7天签满从0开始）
     */
    private Integer continuiteDay;

}