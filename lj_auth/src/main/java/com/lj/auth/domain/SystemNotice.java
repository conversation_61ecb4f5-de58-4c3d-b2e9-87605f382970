package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

/**
 * 系统公告/通知/消息
 */
@Data
@Accessors(chain = true)
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "lj_auth_system_notice")
public class SystemNotice {
    private static final long serialVersionUID = 1L;
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 公告标题
     */
    @TableField(value = "title")
    private String title;

    /**
     * 描述
     */
    @TableField(value = "description")
    private String description;

    /**
     * 公告内容
     */
    @TableField(value = "content")
    private String content;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 公告类型 1-普通公告 2-强制弹窗
     */
    @TableField(value = "`type`")
    private Integer type;

    /**
     * 消息来源:0-系统消息 1-系统公告2-转账通知 3-市场通知4-活动通知
     */
    @TableField(value = "`source`")
    private Integer source;

    /**
     * 提醒开始时间
     */
    @TableField(value = "reminder_start_time")
    private Date reminderStartTime;

    /**
     * 提醒结束时间
     */
    @TableField(value = "reminder_end_time")
    private Date reminderEndTime;

    /**
     * 频率1-单次 2-重复
     */
    @TableField(value = "frequency")
    private Integer frequency;

    /**
     * 内部路径
     */
    @TableField(value = "jump_path")
    private String jumpPath;

    /**
     * 跳转类型1-原生2-内部webview打开3-外部浏览器打开
     */
    @TableField(value = "jump_type")
    private Integer jumpType;

    /**
     * 外部链接
     */
    @TableField(value = "jump_url")
    private String jumpUrl;

    /**
     * 是否隐藏原生导航栏 1-隐藏
     */
    @TableField(value = "is_hidden_nav")
    private Integer isHiddenNav;

    /**
     * 平台
     */
    @TableField(value = "channel")
    private String channel;

    /**
     * 版本
     */
    @TableField(value = "edition")
    private String edition;

    /**
     * 0-非指定用户1-指定用户
     */
    @TableField(value = "is_appoint")
    private Integer isAppoint;

    /**
     * 跳转参数
     */
    @TableField(value = "jump_param")
    private String jumpParam;

}