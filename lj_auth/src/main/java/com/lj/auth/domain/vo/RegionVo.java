package com.lj.auth.domain.vo;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import com.lj.auth.domain.Region;
import lombok.Data;

/**
 * 地区选项信息
 *
 * <AUTHOR>
 * @date 2025/08/28
 */
@Data

public class RegionVo implements Serializable {
   private String letter;
   private List<Region> regionList;
}