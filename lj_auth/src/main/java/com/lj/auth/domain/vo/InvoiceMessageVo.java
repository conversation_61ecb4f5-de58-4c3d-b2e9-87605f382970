package com.lj.auth.domain.vo;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Description
 * @date 2023/7/24 10:13
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class InvoiceMessageVo {
    /**
     * 主体类型 1-个人 2-企业 3-组织
     */
    @NotNull(message = "主体类型非空")
    private Integer type;
    
    /**
     * 发票抬头
     */
    @NotBlank(message = "发票抬头非空")
    private String invoiceTitle;
    
    /**
     * 纳税人证件号/纳税人识别号/组织机构代码
     */
    @NotBlank(message = "纳税人证件号/纳税人识别号/组织机构代码非空")
    private String idNumber;
    
    /**
     * 发票打印电话
     */
    private String phone;
    
    /**
     * 注册地址
     */
    private String address;
    
    /**
     * 基本户开户行
     */
    private String bank;
    
    /**
     * 基本户开户支行
     */
    private String bankBranch;
    
    /**
     * 基本户银行账号
     */
    private String bankAccount;
    
    /**
     * 备注
     */
    private String remark;
}
