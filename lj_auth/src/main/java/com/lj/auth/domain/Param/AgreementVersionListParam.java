package com.lj.auth.domain.Param;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @describe
 */

@Data
public class AgreementVersionListParam {


    /**
     * 协议类型 1:用户协议 2:隐私协议
     */
    private Integer agreementType;

    /**
     * 搜索关键字
     */
    private String searchKey;

    public void setSearchKey(String searchKey) {
        this.searchKey = searchKey != null ? searchKey.trim() : null;
    }


    @NotNull
    @Min(value = 1, message = "页码最小为1")
    int page=1;

    @NotNull
    @Min(value = 1, message = "数量最小为1")
    int pageSize=10;

}
