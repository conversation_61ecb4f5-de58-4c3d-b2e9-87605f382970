package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 短信模板配置表
 */
@Data
@Accessors(chain = true)
@TableName(value = "lj_auth_sms_config")
public class SmsConfig implements Serializable {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    @NotNull(message = "id不能为null")
    private Integer id;

    /**
     * 开关 1:打开 0:关闭(默认)
     */
    @TableField(value = "switchs")
    @NotNull(message = "开关 1:打开 0:关闭(默认)不能为null")
    private Boolean switchs;

    /**
     * 短信API商品名称
     */
    @TableField(value = "product")
    @Size(max = 255,message = "短信API商品名称最大长度要小于 255")
    @NotBlank(message = "短信API商品名称不能为空")
    private String product;

    /**
     * 短信API商品域名
     */
    @TableField(value = "`domain`")
    @Size(max = 255,message = "短信API商品域名最大长度要小于 255")
    @NotBlank(message = "短信API商品域名不能为空")
    private String domain;

    /**
     * accessKeyId
     */
    @TableField(value = "accessKeyId")
    @Size(max = 255,message = "accessKeyId最大长度要小于 255")
    @NotBlank(message = "accessKeyId不能为空")
    private String accesskeyid;

    /**
     * accessKeySecret
     */
    @TableField(value = "accessKeySecret")
    @Size(max = 255,message = "accessKeySecret最大长度要小于 255")
    @NotBlank(message = "accessKeySecret不能为空")
    private String accesskeysecret;

    /**
     * 签名
     */
    @TableField(value = "signName")
    @Size(max = 255,message = "签名最大长度要小于 255")
    @NotBlank(message = "签名不能为空")
    private String signname;

    /**
     * 模板
     */
    @TableField(value = "templateCode")
    @Size(max = 255,message = "模板最大长度要小于 255")
    @NotBlank(message = "模板不能为空")
    private String templatecode;

    /**
     * 短信模板变量
     */
    @TableField(value = "`variable`")
    @Size(max = 255,message = "短信模板变量最大长度要小于 255")
    @NotBlank(message = "短信模板变量不能为空")
    private String variable;

    /**
     * 经销商uuid
     */
    @TableField(value = "operate_uuid")
    @Size(max = 20,message = "经销商uuid最大长度要小于 20")
    @NotBlank(message = "经销商uuid不能为空")
    private String operateUuid;

    /**
     * 模板类型 0:验证码 1:域名审核通过通知
     */
    @TableField(value = "`type`")
    @NotNull(message = "模板类型 0:验证码 1:域名审核通过通知不能为null")
    private Integer type;

    /**
     * 短信服务提供商
     */
    @TableField(value = "smsServiceProvider")
    private String smsserviceprovider;

    /**
     * appi 腾讯云短信使用
     */
    @TableField(value = "appid")
    private String appid;

    private static final long serialVersionUID = 1L;
}