package com.lj.auth.domain.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 版本记录
 */
@Data
public class AgreementVersionVo implements Serializable {


    /**
     * 版本id
     */
    private Integer versionId;

    /**
     * 版本标题
     */
    private String versionTitle;

    /**
     * 版本号
     */
    private String versionNumber;

    /**
     * 1:用户协议 2:隐私协议
     */
    private Integer agreementType;

    /**
     * 更新内容
     */
    private String updateContent;


    /**
     * 协议内容
     */
    private String agreementContent;




    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

}