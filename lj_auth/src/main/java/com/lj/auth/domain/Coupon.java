package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: wxm
 * @description:
 * @date: 2024/11/5 9:15
 */
@Data
@TableName(value = "lj_coupon")
public class Coupon {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 券批次id
     */
    @TableField("coupon_batch_id")
    private Long couponBatchId;
    /**
     * 券名称(简述)
     */
    @TableField("name")
    private String name;
    /**
     * 描述(使用说明)
     */
    @TableField("description")
    private String description;
    /**
     * 券金额
     */
    @TableField("amount")
    private BigDecimal amount;
    /**
     * 折扣率(0.45代表折扣后价格后原价的45%)
     */
    @TableField("discount_rate")
    private BigDecimal discountRate;

    @TableField(exist = false)
    private String discountRateStr;
    /**
     * 券发行数量
     */
    @TableField("publish_number")
    private Integer publishNumber;
    /**
     * 剩余券数量
     */
    @TableField("residue_number")
    private Integer residueNumber;
    /**
     * 已领取数量
     */
    @TableField("received_number")
    private Integer receivedNumber;
    /**
     * 单个用户可领取数量
     */
    @TableField("get_number")
    private Integer getNumber;
    /**
     * 券有效期开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField("use_start_time")
    private Date useStartTime;
    /**
     * 券有效期结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField("use_end_time")
    private Date useEndTime;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField("create_time")
    private Date createTime;
    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField("update_time")
    private Date updateTime;
    /**
     * 有效状态 1-未生效 2-有效 3-失效
     */
    @TableField("effective_state")
    private Integer effectiveState;
    /**
     * 展示状态 1-展示 2-隐藏
     */
    @TableField("show_state")
    private Integer showState;
    /**
     * 排序(值小在前)
     */
    @TableField("sort")
    private Integer sort;

    /**
     * 模式  1-抵扣券 2-折扣券
     */
    @TableField("model")
    private Integer model;

}
