package com.lj.auth.domain.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户读取协议记录Vo
 */
@Data
public class AgreementAccountRecordVo implements Serializable {

    /**
     * 记录id
     */
    private Long recordId;

    /**
     * 协议版本id
     */
    private Integer versionId;

    /**
     * 账户UUID
     */
    private String accountUuid;


    /**
     * 版本名称
     */
    private String versionName;

    /**
     * 版本号
     */
    private String versionNumber;

    /**
     * 1:用户协议 2:隐私协议
     */
    private Integer agreementType;

    /**
     * 更新内容
     */
    private String desc;

    /**
     * 协议内容
     */
    private String agruemtnContent;

    /**
     * 发布时间
     */
    private Date reaseTime;


    /**
     * 备注
     */
    private String remark;

    /**
     * 已读且确认时间
     */
    private Date readAndConfimTime;


}