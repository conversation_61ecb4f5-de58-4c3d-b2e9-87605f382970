package com.lj.auth.domain;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 关于我们
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class About implements Serializable {

    private static final long serialVersionUID=1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * logo
     */
    private String logo;

    /**
     * 系统名称
     */
    private String systemName;

    /**
     * 当前版本
     */
    private String currentVersion;

    /**
     * 网站
     */
    private String website;

    /**
     * 微信公众号
     */
    @TableField("weChat_account")
    private String wechatAccount;

    /**
     * 联系邮箱
     */
    private String email;

    /**
     * icp备案号:文案
     */
    private String icpText;

    /**
     * icp备案号:固定链接
     */
    private String icpLink;

    /**
     * 版权信息
     */
    private String copyright;

    /**
     * 用户协议
     */
    private String userAgreement;

    /**
     * 隐私协议
     */
    private String privacyAgreement;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     *  经销商uuid
     */
    private String operateUuid;

    /**
     * 公网安备案号:icon
     */
    private String publicIcon;

    /**
     * 公网安备案号:文案
     */
    private String publicText;

    /**
     * 公网安备案号:固定链接
     */
    private String publicLink;

    /**
     * 浏览器ICON
     */
    private String browserIcon;

    /**
     * 浏览器门户标题
     */
    private String browserPortalTitle;

    /**
     * 浏览器运营标题
     */
    private String browerOperateTitle;

    /**
     * 门户通用设置-注册协议
     */
    private String registerAgreement;

    /**
     * uuid前缀
     */
    private String uuidPrefix;

    /**
     * 经销商授权证书
     */
    private String authorizationCertificate;

    /**
     * 企业名称
     */
    private String enterpriseName;


}
