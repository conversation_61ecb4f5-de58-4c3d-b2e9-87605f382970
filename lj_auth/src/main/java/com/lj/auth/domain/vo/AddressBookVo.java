package com.lj.auth.domain.vo;

import java.io.Serializable;
import java.util.Date;


import com.lj.auth.annotation.PrefixPath;
import lombok.Data;

/**
 * 通讯录
 */
@Data
public class AddressBookVo implements Serializable {
    /**
     * id
     */
    private Long id;

    /**
     * 用户uuid
     */
    private String acountUuid;

    /**
     * 链id
     */
    private Integer chainId;

    /**
     * 链名称
     */
    private String chainName;

    /**
     * 链logo
     */
    @PrefixPath
    private String chainLogo;

    /**
     * 链地址
     */
    private String chainAddress;

    /**
     * 联系人描述
     */
    private String contactDescription;

    /**
     * true=未删除false=删除
     */
    private Boolean state;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

}