package com.lj.auth.domain.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 描述：链上资产 创建人: CFJ 创建时间: 2024/05/08
 * 
 * <AUTHOR>
 */
@Data
public class ChainAssetsVO implements Serializable {
    private static final long serialVersionUID = 1L;

    private String realName;
    private String phoneNumber;
    private String accountUuid;
    private String address;
    private BigDecimal gas;
}
