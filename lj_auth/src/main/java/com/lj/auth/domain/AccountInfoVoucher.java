package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
* <AUTHOR>
* @Description 
* @date 2024/4/26 16:04
*/
/**
    * 用户信息凭证
    */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "ym_account_info_voucher")
public class AccountInfoVoucher {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 身份证hash
     */
    @TableField(value = "id_no")
    private String idNo;

    /**
     * 姓名hash
     */
    @TableField(value = "id_name")
    private String idName;

    /**
     * 凭证标识符
     */
    @TableField(value = "vc_id")
    private String vcId;

    /**
     * 凭证密文
     */
    @TableField(value = "vc_enc")
    private String vcEnc;

    /**
     * 模版id
     */
    @TableField(value = "template_id")
    private String templateId;

    /**
     * 模版
     */
    @TableField(value = "`template`")
    private String template;

    /**
     * 版本
     */
    @TableField(value = "version")
    private String version;

    /**
     * 机构did
     */
    @TableField(value = "org_did")
    private String orgDid;

    /**
     * type
     */
    @TableField(value = "vc_type")
    private String vcType;

    /**
     * txid
     */
    @TableField(value = "tx_id")
    private String txId;

    /**
     * 到期时间
     */
    @TableField(value = "expiration_time")
    private Date expirationTime;

    /**
     * 账户uuid
     */
    @TableField(value = "account_uuid")
    private String accountUuid;

    /**
     * 经销商uuid
     */
    @TableField(value = "operate_uuid")
    private String operateUuid;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;
}