package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 订单详情-域名服务结果
 */
@Data
@TableName(value = "ym_order_bsn_info")
public class OrderBsnInfo implements Serializable {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 订单id
     */
    @TableField(value = "order_id")
    private Long orderId;

    /**
     * 服务金额，单位为分
     */
    @TableField(value = "amount")
    private Integer amount;

    /**
     * 附加数据
     */
    @TableField(value = "attach")
    private String attach;

    /**
     * 域名
     */
    @TableField(value = "`domain`")
    private String domain;

    /**
     * 域名服务流水号
     */
    @TableField(value = "domain_serve_sn")
    private String domainServeSn;

    /**
     * 注册时长 单位：年
     */
    @TableField(value = "duration")
    private Integer duration;

    /**
     * 过期时间
     */
    @TableField(value = "expiration_time")
    private String expirationTime;

    /**
     * 域名等级
     */
    @TableField(value = "`level`")
    private Integer level;

    /**
     * 域名所有者
     */
    @TableField(value = "`owner`")
    private String owner;

    /**
     * 域名管理者
     */
    @TableField(value = "domain_admin")
    private String domainAdmin;

    /**
     * 上级域名
     */
    @TableField(value = "parent_domain")
    private String parentDomain;

    /**
     * 支付状态 0=未支付，1=已支付，2=支付失败
     */
    @TableField(value = "payment_status")
    private Integer paymentStatus;

    /**
     * 支付时间
     */
    @TableField(value = "payment_time")
    private String paymentTime;

    /**
     * 退款状态 0=未退款，1=已退款
     */
    @TableField(value = "refund_status")
    private Integer refundStatus;

    /**
     * 服务状态 0=待处理，1=处理中，2=完成，3=失败，4=待审核，5=审核未通过
     */
    @TableField(value = "`status`")
    private Integer status;

    /**
     * 用户交易流水号
     */
    @TableField(value = "trade_code")
    private String tradeCode;

    /**
     * 域名服务类型 0：域名注册，1：设置域名所有者，2：设置域名管理者，4：设置域名解析信息，7：域名注销 , 10-域名交易, 11-域名转让
     */
    @TableField(value = "domain_serve_type")
    private Integer domainServeType;

    /**
     * 域名新所有者账户地址
     */
    @TableField(value = "new_owner")
    private String newOwner;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 经销商uuid
     */
    @TableField(value = "operate_uuid")
    private String operateUuid;

    /**
     * 终端用户uuid
     */
    @TableField(value = "account_uuid")
    private String accountUuid;

    /**
     * 域名类型 1-普通域名 2-星级域名 3-星级域名(到期未续费释放,无需购买申请权,直接调用注册接口)
     */
    @TableField(value = "`type`")
    private Integer type;

    /**
     * 经销商分成比例
     */
    @TableField(value = "level1_ratio")
    private String level1Ratio;

    /**
     * 上级渠道商分成比例(0.05)
     */
    @TableField(value = "level2_ratio")
    private String level2Ratio;

    /**
     * 本级经销商的返佣
     */
    @TableField(value = "local_rebate")
    private BigDecimal localRebate;

    /**
     * 上级经销商的返佣
     */
    @TableField(value = "superior_rebate")
    private BigDecimal superiorRebate;

    /**
     * 钉钉消息推送次数
     */
    @TableField(value = "robot_push_message")
    private Integer robotPushMessage;

    /**
     * 开票状态 1-未开票 2-已开票
     */
    @TableField(value = "ticket_state")
    private Integer ticketState;

    /**
     * 上级账户返佣比例
     */
    @TableField(value = "superior_account_scale")
    private BigDecimal superiorAccountScale;

    /**
     * 上级账户返佣金额
     */
    @TableField(value = "superior_account_amount")
    private BigDecimal superiorAccountAmount;

    /**
     * 上级账户uuid
     */
    @TableField(value = "parent_uuid")
    private String parentUuid;

    /**
     * 当前账户返佣比例
     */
    @TableField(value = "this_account_scale")
    private BigDecimal thisAccountScale;

    /**
     * 当前账户返佣金额
     */
    @TableField(value = "this_account_amount")
    private BigDecimal thisAccountAmount;

    /**
     * 原始价格(未使用优惠券的金额)
     */
    @TableField(value = "primitive_amount")
    private BigDecimal primitiveAmount;

    /**
     * 优惠金额
     */
    @TableField(value = "discount_amount")
    private BigDecimal discountAmount;

    /**
     * 域名交易状态 1-售出 2-购入
     */
    @TableField(value = "transaction_state")
    private Integer transactionState;

    /**
     * 域名转让状态 1-转让 2-转入
     */
    @TableField(value = "transfer_state")
    private Integer transferState;

    /**
     * 交易hash(域名转让)
     */
    @TableField(value = "transaction_hash")
    private String transactionHash;

    /**
     * 平台来源 1-门户 2-灵戒
     */
    @TableField(value = "platform_source")
    private Integer platformSource;

    /**
     * 订单编号(关联汇总订单表)
     */
    @TableField(value = "order_number")
    private String orderNumber;

    /**
     * 订单标记 1-原订单 2-合并订单改造
     */
    @TableField(value = "order_mark")
    private Integer orderMark;

    private static final long serialVersionUID = 1L;
}