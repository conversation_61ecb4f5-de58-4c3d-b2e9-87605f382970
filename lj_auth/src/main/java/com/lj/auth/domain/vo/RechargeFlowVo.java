package com.lj.auth.domain.vo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 描述： 资金明细对象 创建人: CFJ 创建时间: 2024/04/12
 */
@Data
@Accessors(chain = true)
public class RechargeFlowVo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 终端用户uuid
     */
    private String accountUuid;

    /**
     * (充值/提现/消耗)金额 (扣手续费前金额)
     */
    private String amount;

    /**
     * 交易类型
     */
    private String typeName;

    /**
     * 类型id
     */
    private Integer typeId;

    /**
     * 大类型：1-支出2-收入
     */
    private Integer bigType;

    /**
     * 类型状态
     */
    private String typeState;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 实际金额
     */
    private String realAmount;

}
