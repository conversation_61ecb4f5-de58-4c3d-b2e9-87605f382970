package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

/**
    * 支付交易记录表
    */
@Data
@Accessors(chain = true)
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "lj_payment_transaction_record")
public class LjPaymentTransactionRecord {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 商户支付订单号(唯一)
     */
    @TableField(value = "pay_order_id")
    private String payOrderId;

    /**
     * 支付金额
     */
    @TableField(value = "pay_amount")
    private BigDecimal payAmount;

    /**
     * 支付状态 1-未支付 2-已支付 3-支付失败  4-交易关闭(4.1指定时间段内未支付时关闭的交易 4.2交易完成全额退款成功是关闭的交易 4.3支付失败的交易)
     */
    @TableField(value = "pay_state")
    private Integer payState;

    /**
     * 退款状态 1-未退款 2-已退款 3-部分退款(天满没有) 4-退款中
     */
    @TableField(value = "refund_state")
    private Integer refundState;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 支付方式 0-未知(生成PC支付订单时,无法确定支付类型) 1-微信 2-支付宝
     */
    @TableField(value = "pay_type")
    private Integer payType;

    /**
     * 支付工具 1-聚合H5 2-聚合PC 3-聚合微信小程序 4-聚合APP
     */
    @TableField(value = "pay_tool")
    private Integer payTool;

    /**
     * 订单类型 1-域名续费 2-DID申领
     */
    @TableField(value = "order_type")
    private Integer orderType;

    /**
     * 账户UUID
     */
    @TableField(value = "account_uuid")
    private String accountUuid;

    /**
     * 订单ID,根据订单类型关联不同的表(1-ym_order_domain_renewal主键，2-ym_did_order主键)
     */
    @TableField(value = "order_id")
    private Long orderId;
}