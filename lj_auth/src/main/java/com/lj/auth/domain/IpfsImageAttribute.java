package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
* <AUTHOR>
* @Description 
* @date 2024/4/27 15:48
*/

/**
 * nft图片属性信息
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "ym_ipfs_image_attribute")
public class IpfsImageAttribute {
    /**
     * 状态 1-未使用 2-已使用
     */
    public static Integer state1 = 1;
    public static Integer state2 = 2;
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    
    /**
     * 索引(文件名)
     */
    @TableField(value = "`index`")
    private Integer index;
    
    /**
     * 头饰
     */
    @TableField(value = "headwear")
    private String headwear;
    
    /**
     * 饰品
     */
    @TableField(value = "glasses")
    private String glasses;
    
    /**
     * 表情
     */
    @TableField(value = "emote")
    private String emote;
    
    /**
     * 头套
     */
    @TableField(value = "head")
    private String head;
    
    /**
     * 衣服
     */
    @TableField(value = "clothes")
    private String clothes;
    
    /**
     * 身体
     */
    @TableField(value = "body")
    private String body;
    
    /**
     * 背景
     */
    @TableField(value = "bg")
    private String bg;
    
    /**
     * 状态 1-未使用 2-已使用
     */
    @TableField(value = "`state`")
    private Integer state;
}