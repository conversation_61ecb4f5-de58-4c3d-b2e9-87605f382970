package com.lj.auth.domain;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import jnr.ffi.annotations.In;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 账号注销条件
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("lj_auth_account_logout_condition")
public class AccountLogoutCondition implements Serializable {

    private static final long serialVersionUID=1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 提醒
     */
    private String remind;
    private Integer isRemind;

    /**
     * 条件
     */
    private String content;

    /**
     * 不满足条件
     */
    private String errorContent;

    /**
     * 排序字段：降序
     */
    private Integer number;


    /**
     * 是否忽略:0-未处理 1-已忽略 2-已处理3-后台数据已清除
     */
    @TableField(exist = false)
    private Integer isIgnore;


}
