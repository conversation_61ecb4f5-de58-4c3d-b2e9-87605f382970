package com.lj.auth.domain.vo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 描述： 云链平添能量充值记录表
 *
 * 创建人: CFJ 创建时间: 2024/04/02
 */
@Data
@Accessors(chain = true)
public class EnergyRechargeFlowVo extends RechargeMessageVo {
    /**
     * id
     */
    private Long id;

    /**
     * 终端用户uuid
     */
    private String accountUuid;

    /**
     * 充值金额，单位元
     */
    private BigDecimal businessMoney;

    /**
     * gas值
     */
    private String gas;

    /**
     * 交易流水号
     */
    private String reqTransactionSn;

    /**
     * 交易hash
     */
    private String hash;

    /**
     * 交易类型：1=充值2=消耗
     */
    private Integer type;

    /**
     * 充值状态1-充值中 5-充值失败 10=充值成功
     */
    private Integer rechargeState;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}