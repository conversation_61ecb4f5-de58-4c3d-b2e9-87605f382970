package com.lj.auth.domain.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: wxm
 * @description:
 * @date: 2024/11/5 16:43
 */
@Data
public class CouponVo {

    private String couponName;
    private String couponModel;
    private String useStartTime;
    private String useEndTime;
    private String description;
    private String discountRate;
    private String couponBatchId;
    private String couponId;
    private String couponAccountId;
    private BigDecimal amount;
    /**
     * 优惠券码
     */
    private String couponNumber;
    /**
     * 是否可叠加使用 0-不可叠加 1-可叠加
     */
    private Integer useType;
    /**
     * 券状态 1-未使用 2-已使用
     */
    private Integer state;
    /**
     * 优惠金额
     */
    private String discountAmount;
    /**
     * 进入sp池子的金额
     */
    private String spAmount;
    /**
     * 备注/说明
     */
    private String remark;
}
