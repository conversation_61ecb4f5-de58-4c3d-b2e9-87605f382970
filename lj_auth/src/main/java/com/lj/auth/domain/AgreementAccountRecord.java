package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 用户读取协议记录
 */
@Data
@TableName(value = "lj_agreement_account_record")
public class AgreementAccountRecord implements Serializable {
    /**
     * 记录id
     */
    @TableId(value = "record_id", type = IdType.AUTO)
    private Long recordId;

    /**
     * 协议版本id
     */
    @TableField(value = "version_id")
    private Integer versionId;

    /**
     * 账户UUID
     */
    @TableField(value = "account_uuid")
    private String accountUuid;

    /**
     * 阅读并确认内容
     */
    @TableField(value = "confirm_content")
    private String confirmContent;

    /**
     * 阅读并确认时间
     */
    @TableField(value = "read_and_confim_time")
    private Date readAndConfimTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}