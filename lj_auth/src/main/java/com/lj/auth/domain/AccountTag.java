package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
    * 用户标签表
    */
@Data
@TableName(value = "account_tag")
public class AccountTag implements Serializable {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 排序字段：升序
     */
    @TableField(value = "sort")
    private Integer sort;

    /**
     * 类型名称
     */
    @TableField(value = "`name`")
    private String name;

    /**
     * 用户标签类型表id
     */
    @TableField(value = "account_tag_type_id")
    private Integer accountTagTypeId;

    /**
     * 选中状态 false:未选中 true:选中
     */
    @TableField(exist = false)
    private Boolean isSelected=false;

    private static final long serialVersionUID = 1L;
}