package com.lj.auth.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 任务中心个人完成情况
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("lj_auth_task_center_user")
public class TaskCenterUser implements Serializable {

    private static final long serialVersionUID=1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户uuid
     */
    private String accountUuid;

    /**
     * 用户领取奖励地址
     */
    private String address;

    /**
     * 任务中心id
     */
    private Integer taskCenterId;

    /**
     * 完成任务id
     */
    private String taskIdCompleted;

    /**
     * 任务奖励
     */
    private BigDecimal reward;

    /**
     * 0-未完成任务 1-完成任务
     */
    private Integer isComplete;

    /**
     * 是否领取奖励1-已领取 0-未领取
     */
    private Integer isReceive;

    /**
     * 任务上链奖励hash
     */
    private String hash;

    /**
     * 上链状态1-成功0-失败
     */
    private Integer state;

    /**
     * 实际任务完成时间
     */
    private Date completionTime;

    /**
     * 实际任务完成数量
     */
    private Integer completionCount;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;


}
