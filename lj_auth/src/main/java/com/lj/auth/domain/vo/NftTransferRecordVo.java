package com.lj.auth.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: zjd
 * @Description:
 * @Date:2024/03/14 16:12
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class NftTransferRecordVo {

    /**
     * 主键
     */
    private Long id;

    /**
     * tokenId
     */
    private Integer tokenId;

    /**
     * NFT图片
     */
    private String nftUrl;

    /**
     * NFT名称
     */
    private String nftName;

    /**
     * 合约地址
     */
    private String contractAddress;

    /**
     * 发送者地址
     */
    private String fromAddress;

    /**
     * 接收者地址
     */
    private String toAddress;

    /**
     * 交易hash
     */
    private String transactionHash;

    /**
     * 区块高度
     */
    private Integer blockNumber;

    /**
     * 交易类型  1-铸币 2-转账  3-销毁  4-单个token授权  5-批量授权
     */
    private Integer transactionType;

    /**
     * gas费用
     */
    private String gasPriceUsed;

    /**
     * 交易固定费用
     */
    private String fixPriceUsed;

    /**
     * 交易时间
     */
    private String transactionTime;

    private String nftImage;
}
