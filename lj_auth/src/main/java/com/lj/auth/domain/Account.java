package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 账户表
 */
@Data
@TableName(value = "account")
public class Account implements Serializable {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 手机号
     */
    @TableField(value = "phone_number")
    private String phoneNumber;

    /**
     * uuid
     */
    @TableField(value = "uuid")
    private String uuid;

    /**
     * 密码
     */
    @TableField(value = "`password`")
    private String password;

    /**
     * 支付密码
     */
    @TableField(value = "pay_password")
    private String payPassword;

    /**
     * 头像
     */
    @TableField(value = "head_portrait")
    private String headPortrait;

    /**
     * 昵称
     */
    @TableField(value = "nick_name")
    private String nickName;

    /**
     * 注册ip
     */
    @TableField(value = "register_ip")
    private String registerIp;

    /**
     * 注册时间
     */
    @TableField(value = "register_time")
    private Date registerTime;

    /**
     * 最近登录ip
     */
    @TableField(value = "recent_login_ip")
    private String recentLoginIp;

    /**
     * 最近登录时间
     */
    @TableField(value = "recent_login_time")
    private Date recentLoginTime;

    /**
     * 是否实名认证 0:未认证(默认) 1:认证中 2:认证通过 3:认证失败
     */
    @TableField(value = "is_real_name")
    private Integer isRealName;

    /**
     * 用户状态 1:正常 2:黑名单 3:已注销
     */
    @TableField(value = "blacklist")
    private Integer blacklist;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 经销商uuid
     */
    @TableField(value = "operate_uuid")
    private String operateUuid;

    /**
     * 实名姓名
     */
    @TableField(value = "real_name")
    private String realName;

    /**
     * 身份证号
     */
    @TableField(value = "id_card")
    private String idCard;

    /**
     * 用户标识 1普通用户 2经销商 3渠道商
     */
    @TableField(value = "`identity`")
    private Integer identity;

    /**
     * uuid后缀
     */
    @TableField(value = "uuid_suffix")
    private String uuidSuffix;

    /**
     * 上级uuid 注册时填入
     */
    @TableField(value = "parent_uuid")
    private String parentUuid;

    /**
     * 是否禁言 1:禁言 0:否(默认)
     */
    @TableField(value = "is_banned")
    private Boolean isBanned;

    /**
     * 提成比例
     */
    @TableField(value = "commission_ratio")
    private BigDecimal commissionRatio;

    /**
     * 抖音openid
     */
    @TableField(value = "douyin_openid")
    private String douyinOpenid;

    /**
     * 微信openid
     */
    @TableField(value = "weixin_openid")
    private String weixinOpenid;

    /**
     * 推广等级 0-lv1推广大使(待激活) 1-lv1推广大使2-lv2推广大使 3-lv3推广大使  4-默认(不满足条件) 9-推广大使(新等级)
     */
    @TableField(value = "promotion_level")
    private Integer promotionLevel;

    /**
     * 推广返佣状态 1-参与返佣(默认) 2-不参与返佣
     */
    @TableField(value = "promotion_rebate_state")
    private Integer promotionRebateState;

    /**
     * 活跃标识 1:活跃 0:非活跃
     */
    @TableField(value = "active_flag")
    private Boolean activeFlag;

    /**
     * DID标识
     */
    @TableField(value = "did_symbol")
    private String didSymbol;

    /**
     * 福穗平台人员id
     */
    @TableField(value = "people_id")
    private String peopleId;

    /**
     * 福穗平台签约状态(0-待处理 1-校验失败 2-待签约 3-签约成功 4-签约校验错误)
     */
    @TableField(value = "sign_status")
    private Integer signStatus;

    /**
     * 签约地址
     */
    @TableField(value = "sign_url")
    private String signUrl;

    /**
     * 邀请码
     */
    @TableField(value = "invite_code")
    private String inviteCode;

    /**
     * 图像类型1-普通图像 2-nft图像
     */
    @TableField(value = "head_portrait_type")
    private Integer headPortraitType;

    /**
     * nft表（lj_auth_nft）的id
     */
    @TableField(value = "head_portrait_nft_id")
    private Long headPortraitNftId;

    /**
     * 1-昵称展示 2-域名昵称展示
     */
    @TableField(value = "show_type")
    private Integer showType;

    /**
     * 域名昵称
     */
    @TableField(value = "domain_nick_name")
    private String domainNickName;

    /**
     * 注册来源：0-CMS撮单运营账号 1-域名门户 2-灵戒App  3-游客模式  4-CMS社区运营账号
     */
    @TableField(value = "registration_source")
    private Integer registrationSource;

    /**
     * im状态 0:未同步 1:已同步
     */
    @TableField(value = "im_status")
    private Integer imStatus;

    /**
     * 个人主页背景图片
     */
    @TableField(value = "background_img")
    private String backgroundImg;

    /**
     * 裂空者等级
     */
    @TableField(value = "sky_split_level")
    private Integer skySplitLevel;

    /**
     * 佩戴徽章图片
     */
    @TableField(value = "badge_image")
    private String badgeImage;

    /**
     * 佩戴挂件图片
     */
    @TableField(value = "avatar_frame_image")
    private String avatarFrameImage;

    /**
     * 成为推广大使时间
     */
    @TableField(value = "promotion_time")
    private Date promotionTime;

    private static final long serialVersionUID = 1L;
}