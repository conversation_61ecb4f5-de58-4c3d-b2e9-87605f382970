package com.lj.auth.domain.vo.orderDetail;

import lombok.Data;

/**
 * @describe：
 * @author: cfj
 * @date: 2024/12/06
 */
@Data
public class StyleButtonVo {
//    黑色：#08090A
//    灰色：#525B66
//    红色：#F55549
//    蓝色：#1A7DFF
//    黄色：#FFA033
//    白色：#FFFFFF
    public final static String BLACK = "#08090A";
    public final static String  GREY = "#525B66";
    public final static String  RED = "#F55549";
    public final static String  BLUE = "#1A7DFF";
    public final static String  YELLOW = "#FFA033";
    public final static String  WHITE= "#FFFFFF";

//
    /**    public final static int  Size12 = 12;
     //    public final static int  Size114 = 14;
     * 颜色
     */
    private String  color;
    /**
     * 字体大小
     */
//    private int font;
    /**
     * 背景颜色
     */
    private String backColor;

}
