package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 聚合订单预览信息表
 */
@Data
@TableName(value = "mix_order_view")
public class MixOrderView implements Serializable {
    /**
     * 订单编号
     */
    @TableId(value = "order_number", type = IdType.AUTO)
    private String orderNumber;

    /**
     * 应用id
     */
    @TableField(value = "applicaton_id")
    private Integer applicatonId;

    /**
     * 应用名称
     */
    @TableField(value = "application_name")
    private String applicationName;

    /**
     * 账户uuid
     */
    @TableField(value = "account_uuid")
    private String accountUuid;

    /**
     * 用户did标识
     */
    @TableField(value = "did_symbol")
    private String didSymbol;

    /**
     * 订单标题
     */
    @TableField(value = "order_title")
    private String orderTitle;

    /**
     * 订单展示json数据
     */
    @TableField(value = "order_view_json")
    private String orderViewJson;

    /**
     * 订单详情json数据
     */
    @TableField(value = "order_detail_json")
    private String orderDetailJson;

    /**
     * 创建时间 业务订单表的创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 修改时间 业务订单表的修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}