package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 探索应用类别
 */
@Data
@Accessors(chain = true)
@TableName(value = "lj_auth_explore_app_type")
public class ExploreAppType implements Serializable {
    @TableId(value = "id", type = IdType.AUTO)
    @NotNull(message = "不能为null")
    private Integer id;

    /**
     * 应用类型名称
     */
    @TableField(value = "type_name")
    @Size(max = 100, message = "应用类型名称最大长度要小于 100")
    @NotBlank(message = "应用类型名称不能为空")
    private String typeName;

    /**
     * 排序：升序
     */
    @TableField(value = "sort")
    @NotNull(message = "排序：升序不能为null")
    private Integer sort;

    /**
     * 1-开启 0-关闭
     */
    @TableField(value = "`state`")
    @NotNull(message = "1-开启 0-关闭不能为null")
    private Boolean state;

    @TableField(exist = false)
    private Integer count;

    private static final long serialVersionUID = 1L;
}