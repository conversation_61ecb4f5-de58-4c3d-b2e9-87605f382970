package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
    * 经销商申请单
    */
@Data
@TableName(value = "ym_operate_apply_order")
public class OperateApplyOrder {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 订单金额
     */
    @TableField(value = "total_amount")
    private BigDecimal totalAmount;

    /**
     * 折扣金额
     */
    @TableField(value = "discount_amount")
    private BigDecimal discountAmount;

    /**
     * 实际金额
     */
    @TableField(value = "real_amount")
    private BigDecimal realAmount;

    /**
     * 实付金额
     */
    @TableField(value = "pay_amount")
    private BigDecimal payAmount;

    /**
     * 支付状态 1:待支付 2:支付完成  
     */
    @TableField(value = "pay_status")
    private Integer payStatus;

    /**
     * 工单类型 1:服务订购
     */
    @TableField(value = "`type`")
    private Integer type;

    /**
     * 用户交易流水号
     */
    @TableField(value = "order_number")
    private String orderNumber;

    /**
     * 支付方式 1:微信 2:支付宝 3:余额 4:银行卡 5:pc聚合支付
     */
    @TableField(value = "pay_type")
    private Integer payType;

    /**
     * 支付(商户订单号)
     */
    @TableField(value = "out_trade_no")
    private String outTradeNo;

    /**
     * 经销商uuid
     */
    @TableField(value = "operate_uuid")
    private String operateUuid;

    /**
     * 终端用户uuid
     */
    @TableField(value = "account_uuid")
    private String accountUuid;

    /**
     * 支付时间
     */
    @TableField(value = "pay_time")
    private Date payTime;

    /**
     * 1-lv1推广大使 2-lv2推广大使 3-lv3推广大使  
     */
    @TableField(value = "promotion_level")
    private Integer promotionLevel;

    /**
     * 订购单详情
     */
    @TableField(value = "detail")
    private String detail;

    /**
     * 状态 0:处理中(默认) 1:已完成
     */
    @TableField(value = "`status`")
    private Integer status;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 手机号
     */
    @TableField(value = "phone_number")
    private String phoneNumber;

    /**
     * 订购服务版本
     */
    @TableField(value = "version")
    private Integer version;

    /**
     * 钉钉消息推送次数
     */
    @TableField(value = "robot_push_message")
    private Integer robotPushMessage;
}