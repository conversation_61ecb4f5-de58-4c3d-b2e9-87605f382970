package com.lj.auth.domain.vo;

import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ContractVo {
    /**
     * 主键
     */

    private Long id;

    /**
     * 创建时间
     */

    private Date createTime;

    /**
     * 更新时间
     */

    private Date updateTime;

    /**
     * 合约类型 1-ERC721 2-域名注册管理器 3-灵戒域名注册器 4-灵戒公共解析器
     */
    private Integer contractType;

    /**
     * 合约名
     */
    private String contractName;

    /**
     * 合约标识
     */
    private String contractSymbol;

    /**
     * 合约地址
     */
    private String contractAddress;

    /**
     * 链框架id
     */
    private Integer opbChainId;

    /**
     * token前缀 部分合约可用
     */
    private String imageUrl;

    /**
     * NFT封面
     */
    private String contractUrl;

    /**
     * 状态 0-禁用 1-启用
     */
    private Integer status;

    /**
     * 用途 1-注册域名领NFT
     */
    private Integer toUse;

    /**
     * 合约描述
     */
    private String contractDescribe;

    /**
     * nft数量
     */
    private Long nftNum;

    /**
     * 持仓地址数
     */
    private Long nftAddressNum;

    /**
     * 交易数量
     */
    private Long nftTransferNum;
    /**
     * 创建者地址
     *
     */
    private String creatorAddress;

    /**
     * 创建合约hash
     */
    private String creatorAddressHash;

}