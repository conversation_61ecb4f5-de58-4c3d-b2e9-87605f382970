package com.lj.auth.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Description
 * @date 2024/3/6 17:02
 */

/**
 * nft图片 ipfs信息
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class IpfsImageVo {
    
    /**
     * 图片CID
     */
    private String cid;
    
    /**
     * 域名
     */
    private String domain;
    
    /**
     * TOKENID
     */
    private Integer tokenId;
    
}
