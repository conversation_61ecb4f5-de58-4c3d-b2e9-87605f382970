package com.lj.auth.domain.vo;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lj.auth.annotation.PrefixPath;

import lombok.Data;

/**
 * 微章表
 */
@Data
public class BadgeVo implements Serializable {
    /**
     * id
     */
    private Long badgeId;

    /**
     * 微章名称
     */

    private String badgeName;

    /**
     * 微章类型 1-基础微章
     */
    private Integer badgeType;

    /**
     * 默认图片
     */
    @PrefixPath
    private String defaultImage;
}