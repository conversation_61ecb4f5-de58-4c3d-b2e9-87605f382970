package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
    * DID授权信息记录
    */
@Data
@TableName(value = "lj_auth_did_grant_record")
public class DidGrantRecord implements Serializable {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户uuid
     */
    @TableField(value = "account_uuid")
    private String accountUuid;

    /**
     * 应用Id
     */
    @TableField(value = "application_id")
    private Integer applicationId;

    /**
     * 授权信息时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * token
     */
    @TableField(value = "token")
    private String token;

    /**
     * 授权类型
     */
    @TableField(value = "type_name")
    private String typeName;

    /**
     * 1-授权0-解除
     */
    @TableField(value = "is_relieve")
    private Integer isRelieve;

    private static final long serialVersionUID = 1L;
}