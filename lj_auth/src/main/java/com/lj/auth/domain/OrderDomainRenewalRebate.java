package com.lj.auth.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 域名续费返佣关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("ym_order_domain_renewal_rebate")
public class OrderDomainRenewalRebate implements Serializable {

    private static final long serialVersionUID=1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 域名续费订单详情表(关联表:ym_order_domain_renewal_info)
     */
    private Long orderInfoId;

    /**
     * 账户uuid
     */
    private String accountUuid;

    /**
     * 实付金额
     */
    private BigDecimal amount;

    /**
     * 当前账户返佣比例
     */
    private BigDecimal thisAccountScale;

    /**
     * 当前账户返佣金额
     */
    private BigDecimal thisAccountAmount;

    /**
     * 上级账户uuid
     */
    private String parentUuid;

    /**
     * 上级账户返佣比例
     */
    private BigDecimal parentAccountScale;

    /**
     * 上级账户返佣金额
     */
    private BigDecimal parentAccountAmount;

    /**
     * 本级经销商uuid
     */
    private String thisOperateUuid;

    /**
     * 本级经销商返佣比例
     */
    private BigDecimal thisOperateScale;

    /**
     * 本地经销商返佣金额
     */
    private BigDecimal thisOperateAmount;

    /**
     * 上级经销商uuid
     */
    private String parentOperateUuid;

    /**
     * 上级经销商返佣比例
     */
    private BigDecimal parentOperateScale;

    /**
     * 上级经销商返佣金额
     */
    private BigDecimal parentOperateAmount;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 返佣状态 1-待发放 2-发放中 3-已发放 4-订单失效(不返佣)
     */
    private Integer rebateState;


}
