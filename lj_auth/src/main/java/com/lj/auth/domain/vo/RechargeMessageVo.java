package com.lj.auth.domain.vo;

import java.math.BigInteger;
import java.util.Date;

import com.lj.auth.annotation.PrefixPath;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 描述：充值信息 创建人: CFJ 创建时间: 2024/04/10
 */
@Accessors(chain = true)
@Data
public class RechargeMessageVo {
    /**
     * 链名称
     */
    private String chainName;

    /**
     * 链logo
     */
    @PrefixPath
    private String chainLogo;

    /**
     * 接收地址
     */
    private String receiveAddress;

    /**
     * 手续费
     */
    private String fee;

    /**
     * 交易hash
     */
    private String hash;

    /**
     * 区块
     */
    private BigInteger block;

}
