package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
    * 用户微章表
    */
@Data
@TableName(value = "lj_user_badge")
public class UserBadge implements Serializable {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户uuid
     */
    @TableField(value = "account_uuid")
    private String accountUuid;

    /**
     * 微章表id
     */
    @TableField(value = "badge_id")
    private Long badgeId;

    /**
     * 状态  0-未激活 1-已激活
     */
    @TableField(value = "`state`")
    private Integer state;

    /**
     * 佩戴标识 0-未佩戴 1-已佩戴
     */
    @TableField(value = "wear_flag")
    private Integer wearFlag;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}