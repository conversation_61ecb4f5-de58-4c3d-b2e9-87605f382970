package com.lj.auth.domain.vo;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 任务类别
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class TaskTypeVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 任务类型下任务数量
     */
    private Integer num;

    /**
     * 1-日常任务2-周期任务3-新手任务4-限时任务5-长期任务
     */
    private Integer taskTypeId;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 任务描述
     */
    private String message;

    /**
     * 排序字段：降序
     */
    private Integer sort;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

}
