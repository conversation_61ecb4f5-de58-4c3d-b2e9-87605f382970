package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 微章表
 */
@Data
@TableName(value = "lj_badge")
public class Badge implements Serializable {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 微章名称
     */
    @TableField(value = "`name`")
    private String name;

    /**
     * 微章类型 1-基础微章
     */
    @TableField(value = "`type`")
    private Integer type;

    /**
     * 默认图片
     */
    @TableField(value = "default_image")
    private String defaultImage;

    /**
     * 显示标识 0-不显示 1-显示
     */
    @TableField(value = "show_flag")
    private Integer showFlag;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 解锁说明
     */
    @TableField(value = "unlock_instructions")
    private String unlockInstructions;

    /**
     * 跳转路径
     */
    @TableField(value = "jump_url")
    private String jumpUrl;

    /**
     * 1-原生2-H53-webView
     */
    @TableField(value = "jump_type")
    private Integer jumpType;

    /**
     * 1-不隐藏0-隐藏
     */
    @TableField(value = "is_hidden_nav")
    private Integer isHiddenNav;

    private static final long serialVersionUID = 1L;
}