package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

/**
    * 周k线
    */
@Data
@Accessors(chain = true)
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "lj_auth_week_k_line")
public class WeekKLine {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 时间：yyyy-m-d
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 最低单价
     */
    @TableField(value = "low")
    private BigDecimal low;

    /**
     * 最高单价
     */
    @TableField(value = "`max`")
    private BigDecimal max;

    /**
     * sp实时单价
     */
    @TableField(value = "sp_price")
    private BigDecimal spPrice;

    /**
     * 涨跌幅
     */
    @TableField(value = "rise_and_fall")
    private String riseAndFall;

    /**
     * 开盘价
     */
    @TableField(value = "`start`")
    private BigDecimal start;

    /**
     * 收盘价
     */
    @TableField(value = "`end`")
    private BigDecimal end;

    /**
     * 数据更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;
}