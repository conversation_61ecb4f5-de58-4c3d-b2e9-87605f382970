package com.lj.auth.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 域名交易记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("lj_transfer_domain_transfer_record")
public class DomainTransferRecord implements Serializable {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 交易流水号
     */
    private String transferUuid;

    /**
     * 域名
     */
    private String domain;

    /**
     * 售卖用户uuid
     */
    private String sellerUuid;

    /**
     * 购买用户uuid
     */
    private String buyerUuid;

    /**
     * 1-一口价 2-拍卖
     */
    private Integer transferType;

    /**
     * 交易价格
     */
    private BigDecimal price;

    /**
     * 手续费
     */
    private BigDecimal servicePrice;

    /**
     * 开票状态  1未开票  2已开票
     */
    private Integer invoiceState;

    /**
     * 交易成功时间
     */
    private LocalDateTime createTime;

    /**
     * 买方昵称
     */
    private String buyerNickName;

    /**
     * 买方头像
     */
    private String buyerPortrait;


}
