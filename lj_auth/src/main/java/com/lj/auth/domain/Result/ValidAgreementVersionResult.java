package com.lj.auth.domain.Result;

import lombok.Data;

import java.util.Date;


/**
 * <AUTHOR>
 * @describe
 */

@Data
public class ValidAgreementVersionResult {

    /**
     * 用户uuid
     */
    private String accountUUID;


    /**
     * 用户协议详情
     */
    private AccountAgreementDetail accountAgreementDetail;

    /**
     * 隐私协议详情
     */
    private PrivacyAgreementDetai privacyAgreementDetai;

    /**
     * 弹窗类型
     * 0：不需要弹窗
     * 1:用户协议
     * 2:隐私协议
     * 3.用户协议+隐私协议
     */
    private Integer popType =0;

}
