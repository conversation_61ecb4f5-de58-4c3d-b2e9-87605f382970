package com.lj.auth.domain.vo.order;

import com.lj.auth.domain.vo.orderDetail.StyleButtonVo;
import lombok.Data;

/**
 * @describe：订单按钮
 * @author: cfj
 * @date: 2024/12/09
 */
@Data
public class OrderButton {
    /**
     * 按钮名称
     */
    private  String name;
    /**
     * 0-取消订单 1-立即支付
     * 2-取消申领 3-立即申领
     * 4-取消恢复 5-立即恢复
     */
    private  String type;
    private StyleButtonVo  styleButtonVo;
}
