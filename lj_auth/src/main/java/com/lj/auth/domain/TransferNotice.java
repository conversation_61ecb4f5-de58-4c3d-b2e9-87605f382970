package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 转账通知
 */
@Data
@Accessors(chain = true)
@TableName(value = "lj_auth_transfer_notice")
public class TransferNotice implements Serializable {
    @TableId(value = "id", type = IdType.AUTO)
    @NotNull(message = "不能为null")
    private Long id;

    /**
     * 公告标题
     */
    @TableField(value = "title")
    @Size(max = 255,message = "公告标题最大长度要小于 255")
    @NotBlank(message = "公告标题不能为空")
    private String title;

    /**
     * 描述
     */
    @TableField(value = "description")
    @Size(max = 255,message = "描述最大长度要小于 255")
    @NotBlank(message = "描述不能为空")
    private String description;

    /**
     * 公告内容
     */
    @TableField(value = "content")
    @NotBlank(message = "公告内容不能为空")
    private String content;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @NotNull(message = "创建时间不能为null")
    private Date createTime;

    /**
     * 公告类型 1-普通公告 2-强制弹窗
     */
    @TableField(value = "`type`")
    private Integer type;

    private static final long serialVersionUID = 1L;
}