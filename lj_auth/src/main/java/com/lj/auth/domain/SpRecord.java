package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

/**
    * sp发放记录
    */
@Data
@Accessors(chain = true)
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "lj_auth_sp_record")
public class SpRecord {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户uuid
     */
    @TableField(value = "account_uuid")
    private String accountUuid;

    /**
     * 用户领取奖励地址
     */
    @TableField(value = "address")
    private String address;

    /**
     * 任务类型1-签到2-任务中心3-活动
     */
    @TableField(value = "`type`")
    private Integer type;

    /**
     * 任务类别1-日常任务2-周期任务3-新手任务4-限时任务5-长期任务6-签到7-直播奖励
     */
    @TableField(value = "task_type")
    private Integer taskType;

    /**
     * 任务类别名称
     */
    @TableField(value = "task_type_name")
    private String taskTypeName;

    /**
     * 任务名称
     */
    @TableField(value = "task_name")
    private String taskName;

    /**
     * 任务奖励
     */
    @TableField(value = "reward")
    private BigDecimal reward;

    /**
     * 任务上链奖励hash
     */
    @TableField(value = "hash")
    private String hash;

    /**
     * 发放时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 关联id
     */
    @TableField(value = "detail_id")
    private Integer detailId;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;
}