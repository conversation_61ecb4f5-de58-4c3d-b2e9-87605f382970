package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 电影订单表
 */
@Data
@TableName(value = "lj_movie_order")
public class MovieOrder {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户uuid
     */
    @TableField(value = "account_uuid")
    private String accountUuid;

    /**
     * did标识
     */
    @TableField(value = "did_symbol")
    private String didSymbol;

    /**
     * 下单后天河点播开放平台返回的订单id
     */
    @TableField(value = "tianhe_order_id")
    private String tianheOrderId;

    /**
     * 影院名称
     */
    @TableField(value = "cinema_name")
    private String cinemaName;

    /**
     * 影厅名称
     */
    @TableField(value = "hall_name")
    private String hallName;

    /**
     * 电影名称
     */
    @TableField(value = "movie_name")
    private String movieName;

    /**
     * 电影海报
     */
    @TableField(value = "film_pic")
    private String filmPic;

    /**
     * 电影语种
     */
    @TableField(value = "film_language")
    private String filmLanguage;

    /**
     * 座位数
     */
    @TableField(value = "order_num")
    private Integer orderNum;

    /**
     * 订单状态说明
     */
    @TableField(value = "order_status_str")
    private String orderStatusStr;

    /**
     * 1-待支付 2-受理中，3-待出票，4-已出票待结算，5-已结算，10-订单关闭 6-已取消  7-已退款  8-已退款
     */
    @TableField(value = "order_status")
    private Integer orderStatus;

    /**
     * 实际出票座位：英文逗号隔开(有调座才会有值)
     */
    @TableField(value = "real_seat")
    private String realSeat;

    /**
     * 座位
     */
    @TableField(value = "seat")
    private String seat;

    /**
     * 下单时预留的手机号
     */
    @TableField(value = "reserved_phone")
    private String reservedPhone;

    /**
     * 灵戒APP的订单号
     */
    @TableField(value = "third_order_id")
    private String thirdOrderId;

    /**
     * 所有座位市场的总和(单位：分)
     */
    @TableField(value = "net_price")
    private Integer netPrice;

    /**
     * 锁座数据
     */
    @TableField(value = "seat_data")
    private String seatData;

    /**
     * 待出票时间
     */
    @TableField(value = "ready_ticket_time")
    private Date readyTicketTime;

    /**
     * 出票时间
     */
    @TableField(value = "ticket_time")
    private Date ticketTime;

    /**
     * 关闭时间
     */
    @TableField(value = "close_time")
    private Date closeTime;

    /**
     * 关闭原因
     */
    @TableField(value = "close_cause")
    private String closeCause;

    /**
     * 取票码
     */
    @TableField(value = "ticket_code")
    private String ticketCode;

    /**
     * 订单市场价
     */
    @TableField(value = "init_price")
    private BigDecimal initPrice;

    /**
     * 放映时间
     */
    @TableField(value = "show_time")
    private Date showTime;

    /**
     * 时长
     */
    @TableField(value = "duration")
    private Integer duration;

    /**
     * 结束放映时间
     */
    @TableField(value = "stop_show_time")
    private Date stopShowTime;

    /**
     * 支付方式 1:微信  2：支付宝  3:余额 4:业务方支付
     */
    @TableField(value = "pay_type")
    private Integer payType;

    /**
     * 支付的源力值数量
     */
    @TableField(value = "sp_num")
    private Integer spNum;

    /**
     * 源力值支付hash
     */
    @TableField(value = "sp_pay_hash")
    private String spPayHash;

    /**
     * 回调标识 0-未回调 1-已回调
     */
    @TableField(value = "notify_flag")
    private Integer notifyFlag;

    /**
     * 资产处理标识 0-冻结中 1-处理完成
     */
    @TableField(value = "assets_handle_flag")
    private Integer assetsHandleFlag;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 订单实际支付金额，单位分
     */
    @TableField(value = "real_amount")
    private Integer realAmount;

    /**
     * 进入sp池的金额(必须小于等于订单金额的10%)
     */
    @TableField(value = "sp_amount")
    private Integer spAmount;

    /**
     * 本单优惠金额
     */
    @TableField(value = "discount_amount")
    private Integer discountAmount;

    /**
     * 小程序支付订单号
     */
    @TableField(value = "wechat_order_id")
    private String wechatOrderId;

    /**
     * 0:待支付 1:已支付 2:支付失败 3:支付超时关闭的交易 4:未支付关闭的订单 5:支付成功全额退款成功关闭的交易
     */
    @TableField(value = "pay_status")
    private Integer payStatus;

    /**
     * 场次id
     */
    @TableField(value = "show_id")
    private String showId;

    /**
     * 座位接口的area字段
     */
    @TableField(value = "area")
    private String area;

    /**
     * 座位接口的seatId字段
     */
    @TableField(value = "seat_id")
    private String seatId;

    /**
     * 座位接口的seatNo字段
     */
    @TableField(value = "seat_no")
    private String seatNo;

    /**
     * 电影id
     */
    @TableField(value = "movie_id")
    private String movieId;

    /**
     * 影院id
     */
    @TableField(value = "cinema_id")
    private String cinemaId;

    /**
     * 所有座位市场的总价(单位元)
     */
    @TableField(value = "settle_price")
    private BigDecimal settlePrice;

    /**
     * 退款单号
     */
    @TableField(value = "refund_no")
    private String refundNo;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 向天微下单失败原因
     */
    @TableField(value = "order_fail_reason")
    private String orderFailReason;

    /**
     * 用户券表id，多个用逗号连接
     */
    @TableField(value = "coupon_account_ids")
    private String couponAccountIds;

    /**
     * 支付时间
     */
    @TableField(value = "pay_time")
    private Date payTime;

    /**
     * 退款状态 0-未退款 1-已退款 2-部分退款 3-退款中 4:退款失败
     */
    @TableField(value = "refund_state")
    private Integer refundState;

    /**
     * 退款时间
     */
    @TableField(value = "refund_time")
    private Date refundTime;

    /**
     * 取消订单时间
     */
    @TableField(value = "cancel_time")
    private Date cancelTime;

    /**
     * 快慢票 0-慢票 1-快票
     */
    @TableField(value = "fast_charge")
    private Integer fastCharge;
}