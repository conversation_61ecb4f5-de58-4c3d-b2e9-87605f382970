package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

/**
    * DID授权登录应用管理
    */
@Data
@Accessors(chain = true)
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "lj_auth_did_login_application")
public class DidLoginApplication {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 应用Id
     */
    @TableField(value = "application_id")
    private Integer applicationId;

    /**
     * 应用名称
     */
    @TableField(value = "application_name")
    private String applicationName;

    /**
     * 网址
     */
    @TableField(value = "website")
    private String website;

    /**
     * 应用介绍
     */
    @TableField(value = "application_introduce")
    private String applicationIntroduce;

    /**
     * 1-开启0-关闭
     */
    @TableField(value = "`state`")
    private Integer state;
}