package com.lj.auth.domain.vo;

import java.util.Date;

import com.lj.auth.annotation.PrefixPath;

import lombok.Data;

/**
 * <AUTHOR>
 * @Description
 * @date 2024/4/26 15:58
 */

/**
 * 凭证授权
 */
@Data
public class VoucherAccreditVo {

    private Integer id;

    /**
     * 账户uuid
     */
    private String accountUuid;

    /**
     * 经销商uuid
     */
    private String operateUuid;

    /**
     * 凭证标识符
     */
    private String vcId;

    /**
     * 机构/组织logo
     */
    private String logo;

    /**
     * 机构/组织授权事项
     */
    private String title;

    /**
     * 机构/组织名称
     */
    private String orgName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 删除状态 1-未删 2-已删
     */
    private Integer delState;

    /**
     * 批次类型 1-2023年会 2-2024灵戒发布 3-2024.5.22重庆 4-重庆开州签到-2024.5.24 5-四川********
     */
    private Integer type;

    private Integer codeType;

}
