package com.lj.auth.domain.resp;

import com.lj.auth.annotation.PrefixPath;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description
 * @date 2025/6/11 19:43
 */
@Data
public class InviteCodeAccountResp {
    /**
     * 头像
     */
    @PrefixPath
    private String headPortrait;
    /**
     * 昵称
     */
    private String nickName;
    /**
     * uuid
     */
    private String uuid;
    /**
     * did
     */
    private String did;
    /**
     * 邀请码
     */
    private String inviteCode;
}
