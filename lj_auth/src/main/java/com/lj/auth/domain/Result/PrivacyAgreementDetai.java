package com.lj.auth.domain.Result;

import lombok.Data;

import java.util.Date;


/**
 * <AUTHOR>
 * @describe
 */

@Data
public class PrivacyAgreementDetai {

    /**
     * 隐私协议已读且确认最新一条版本号
     */
    private String privacyAgreementReadAndConfirmVersion;

    /**
     * 隐私协议最新版本号
     */
    private String privacyAgreementLatestVersion;

    /**
     * 隐私协议已读时间
     */
    private Date privacyAgreementReadTime;


}
