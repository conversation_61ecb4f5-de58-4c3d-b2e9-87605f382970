package com.lj.auth.domain;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 提醒记录
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("lj_square_remind")
public class Remind implements Serializable {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户uuid
     */
    private String accountUuid;

    /**
     * 对方uuid
     */
    private String otherUuid;

    /**
     * 类型 0-关注 1-点赞动态 2-点赞评论 3-收藏动态 4-回复动态 5-回复评论 6-违规处理
     */
    private Integer type;

    /**
     * 动态id
     */
    private Integer trendsId;

    /**
     * 评论id
     */
    private Integer commentId;

    /**
     * 回复id
     */
    private Integer replyId;

    private String content;

    /**
     * 已读状态 0-未读 1-已读
     */
    private Integer readFlag;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;


}
