package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
    * 话费充值记录表
    */
@Data
@TableName(value = "lj_phone_recharge_record")
public class PhoneRechargeRecord {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 经销商uuid
     */
    @TableField(value = "operate_uuid")
    private String operateUuid;

    /**
     * 终端用户uuid
     */
    @TableField(value = "account_uuid")
    private String accountUuid;

    /**
     * 类型：42-话费充值 43-话费充值退款
     */
    @TableField(value = "`type`")
    private Integer type;

    /**
     * 充值号码
     */
    @TableField(value = "recharge_number")
    private String rechargeNumber;

    /**
     * 服务商
     */
    @TableField(value = "service_provider")
    private String serviceProvider;

    /**
     * 充值话费金额
     */
    @TableField(value = "amount")
    private BigDecimal amount;

    /**
     * 支付状态 0:待支付 1:已支付 2:支付失败 3:支付超时关闭的交易 4:支付成功全额退款成功关闭的交易 5:支付失败的交易6-取消支付
     */
    @TableField(value = "`state`")
    private Integer state;

    /**
     * 退款状态 0-未退款 1-已退款 2-部分退款 3-退款中 4:退款失败
     */
    @TableField(value = "refund_state")
    private Integer refundState;

    /**
     * 支付订单号
     */
    @TableField(value = "order_number")
    private String orderNumber;

    /**
     * 话费充值流水号
     */
    @TableField(value = "tran_number")
    private String tranNumber;

    /**
     * 支付方式 1:微信 2:支付宝 3:余额 4:银行卡  5:pc聚合支付 6:抖音支付
     */
    @TableField(value = "pay_type")
    private Integer payType;

    /**
     * 平台：1:H5 2:pc 3:app 4:微信小程序 5:抖音小程序
     */
    @TableField(value = "platform")
    private Integer platform;

    /**
     * 实际付款金额
     */
    @TableField(value = "real_amount")
    private BigDecimal realAmount;

    /**
     * 优惠金额（券优惠金额+活动优惠金额）
     */
    @TableField(value = "discount_amount")
    private BigDecimal discountAmount;

    /**
     * 状态：0-未到账1-话费充值已到账 2-话费已退款3-话费充值中
     */
    @TableField(value = "`status`")
    private Integer status;

    /**
     * 1-域名门户2-灵戒APP
     */
    @TableField(value = "source_Funds")
    private Integer sourceFunds;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 支付时间
     */
    @TableField(value = "pay_time")
    private Date payTime;

    /**
     * 退款金额
     */
    @TableField(value = "refund_amount")
    private BigDecimal refundAmount;

    /**
     * 退款时间
     */
    @TableField(value = "refund_time")
    private Date refundTime;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 天微话费类别id
     */
    @TableField(value = "cate_id")
    private Integer cateId;

    /**
     * 天微话费产品id
     */
    @TableField(value = "product_id")
    private Integer productId;

    /**
     * 话费到账时间
     */
    @TableField(value = "received_time")
    private Date receivedTime;

    /**
     * 退款订单号
     */
    @TableField(value = "refund_order_number")
    private String refundOrderNumber;

    /**
     * 支付过期时间
     */
    @TableField(value = "expiration_time")
    private Date expirationTime;

    /**
     * 支付流水号
     */
    @TableField(value = "pay_out_trade_no")
    private String payOutTradeNo;

    /**
     * 业务状态汇总1-待支付 0-已到账 2-充值中 3-已取消 4-已关闭 5-充值失败
     */
    @TableField(value = "simple_state")
    private Integer simpleState;

    /**
     * 活动id,逗号分割
     */
    @TableField(value = "community_activity_id")
    private String communityActivityId;

    /**
     * 活动优惠金额
     */
    @TableField(value = "community_activity_amount")
    private BigDecimal communityActivityAmount;

    /**
     * 用户券id(逗号分割)
     */
    @TableField(value = "lj_coupon_account_id")
    private String ljCouponAccountId;

    /**
     * 券优惠金额
     */
    @TableField(value = "lj_coupon_account_amount")
    private BigDecimal ljCouponAccountAmount;

    /**
     * 天微优惠金额
     */
    @TableField(value = "tian_wei_discount_amount")
    private BigDecimal tianWeiDiscountAmount;

    /**
     * 公司实付金额
     */
    @TableField(value = "company_real_amount")
    private BigDecimal companyRealAmount;
}