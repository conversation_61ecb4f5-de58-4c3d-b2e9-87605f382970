package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
* <AUTHOR>
* @Description 
* @date 2025/8/13 17:11
*/
/**
    * 抖店卡券
    */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "lj_dou_dian_coupon")
public class DouDianCoupon {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 兑换码
     */
    @TableField(value = "redeem_code")
    private String redeemCode;

    /**
     * 分销ID/商家ID
     */
    @TableField(value = "distribution_id")
    private Integer distributionId;

    /**
     * 状态 0-默认 1-已售出/已同步/待使用(抖店卖出后同步卡券信息) 2-已兑换(同步核销)/已核销 3-已锁定/使用中  4-已使用 5-冻结(申请售后中) 6-废弃(售后成功)
     */
    @TableField(value = "`state`")
    private Integer state;

    /**
     * 核销状态 0-未核销 1-已核销(已兑换必须核销)
     */
    @TableField(value = "write_off_state")
    private Integer writeOffState;

    /**
     * 券批次ID
     */
    @TableField(value = "coupon_batch_id")
    private Long couponBatchId;

    /**
     * 券类ID
     */
    @TableField(value = "coupon_id")
    private Long couponId;

    /**
     * 父订单ID
     */
    @TableField(value = "p_id")
    private Long pId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 券有效期
     */
    @TableField(value = "validity_time")
    private Date validityTime;
}