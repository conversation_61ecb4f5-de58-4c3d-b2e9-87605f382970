package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * @author: wxm
 * @description:
 * @date: 2024/11/5 9:06
 */
@Data
@TableName(value = "lj_coupon_batch")
public class CouponBatch {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 券类型 1-域名抵扣券 2-电影票折扣券
     */
    @TableField("type")
    private Integer type;
    /**
     * 券批次名称(简述)
     */
    @TableField("name")
    private String name;
    /**
     * 描述(使用说明)
     */
    @TableField("description")
    private String description;
    /**
     * 优惠说明
     */
    @TableField("discount_description")
    private String discountDescription;
    /**
     * 券发行数量
     */
    @TableField("publish_number")
    private Integer publishNumber;
    /**
     * 领券条件
     */
    @TableField("get_condition")
    private String getCondition;
    /**
     * 可领取数量
     */
    @TableField("get_number")
    private Integer getNumber;
    /**
     * 领券类型 1-按批次表数量领取  2-按券表数量领取
     */
    @TableField("get_coupon_type")
    private Integer getCouponType;
    /**
     * 开始显示时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField("show_start_time")
    private Date showStartTime;
    /**
     * 截止显示时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField("show_end_time")
    private Date showEndTime;
    /**
     * 参与开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField("join_start_time")
    private Date joinStartTime;
    /**
     * 参与结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField("join_end_time")
    private Date joinEndTime;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField("create_time")
    private Date createTime;
    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField("update_time")
    private Date updateTime;
    /**
     * 有效状态 1-未生效 2-有效 3-失效
     */
    @TableField("effective_state")
    private Integer effectiveState;
    /**
     * 展示状态 1-展示 2-隐藏
     */
    @TableField("show_state")
    private Integer showState;



}
