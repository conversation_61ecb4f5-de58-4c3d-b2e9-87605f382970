package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户持有域名表
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "ym_domain_account")
public class DomainAccount {
    private static final long serialVersionUID = 1L;
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 域名
     */
    @TableField(value = "`domain`")
    private String domain;

    /**
     * 父域名
     */
    @TableField(value = "parent_domain")
    private String parentDomain;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 过期时间
     */
    @TableField(value = "expiration_time")
    private String expirationTime;

    /**
     * 支付时间
     */
    @TableField(value = "payment_time")
    private String paymentTime;

    /**
     * 域名状态 0=正常，1=冻结，2=过期，3=注销
     */
    @TableField(value = "`status`")
    private Integer status;

    /**
     * 注销时间
     */
    @TableField(value = "deregistration_time")
    private String deregistrationTime;

    /**
     * 冻结原因
     */
    @TableField(value = "freeze_reason")
    private String freezeReason;

    /**
     * 冻结时间
     */
    @TableField(value = "freeze_time")
    private String freezeTime;

    /**
     * 所有者账户地址
     */
    @TableField(value = "`owner`")
    private String owner;

    /**
     * 注册时间
     */
    @TableField(value = "register_time")
    private String registerTime;

    /**
     * 注册商
     */
    @TableField(value = "registrar")
    private String registrar;

    /**
     * 经销商uuid
     */
    @TableField(value = "operate_uuid")
    private String operateUuid;

    /**
     * 终端用户uuid
     */
    @TableField(value = "account_uuid")
    private String accountUuid;

    /**
     * 转出状态 1-未转出 2-已转出
     */
    @TableField(value = "transfer_state")
    private Integer transferState;

    /**
     * 购买状态 1-原价购买 2-券购买
     */
    @TableField(value = "pay_state")
    private Integer payState;

    /**
     * nft领取状态 1-未领取 2-已领取
     */
    @TableField(value = "nft_get_state")
    private Integer nftGetState;

    /**
     * did绑定状态 1-未绑定 2-已绑定
     */
    @TableField(value = "did_bind_state")
    private Integer didBindState;

    /**
     * 后缀
     */
    @TableField(value = "suffix")
    private String suffix;

    /**
     * 交易状态  0未上架   1上架一口价中  2上架竞价中
     */
    @TableField(value = "transaction_status")
    private Integer transactionStatus;

    /**
     * 是否是星级域名  0否  1是
     */
    @TableField(value = "is_star")
    private Integer isStar;

    /**
     * 星数
     */
    @TableField(value = "star_count")
    private Integer starCount;

    /**
     * 链地址绑定状态  1-未绑定  2-已绑定
     */
    @TableField(value = "chain_address_bind_state")
    private Integer chainAddressBindState;

    /**
     * did绑定状态 1-未绑定 2-已绑定
     */
    public static final int didBindState1 = 1;
    public static final int didBindState2 = 2;

    /**
     * 交易状态  0未上架   1上架一口价中  2上架竞价中
     */
    public static final int transactionStatus0 = 0;
    public static final int transactionStatus1 = 1;
    public static final int transactionStatus2 = 2;


    /**
     * 购买状态 1-原价购买
     */
    public static final int PAYSTATE_ORIGINAL_PRICE = 1;
    /**
     * 购买状态  2-券购买
     */
    public static final int PAYSTATE_COUPON_PURCHASE = 2;
    /**
     * nft领取状态 1-未领取
     */
    public static final int NFT_GET_STATE_UNRECEIVED = 1;
    /**
     * nft领取状态  2-已领取
     */
    public static final int NFT_GET_STATE_RECEIVED = 2;
}