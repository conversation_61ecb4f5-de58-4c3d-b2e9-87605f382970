package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
    * 地区选项信息
    */
@Data
@TableName(value = "region")
public class Region implements Serializable {
    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private String id;

    /**
     * 区域编码
     */
    @TableField(value = "ad_code")
    private String adCode;

    /**
     * 区域中心点经纬度
     */
    @TableField(value = "center")
    private String center;

    /**
     * 城市代码
     */
    @TableField(value = "city_code")
    private String cityCode;

    /**
     * 行政区划级别
     */
    @TableField(value = "`level`")
    private String level;

    /**
     * 等级 1 一级 2:二级 3:三级
     */
    @TableField(value = "grade")
    private Integer grade;

    /**
     * 名称
     */
    @TableField(value = "`name`")
    private String name;

    /**
     * 全拼
     */
    @TableField(value = "full_spell")
    private String fullSpell;

    /**
     * 简拼
     */
    @TableField(value = "spell")
    private String spell;

    /**
     * 排序
     */
    @TableField(value = "order_num")
    private Integer orderNum;

    /**
     * 父ID
     */
    @TableField(value = "parent_id")
    private Long parentId;

    /**
     * 行政地区路径
     */
    @TableField(value = "`path`")
    private String path;

    /**
     * 是否可选
     */
    @TableField(value = "is_select")
    private Boolean isSelect;

    /**
     * 是否热门
     */
    @TableField(value = "is_host")
    private Boolean isHost;

    /**
     * -1:删除 1:正常 2:未开通
     */
    @TableField(value = "`status`")
    private Integer status;

    /**
     * 是否可用  0:否 1:是
     */
    @TableField(value = "is_enable")
    private Boolean isEnable;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}