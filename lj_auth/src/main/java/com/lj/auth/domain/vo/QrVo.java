package com.lj.auth.domain.vo;

import java.io.Serializable;
import java.util.Date;

import com.lj.auth.annotation.PrefixPath;
import com.lj.auth.domain.Nft;
import com.lj.auth.domain.Operate;

import lombok.Data;

@Data
public class QrVo implements Serializable {

    /**
     * uuid
     */
    private String uuid;

    /**
     * 头像
     */
    @PrefixPath
    private String headPortrait;

    /**
     * 昵称
     */
    private String nickName;

    /**
     * 申请天数
     */
    private Integer daysDifference;

    /**
     * DID标识
     */
    private String didSymbol;

    /**
     * 1-昵称展示 2-域名昵称展示
     */
    private Integer showType;

    /**
     * 域名昵称
     */
    private String domainNickName;

    /**
     * 图像类型 1-普通图像 2-nft图像
     */
    private Integer headPortraitType;

    /**
     * nft表(lj_auth_nft)的id
     */
    private Long headPortraitNftId;

    private String nftImage;

}
