package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 应用管理表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("lj_auth_application_manage")
public class ApplicationManage implements Serializable {

    private static final long serialVersionUID=1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 应用名称
     */
    private String name;

    /**
     * 应用logo
     */
    private String logo;

    /**
     * 应用描述
     */
    private String describes;

    /**
     * 排序：最大的在前
     */
    private Integer sort;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;


}
