package com.lj.auth.domain;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 动态转发记录
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-03
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("lj_square_trends_forward")
public class TrendsForward implements Serializable {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 转发的用户uuid
     */
    private String accountUuid;

    /**
     * 动态id
     */
    private Integer trendsId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;


}
