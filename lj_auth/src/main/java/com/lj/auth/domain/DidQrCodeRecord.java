package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

/**
    * 二维码获取DID信息记录
    */
@Data
@Accessors(chain = true)
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "lj_auth_did_qr_code_record")
public class DidQrCodeRecord {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 应用Id
     */
    @TableField(value = "application_id")
    private Integer applicationId;

    /**
     * 过期时间
     */
    @TableField(value = "expiration_time")
    private Date expirationTime;

    /**
     * 获取did信息时间
     */
    @TableField(value = "create_time")
    private Date createTime;
}