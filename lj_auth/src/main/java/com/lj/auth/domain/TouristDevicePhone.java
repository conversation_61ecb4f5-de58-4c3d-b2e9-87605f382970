package com.lj.auth.domain;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 游客设备码对应手机号
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("lj_auth_tourist_device_phone")
public class TouristDevicePhone implements Serializable {

    private static final long serialVersionUID=1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 随机手机号
     */
    @TableField(value = "phone_number")
    private String phoneNumber;

    /**
     * 设备机器码
     */
    @TableField(value = "meid")
    private String meid;

    /**
     * 1-APP2-微信小程序3-抖音小程序4-支付宝小程序5-小红书
     */
    @TableField(value = "`source`")
    private Integer source;

}