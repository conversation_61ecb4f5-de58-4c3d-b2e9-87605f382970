package com.lj.auth.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UpChainDataVoV3 {

    private Integer opbChainId;

    private String platformAccountAddress;

    private String contractAddress;

    private Long nftGetRecordId;


    private Long nftManualDisposeId;

    private String chainAccountAddress;

    private String accountUUID;

    private Long nftRecordDomainId;
}
