package com.lj.auth.domain.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
    * nft领取记录表Vo
    */
@Data
public class NftGetRecordVo implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 记录标题
     */
    private String title;

    /**
     * 域名
     */
    private String domain;

    /**
     * 领取人
     */
    private String getAccountUuid;

    /**
     * 领取地址
     */
    private String getAccountAddress;

    /**
     * 链框架id
     */
    private Integer opbChainId;

    /**
     * 1注册   2领取
     */
    private Integer status;

    /**
     * 0-不满足  1-满足
     */
    private Integer getStatus;

    /**
     * 领取数量/注册数量
     */
    private Integer getCount;

    /**
     * 是否成功  0-失败  1-成功 2-处理中
     */
    private Integer isSuccess;

    /**
     * 无法领取原因
     */
    private String claimFailureReasons;
}
