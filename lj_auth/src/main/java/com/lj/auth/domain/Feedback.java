package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import com.lj.auth.annotation.PrefixPath;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 反馈表
 */
@Data
@Accessors(chain = true)
@TableName(value = "lj_auth_feedback")
public class Feedback implements Serializable {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    @NotNull(message = "id不能为null")
    private Long id;

    /**
     * 账户uuid
     */
    @TableField(value = "account_uuid")
    @Size(max = 50,message = "账户uuid最大长度要小于 50")
    @NotBlank(message = "账户uuid不能为空")
    private String accountUuid;

    /**
     * 标题
     */
    @TableField(value = "title")
    @Size(max = 500,message = "标题最大长度要小于 500")
    @NotBlank(message = "标题不能为空")
    private String title;

    /**
     * 内容
     */
    @TableField(value = "content")
    private String content;

    /**
     * 回复内容
     */
    @TableField(value = "reply_content")
    private String replyContent;

    /**
     * 上传图片地址
     */
    @TableField(value = "images")
    @Size(max = 500,message = "上传图片地址最大长度要小于 500")
    @PrefixPath
    private String images;

    /**
     * 1-已回复0-未回复
     */
    @TableField(value = "`state`")
    @NotNull(message = "1-已回复0-未回复不能为null")
    private Integer state;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @NotNull(message = "创建时间不能为null")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    @NotNull(message = "修改时间不能为null")
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}