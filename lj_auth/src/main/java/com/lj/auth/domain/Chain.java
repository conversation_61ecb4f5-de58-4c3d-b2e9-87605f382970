package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import com.lj.auth.annotation.PrefixPath;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 链信息表
 */
@Data
@Accessors(chain = true)
@TableName(value = "lj_auth_chain")
public class Chain implements Serializable {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    @NotNull(message = "id不能为null")
    private Long id;

    /**
     * 链框架id
     */
    @TableField(value = "opb_chain_id")
    @NotNull(message = "链框架id不能为null")
    private Integer opbChainId;

    /**
     * 链id
     */
    @TableField(value = "chain_id")
    private Integer chainId;

    /**
     * 链logo
     */
    @TableField(value = "chain_logo")
    @Size(max = 50,message = "链logo最大长度要小于 50")
    @NotBlank(message = "链logo不能为空")
    @PrefixPath
    private String chainLogo;

    /**
     * 算法类型:0=ECDSA,1=GM
     */
    @TableField(value = "algorithm_type")
    private Integer algorithmType;

    /**
     * 算法名称
     */
    @TableField(value = "algorithm_name")
    @Size(max = 100,message = "算法名称最大长度要小于 100")
    private String algorithmName;

    /**
     * 链名称
     */
    @TableField(value = "chain_name")
    @Size(max = 50,message = "链名称最大长度要小于 50")
    @NotBlank(message = "链名称不能为空")
    private String chainName;

    /**
     * 技术平台
     */
    @TableField(value = "technology_platform")
    @Size(max = 100,message = "技术平台最大长度要小于 100")
    private String technologyPlatform;

    /**
     * 链状态 true开启 false关闭
     */
    @TableField(value = "`state`")
    @NotNull(message = "链状态 true开启 false关闭不能为null")
    private Boolean state;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @NotNull(message = "创建时间不能为null")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    @NotNull(message = "修改时间不能为null")
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}