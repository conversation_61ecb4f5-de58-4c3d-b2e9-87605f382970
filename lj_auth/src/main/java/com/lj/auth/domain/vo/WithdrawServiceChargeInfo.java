package com.lj.auth.domain.vo;

import java.math.BigDecimal;

import lombok.Data;

/**
 * 描述： 提现服务费 创建人: CFJ 创建时间: 2024/04/03
 */
@Data
public class WithdrawServiceChargeInfo {

    /**
     * 提现金额
     */
    private BigDecimal withdrawAmount;

    /**
     * 服务费
     */
    private BigDecimal serviceCharge;

    /**
     * 实际金额
     */
    private BigDecimal actualAmount;

    /**
     * 税率
     */
    private BigDecimal taxRate;

    /**
     * 税率(字符串)
     */
    private String taxRateStr;

    /**
     * 税额
     */
    private BigDecimal taxAmount;
}
