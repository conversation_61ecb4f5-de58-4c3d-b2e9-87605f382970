package com.lj.auth.domain.vo.orderDetail;

import com.lj.auth.domain.vo.order.OrderButton;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @describe：
 * @author: cfj
 * @date: 2024/12/06
 */
@Data
public class OrderDetailJsonData {
    /**
     *订单状态
     */
    private StateInfo  stateInfo;
    /**
     * 业务信息
     */
    private Object mainInfo;
    /**
     * 订单模块详情
     */
    private List<OrderDetailVo> orderDetailJson;

    /**
     * 按钮集合
     */
    private List<OrderButton> orderButtonList;
}
