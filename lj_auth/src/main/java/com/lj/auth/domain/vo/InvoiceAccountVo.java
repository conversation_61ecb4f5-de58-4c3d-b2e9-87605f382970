package com.lj.auth.domain.vo;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Description
 * @date 2023/7/24 15:40
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class InvoiceAccountVo {
    /**
     * 主体类型 1-个人 2-企业 3-组织
     */
    @Min(value = 1, message = "主体类型非法")
    @Max(value = 3, message = "主体类型非法")
    @NotNull(message = "主体类型非空")
    private Integer type;

    /**
     * 发票内容
     */
    private String ticketContent;

    /**
     * 税率
     */
    private String taxRate;

    /**
     * 开票类型 1-增值税普通发票 2-增值税专用发票
     */
    @Min(value = 1, message = "开票类型非法")
    @Max(value = 2, message = "开票类型非法")
    @NotNull(message = "开票类型非空")
    private Integer ticketType;

    /**
     * 开票介质 1-纸质发票 2-电子发票
     */
    @Min(value = 1, message = "开票介质非法")
    @Max(value = 2, message = "开票介质非法")
    @NotNull(message = "开票介质非空")
    private Integer ticketMedium;

    /**
     * (wallet_recharge_record主键ID)
     */
    @NotBlank(message = "ID非空")
    private String rechargeRecordIds;

    /**
     * 发票抬头
     */
    private String invoiceTitle;

    /**
     * 纳税人证件号/纳税人识别号/组织机构代码
     */
    private String idNumber;

    /**
     * 发票打印电话
     */
    private String printPhone;

    /**
     * 注册地址
     */
    private String registerAddress;

    /**
     * 基本户开户行
     */
    private String bank;

    /**
     * 基本户开户支行
     */
    private String bankBranch;

    /**
     * 基本户银行账号
     */
    private String bankAccount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 收件人姓名
     */
    private String name;

    /**
     * 收件人电话
     */
    private String phone;

    /**
     * 发票接收邮箱
     */
    private String email;

    /**
     * 接收地址
     */
    private String address;

}
