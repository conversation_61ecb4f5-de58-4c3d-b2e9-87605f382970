package com.lj.auth.domain.vo;

import java.math.BigDecimal;
import java.util.Date;

import com.lj.auth.annotation.PrefixPath;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 描述： 我的贡献记录
 * <p>
 * 创建人: CFJ 创建时间: 2024/07/08
 */
@Data
@Accessors(chain = true)
public class MyContributionRecordVo {
    private Integer id;

    /**
     * 用户uuid
     */
    private String accountUuid;

    /**
     * 贡献金额（单位：元）
     */
    private BigDecimal amount;


    /**
     * 创建时间
     */

    private Date createTime;


    private String nickName;

    private String domainNickName;

    private Integer showType;

    @PrefixPath
    private String headPortrait;

    private Integer headPortraitNftId;

    private Integer headPortraitType;

    private String nftImage;

}