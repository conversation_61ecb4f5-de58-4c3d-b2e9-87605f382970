package com.lj.auth.domain.vo;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class OrderNotifyRequest {
    /**
     * 订单号
     */
    private String orderNumber;
    /**
     * 支付方式  1:微信  2：支付宝  3:余额
     */
    private Integer payType;
    /**
     * 支付状态 0:待支付 1:已支付 2:支付失败 3:支付超时关闭的交易 4:支付成功全额退款成功关闭的交易
     */
    private Integer payStatus;
    /**
     * 支付时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;
    /**
     * 支付金额
     */
    private BigDecimal payAmount;
    /**
     * 交易流水号
     */
    private String payOutTradeNo;

}
