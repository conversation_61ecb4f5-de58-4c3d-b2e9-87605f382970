package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 用户选择标签
 */
@Data
@TableName(value = "account_tag_selected")
public class AccountTagSelected implements Serializable {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 排序字段：升序
     */
    @TableField(value = "sort")
    private Integer sort;

    /**
     * 用户标签表id
     */
    @TableField(value = "account_tag_id")
    private Integer accountTagId;

    /**
     * 用户uuid
     */
    @TableField(value = "account_uuid")
    private String accountUuid;

    private static final long serialVersionUID = 1L;
    /**
     * 用户uuid
     */
    @TableField(exist = false)
    private String name;
}