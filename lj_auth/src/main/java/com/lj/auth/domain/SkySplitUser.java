package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
    * 裂空者用户列表
    */
@Data
@TableName(value = "sky_split_user")
public class SkySplitUser implements Serializable {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 账户uuid
     */
    @TableField(value = "account_uuid")
    private String accountUuid;

    /**
     * 账户did
     */
    @TableField(value = "did")
    private String did;

    /**
     * 电子合同pdf文件
     */
    @TableField(value = "contract_pdf_url")
    private String contractPdfUrl;

    /**
     * 签署时间
     */
    @TableField(value = "sign_time")
    private Date signTime;

    /**
     * 专属排名编号(唯一、递增)
     */
    @TableField(value = "exclusive_number")
    private Integer exclusiveNumber;

    /**
     * 地区id
     */
    @TableField(value = "region_id")
    private String regionId;

    /**
     * 地区path路径
     */
    @TableField(value = "region_path")
    private String regionPath;

    /**
     * 地区排名编号(地区递增)
     */
    @TableField(value = "region_number")
    private Integer regionNumber;

    /**
     * 推广等级
     */
    @TableField(value = "`level`")
    private Integer level;

    /**
     * 推广称号
     */
    @TableField(value = "level_name")
    private String levelName;

    /**
     * 链地址,用于发放SP
     */
    @TableField(value = "chain_address")
    private String chainAddress;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 通知弹窗状态 1-未读 2-已读
     */
    @TableField(value = "notice_state")
    private Integer noticeState;

    /**
     * 下级用户(首次)申领DID返佣比例(首年90%,次年待定)
     */
    @TableField(value = "apply_did_rebate_scale")
    private BigDecimal applyDidRebateScale;

    /**
     * 下级数量(直推一级&申领DID)
     */
    @TableField(value = "lower_number")
    private Integer lowerNumber;

    private static final long serialVersionUID = 1L;
}