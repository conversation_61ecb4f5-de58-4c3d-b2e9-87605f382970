package com.lj.auth.domain.Result;

import lombok.Data;

import java.math.BigInteger;

/**
 * <AUTHOR>
 * @describe
 */

@Data
public class TransactionSimpleInfo {

        private String from;
        private String to;
        private BigInteger value;
        private BigInteger gasPrice;
        private BigInteger gasLimit;
        private BigInteger nonce;
        private String data;
        private BigInteger chainId;
        private String transactionHash;
        private SignatureInfo signature;
        private boolean success;
        private String errorMessage;

        // 签名信息内部类
        public static class SignatureInfo {
            private BigInteger v;
            private BigInteger r;
            private BigInteger s;
            private int recoveryId;

            public SignatureInfo(BigInteger v, BigInteger r, BigInteger s, int recoveryId) {
                this.v = v;
                this.r = r;
                this.s = s;
                this.recoveryId = recoveryId;
            }

            // Getters
            public BigInteger getV() { return v; }
            public BigInteger getR() { return r; }
            public BigInteger getS() { return s; }
            public int getRecoveryId() { return recoveryId; }
            public String getVHex() { return "0x" + v.toString(16); }
            public String getRHex() { return "0x" + r.toString(16); }
            public String getSHex() { return "0x" + s.toString(16); }
        }

        // 构造函数
        public TransactionSimpleInfo() {
            this.success = false;
        }

        // Getters and Setters
        public String getFrom() { return from; }
        public void setFrom(String from) { this.from = from; }

        public String getTo() { return to; }
        public void setTo(String to) { this.to = to; }

        public BigInteger getValue() { return value; }
        public void setValue(BigInteger value) { this.value = value; }

        public BigInteger getGasPrice() { return gasPrice; }
        public void setGasPrice(BigInteger gasPrice) { this.gasPrice = gasPrice; }

        public BigInteger getGasLimit() { return gasLimit; }
        public void setGasLimit(BigInteger gasLimit) { this.gasLimit = gasLimit; }

        public BigInteger getNonce() { return nonce; }
        public void setNonce(BigInteger nonce) { this.nonce = nonce; }

        public String getData() { return data; }
        public void setData(String data) { this.data = data; }

        public BigInteger getChainId() { return chainId; }
        public void setChainId(BigInteger chainId) { this.chainId = chainId; }

        public String getTransactionHash() { return transactionHash; }
        public void setTransactionHash(String transactionHash) { this.transactionHash = transactionHash; }

        public SignatureInfo getSignature() { return signature; }
        public void setSignature(SignatureInfo signature) { this.signature = signature; }

        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }

        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }

        // 便利方法
        public String getValueInEther() {
            if (value == null) return "0";
            return value.divide(new BigInteger("1000000000000000000")).toString();
        }

        public String getValueInWei() {
            return value != null ? value.toString() : "0";
        }

        public boolean isContractCreation() {
            return to == null || to.isEmpty() || "0x".equals(to);
        }

        public boolean hasData() {
            return data != null && !data.isEmpty() && !"0x".equals(data);
        }

        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder();
            sb.append("TransactionInfo{\n");
            sb.append("  success=").append(success).append("\n");
            if (!success && errorMessage != null) {
                sb.append("  error='").append(errorMessage).append("'\n");
            } else {
                sb.append("  from='").append(from).append("'\n");
                sb.append("  to='").append(to != null && !to.isEmpty() ? to : "Contract Creation").append("'\n");
                sb.append("  value=").append(getValueInWei()).append(" Wei (").append(getValueInEther()).append(" ETH)\n");
                sb.append("  gasPrice=").append(gasPrice).append("\n");
                sb.append("  gasLimit=").append(gasLimit).append("\n");
                sb.append("  nonce=").append(nonce).append("\n");
                sb.append("  chainId=").append(chainId).append("\n");
                sb.append("  data='").append(data).append("'\n");
                sb.append("  transactionHash='").append(transactionHash).append("'\n");
                if (signature != null) {
                    sb.append("  signature={v=").append(signature.getVHex())
                            .append(", r=").append(signature.getRHex())
                            .append(", s=").append(signature.getSHex())
                            .append(", recoveryId=").append(signature.getRecoveryId()).append("}\n");
                }
            }
            sb.append("}");
            return sb.toString();
        }
}
