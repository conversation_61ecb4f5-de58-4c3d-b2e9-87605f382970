package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * nft 领取记录 具体域名信息
 */
@Data
@TableName(value = "ym_nft_get_record_domain")
public class NftGetRecordDomain implements Serializable {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * nft领取记录id
     */
    @TableField(value = "nft_get_record_id")
    private Long nftGetRecordId;

    /**
     * 域名
     */
    @TableField(value = "domian")
    private String domian;

    /**
     * tokenId
     */
    @TableField(value = "token_id")
    private Integer tokenId;

    /**
     * 状态   0-未领取(默认)  1-已领取
     */
    @TableField(value = "`status`")
    private Integer status;

    /**
     * 序号
     */
    @TableField(value = "sort")
    private Integer sort;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 状态 未领取
     */
    public static final Integer STATUS_UNRECEIVED = 0;

    /**
     * 状态 已领取
     */
    public static final Integer STATUS_RECEIVED = 1;

    private static final long serialVersionUID = 1L;
}