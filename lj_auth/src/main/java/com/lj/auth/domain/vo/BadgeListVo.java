package com.lj.auth.domain.vo;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableName;
import com.lj.auth.annotation.PrefixPath;

import lombok.Data;

/**
 * 微章表
 */
@Data
public class BadgeListVo implements Serializable {
    /**
     * id
     */
    private Long id;

    /**
     * 微章名称
     */
    private String name;

    /**
     * 微章类型 1-基础微章
     */
    private Integer type;

    /**
     * 默认图片
     */
    @PrefixPath
    private String defaultImage;

    /**
     * 显示标识 0-不显示 1-显示
     */
    private Integer showFlag;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 解锁说明
     */
    private String unlockInstructions;

    /**
     * 跳转路径
     */
    private String jumpUrl;

    /**
     * 1-原生2-H53-webView
     */
    private Integer jumpType;


    /**
     * 1-不隐藏0-隐藏
     */
    private Integer isHiddenNav;


    /**
     * 状态  0-未激活 1-已激活
     */
    private Integer state;

    /**
     * 佩戴标识 0-未佩戴 1-已佩戴
     */
    private Integer wearFlag;

}