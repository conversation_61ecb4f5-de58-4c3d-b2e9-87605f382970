package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 域名续费NFT关联表
 */
@Data
@TableName(value = "ym_order_domain_renewal_nft")
public class OrderDomainRenewalNft implements Serializable {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 域名续费订单详情表(关联表:ym_order_domain_renewal_info主键)
     */
    @TableField(value = "order_info_id")
    private Long orderInfoId;

    /**
     * 合约地址
     */
    @TableField(value = "contract_address")
    private String contractAddress;

    /**
     * TOKENID
     */
    @TableField(value = "token")
    private Integer token;

    /**
     * 交易状态 1-待交易 2-交易中(已提交上链) 3-交易完成/成功 4-交易失败
     */
    @TableField(value = "tran_state")
    private Integer tranState;

    /**
     * 交易hash
     */
    @TableField(value = "hash")
    private String hash;

    /**
     * 发送NFT地址
     */
    @TableField(value = "from_address")
    private String fromAddress;

    /**
     * 接收NFT地址
     */
    @TableField(value = "to_address")
    private String toAddress;

    /**
     * 授权发起人地址
     */
    @TableField(value = "accredit_from_address")
    private String accreditFromAddress;

    /**
     * 授权接收人地址
     */
    @TableField(value = "accredit_to_address")
    private String accreditToAddress;

    /**
     * 发起交易地址(实际调用发送NFT的地址)
     */
    @TableField(value = "sponsor_transfer_address")
    private String sponsorTransferAddress;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    private static final long serialVersionUID = 1L;

    /**
     * 交易状态待交易
     */
    public static final int TRAN_STATE_WAIT = 1;

    /**
     * 交易状态 交易中(已提交上链)
     */
    public static final int TRAN_STATE_PROCESSING = 2;

    /**
     * 交易状态 交易完成/成功
     */
    public static final int TRAN_STATE_SUCCESS = 3;

    /**
     * 交易状态 交易失败
     */
    public static final int TRAN_STATE_FAIL = 4;
}