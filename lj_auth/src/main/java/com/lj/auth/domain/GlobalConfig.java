package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 全局配置表
 */
@Data
@Accessors(chain = true)
@TableName(value = "lj_auth_global_config")
public class GlobalConfig implements Serializable {
    @TableId(value = "id", type = IdType.AUTO)
    @NotNull(message = "不能为null")
    private Integer id;

    /**
     * 配置项
     */
    @TableField(value = "`key`")
    @Size(max = 255,message = "配置项最大长度要小于 255")
    @NotBlank(message = "配置项不能为空")
    private String key;

    /**
     * 配置内容
     */
    @TableField(value = "`value`")
    @NotBlank(message = "配置内容不能为空")
    private String value;

    /**
     * 类型：1-字符串 2-整数 3-小数 4-图片 5-富文本
     */
    @TableField(value = "`type`")
    private Integer type;

    /**
     * 配置项描述
     */
    @TableField(value = "describes")
    @Size(max = 255,message = "配置项描述最大长度要小于 255")
    private String describes;

    private static final long serialVersionUID = 1L;
    
    /**
     * 凭证授权记录机构logo(灵戒)
     */
    public static final String VOUCHER_ORG_LOGO_LJ = "voucher_org_logo_lj";
    /**
     * 凭证授权记录机构logo(重庆派棠，2024.5.22)
     */
    public static final String VOUCHER_ORG_LOGO_CQ_PT = "voucher_org_logo_cq_pt";
    /**
     * 凭证授权记录机构logo(晟世派商)
     */
    public static final String VOUCHER_ORG_LOGO_SSPS = "voucher_org_logo_ssps";
}
