package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: wxm
 * @description:
 * @date: 2024/12/5 13:44
 */
@Data
@TableName("community_activity")
public class CommunityActivity {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    /**
     * 类型 1-电影票快票 2-电影票慢票 3-话费快充 4-话费慢充 5-DID限免 6-DID限免(三方渠道)
     */
    @TableField("type")
    private Integer type;
    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField("start_time")
    private Date startTime;
    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField("end_time")
    private Date endTime;
    /**
     * 优惠模式 1-按比例优惠 2-按金额优惠(每座,话费为每单)
     */
    @TableField("discount_type")
    private Integer discountType;
    /**
     * 折扣比例,0＜折扣＜1
     */
    @TableField("discount_rate")
    private BigDecimal discountRate;
    /**
     * 优惠金额(每座,话费为每单)
     */
    @TableField("discount_amount")
    private BigDecimal discountAmount;
    /**
     * 开关 1-开 2-关
     */
    @TableField("activity_switch")
    private Integer activitySwitch;
    /**
     * 活动描述
     */
    @TableField("activity_desc")
    private String activityDesc;
    /**
     * 优惠描述
     */
    @TableField("discount_desc")
    private String discountDesc;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField("create_time")
    private Date createTime;
    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField("update_time")
    private Date updateTime;
    /**
     * 是否有进入资金池的资金 0-没有 1-有
     */
    @TableField("partake_capital_pool")
    private Integer partakeCapitalPool;
    /**
     * 进入资金池的资金比例(按订单金额计算)
     */
    @TableField("capital_pool_rate")
    private BigDecimal capitalPoolRate;

}
