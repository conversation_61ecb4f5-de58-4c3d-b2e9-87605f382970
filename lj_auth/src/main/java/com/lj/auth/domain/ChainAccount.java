package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 链账户
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("lj_auth_chain_account")
public class ChainAccount implements Serializable {

    private static final long serialVersionUID=1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 链框架id
     */
    private Integer opbChainId;

    /**
     * 用户uuid
     */
    private String accountUuid;

    /**
     * 链地址
     */
    private String address;

    /**
     * 私钥
     */
    private String privatekey;

    /**
     * 公钥
     */
    private String publickey;

    /**
     * 安全密钥
     */
    private String securePassword;

    /**
     * 密码提示
     */
    private String passwordPrompt;

    /**
     * 链账户名称
     */
    private String chainClientName;

    /**
     * 链地址名称
     */
    private String chainAddressName;

    /**
     * true-不隐藏false-隐藏
     */
    private Boolean state;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;


}
