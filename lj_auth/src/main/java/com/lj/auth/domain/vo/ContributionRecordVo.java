package com.lj.auth.domain.vo;

import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 贡献记录
 */
@Data
@Accessors(chain = true)
public class ContributionRecordVo {
    private Integer id;

    /**
     * 贡献金额（单位：元）
     */
    private BigDecimal amount;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

}