package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 描述： 云链平添能量充值记录表
 *
 * 创建人: CFJ 创建时间: 2024/04/02
 */
@Data
@Accessors(chain = true)
@TableName(value = "lj_auth_energy_recharge_flow")
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class EnergyRechargeFlow implements Serializable {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    @NotNull(message = "id不能为null")
    private Long id;

    /**
     * 终端用户uuid
     */
    @TableField(value = "account_uuid")
    @Size(max = 15, message = "终端用户uuid最大长度要小于 15")
    @NotBlank(message = "终端用户uuid不能为空")
    private String accountUuid;

    /**
     * 充值金额，单位元
     */
    @TableField(value = "business_money")
    private BigDecimal businessMoney;

    /**
     * gas值
     */
    @TableField(value = "gas")
    @Size(max = 100, message = "gas值最大长度要小于 100")
    private String gas;

    /**
     * 充值链地址
     */
    private String address;

    /**
     * 交易流水号
     */
    @TableField(value = "req_transaction_sn")
    @Size(max = 100, message = "交易流水号最大长度要小于 100")
    @NotBlank(message = "交易流水号不能为空")
    private String reqTransactionSn;

    /**
     * 交易hash
     */
    @TableField(value = "hash")
    @Size(max = 100, message = "交易hash最大长度要小于 100")
    private String hash;

    /**
     * 交易类型：1=充值2=消耗
     */
    @TableField(value = "`type`")
    private Integer type;

    /**
     * 充值状态1-充值中 5-充值失败 10=充值成功
     */
    @TableField(value = "recharge_state")
    private Integer rechargeState;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @NotNull(message = "创建时间不能为null")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    @NotNull(message = "更新时间不能为null")
    private Date updateTime;
    /**
     * 上链状态1-成功0-失败
     */
    private Integer state;


    private static final long serialVersionUID = 1L;
}