package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
    * app版本信息更新记录
    */
@Data
@TableName(value = "lj_auth_version_record")
public class VersionRecord implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 下载地址
     */
    @TableField(value = "down_url")
    private String downUrl;

    /**
     * 语言,0:中文(默认),1:英文
     */
    @TableField(value = "`language`")
    private Byte language;

    /**
     * 标题
     */
    @TableField(value = "title")
    private String title;

    /**
     * 系统类型：1-安卓 2-IOS
     */
    @TableField(value = "system_type")
    private Integer systemType;

    /**
     * 更新内容
     */
    @TableField(value = "update_log")
    private String updateLog;

    /**
     * 版本
     */
    @TableField(value = "version")
    private String version;

    /**
     * 版本名称
     */
    @TableField(value = "version_name")
    private String versionName;

    /**
     * 版本类型,0:商店版,1:非商店版
     */
    @TableField(value = "version_type")
    private Byte versionType;

    /**
     * 状态,0:不提示,1:更新,2:强制更新.
     */
    @TableField(value = "`status`")
    private Integer status;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 构建次数
     */
    @TableField(value = "build")
    private String build;

    /**
     * JIEWAI-界外线下分发
XIAOMI-小米应用市场
HUAWEI-华为应用市场
OPPO-OPPO应用市场
VIVO-VIVO应用市场
HONOUR-荣耀应用市场
APPSTORE-苹果应用市场
     */
    @TableField(value = "platform")
    private String platform;

    /**
     * 1-外部更新2-内部更新
     */
    @TableField(value = "update_type")
    private Integer updateType;

    /**
     * apk下载链接
     */
    @TableField(value = "apk_link")
    private String apkLink;

    /**
     * app版本信息id
     */
    @TableField(value = "auth_version_id")
    private Integer authVersionId;

    private static final long serialVersionUID = 1L;
}