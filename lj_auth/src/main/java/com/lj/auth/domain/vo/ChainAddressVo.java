package com.lj.auth.domain.vo;

import java.io.Serializable;
import java.util.Date;


import com.lj.auth.annotation.PrefixPath;
import lombok.Data;

/**
 * 域名解析合约地址
 */
@Data
public class ChainAddressVo implements Serializable {
    private Long id;

    /**
     * 链名称
     */
    private String chainName;

    @PrefixPath
    private String chainLogo;

    /**
     * 合约地址
     */
    private String contractAddress;

    /**
     * 上链时间
     */
    private Date windTime;

    /**
     * 上链状态
     */
    private String state;

    /**
     * 排序
     */
    private Integer sorter;

}
