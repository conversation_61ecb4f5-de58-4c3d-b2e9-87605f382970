package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
* <AUTHOR>
* @Description
* @date 2024/4/26 15:58
*/
/**
    * 凭证授权
    */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "ym_voucher_accredit")
public class VoucherAccredit {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 账户uuid
     */
    @TableField(value = "account_uuid")
    private String accountUuid;

    /**
     * 经销商uuid
     */
    @TableField(value = "operate_uuid")
    private String operateUuid;

    /**
     * 凭证标识符
     */
    @TableField(value = "vc_id")
    private String vcId;

    /**
     * 机构/组织logo
     */
    @TableField(value = "logo")
    private String logo;

    /**
     * 机构/组织授权事项
     */
    @TableField(value = "title")
    private String title;

    /**
     * 机构/组织名称
     */
    @TableField(value = "org_name")
    private String orgName;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 删除状态 1-未删 2-已删
     */
    @TableField(value = "del_state")
    private Integer delState;

    /**
     * 批次类型 1-2023年会 2-2024灵戒发布 3-2024.5.22重庆  4-重庆开州签到-2024.5.24 5-四川********
     */
    @TableField(value = "`type`")
    private Integer type;

    @TableField(value = "code_type")
    private Integer codeType;

    public static final Integer type1 = 1;
    public static final Integer type2 = 2;
    public static final Integer type3 = 3;
    public static final Integer type4 = 4;
    public static final Integer type5 = 5;
}
