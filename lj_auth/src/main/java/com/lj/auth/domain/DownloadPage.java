package com.lj.auth.domain;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import com.lj.auth.annotation.PrefixPath;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * web下载页
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("ym_download_page")
public class DownloadPage implements Serializable {

    private static final long serialVersionUID=1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 顶部logo
     */
    @PrefixPath
    private String topLogo;

    /**
     * 应用名称
     */
    private String applyName;

    /**
     * 应用简述
     */
    private String applySketch;

    /**
     * 应用展示图
     */
    @PrefixPath
    private String applyShow;

    /**
     * h5链接
     */
    private String h5Link;

    /**
     * 安卓链接
     */
    private String androidLink;

    /**
     * ios链接
     */
    private String iosLink;

    /**
     * 经销商uuid
     */
    private String operateUuid;

    /**
     * 应用类型 1-门户 2-钱包
     */
    private Integer applyType;


    /**
     * 浏览器ICON：about表数据
     */
    @TableField(exist = false)
    private String browserIcon;


}
