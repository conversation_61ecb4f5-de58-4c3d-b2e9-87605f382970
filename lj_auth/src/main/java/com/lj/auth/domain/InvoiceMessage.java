package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 用户发票开票信息
 */
@Data
@Accessors(chain = true)
@TableName(value = "lj_auth_invoice_message")
public class InvoiceMessage implements Serializable {
    @TableId(value = "id", type = IdType.AUTO)
    @NotNull(message = "不能为null")
    private Long id;

    /**
     * 账户UUID
     */
    @TableField(value = "account_uuid")
    @Size(max = 20, message = "账户UUID最大长度要小于 20")
    private String accountUuid;

    /**
     * 主体类型 1-个人 2-企业 3-组织
     */
    @TableField(value = "`type`")
    @NotNull(message = "主体类型 1-个人 2-企业 3-组织不能为null")
    private Integer type;

    /**
     * 发票抬头
     */
    @TableField(value = "invoice_title")
    @Size(max = 255, message = "发票抬头最大长度要小于 255")
    @NotBlank(message = "发票抬头不能为空")
    private String invoiceTitle;

    /**
     * 纳税人证件号/纳税人识别号/组织机构代码
     */
    @TableField(value = "id_number")
    @Size(max = 255, message = "纳税人证件号/纳税人识别号/组织机构代码最大长度要小于 255")
    @NotBlank(message = "纳税人证件号/纳税人识别号/组织机构代码不能为空")
    private String idNumber;

    /**
     * 发票打印电话
     */
    @TableField(value = "phone")
    @Size(max = 20, message = "发票打印电话最大长度要小于 20")
    private String phone;

    /**
     * 注册地址
     */
    @TableField(value = "address")
    @Size(max = 255, message = "注册地址最大长度要小于 255")
    private String address;

    /**
     * 基本户开户行
     */
    @TableField(value = "bank")
    @Size(max = 255, message = "基本户开户行最大长度要小于 255")
    private String bank;

    /**
     * 基本户开户支行
     */
    @TableField(value = "bank_branch")
    @Size(max = 255, message = "基本户开户支行最大长度要小于 255")
    private String bankBranch;

    /**
     * 基本户银行账号
     */
    @TableField(value = "bank_account")
    @Size(max = 255, message = "基本户银行账号最大长度要小于 255")
    private String bankAccount;

    /**
     * 备注
     */
    @TableField(value = "remark")
    @Size(max = 255, message = "备注最大长度要小于 255")
    private String remark;

    /**
     * 状态 1-默认 2-非默认
     */
    @TableField(value = "`state`")
    private Integer state;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    private static final long serialVersionUID = 1L;
}