package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 任务中心
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName(value = "lj_auth_task_center")
public class TaskCenter implements Serializable {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 1-日常任务2-周期任务3-新手任务4-限时任务5-长期任务
     */
    @TableField(value = "task_type_id")
    private Integer taskTypeId;

    /**
     * 任务名称
     */
    @TableField(value = "task_name")
    private String taskName;

    /**
     * 任务介绍
     */
    @TableField(value = "introduce")
    private String introduce;

    /**
     * 任务描述
     */
    @TableField(value = "`describe`")
    private String describe;

    /**
     * 任务奖励
     */
    @TableField(value = "reward")
    private BigDecimal reward;

    /**
     * 任务完成数量
     */
    @TableField(value = "completion_count")
    private Integer completionCount;

    /**
     * 任务开启时间
     */
    @TableField(value = "opening_time")
    private Date openingTime;

    /**
     * 任务完成时间
     */
    @TableField(value = "completion_time")
    private Date completionTime;

    /**
     * 任务状态1-开启 0-关闭
     */
    @TableField(value = "task_state")
    private Integer taskState;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}