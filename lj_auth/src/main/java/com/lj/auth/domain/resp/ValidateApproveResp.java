package com.lj.auth.domain.resp;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @describe  校验是否授权返回结果
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ValidateApproveResp {
    /**
     * NFTID
     */
    private Long NFTId;

    /**
     * 授权结果
     */
    private Boolean isSuccess;

    /**
     * 消息
     */
    private String message;


}
