package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "lj_auth_nft_manual_dispose")
public class NftManualDispose implements Serializable {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * NFT领取记录表id
     */
    @TableField(value = "nft_record_id")
    private Long nftRecordId;

    /**
     * 链id
     */
    @TableField(value = "opb_chain_id")
    private Integer opbChainId;

    /**
     * 用户uuid
     */
    @TableField(value = "uuid")
    private String uuid;

    /**
     * 链账户地址
     */
    @TableField(value = "chain_account_address")
    private String chainAccountAddress;

    /**
     * 合约地址
     */
    @TableField(value = "contract_address")
    private String contractAddress;

    /**
     * nft数量
     */
    @TableField(value = "nft_count")
    private Integer nftCount;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 处理状态  0-未处理  1-处理中  2-已处理
     */
    @TableField(value = "`status`")
    private Integer status;

    /**
     * NFT领取记录域名表
     */
    @TableField(value = "nft_record_domain_id")
    private Long nftRecordDomainId;


    /**
     * 处理状态未处理
     */
    public static final Integer STATUS_UNTREATED = 0;
    /**
     * 处理状态处理中
     */
    public static final Integer STATUS_PROCESSING = 1;
    /**
     * 处理状态已处理
     */
    public static final Integer STATUS_PROCESSED = 2;

    private static final long serialVersionUID = 1L;
}