package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * sp批量发放
 */
@Data
@TableName(value = "lj_auth_sp_record_batch")
public class SpRecordBatch implements Serializable {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户领取奖励地址
     */
    @TableField(value = "address")
    private String address;

    /**
     * 任务奖励
     */
    @TableField(value = "reward")
    private BigDecimal reward;

    /**
     * 任务上链奖励hash
     */
    @TableField(value = "hash")
    private String hash;

    /**
     * 发放时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 1-成功0-失败
     */
    @TableField(value = "`state`")
    private Integer state;

    /**
     * 发放时间
     */
    @TableField(value = "distribution_time")
    private Date distributionTime;

    /**
     * 1-已检查0-未检查
     */
    @TableField(value = "is_check")
    private Integer isCheck;

    private static final long serialVersionUID = 1L;
}