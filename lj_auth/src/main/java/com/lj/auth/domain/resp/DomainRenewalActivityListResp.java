package com.lj.auth.domain.resp;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description
 * @date 2025/1/4 15:01
 */
@Data
public class DomainRenewalActivityListResp {
    /**
     * 活动id
     */
    private Long activityId;
    /**
     * 活动描述
     */
    private String activityDesc;
    /**
     * 折扣描述
     */
    private String discountDesc;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;
    /**
     * 折扣比例,0＜折扣＜1
     */
    private BigDecimal discountScale;
}
