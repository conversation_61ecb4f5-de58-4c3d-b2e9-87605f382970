package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 发票收件信息
 */
@Data
@Accessors(chain = true)
@TableName(value = "lj_auth_addressee_message")
public class AddresseeMessage implements Serializable {
    @TableId(value = "id", type = IdType.AUTO)
    @NotNull(message = "不能为null")
    private Long id;

    /**
     * 账户UUID
     */
    @TableField(value = "account_uuid")
    @Size(max = 20,message = "账户UUID最大长度要小于 20")
    private String accountUuid;

    /**
     * 收件人姓名
     */
    @TableField(value = "`name`")
    @Size(max = 20,message = "收件人姓名最大长度要小于 20")
    private String name;

    /**
     * 收件人电话
     */
    @TableField(value = "phone")
    @Size(max = 50,message = "收件人电话最大长度要小于 50")
    private String phone;

    /**
     * 发票接收邮箱
     */
    @TableField(value = "email")
    @Size(max = 50,message = "发票接收邮箱最大长度要小于 50")
    private String email;

    /**
     * 接收地址
     */
    @TableField(value = "address")
    @Size(max = 255,message = "接收地址最大长度要小于 255")
    private String address;

    /**
     * 状态 1-默认 2-非默认
     */
    @TableField(value = "`state`")
    private Integer state;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    private static final long serialVersionUID = 1L;
}