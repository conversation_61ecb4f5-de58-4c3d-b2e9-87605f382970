package com.lj.auth.domain.vo;

import java.io.Serializable;
import java.util.Date;

import com.lj.auth.annotation.PrefixPath;
import lombok.Data;

/**
 * 探索应用
 */
@Data
public class ExploreAppVo implements Serializable {
    private Integer id;

    /**
     * 应用名称
     */
    private String appName;

    /**
     * 探索应用类型id
     */
    private Integer appTypeId;

    /**
     * 应用描述
     */
    private String describe;

    /**
     * 应用logo
     */
    @PrefixPath
    private String logo;

    /**
     * 应用链接
     */
    private String appLink;

    /**
     * 1-推荐 0-不推荐
     */
    private Boolean isRecommendation;

    /**
     * 1-官方0-非官方
     */
    private Boolean isOfficial;

    /**
     * 跳转方式
     */
    private Integer jumpType;

    /**
     * 用户uuid
     */
    private String accountUuid;

    /**
     * 创建时间
     */
    private Date createTime;
}