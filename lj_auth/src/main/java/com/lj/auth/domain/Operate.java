package com.lj.auth.domain;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableName;

import com.lj.auth.annotation.PrefixPath;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 经销商分成比例表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("ym_operate")
public class Operate implements Serializable {

    private static final long serialVersionUID=1L;

    /**
     * 用户id
     */
    private String accountUuid;

    /**
     * 用户标识  2经销商 3渠道商
     */
    private Integer identity;

    /**
     * 上级账户
     */
    private String parentAccountUuid;

    /**
     * account表uuid
     */
    private String oldUuid;

    /**
     * 经销商分成比例
     */
    private String level1Ratio;

    /**
     * 渠道商分成比例
     */
    private String level2Ratio;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 门户链接
     */
    private String link;

    /**
     * 手机号
     */
    private String phoneNumber;

    /**
     * 登录密码
     */
    private String password;

    /**
     * 支付密码
     */
    private String payPassword;

    /**
     * 图像
     */
    @PrefixPath
    private String headPortrait;

    /**
     * 昵称
     */
    private String nickName;

    /**
     * 真实姓名
     */
    private String realName;

    /**
     * 身份证号
     */
    private String idCard;

    /**
     * 运营端ID
     */
    private String opearteNumber;

    /**
     * 0-非热门经销商 1-热门经销商
     */
    private Integer hotFlag;

    /**
     * 0-不显示 1-显示
     */
    private Integer showFlag;

    /**
     * 排序
     */
    private Integer sort;

    @PrefixPath
    private String  operateImage;


}
