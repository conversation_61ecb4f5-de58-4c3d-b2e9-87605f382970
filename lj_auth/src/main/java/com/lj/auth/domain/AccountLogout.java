package com.lj.auth.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 账户表-注销账号
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class AccountLogout implements Serializable {

    private static final long serialVersionUID=1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 手机号
     */
    private String phoneNumber;

    /**
     * uuid
     */
    private String uuid;

    /**
     * 密码
     */
    private String password;

    /**
     * 支付密码
     */
    private String payPassword;

    /**
     * 头像
     */
    private String headPortrait;

    /**
     * 昵称
     */
    private String nickName;

    /**
     * 注册ip
     */
    private String registerIp;

    /**
     * 注册时间
     */
    private Date registerTime;

    /**
     * 最近登录ip
     */
    private String recentLoginIp;

    /**
     * 最近登录时间
     */
    private Date recentLoginTime;

    /**
     * 是否实名认证 0:未认证(默认) 1:认证中 2:认证通过 3:认证失败
     */
    private Integer isRealName;

    /**
     * 用户状态 1:正常 2:黑名单 3:已注销
     */
    private Integer blacklist;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 经销商uuid
     */
    private String operateUuid;

    /**
     * 实名姓名
     */
    private String realName;

    /**
     * 身份证号
     */
    private String idCard;

    /**
     * 用户标识 1普通用户 2经销商 3渠道商
     */
    private Integer identity;

    /**
     * uuid后缀
     */
    private String uuidSuffix;

    /**
     * 上级uuid 注册时填入
     */
    private String parentUuid;

    /**
     * 是否禁言 1:禁言 0:否(默认)
     */
    private Boolean isBanned;

    /**
     * 提成比例
     */
    private BigDecimal commissionRatio;

    /**
     * 抖音openid
     */
    private String douyinOpenid;

    /**
     * 微信openid
     */
    private String weixinOpenid;

    /**
     * 推广等级 0-lv1推广大使(待激活) 1-lv1推广大使2-lv2推广大使 3-lv3推广大使  4-默认(不满足条件) 9-推广大使(新等级)
     */
    private Integer promotionLevel;

    /**
     * 推广返佣状态 1-参与返佣(默认) 2-不参与返佣
     */
    private Integer promotionRebateState;

    /**
     * 活跃标识 1:活跃 0:非活跃
     */
    private Boolean activeFlag;

    /**
     * DID标识
     */
    private String didSymbol;

    /**
     * 福穗平台人员id
     */
    private String peopleId;

    /**
     * 福穗平台签约状态(0-待处理 1-校验失败 2-待签约 3-签约成功 4-签约校验错误)
     */
    private Boolean signStatus;

    /**
     * 签约地址
     */
    private String signUrl;

    /**
     * 邀请码
     */
    private String inviteCode;

    /**
     * 图像类型1-普通图像 2-nft图像
     */
    private Integer headPortraitType;

    /**
     * nft表（lj_auth_nft）的id
     */
    private Long headPortraitNftId;

    /**
     * 1-昵称展示 2-域名昵称展示
     */
    private Integer showType;

    /**
     * 域名昵称
     */
    private String domainNickName;

    /**
     * 注册来源：2-灵戒App 1-域名门户 0-CMS运营账号 3-游客模式
     */
    private Integer registrationSource;

    /**
     * 注销原因id
     */
    private String reasonId;

    /**
     * 注销自定义原因
     */
    private String reasonSelf;


    /**
     * 注销时间
     */
    private Date logoutTime;



}
