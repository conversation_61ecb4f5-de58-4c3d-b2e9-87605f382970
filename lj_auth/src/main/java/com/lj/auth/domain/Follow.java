package com.lj.auth.domain;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 广场关注
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("lj_square_follow")
public class Follow implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户uuid(如张三关注李四，此为李四)
     */
    private String accountUuid;

    /**
     * 关注用户的uuid(如张三关注李四，此为张三)
     */
    private String followUuid;

    /**
     * 已读状态 0-未读 1-已读
     */
    private Integer readFlag;

    /**
     * 0-未删除 1-已删除
     */
    private Integer removeFlag;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

}
