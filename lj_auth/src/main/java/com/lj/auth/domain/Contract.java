package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "lj_auth_contract")
public class Contract {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 合约类型  1-ERC721 2-域名注册管理器  3-灵戒域名注册器 4-灵戒公共解析器
     */
    @TableField(value = "contract_type")
    private Integer contractType;

    /**
     * 合约名
     */
    @TableField(value = "contract_name")
    private String contractName;

    /**
     * 合约标识
     */
    @TableField(value = "contract_symbol")
    private String contractSymbol;

    /**
     * 合约地址
     */
    @TableField(value = "contract_address")
    private String contractAddress;

    /**
     * 链框架id
     */
    @TableField(value = "opb_chain_id")
    private Integer opbChainId;

    /**
     * token前缀  部分合约可用
     */
    @TableField(value = "image_url")
    private String imageUrl;

    /**
     * NFT封面
     */
    @TableField(value = "contract_url")
    private String contractUrl;

    /**
     * 状态   0-禁用   1-启用
     */
    @TableField(value = "`status`")
    private Integer status;

    /**
     * 用途 1-注册域名领NFT
     */
    @TableField(value = "to_use")
    private Integer toUse;

    /**
     * 合约描述
     */
    @TableField(value = "contract_describe")
    private String contractDescribe;

    /**
     * 合约类型  1-ERC721
     */
    public static final int CONTRACT_TYPE_ERC721 = 1;

    /**
     * 合约类型   2-域名注册管理器
     */
    public static final int CONTRACT_TYPE_DOMAIN_REGISTER_MANAGER = 2;

    /**
     * 合约类型   3-灵戒域名注册器
     */
    public static final int CONTRACT_TYPE_LINGJIE_DOMAIN_REGISTER = 3;

    /**
     * 合约类型   4-灵戒公共解析器
     */
    public static final int CONTRACT_TYPE_LINGJIE_PUBLIC_RESOLVER = 4;

    /**
     * 状态 启用
     */
    public static final int STATUS_ENABLE = 1;

    /**
     * 用途 注册域名领取NFT
     */
    public static final int TO_USE_REGISTER_DOMAIN_NFT = 1;

}