package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 用户银行卡表
 */
@Data
@Accessors(chain = true)
@TableName(value = "lj_auth_account_card")
public class AccountCard implements Serializable {
    @TableId(value = "id", type = IdType.AUTO)
    @NotNull(message = "不能为null")
    private Long id;

    /**
     * 账户uuid
     */
    @TableField(value = "account_uuid")
    @Size(max = 20,message = "账户uuid最大长度要小于 20")
    private String accountUuid;

    /**
     * 持卡姓名
     */
    @TableField(value = "`name`")
    @Size(max = 255,message = "持卡姓名最大长度要小于 255")
    private String name;

    /**
     * 卡号
     */
    @TableField(value = "card_number")
    @Size(max = 30,message = "卡号最大长度要小于 30")
    private String cardNumber;

    /**
     * 银行
     */
    @TableField(value = "bank")
    @Size(max = 255,message = "银行最大长度要小于 255")
    private String bank;

    /**
     * 银行卡正面
     */
    @TableField(value = "card_picture")
    @Size(max = 255,message = "银行卡正面最大长度要小于 255")
    private String cardPicture;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 1未移除 0:已移除
     */
    @TableField(value = "`state`")
    private Boolean state;

    private static final long serialVersionUID = 1L;
}