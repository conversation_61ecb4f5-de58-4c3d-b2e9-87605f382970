package com.lj.auth.domain;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 广场动态
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("lj_square_trends")
public class Trends implements Serializable {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 用户uuid
     */
    private String accountUuid;

    private String content;

    /**
     * 图片链接(多个用逗号连接)
     */
    private String pictures;

    /**
     * 视频(最多只能有一个视频)
     */
    private String video;

    /**
     * 类型 1-文本 2-图片 3-图文 4-视频 5-视文
     */
    private Integer type;

    /**
     * 热门标签 0-普通 1-热门
     */
    private Integer hotFlag;

    /**
     * 浏览量
     */
    private Integer pageviews;

    /**
     * 点赞数量
     */
    private Integer likesNum;

    /**
     * 收藏数量
     */
    private Integer collectNum;

    /**
     * 转发数量
     */
    private Integer forwardNum;

    /**
     * 删除标签 0-未删除 1-主动删除 2-举报核实后删除
     */
    private Integer removeFlag;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;


}
