package com.lj.auth.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 一口价域名库
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("lj_transfer_fix_domain")
public class FixDomain implements Serializable {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 交易流水号
     */
    private String transferUuid;

    /**
     * 域名
     */
    private String domain;

    /**
     * 后缀
     */
    private String suffix;

    /**
     * 账户uuid
     */
    private String accountUuid;

    /**
     * 售卖人昵称
     */
    private String accountNickName;

    /**
     * 上架时间
     */
    private LocalDateTime upTime;

    /**
     * 下架时间
     */
    private LocalDateTime downTime;

    /**
     * 出售周期
     */
    private Integer period;

    /**
     * 一口价
     */
    private BigDecimal price;

    /**
     * 域名描述
     */
    private String description;

    /**
     * 费率
     */
    private String rate;

    /**
     * 手续费
     */
    private BigDecimal servicePrice;

    /**
     * 状态   0待开售  1售卖中  2已结束  3已取消
     */
    private Integer domainStatus;

    /**
     * 交易状态  0无人参与  1交易成功  2交易失败  3取消交易
     */
    private Integer transactionStatus;

    /**
     * 浏览次数
     */
    private Integer browseCount;

    /**
     * 购买用户uuid
     */
    private String buyerUuid;

    /**
     * 购买用户昵称
     */
    private String buyerNickName;

    /**
     * 是否热门  0否   1是
     */
    private Integer isHot;

    /**
     * 热门排序 值越小越靠前
     */
    private Integer sort;

    /**
     * 是否删除  0未删除    1已删除
     */
    private Integer isDelete;

    /**
     * 是否推荐  0否   1是
     */
    private Integer isRecommend;

    /**
     * 推荐排序  值越小越靠前
     */
    private Integer recommendSort;

    /**
     * 是否是星级域名  0否  1是
     */
    private Integer isStar;

    /**
     * 星数
     */
    private Integer starCount;

    /**
     * 交易hash
     */
    private String transactionHash;

    /**
     * 支付状态   0-待支付  1-已支付  2-已取消
     */
    private Integer payStatus;

    /**
     * 封面url
     */
    private String coverUrl;

    /**
     * 是否是NFT封面  1-是  0-否
     */
    private Integer isNftCover;

    /**
     * 能量花费
     */
    private String energyUsed;

    /**
     * 付款链地址
     */
    private String payAddress;


}
