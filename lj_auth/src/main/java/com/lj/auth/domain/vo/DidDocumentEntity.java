package com.lj.auth.domain.vo;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Description
 * @date 2023/12/14 9:45
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DidDocumentEntity {
    private String id;
    private String version;
    private String created;
    private String update;
    private String status;
    private List<DocumentAuthentication> authentication;
    private List<DocumentService> service;
    private DocumentProof proof;
    
    
    
    @Data
    @NoArgsConstructor
    public static class DocumentAuthentication{
        String id;
        String type;
        String expirationDate;
        String publicKeyHex;
    }
    
    @Data
    @NoArgsConstructor
    static class DocumentService{
        String id;
        String type;
        String serviceEndpoint;
    }
    
    @Data
    @NoArgsConstructor
    static class DocumentProof{
        String type;
        String created;
        String creator;
        String signatureValue;
    }
}

