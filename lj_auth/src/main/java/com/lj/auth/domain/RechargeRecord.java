package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 用户充值、消耗记录表
 */
@Data
@Accessors(chain = true)
@TableName(value = "lj_auth_recharge_record")
public class RechargeRecord implements Serializable {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    @NotNull(message = "id不能为null")
    private Long id;

    /**
     * 终端用户uuid
     */
    @TableField(value = "account_uuid")
    @Size(max = 20, message = "终端用户uuid最大长度要小于 20")
    @NotBlank(message = "终端用户uuid不能为空")
    private String accountUuid;

    /**
     * 充值/消耗金额(单位元)
     */
    @TableField(value = "amount")
    @NotNull(message = "充值/消耗金额(单位元)不能为null")
    private BigDecimal amount;

    /**
     * 充值/消耗时余额（单位元）
     */
    @TableField(value = "balance")
    @NotNull(message = "充值/消耗时余额（单位元）不能为null")
    private BigDecimal balance;

    /**
     * 提现服务费（单位元）
     */
    @TableField(value = "withdraw_service_charge")
    private BigDecimal withdrawServiceCharge;

    /**
     * 实际提现金额(单位元)
     */
    @TableField(value = "real_amount")
    private BigDecimal realAmount;

    /**
     * 审核失败原因
     */
    @TableField(value = "reason")
    @Size(max = 200, message = "审核失败原因最大长度要小于 200")
    private String reason;

    /**
     * 交易流水号
     */
    @TableField(value = "tran_number")
    @Size(max = 60, message = "交易流水号最大长度要小于 60")
    @NotBlank(message = "交易流水号不能为空")
    private String tranNumber;

    /**
     * 银行卡id
     */
    @TableField(value = "card_id")
    private Integer cardId;

    /**
     * 类型 1:充值 2:提现 3：域名注册4：返佣 5：服务订购26:能量充值
     */
    @TableField(value = "`type`")
    @NotNull(message = "类型 1:充值 2:提现 3：域名注册4：返佣 5：服务订购26:能量充值不能为null")
    private Integer type;

    /**
     * 状态( 1=成功2=充值中\审核中3=失败4=用户取消)
     */
    @TableField(value = "`state`")
    private Integer state;

    /**
     * 开票状态：1=未开票2=已开票
     */
    @TableField(value = "ticket_state")
    private Integer ticketState;

    /**
     * 平台：1:H5 2:pc 3:app 4:微信小程序 5:抖音小程序
     */
    @TableField(value = "platform")
    private Integer platform;

    /**
     * 支付方式 1:微信 2:支付宝 3:银行卡 4:pc聚合支付5：数字人民币
     */
    @TableField(value = "pay_type")
    private Integer payType;

    /**
     * 充值转账备注码(充值用)
     */
    @TableField(value = "recharge_code")
    @Size(max = 50, message = "充值转账备注码(充值用)最大长度要小于 50")
    private String rechargeCode;

    /**
     * 回执姓名(充值用)
     */
    @TableField(value = "receipt_name")
    @Size(max = 255, message = "回执姓名(充值用)最大长度要小于 255")
    private String receiptName;

    /**
     * 回执手机号(充值用)
     */
    @TableField(value = "receipt_phone")
    @Size(max = 50, message = "回执手机号(充值用)最大长度要小于 50")
    private String receiptPhone;

    /**
     * 回执图片(充值用)
     */
    @TableField(value = "receipt_picture")
    @Size(max = 255, message = "回执图片(充值用)最大长度要小于 255")
    private String receiptPicture;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @NotNull(message = "创建时间不能为null")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    @NotNull(message = "更新时间不能为null")
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}