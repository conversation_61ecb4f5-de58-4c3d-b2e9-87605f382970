package com.lj.auth.domain.vo;

import com.lj.auth.domain.PlatformAccount;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: zjd
 * @Description:
 * @Date:2024/04/11 09:43
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UpChainDataVo {

    private Integer opbChainId;

    private String chainAccountAddress;

    private Integer nftCount;

    private String uuid;

    private PlatformAccount ylPlatformAccount;

    private Long contractId;

    private String contractAddress;

    private Long nftRecordId;
}
