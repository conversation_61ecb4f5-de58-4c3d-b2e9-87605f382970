package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

/**
 * DID签到活动
 */
@Data
@Accessors(chain = true)
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "did_check_in_activity")
public class DidCheckInActivity {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 活动名称
     */
    @TableField(value = "`name`")
    private String name;

    /**
     * 活动封面
     */
    @TableField(value = "cover")
    private String cover;

    /**
     * 活动地址
     */
    @TableField(value = "address")
    private String address;

    /**
     * 活动状态 1-未开始 2-进行中 3-已结束 4-已取消
     */
    @TableField(value = "`status`")
    private Integer status;

    /**
     * 页面样式
     */
    @TableField(value = "page_style")
    private Integer pageStyle;

    /**
     * 当前参与人数
     */
    @TableField(value = "current_join_num")
    private Integer currentJoinNum;

    /**
     * 开始时间
     */
    @TableField(value = "start_time")
    private Date startTime;

    /**
     * 结束时间
     */
    @TableField(value = "end_time")
    private Date endTime;
}