package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import com.lj.auth.annotation.PrefixPath;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 关于我们
 */
@Data
@Accessors(chain = true)
@TableName(value = "lj_auth_about")
public class LjAbout implements Serializable {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    @NotNull(message = "id不能为null")
    private Integer id;

    /**
     * logo
     */
    @TableField(value = "logo")
    @Size(max = 255,message = "logo最大长度要小于 255")
    @NotBlank(message = "logo不能为空")
    @PrefixPath
    private String logo;

    /**
     * 系统名称
     */
    @TableField(value = "system_name")
    @Size(max = 255,message = "系统名称最大长度要小于 255")
    @NotBlank(message = "系统名称不能为空")
    private String systemName;

    /**
     * 当前版本
     */
    @TableField(value = "current_version")
    @Size(max = 255,message = "当前版本最大长度要小于 255")
    @NotBlank(message = "当前版本不能为空")
    private String currentVersion;

    /**
     * 网站
     */
    @TableField(value = "website")
    @Size(max = 255,message = "网站最大长度要小于 255")
    @NotBlank(message = "网站不能为空")
    private String website;

    /**
     * 微信公众号
     */
    @TableField(value = "weChat_account")
    @Size(max = 255,message = "微信公众号最大长度要小于 255")
    @NotBlank(message = "微信公众号不能为空")
    private String wechatAccount;

    /**
     * 联系邮箱
     */
    @TableField(value = "email")
    @Size(max = 255,message = "联系邮箱最大长度要小于 255")
    @NotBlank(message = "联系邮箱不能为空")
    private String email;

    /**
     * icp备案号:文案
     */
    @TableField(value = "icp_text")
    @Size(max = 255,message = "icp备案号:文案最大长度要小于 255")
    private String icpText;

    /**
     * icp备案号:固定链接
     */
    @TableField(value = "icp_link")
    @Size(max = 255,message = "icp备案号:固定链接最大长度要小于 255")
    private String icpLink;

    /**
     * 版权信息
     */
    @TableField(value = "copyright")
    @Size(max = 255,message = "版权信息最大长度要小于 255")
    private String copyright;

    /**
     * 用户协议
     */
    @TableField(value = "user_agreement")
    private String userAgreement;

    /**
     * 隐私协议
     */
    @TableField(value = "privacy_agreement")
    private String privacyAgreement;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     *  经销商uuid
     */
    @TableField(value = "operate_uuid")
    @Size(max = 20,message = " 经销商uuid最大长度要小于 20")
    @NotBlank(message = " 经销商uuid不能为空")
    private String operateUuid;

    /**
     * 公网安备案号:icon
     */
    @TableField(value = "public_icon")
    @Size(max = 255,message = "公网安备案号:icon最大长度要小于 255")
    private String publicIcon;

    /**
     * 公网安备案号:文案
     */
    @TableField(value = "public_text")
    @Size(max = 255,message = "公网安备案号:文案最大长度要小于 255")
    private String publicText;

    /**
     * 公网安备案号:固定链接
     */
    @TableField(value = "public_link")
    @Size(max = 255,message = "公网安备案号:固定链接最大长度要小于 255")
    private String publicLink;

    /**
     * 浏览器ICON
     */
    @TableField(value = "browser_icon")
    @Size(max = 255,message = "浏览器ICON最大长度要小于 255")
    @NotBlank(message = "浏览器ICON不能为空")
    private String browserIcon;

    /**
     * 浏览器门户标题
     */
    @TableField(value = "browser_portal_title")
    @Size(max = 255,message = "浏览器门户标题最大长度要小于 255")
    @NotBlank(message = "浏览器门户标题不能为空")
    private String browserPortalTitle;

    /**
     * 浏览器运营标题
     */
    @TableField(value = "brower_operate_title")
    @Size(max = 255,message = "浏览器运营标题最大长度要小于 255")
    @NotBlank(message = "浏览器运营标题不能为空")
    private String browerOperateTitle;

    /**
     * 门户通用设置-注册协议
     */
    @TableField(value = "register_agreement")
    private String registerAgreement;

    /**
     * uuid前缀
     */
    @TableField(value = "uuid_prefix")
    @Size(max = 50,message = "uuid前缀最大长度要小于 50")
    private String uuidPrefix;

    /**
     * 经销商授权证书
     */
    @TableField(value = "authorization_certificate")
    @Size(max = 255,message = "经销商授权证书最大长度要小于 255")
    private String authorizationCertificate;

    /**
     * 企业名称
     */
    @TableField(value = "enterprise_name")
    @Size(max = 255,message = "企业名称最大长度要小于 255")
    private String enterpriseName;


    /**
     * 系统描述
     */
    private String systemDescribes;

    /**
     * web下载页链接
     */
    private String webDownloadUrl;

    /**
     * 支付协议
     */
    @TableField(exist = false)
    private String payAgreement;

    private static final long serialVersionUID = 1L;
}