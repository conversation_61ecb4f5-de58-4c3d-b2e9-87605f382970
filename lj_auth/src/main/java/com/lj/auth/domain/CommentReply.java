package com.lj.auth.domain;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 广场评论的回复
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("lj_square_comment_reply")
public class CommentReply implements Serializable {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 当前回复的用户uuid
     */
    private String accountUuid;

    /**
     * 当前回复的用户是否是楼主 0-否 1-是
     */
    private Integer landlordFlag;

    /**
     * 动态id
     */
    private Integer trendsId;

    /**
     * 评论id
     */
    private Integer commentId;

    private String content;

    /**
     * 上级回复id(第一级回复时，该字段为空null)
     */
    private Integer upReply;

    /**
     * 上级回复的用户uuid
     */
    private String upAccountUuid;

    /**
     * 上级回复的用户是否是楼主 0-否 1-是
     */
    private Integer upLandlordFlag;

    /**
     * 层级
     */
    private Integer level;

    /**
     * 删除标签 0-未删除 1-主动删除 2-举报核实后删除 3-因上级动态删除而删除  4-因上级评论删除而删除 5-因上级回复删除而删除
     */
    private Integer removeFlag;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;


}
