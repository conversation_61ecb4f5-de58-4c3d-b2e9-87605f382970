package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 探索应用收藏
 */
@Data
@Accessors(chain = true)
@TableName(value = "lj_auth_explore_app_collection")
public class ExploreAppCollection implements Serializable {
    @TableId(value = "id", type = IdType.AUTO)
    @NotNull(message = "不能为null")
    private Integer id;

    /**
     * 用户uuid
     */
    @TableField(value = "account_uuid")
    @Size(max = 100,message = "用户uuid最大长度要小于 100")
    @NotBlank(message = "用户uuid不能为空")
    private String accountUuid;

    /**
     * app应用id
     */
    @TableField(value = "app_id")
    @NotNull(message = "app应用id不能为null")
    private Integer appId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @NotNull(message = "创建时间不能为null")
    private Date createTime;

    private static final long serialVersionUID = 1L;
}