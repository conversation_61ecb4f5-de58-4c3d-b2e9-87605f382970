package com.lj.auth.domain.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: wxm
 * @description:
 * @date: 2024/11/28 13:49
 */
@Data
public class ActivityVo {
    private Long activityId;
    private String activityName;
    private String startTime;
    private String endTime;
    private Integer activitySwitch;
    private Integer discountType;
    private BigDecimal discountRate;
    private BigDecimal discountAmount;
    private String activityDesc;
    private String discountDesc;
    private Integer partakeCapitalPool;
    private BigDecimal capitalPoolRate;
    private String remark;
    private String discountRateStr;

}
