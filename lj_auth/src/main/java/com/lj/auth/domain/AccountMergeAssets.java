package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 账号合并资产记录
 */
@Data
@TableName(value = "account_merge_assets")
public class AccountMergeAssets {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 经销商uuid
     */
    @TableField(value = "f_operate_uuid")
    private String fOperateUuid;

    /**
     * 经销商uuid
     */
    @TableField(value = "t_operate_uuid")
    private String tOperateUuid;

    /**
     * A合并到B 这个是A
     */
    @TableField(value = "from_uuid")
    private String fromUuid;

    /**
     * A合并到B  这个是B
     */
    @TableField(value = "to_uuid")
    private String toUuid;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 合并前资产
     */
    @TableField(value = "f_balance")
    private BigDecimal fBalance;

    /**
     * 合并前资产
     */
    @TableField(value = "t_balance")
    private BigDecimal tBalance;

    /**
     * 1-资产已合并0-资产未合并
     */
    @TableField(value = "is_assets")
    private Integer isAssets;

    /**
     * 1-所有资产记录合并完毕0-所有资产记录未合并完毕
     */
    @TableField(value = "is_all")
    private Integer isAll;
}