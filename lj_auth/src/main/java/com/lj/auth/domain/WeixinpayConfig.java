package com.lj.auth.domain;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 域名微信支付配置
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("lj_auth_weixinpay_config")
public class WeixinpayConfig implements Serializable {

    private static final long serialVersionUID=1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 公司简称
     */
    private String type;

    /**
     * 经销商uuid
     */
    private String operaterUuid;

    /**
     * 商户id
     */
    private String mchId;

    /**
     * 微信私钥pem文件内容
     */
    private String apiclientKeyPem;

    /**
     * 商户证书序列号
     */
    private String mchSerialNo;

    /**
     * 商户api v3密钥
     */
    private String apiV3Key;

    /**
     * APPID
     */
    private String appid;

    /**
     * 获取openid_url
     */
    private String openidUrl;

    /**
     * 回调地址
     */
    private String notifyDomian;

    /**
     * openid获取secret
     */
    private String secret;


}
