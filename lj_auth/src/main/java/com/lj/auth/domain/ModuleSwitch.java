package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 模块功能开关
 */
@Data
@TableName(value = "lj_auth_module_switch")
public class ModuleSwitch implements Serializable {
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 功能id
     */
    @TableField(value = "type_id")
    private Integer typeId;

    /**
     * 配置项
     */
    @TableField(value = "`key`")
    private String key;

    /**
     * 配置内容
     */
    @TableField(value = "`value`")
    private Boolean value;

    /**
     * 配置项描述
     */
    @TableField(value = "describes")
    private String describes;

    /**
     * 渠道
     */
    @TableField(value = "channel")
    private String channel;

    /**
     * 版本
     */
    @TableField(value = "version")
    private String version;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}