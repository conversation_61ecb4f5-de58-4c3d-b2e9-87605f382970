package com.lj.auth.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * nft图片 ipfs信息
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "ym_ipfs_image")
public class IpfsImage implements Serializable {
    /**
     * 状态 1-未使用
     */
    public static Integer STATE_UNUSED = 1;
    /**
     * 状态 2-已使用
     */
    public static Integer STATE_USED = 2;
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 图片CID
     */
    @TableField(value = "cid")
    private String cid;

    /**
     * 状态 1-未使用 2-已使用
     */
    @TableField(value = "`state`")
    private Integer state;

    /**
     * 域名
     */
    @TableField(value = "`domain`")
    private String domain;

    /**
     * TOKENID
     */
    @TableField(value = "token_id")
    private Integer tokenId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 合约地址
     */
    @TableField(value = "contract_address")
    private String contractAddress;

    /**
     * 索引(文件名)-关联ym_ipfs_image_attribute的index
     */
    @TableField(value = "`index`")
    private Integer index;

    private static final long serialVersionUID = 1L;
}