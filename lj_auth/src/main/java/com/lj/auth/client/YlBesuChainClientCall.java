package com.lj.auth.client;


import com.dtflys.forest.annotation.BaseRequest;
import com.dtflys.forest.annotation.Body;
import com.dtflys.forest.annotation.Post;
import com.lj.auth.common.R;


/**
 * 描述：besu浏览器
 * <p>
 * <p>
 * 创建人: CFJ 创建时间: 2024/03/08
 */
@BaseRequest(baseURL = "#{ylBesuChain.url}", // 默认域名
    headers = {"serviceName:wallet" // 默认请求头
    })
public interface YlBesuChainClientCall {

    /**
     * 个人地址 查询交易记录
     *
     * @param page
     * @param pageSize
     * @param address
     * @param type
     * @return {@link R }
     * <AUTHOR>
     * @date 2024/03/08
     */
    @Post("/brower/wallet_chain_browser/getAddressAllOfPage")
    public R getAddressAllOfPage(@Body("page") Integer page, @Body("pageSize") Integer pageSize,
                                 @Body("address") String address, @Body("type") Integer type);

    @Post("/brower/wallet_chain_browser/detail")
    public R getHashDetail(@Body("hash") String hash);

}
