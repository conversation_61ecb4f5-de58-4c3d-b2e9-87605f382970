package com.lj.auth.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lj.auth.domain.NoticeAccount;

import java.util.List;
import java.util.Map;

import com.lj.auth.domain.SystemNotice;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @Description
 * @date 2025/3/20 14:52
 */
@Mapper
public interface NoticeAccountMapper extends BaseMapper<NoticeAccount> {
    int updateBatchSelective(List<NoticeAccount> list);
    
    int batchInsert(@Param("list") List<NoticeAccount> list);
    
    /**
     * 系统消息分页查询
     *
     * @param page        页
     * @param pageSize    页大小
     * @param accountUuid 用户UUID
     * @param source      息来源:0-系统消息 1-系统公告2-转账通知
     * @return {@link List }<{@link Map }<{@link String }, {@link String }>>
     * <AUTHOR>
     * @date 2024/03/29
     */
    List<Map<String, String>> getPage(@Param("page") int page, @Param("pageSize") Integer pageSize,
                                      @Param("accountUuid") String accountUuid, @Param("source") int source);
    
    /**
     * @param page
     * @param pageSize
     * @param accountUuid
     * @param source
     * @param edition
     * @param channel
     * @return {@link List }<{@link Map }<{@link String }, {@link String }>>
     * <AUTHOR>
     * @date 2024/11/11
     */
    List<Map<String, String>> getPage1(@Param("page") int page, @Param("pageSize") Integer pageSize,
                                       @Param("accountUuid") String accountUuid, @Param("source") int source,
                                       @Param("edition") String edition, @Param("channel") String channel);
    
    /**
     * @param page        页
     * @param pageSize    页大小
     * @param accountUuid 用户UUID
     * @param source      息来源:0-系统消息 1-系统公告2-转账通知
     * @return {@link List }<{@link Map }<{@link String }, {@link String }>>
     * <AUTHOR>
     * @date 2024/03/29
     */
    List<Map<String, String>> getTransPage(@Param("page") int page, @Param("pageSize") Integer pageSize,
                                           @Param("accountUuid") String accountUuid, @Param("source") int source);
    
    /**
     * 获取最新一条弹窗通知
     *
     * @param accountUuid 用户uuid
     * @return {@link Map }<{@link String }, {@link Object }>
     * <AUTHOR>
     * @date 2024/03/29
     */
    Map<String, Object> getLatestNotice(@Param("accountUuid") String accountUuid);
    
    /**
     * @param page
     * @param pageSize
     * @param uuid
     * @param systemNoticeList
     * @return {@link List }<{@link Map }<{@link String }, {@link String }>>
     * <AUTHOR>
     * @date 2025/03/19
     */
    List<Map<String, String>> getPage2(@Param("page") int page, @Param("pageSize") Integer pageSize, @Param("uuid") String uuid, @Param("systemNoticeList") List<SystemNotice> systemNoticeList);
}