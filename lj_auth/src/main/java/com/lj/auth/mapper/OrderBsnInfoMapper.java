package com.lj.auth.mapper;

import cn.dev33.satoken.exception.BackResultException;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lj.auth.domain.OrderBsnInfo;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * <p>
 * 订单详情-域名服务结果 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-12
 */
public interface OrderBsnInfoMapper extends BaseMapper<OrderBsnInfo> {

    /**
     * 根据账户UUID和域名查询
     * 
     * @param accountUUID
     * @param domain
     * @param domainServeType
     * @param status
     * @return
     */
    OrderBsnInfo queryByAccountUUIDAndDomain(@Param("accountUUID") String accountUUID,
        @Param("domain") String domain, @Param("domainServeType") Integer domainServeType,
        @Param("status") Integer status);

    /**
     * 查询域名信息 订单信息
     * 
     * @param orderId
     * @return {@link Map }<{@link String }, {@link Object }>
     * <AUTHOR>
     * @date 2024/06/05
     */
    Map<String, Object> selectDomainOrderInfo(@Param("orderId") Integer orderId);

    /**
     * 查询域名退款订单信息
     * 
     * @param orderId
     * @return {@link Map }<{@link String }, {@link Object }>
     * <AUTHOR>
     * @date 2024/06/07
     */
    Map<String, Object> selectDomainOrderInfoTuikuan(@Param("orderId") Integer orderId);

    /**
     * 获取域名信息
     *
     * @param domain -域名
     * @param accountUUID
     * @param domianSN
     * @return {@link Map }<{@link String }, {@link Object }>
     * <AUTHOR>
     * @date 2024/06/11
     */
    Map<String, Object> selectDomainInfo(@Param("domain") String domain,
        @Param("accountUUID") String accountUUID, @Param("domianSN") String domianSN);

    /**
     * 获取域名转让信息
     * 
     * @param domainServeSn
     * @param transferState
     * @return {@link Map }<{@link String }, {@link Object }>
     * <AUTHOR>
     * @date 2024/06/11
     */
    Map<String, Object> selectTransferDomain(@Param("domainServeSn") String domainServeSn,
        @Param("transferState") Integer transferState);

    /**
     * @param domain
     * @param accountUuid
     * @param transferUuid
     * @return {@link Map }<{@link String }, {@link Object }>
     * <AUTHOR>
     * @date 2024/06/11
     */
    Map<String, Object> selectDomainInfoMarket(@Param("domain") String domain,
        @Param("accountUuid") String accountUuid, @Param("transferUuid") String transferUuid);
}
