package com.lj.auth.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lj.auth.domain.ExploreAppRecently;
import com.lj.auth.domain.vo.ExploreAppRecentlyVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ExploreAppRecentlyMapper extends BaseMapper<ExploreAppRecently> {
    /**
     * 最近访问纪录统计
     * 
     * @param uuid 用户UUID
     * @return {@link Integer }
     * <AUTHOR>
     * @date 2024/04/01
     */
    Integer appRecentlyCount(@Param("uuid") String uuid);

    /**
     * 最近访问纪录分页查询
     * 
     * @param page 页
     * @param pageSize 页大小
     * @param uuid
     * @return {@link List }<{@link ExploreAppRecentlyVo }>
     * <AUTHOR>
     * @date 2024/04/01
     */
    List<ExploreAppRecentlyVo> getAppRecentlyPage(@Param("page") int page, @Param("pageSize") int pageSize,
        @Param("uuid") String uuid);
}