package com.lj.auth.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lj.auth.domain.CouponBatch;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: wxm
 * @description:
 */
@Mapper
public interface CouponBatchMapper extends BaseMapper<CouponBatch> {

    List<CouponBatch> couponBatchList(@Param("type") Integer type);

}
