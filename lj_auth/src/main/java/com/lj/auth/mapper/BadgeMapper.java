package com.lj.auth.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lj.auth.domain.Badge;
import com.lj.auth.domain.vo.BadgeListVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BadgeMapper extends BaseMapper<Badge> {
    /**
     * @param uuid
     * @return {@link List }<{@link BadgeListVo }>
     */
    List<BadgeListVo> selectUserBadge(@Param("uuid") String uuid);

    /**
     * @param uuid
     * @return {@link BadgeListVo }
     */
    BadgeListVo selectOneUserBadge(@Param("uuid") String uuid);
}