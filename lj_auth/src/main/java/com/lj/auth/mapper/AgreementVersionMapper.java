package com.lj.auth.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lj.auth.domain.AgreementVersion;
import com.lj.auth.domain.vo.AgreementVersionVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface AgreementVersionMapper extends BaseMapper<AgreementVersion> {
    /**
     * 获取最新的协议版本信息
     *
     * @param agreementType
     * @return
     */
    AgreementVersion queryLatestAgreement(@Param("agreementType") Integer agreementType);

    /**
     * 分页查询用户对应协议类型的已读记录
     *
     * @param agreementType
     * @return
     */
    Page<AgreementVersionVo> pageQueryByAgreementType(Page page, @Param("agreementType") Integer agreementType, @Param("searchKey") String searchKey);
}