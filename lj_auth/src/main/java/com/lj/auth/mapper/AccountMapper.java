package com.lj.auth.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lj.auth.domain.Account;
import com.lj.auth.domain.resp.InviteCodeAccountResp;
import com.lj.auth.domain.vo.AccountVo;
import org.apache.ibatis.annotations.Param;

public interface AccountMapper extends BaseMapper<Account> {
    /**
     * 判断手机号是否存在
     *
     * @param account
     * @return {@link Boolean }
     * <AUTHOR>
     * @date 2024/03/27
     */
    Boolean phoneNumberIsExists(@Param("account") String account);

    /**
     * 判断uuid是否存在
     *
     * @param uuid
     * @return {@link Boolean }
     * <AUTHOR>
     * @date 2024/04/08
     */
    Boolean UUIDIsExists(@Param("uuid") String uuid);

    /**
     * 查询指定经销商邀请码排序最大的一个
     *
     * @param operateUUID 经销商UUID
     * @return {@link String }
     * <AUTHOR>
     * @date 2024/04/08
     */
    String getInviteCodeMaxSort(@Param("operateUUID") String operateUUID);

    Account queryByHeadportraitNftId(Long NFTTokenId);

    boolean headportraitNftIdIsExists(Long NFTTokenId);

    boolean headportraitNftIdUnBind(Long NFTTokenId);

    Page<AccountVo> pageQueryParentUUID(Page page, @Param("parentUUID") String parentUUID);

    Account queryByUUID(@Param("accountUUID") String uuid);

    Account queryByDid(@Param("didSymbol") String didSymbol);

    Account queryByPhone(@Param("phoneNumber") String phoneNumber);

    /**
     * 分页查询没有协议已读记录的用户
     *
     * @param page
     * @return
     */
    Page<String> pageQueryNoAgreementRecords(Page page);

    /**
     * 统计 推广DID用户或者裂空者用户数量
     *
     * @param accountUuid
     * @param didFlag     did标识
     * @param skyFlag     裂空者标识
     * @return {@link Integer }
     */
    Integer selectDidCount(@Param("accountUuid") String accountUuid, @Param("didFlag") String didFlag,
                           @Param("skyFlag") String skyFlag);
    
    InviteCodeAccountResp getByInviteCode(@Param("inviteCode") String inviteCode);
}