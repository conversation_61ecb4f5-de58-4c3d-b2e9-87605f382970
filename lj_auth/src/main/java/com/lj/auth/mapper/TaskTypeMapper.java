package com.lj.auth.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lj.auth.domain.TaskType;
import com.lj.auth.domain.vo.TaskTypeVo;

import java.util.List;

/**
 * <p>
 * 任务类别 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-28
 */
public interface TaskTypeMapper extends BaseMapper<TaskType> {

    /**
     * 获取任务类型列表
     * 
     * @return {@link List }<{@link TaskTypeVo }>
     * <AUTHOR>
     * @date 2024/05/28
     */
    List<TaskTypeVo> getCenterTypeList();
}
