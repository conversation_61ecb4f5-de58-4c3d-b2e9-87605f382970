package com.lj.auth.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lj.auth.domain.NftGetRecordDomain;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface NftGetRecordDomainMapper extends BaseMapper<NftGetRecordDomain> {

    Integer countByRecordIdAndStatus(@Param("nfgRecordId") Long nfgRecordId,@Param("status") Integer status);

    List<NftGetRecordDomain>  queryByRecordIdAndStatus(@Param("nfgRecordId") Long nfgRecordId,@Param("status") Integer status);

}