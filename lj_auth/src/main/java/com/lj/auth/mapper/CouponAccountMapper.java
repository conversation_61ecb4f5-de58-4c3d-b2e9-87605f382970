package com.lj.auth.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lj.auth.domain.CouponAccount;
import com.lj.auth.domain.vo.CouponVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: wxm
 * @description:
 */
@Mapper
public interface CouponAccountMapper extends BaseMapper<CouponAccount> {

    Integer getReceivedNumberByBatchId(@Param("couponBatchId") Long couponBatchId, @Param("accountUuid") String accountUuid);

    Integer getReceivedNumberByCouponId(@Param("couponBatchId") Long couponBatchId, @Param("couponId") Long couponId, @Param("accountUuid") String accountUuid);

    Integer accountCouponListCount(@Param("accountUuid") String accountUuid, @Param("state") Integer state, @Param("historyType") Integer historyType);

    List<CouponVo> accountCouponList(@Param("accountUuid") String accountUuid, @Param("state") Integer state, @Param("historyType") Integer historyType, @Param("start") int start, @Param("pageSize") int pageSize);

    CouponVo getFirstCanUseCoupon(@Param("accountUuid") String accountUuid);

    CouponVo getCouponById(@Param("accountUuid") String accountUuid, @Param("couponAccountId") Long couponAccountId);

    List<CouponVo> couponList(@Param("accountUuid") String accountUuid, @Param("historyType") Integer historyType);

    /**
     * @param accountUuid
     * @param historyType
     * @param type
     * @return {@link List }<{@link CouponVo }>
     * <AUTHOR>
     * @date 2024/12/26
     */
    List<CouponVo> couponListOfType(@Param("accountUuid") String accountUuid,
        @Param("historyType") Integer historyType, @Param("type") Integer type);

}
