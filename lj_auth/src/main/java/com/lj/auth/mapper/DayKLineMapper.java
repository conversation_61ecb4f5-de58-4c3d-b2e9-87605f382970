package com.lj.auth.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lj.auth.domain.DayKLine;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface DayKLineMapper extends BaseMapper<DayKLine> {
    /**
     * @param page
     * @param pageSize
     * @return {@link List }<{@link Map }<{@link String }, {@link Object }>>
     * <AUTHOR>
     * @date 2024/07/19
     */
    List<Map<String, Object>> selectDayKLinePage(@Param("page") int page, @Param("pageSize") int pageSize);
}