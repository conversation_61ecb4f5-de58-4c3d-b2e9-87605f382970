package com.lj.auth.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lj.auth.domain.OutsideChannelApplyConfig;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @Description
* @date 2024/12/10 15:59
*/
@Mapper
public interface OutsideChannelApplyConfigMapper extends BaseMapper<OutsideChannelApplyConfig> {
    int updateBatchSelective(List<OutsideChannelApplyConfig> list);

    int batchInsert(@Param("list") List<OutsideChannelApplyConfig> list);
    
    OutsideChannelApplyConfig queryByAppId(@Param("appId") String appId);
}
