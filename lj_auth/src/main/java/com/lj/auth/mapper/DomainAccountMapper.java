package com.lj.auth.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lj.auth.domain.DomainAccount;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface DomainAccountMapper extends BaseMapper<DomainAccount> {
    int updateBatchSelective(List<DomainAccount> list);

    /**
     *可领取NFT 域名信息
     * @param uuid
     * @param count
     * @return
     */
    List<DomainAccount> selectGetNftList(@Param("uuid") String uuid, @Param("count") Integer count);



    /**
     * 根据状态查询NFT 的域名信息
     * @param uuid
     * @param count
     * @return
     */
    List<DomainAccount> queryNFTByStatus(@Param("uuid") String uuid,@Param("nftGetState") Integer nftGetState,
                                         @Param("count") Integer count);


    /**
     * 根据状态查询一个NFT 的域名信息
     * @param uuid
     * @return
     */
    DomainAccount queryOneByNFTByStatus(@Param("uuid") String uuid,@Param("nftGetState") Integer nftGetState);


    /**
     * 根据支付状态和领取状态查询
     * @param accountUUID
     * @param domain
     * @param payState
     * @param getState
     * @return
     */
    DomainAccount queryByPayStateAndGetState(@Param("accountUUID")String accountUUID,@Param("domain") String domain,
                                             @Param("payState") Integer payState,@Param("getState") Integer getState);


    /**
     * @param domain
     * @return {@link Map }<{@link String }, {@link Object }>
     * <AUTHOR>
     * @date 2024/06/12
     */
    Map<String, Object> selectRenewalInfo(@Param("domain") String domain, @Param("tranNumber") String tranNumber);
}
