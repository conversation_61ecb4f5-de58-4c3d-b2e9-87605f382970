package com.lj.auth.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lj.auth.domain.AgreementAccountRecord;
import com.lj.auth.domain.vo.AgreementAccountRecordVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface AgreementAccountRecordMapper extends BaseMapper<AgreementAccountRecord> {
    AgreementAccountRecord queryByVersionIdAndAccountUUID(@Param("agreementVersionId") Integer agreementVersionId, @Param("accountUUID") String accountUUID);

    /**
     * 查询用户最新的一条协议记录
     *
     * @param accountUUID
     * @return
     */
    AgreementAccountRecord queryLatestVersionByAccountUUID(@Param("accountUUID") String accountUUID);

    /**
     * 查询用户对应协议类型最新的一条已读记录
     *
     * @param accountUUID
     * @param agreementType
     * @return
     */
    AgreementAccountRecordVo queryLatestRecordVoByAccountUUIDAndType(@Param("accountUUID") String accountUUID, @Param("agreementType") Integer agreementType);

    /**
     * 分页查询用户对应协议类型的已读记录
     *
     * @param accountUUID
     * @param agreementType
     * @return
     */
    Page<AgreementAccountRecordVo> pageQueryByAccountAndAgreementType(Page page, @Param("accountUUID") String accountUUID, @Param("agreementType") Integer agreementType, @Param("agreementVersionId") Integer agreementVersionId);

}