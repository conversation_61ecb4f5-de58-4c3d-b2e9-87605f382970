package com.lj.auth.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lj.auth.domain.PlatformAccount;
import org.apache.ibatis.annotations.Param;

public interface PlatformAccountMapper extends BaseMapper<PlatformAccount> {


    /**
     * 根据地址和状态查询
     * @param address  平台链账户地址
     * @param type     地址类型
     * @param status   地址状态
     * @return
     */
    PlatformAccount queryByAddressAndStatus(@Param("address") String address, @Param("type") Integer type,@Param("status") Integer status);


    /**
     * 通过类型和状态查询平台地址信息
     * @param type      地址类型
     * @param status    地址状态
     * @return
     */
    PlatformAccount queryByTypeAndStatus( @Param("type") Integer type,@Param("status") Integer status);
}