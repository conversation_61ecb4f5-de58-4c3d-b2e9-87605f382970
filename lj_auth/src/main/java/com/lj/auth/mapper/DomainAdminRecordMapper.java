package com.lj.auth.mapper;

import com.lj.auth.domain.DomainAdminRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 域名管理记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-07
 */
public interface DomainAdminRecordMapper extends BaseMapper<DomainAdminRecord> {

    /**
     * @param page 页
     * @param pageSize 页码
     * @param uuid 用户uuid
     * @param domain 域名
     * @param type 1=设置所有者2=设置管理者3=设置解析记录
     * @return {@link List }<{@link DomainAdminRecord }>
     * <AUTHOR>
     * @date 2024/04/07
     */
    List<DomainAdminRecord> getPage(@Param("page") int page, @Param("pageSize") int pageSize,
        @Param("uuid") String uuid, @Param("domain") String domain, @Param("type") int type);
}
