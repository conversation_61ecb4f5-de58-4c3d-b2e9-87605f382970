package com.lj.auth.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lj.auth.domain.RechargeWithdrawRecord;

import java.util.List;

/**
 * <p>
 * 终端用户充提订单表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-11
 */
public interface RechargeWithdrawRecordMapper extends BaseMapper<RechargeWithdrawRecord> {

    /**
     * 获取充值 微信小程序、app 平台 支付方式是：微信 和支付宝 支付状态是待支付 时间超过24小时的充值订单
     * 
     * @return {@link List }<{@link RechargeWithdrawRecord }>
     * <AUTHOR>
     * @date 2024/04/27
     */
    List<RechargeWithdrawRecord> getOver24Hours();
}
