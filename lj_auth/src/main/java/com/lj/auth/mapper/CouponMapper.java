package com.lj.auth.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lj.auth.domain.Coupon;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: wxm
 * @description:
 */
@Mapper
public interface CouponMapper extends BaseMapper<Coupon> {

    Integer getCouponResidueNumber(@Param("couponBatchId") Long couponBatchId);

    List<Coupon> getCanReceiveCoupon(@Param("couponBatchId") Long couponBatchId);

    Integer reduceResidueNumber(@Param("couponId") Long couponId);

}
