package com.lj.auth.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lj.auth.domain.Contract;
import org.apache.ibatis.annotations.Param;

public interface ContractMapper extends BaseMapper<Contract> {


    /**
     * 根据链框架id和合约地址查询合约
     * @param opbChainId            链框架id
     * @param contractAddress       合约地址
     * @param contractType          合约类型  1-ERC721 2-域名注册管理器  3-灵戒域名注册器 4-灵戒公共解析器'
     * @param status                状态     0：禁用  1：启用
     * @param toUse                 用途     1：注册域名领NFT
     * @return
     */
    Contract queryByChainIDContractAddress(@Param("opbChainId") Integer opbChainId, @Param("contractAddress") String contractAddress,
                                           @Param("contractType") Integer contractType,@Param("status") Integer status,
                                           @Param("toUse") Integer toUse);

    /**
     * 根据链框架id和合约类型查询
     * @param opbChainId        链框架id
     * @param contractType      合约类型  1-ERC721 2-域名注册管理器  3-灵戒域名注册器 4-灵戒公共解析器'
     * @param status            状态     0：禁用  1：启用
     * @param toUse             用途     1：注册域名领NFT
     * @return
     */
    Contract queryByChainIDAndType(@Param("opbChainId") Integer opbChainId, @Param("contractType") Integer contractType,
                                   @Param("status") Integer status, @Param("toUse") Integer toUse);

    /**
     * 获取最大tokenId
     *
     * @return {@link Long }
     */
    Long selecMaxTokenId();

    /**
     * @param id
     * @return {@link Long }
     */
    Long selectAddressNum(@Param("id") Long id);

    /**
     * @param id
     * @return {@link Long }
     */
    Long selectTransferNum(@Param("id") Long id);
}