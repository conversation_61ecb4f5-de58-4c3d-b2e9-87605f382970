package com.lj.auth.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lj.auth.domain.NftGetRecord;
import com.lj.auth.domain.resp.NftGetRecordResp;
import com.lj.auth.domain.vo.NftGetRecordVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface NftGetRecordMapper extends BaseMapper<NftGetRecord> {
    Integer selectGetCount(@Param("uuid") String uuid);



    NftGetRecordResp countByAccountUUID(@Param("uuid") String uuid);


    List<NftGetRecordVo> pageQueryByAccountUUID(@Param("uuid") String uuid,  @Param("start") Integer start, @Param("limit") Integer limit);


    int totalPageQueryByAccountUUID(@Param("uuid") String uuid);
}