package com.lj.auth.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lj.auth.domain.GlobalConfig;
import org.apache.ibatis.annotations.Param;

public interface GlobalConfigMapper extends BaseMapper<GlobalConfig> {
    /**
     * 通过Key 获取全局配置
     *
     * @param key key
     * @return {@link String }
     * <AUTHOR>
     * @date 2024/03/28
     */
    String queryConfig(@Param("key") String key);

}