package com.lj.auth.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lj.auth.domain.AvatarFrame;
import com.lj.auth.domain.vo.AvatarFrameVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AvatarFrameMapper extends BaseMapper<AvatarFrame> {
    /**
     * 获取个人挂件列表
     *
     * @param uuid
     * @return {@link List }<{@link AvatarFrameVo }>
     */
    List<AvatarFrameVo> selectUserAvatarFrame(@Param("uuid") String uuid);
}