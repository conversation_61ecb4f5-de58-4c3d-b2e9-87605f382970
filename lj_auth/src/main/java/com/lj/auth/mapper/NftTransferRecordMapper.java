package com.lj.auth.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lj.auth.domain.NftTransferRecord;
import com.lj.auth.domain.vo.NftTransferRecordVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface    NftTransferRecordMapper extends BaseMapper<NftTransferRecord> {
    List<NftTransferRecord> getNftTransactions(@Param("opbChainId") Integer opbChainId,
                                               @Param("contractId") Integer contractId,
                                               @Param("tokenId") Integer tokenId,
                                               @Param("holderAddress") String holderAddress,
                                               @Param("type") Integer type);

    List<NftTransferRecord> getAllNftTransactions(@Param("opbChainId") Integer opbChainId,
                                                  @Param("chainAccountAddress") String chainAccountAddress,
                                                  @Param("type") Integer type,
                                                  @Param("time") String time);

    NftTransferRecord getNftTransactionById(@Param("opbChainId") Integer opbChainId,
                                            @Param("contractAddress") String contractAddress,
                                            @Param("tokenId") Integer tokenId,
                                            @Param("holderAddress") String holderAddress,
                                            @Param("status") Integer status);



    NftTransferRecord queryByHashAndTransactionType(@Param("opbChainId") Integer opbChainId, @Param("transactionHash") String transactionHash,
                                                    @Param("tokenId") Integer tokenId,@Param("transactionType") Integer transactionType,
                                                    @Param("isDelete") Boolean isDelete);

    NftTransferRecord queryById( @Param("id") Long id);

    /**
     * @param page
     * @param pageSize
     * @param address
     * @param tokenId
     * @return {@link Page }<{@link NftTransferRecordVo }>
     */
    List<NftTransferRecordVo> selectNftTrRecord(@Param("page") Integer page, @Param("pageSize") Integer pageSize, @Param("address") String address,
                                                @Param("tokenId") Integer tokenId);

    /**
     * @param address
     * @param tokenId
     * @return {@link Long }
     */
    Long selectNftTrRecordCount(@Param("address") String address, @Param("tokenId") Integer tokenId);
}