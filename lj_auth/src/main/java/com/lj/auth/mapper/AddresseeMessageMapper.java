package com.lj.auth.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lj.auth.domain.AddresseeMessage;
import org.apache.ibatis.annotations.Param;

public interface AddresseeMessageMapper extends BaseMapper<AddresseeMessage> {

    /**
     * 修改收件人信息
     *
     * @param id   id
     * @param name  姓名
     * @param phone 手机号
     * @param email  邮箱
     * @param address 地址
     */
    void updateAddressMessage(@Param("id") Long id, @Param("name") String name, @Param("phone") String phone,
                              @Param("email") String email, @Param("address") String address);
}