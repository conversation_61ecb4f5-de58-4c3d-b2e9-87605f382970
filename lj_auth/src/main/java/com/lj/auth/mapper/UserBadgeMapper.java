package com.lj.auth.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lj.auth.domain.UserBadge;
import com.lj.auth.domain.vo.BadgeListVo;
import com.lj.auth.domain.vo.BadgeVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface UserBadgeMapper extends BaseMapper<UserBadge> {

    /**
     * @param uuid
     * @return {@link List }<{@link BadgeVo }>
     */
    List<BadgeVo> getUserBadge(@Param("uuid") String uuid);
}