package com.lj.auth.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lj.auth.domain.EnergyRechargeFlow;
import com.lj.auth.domain.vo.ChainAssetsVO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface EnergyRechargeFlowMapper extends BaseMapper<EnergyRechargeFlow> {
    /**
     * 查询数量统计
     *
     * @param uuid 用户uuid
     * @param type 交易类型：1=充值2=消耗
     * @param date 日期
     * @return {@link Integer }
     * <AUTHOR>
     * @date 2024/04/02
     */
    Integer queryEnergyCount(@Param("uuid") String uuid, @Param("type") Integer type,
        @Param("date") Date date);

    /**
     * 分页查询
     *
     * @param page 页
     * @param pageSize 页大小
     * @param accountUuid 用户uuid
     * @param type 交易类型：1=充值2=消耗
     * @param date 日期
     * @return {@link List }<{@link EnergyRechargeFlow }>
     * <AUTHOR>
     * @date 2024/04/02
     */
    List<EnergyRechargeFlow> getPage(@Param("page") int page, @Param("pageSize") int pageSize,
        @Param("accountUuid") String accountUuid, @Param("type") Integer type, @Param("date") Date date);
}