package com.lj.auth.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lj.auth.domain.AccountLogout;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 账户表-注销账号 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-14
 */
public interface AccountLogoutMapper extends BaseMapper<AccountLogout> {

    /**
     * 获取经销商账号UUID
     * @param operateUuid
     * @return {@link String }
     * <AUTHOR>
     * @date 2024/06/25
     */
    String selectOperateAccountUUID(@Param("operateUuid") String operateUuid);
}
