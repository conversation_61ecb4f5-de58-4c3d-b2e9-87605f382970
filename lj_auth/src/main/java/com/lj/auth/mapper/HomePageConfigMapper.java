package com.lj.auth.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lj.auth.domain.HomePageConfig;
import com.lj.auth.domain.vo.HomePageConfigVo;
import com.lj.auth.domain.vo.HomePageVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 首页
 */
public interface HomePageConfigMapper extends BaseMapper<HomePageConfig> {

    List<HomePageConfigVo> getHomePageConfigList(@Param("channel")String channel, @Param("area")String area);


    List<HomePageConfigVo> getHomePageConfigConditionSceneType(@Param("channel")String channel, @Param("area")String area,@Param("sceneTypeList") List<Integer> sceneTypeList);


}
