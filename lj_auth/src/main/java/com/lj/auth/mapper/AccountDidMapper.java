package com.lj.auth.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lj.auth.domain.AccountDid;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @date 2025/3/19 10:34
 */
@Mapper
public interface AccountDidMapper extends BaseMapper<AccountDid> {
    int updateBatchSelective(List<AccountDid> list);
    
    int batchInsert(@Param("list") List<AccountDid> list);
    
    AccountDid queryByAccountUuid(@Param("accountUuid") String accountUuid);
    
    List<AccountDid> queryDidInfoLaskList();
    
    List<AccountDid> queryDidInfoLaskListWithPage(@Param("pageNum") int pageNum, @Param("pageSize") int pageSize);
    
    long queryDidInfoLaskListWithPageCount();
    
}