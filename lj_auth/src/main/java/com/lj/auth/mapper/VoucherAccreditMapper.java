package com.lj.auth.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lj.auth.domain.VoucherAccredit;
import com.lj.auth.domain.vo.VoucherAccreditVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @date 2024/4/26 15:58
 */
@Mapper
public interface VoucherAccreditMapper extends BaseMapper<VoucherAccredit> {

    Integer queryAccreditCount(@Param("uuid") String uuid, @Param("type") Integer type);

    List<VoucherAccreditVo> queryAccreditPage(@Param("page") int page, @Param("pageSize") int pageSize,
        @Param("uuid") String uuid, @Param("type") Integer type);
}