package com.lj.auth.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lj.auth.domain.Version;
import org.apache.ibatis.annotations.Param;

public interface VersionMapper extends BaseMapper<Version> {
    /**
     * @param systemType -系统类型
     * @param platform
     * @return {@link Version }
     * <AUTHOR>
     * @date 2024/10/17
     */
    Version getLatestVersion(@Param("systemType") Integer systemType, @Param("platform") String platform);
}