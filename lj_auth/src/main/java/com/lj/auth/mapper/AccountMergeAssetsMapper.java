package com.lj.auth.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lj.auth.domain.AccountMergeAssets;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

public interface AccountMergeAssetsMapper extends BaseMapper<AccountMergeAssets> {
    @Update("UPDATE ${tableName} " +
            "SET account_uuid = #{newAccountUuid} " +
            "WHERE account_uuid = #{oldAccountUuid}")
    int updateAccountUuid(@Param("tableName") String tableName,
                          @Param("newAccountUuid") String newAccountUuid,
                          @Param("oldAccountUuid") String oldAccountUuid);

    @Update("UPDATE ${tableName} " +
            "SET account_uuid = #{newAccountUuid}" +
            "WHERE account_uuid = #{oldAccountUuid}")
    int updateAccountUuidAndOperateUuid(
            @Param("tableName") String tableName,
            @Param("newAccountUuid") String newAccountUuid,
            @Param("oldAccountUuid") String oldAccountUuid);



    @Update("UPDATE ${tableName} " +
            "SET operate_uuid = #{newOperateUuid} " +
            "WHERE account_uuid = #{oldAccountUuid}")
    int updateAccountUuidOperateUuid(
            @Param("tableName") String tableName,
            @Param("newOperateUuid") String newOperateUuid,
            @Param("oldAccountUuid") String oldAccountUuid);
}