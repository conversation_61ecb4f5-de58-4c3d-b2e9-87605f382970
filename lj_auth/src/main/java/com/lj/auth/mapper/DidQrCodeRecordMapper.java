package com.lj.auth.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lj.auth.domain.DidQrCodeRecord;
import com.lj.auth.domain.vo.QrVo;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

public interface DidQrCodeRecordMapper extends BaseMapper<DidQrCodeRecord> {
    /**
     * 通过did获取用户信息
     * 
     * @param didsymbol
     * @return {@link Map }<{@link String }, {@link Object }>
     * <AUTHOR>
     * @date 2024/07/18
     */
    QrVo selectDIDUserInfo(@Param("didsymbol") Object didsymbol);
}