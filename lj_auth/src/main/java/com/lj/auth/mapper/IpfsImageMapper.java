package com.lj.auth.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lj.auth.domain.IpfsImage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface IpfsImageMapper extends BaseMapper<IpfsImage> {
    int updateBatchSelective(List<IpfsImage> list);

    /**
     * 根据合约地址和tokenIds查询
     *
     * @param contractAddress
     * @param tokenIds
     * @return
     */
    int countByContractAndTokenIds(@Param("contractAddress") String contractAddress, @Param("tokenIds") List<Integer> tokenIds);

    /**
     * 根据合约地址和tokenId查询
     *
     * @param contractAddress
     * @param tokenId
     * @return
     */
    Boolean countByContractAndTokenId(@Param("contractAddress") String contractAddress, @Param("tokenId") Integer tokenId);

    /**
     * 根据合约地址和tokenIds查询
     *
     * @param contractAddress
     * @param tokenIds
     * @return
     */
    List<IpfsImage> queryByContractAndTokenIds(@Param("contractAddress") String contractAddress, @Param("tokenIds") List<Integer> tokenIds);

    /**
     * 根据域名查询ipfs信息
     *
     * @param domains
     * @return
     */
    List<IpfsImage> queryByDomains(@Param("domains") List<String> domains);

    /**
     * 根据域名和合约地址查询
     *
     * @param domains
     * @return
     */
    List<IpfsImage> queryByDomainsAndContract(@Param("domains") List<String> domains, @Param("contractAddress") String contractAddress, @Param("state") Integer state);

    /**
     * 根据域名和合约地址查询
     *
     * @param domain          域名
     * @param contractAddress 合约地址
     * @param tokenId         tokenId
     * @param state           状态
     * @return
     */
    IpfsImage queryByDomainContract(@Param("domain") String domain, @Param("contractAddress") String contractAddress,
                                    @Param("tokenId") Integer tokenId, @Param("state") Integer state);

    /**
     * 查询指定合约IPFS最大的一个
     *
     * @param contractAddress
     * @return
     */
    int getMaxTokenId(@Param("contractAddress") String contractAddress, @Param("state") Integer state);

    List<IpfsImage> queryByStateAndContract(@Param("contractAddress") String contractAddress, @Param("state") Integer state, @Param("limit") Integer limit);

    IpfsImage queryOneByStateAndContract(@Param("contractAddress") String contractAddress, @Param("state") Integer state);
}