package com.lj.auth.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lj.auth.domain.InvoiceAccount;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface InvoiceAccountMapper extends BaseMapper<InvoiceAccount> {

    /**
     * 个人开票统计
     *
     * @param accountUUID 用户uuid
     * @param ticketType 开票类型 1-增值税普通发票 2-增值税专用发票
     * @param examineState 审核状态 1-待审核 2-审核通过 3-审核驳回
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param ticketUuid 申请单号
     * @return {@link Integer}
     */
    Integer invoiceAccountCount(@Param("accountUUID") String accountUUID,
        @Param("ticketType") Integer ticketType, @Param("examineState") Integer examineState,
        @Param("startTime") Date startTime, @Param("endTime") Date endTime,
        @Param("ticketUuid") String ticketUuid);

    /**
     * 个人开票分页查询
     *
     * @param page 页
     * @param pageSize 页大小
     * @param accountUUID 用户uuid
     * @param ticketType 开票类型 1-增值税普通发票 2-增值税专用发票
     * @param examineState 审核状态 1-待审核 2-审核通过 3-审核驳回
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param ticketUuid 申请单号
     * @return {@link List }<{@link InvoiceAccount }>
     * <AUTHOR>
     * @date 2024/03/28
     */
    List<InvoiceAccount> getInvoiceAccountPage(@Param("page") int page, @Param("pageSize") Integer pageSize,
        @Param("accountUUID") String accountUUID, @Param("ticketType") Integer ticketType,
        @Param("examineState") Integer examineState, @Param("startTime") Date startTime,
        @Param("endTime") Date endTime, @Param("ticketUuid") String ticketUuid);
}