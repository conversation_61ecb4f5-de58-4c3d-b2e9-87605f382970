package com.lj.auth.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lj.auth.domain.Sp24hours;
import com.lj.auth.domain.vo.Sp24hoursVo;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 源力24小时趋势 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-04
 */
public interface Sp24hoursMapper extends BaseMapper<Sp24hours> {

    /**
     * sp单价趋势
     * 
     * @param strip
     * @return {@link List }<{@link Sp24hoursVo }>
     * <AUTHOR>
     * @date 2024/06/05
     */
    List<Sp24hoursVo> select30List(@Param("strip") int strip);

    /**
     * 获取当天 的最高价和最低价格
     * 
     * @return {@link Map }<{@link String }, {@link BigDecimal }>
     * <AUTHOR>
     * @date 2024/07/19
     */
    Map<String, BigDecimal> selectMaxAndLow();

    /**
     * 获取 本周 最高价格和最低价格
     * 
     * @return {@link Map }<{@link String }, {@link BigDecimal }>
     * <AUTHOR>
     * @date 2024/07/22
     */
    Map<String, BigDecimal> selectWeekMaxAndLow();

    /**
     * 获取 本月 最高和最低价格
     * 
     * @return {@link Map }<{@link String }, {@link BigDecimal }>
     * <AUTHOR>
     * @date 2024/07/29
     */
    Map<String, BigDecimal> selectMonthMaxAndLow();
}
