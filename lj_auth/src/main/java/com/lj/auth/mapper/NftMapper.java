package com.lj.auth.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lj.auth.domain.Nft;
import com.lj.auth.domain.vo.NftHolderVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface NftMapper extends BaseMapper<Nft> {
    /**
     * 分页查询链框架id和链账户地址查询NFT
     * @param opbChainId
     * @param nftType
     * @param chainAccount
     * @param status
     * @param isApprove
     * @param nftName
     * @param orders
     * @param start
     * @param limit
     * @return
     */
    List<Nft> pageQueryByAddressAndStatus(@Param("opbChainId") Integer opbChainId, @Param("nftType") Integer nftType,
                                          @Param("chainAccount") String chainAccount, @Param("status") Integer status,
                                          @Param("isApprove") Boolean isApprove,
                                          @Param("nftName") String nftName, @Param("orders") List<String> orders,
                                          @Param("start") Integer start, @Param("limit") Integer limit);

    /**
     * 分页查询链框架id和链账户地址查询NFT总数
     *
     * @param opbChainId
     * @param nftType
     * @param chainAccount
     * @param isApprove
     * @param status
     * @return
     */
    int totalPageQueryByAddressAndStatus(@Param("opbChainId") Integer opbChainId, @Param("nftType") Integer nftType,
                                         @Param("chainAccount") String chainAccount, @Param("status") Integer status,
                                         @Param("isApprove") Boolean isApprove,@Param("nftName") String nftName);

    /**
     * 查询指定合约TokenId最大的一个
     *
     * @param contractAddress
     * @return
     */
    int getMaxTokenId(@Param("contractAddress") String contractAddress, @Param("status") Integer status);

    /**
     * 通过合约地址和tokenId查询NFT
     *
     * @param contractAddress
     * @param tokenId
     * @param status
     * @return
     */
    Nft queryByTokenId(@Param("contractAddress") String contractAddress, @Param("tokenId") Integer tokenId, @Param("status") Integer status, @Param("isDelete") Boolean isDelete);

    /**
     * 分页查询NFT详情的NFT列表
     *
     * @param opbChainId
     * @param contractAddress
     * @param nftType
     * @param status
     * @param orders
     * @param start
     * @param limit
     * @return
     */
    List<Nft> pageQueryNftDetailsNFTList(@Param("opbChainId") Integer opbChainId,
                                         @Param("contractAddress") String contractAddress,
                                         @Param("nftType") Integer nftType,
                                         @Param("status") Integer status,
                                         @Param("nftName") String nftName,
                                         @Param("orders") List<String> orders,
                                         @Param("start") Integer start, @Param("limit") Integer limit);

    /**
     * 分页查询NFT详情的NFT列表总数
     *
     * @param opbChainId
     * @param contractAddress
     * @param nftType
     * @param status
     * @return
     */
    int totalPageQueryNftDetailsNFTList(@Param("opbChainId") Integer opbChainId,
                                        @Param("contractAddress") String contractAddress,
                                        @Param("nftType") Integer nftType,
                                        @Param("status") Integer status,
                                        @Param("nftName") String nftName);

    Nft queryOneByContractAndHolder(@Param("contractAddress") String contractAddress, @Param("holder") String holder, @Param("status") Integer status, @Param("isDelete") Boolean isDelete);

    Nft queryById(@Param("id") Long id);

    /**
     * @param objectPage
     * @return {@link Page }<{@link NftHolderVo }>
     */
    Page<NftHolderVo> selectNftHolderVoPage(Page<Object> objectPage);
}