package com.lj.auth.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lj.auth.domain.RechargeRecord;
import com.lj.auth.domain.vo.RechargeFlowVo;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

public interface RechargeRecordMapper extends BaseMapper<RechargeRecord> {

    /**
     * 开票金额校验
     *
     * @param ids 订单id集合
     * @param accountUuid 用户uuid
     * @return {@link BigDecimal }
     * <AUTHOR>
     * @date 2024/03/28
     */
    BigDecimal ticketAmount(@Param("ids") List<String> ids, @Param("accountUuid") String accountUuid);

    /**
     * 修改开票状态
     *
     * @param ids 订单id集合
     * @param accountUuid 用户uuid
     * @return int
     * <AUTHOR>
     * @date 2024/03/28
     */
    int updateTicketState(@Param("ids") List<String> ids, @Param("accountUuid") String accountUuid);

    /**
     * 获取开票详情
     *
     * @param bsnOrderIds ids集合
     * @param accountUUID 用户 uuid
     * @return {@link List}<{@link Map}<{@link String}, {@link String}>>
     */
    List<Map<String, String>> getInvoicInfoByIds(@Param("bsnOrderIds") List<String> bsnOrderIds,
        @Param("accountUUID") String accountUUID);


    /**
     * 资金明细列表
     * 
     * @param page
     * @param accountUuid
     * @param bigType
     * @param date
     * @return {@link Page }<{@link RechargeFlowVo }>
     * <AUTHOR>
     * @date 2024/11/27
     */
    Page<RechargeFlowVo> getFundListPage(@Param("page") Page page, @Param("accountUuid") String accountUuid,
        @Param("bigType") Integer bigType, @Param("date") Date date);
}