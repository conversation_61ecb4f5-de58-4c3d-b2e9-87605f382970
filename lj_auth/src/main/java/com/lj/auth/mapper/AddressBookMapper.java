package com.lj.auth.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lj.auth.domain.AddressBook;
import com.lj.auth.domain.vo.AddressBookVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AddressBookMapper extends BaseMapper<AddressBook> {
    /**
     * 分页查询
     *
     * @param page 页
     * @param pageSize 页大小
     * @param accountUUID 用户UUID
     * @param chainId 链Id
     * @return {@link List }<{@link AddressBookVo }>
     * <AUTHOR>
     * @date 2024/03/29
     */
    List<AddressBookVo> getPage(@Param("page") int page, @Param("pageSize") int pageSize,
        @Param("accountUUID") String accountUUID, @Param("chainId") Integer chainId);
}