package com.lj.auth.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lj.auth.domain.AccountLogoutConditionRecord;

import java.util.List;

/**
 * <p>
 * 账号注销条件记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
public interface AccountLogoutConditionRecordMapper extends BaseMapper<AccountLogoutConditionRecord> {

    /**
     * 查询出完成注销条件的用户
     * 
     * @return {@link List }<{@link String }>
     * <AUTHOR>
     * @date 2024/06/21
     */
    List<String> selectLogOutUser();
}
