package com.lj.auth.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lj.auth.domain.AccountLogoutCondition;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 账号注销条件 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-19
 */
public interface AccountLogoutConditionMapper extends BaseMapper<AccountLogoutCondition> {

    /**
     * 个条件完成状态列表
     * 
     * @param uuid 用户uuid
     * @return {@link List }<{@link AccountLogoutCondition }>
     * <AUTHOR>
     * @date 2024/06/19
     */
    List<AccountLogoutCondition> seleLogoutConditionRecords(@Param("uuid") String uuid);
}
