package com.lj.auth.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lj.auth.domain.WeekKLine;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface WeekKLineMapper extends BaseMapper<WeekKLine> {
    /**
     * @param page
     * @param pageSize
     * @return {@link List }<{@link Map }<{@link String }, {@link Object }>>
     * <AUTHOR>
     * @date 2024/08/01
     */
    List<Map<String, Object>> selectWeekKLinePage(@Param("page") int page, @Param("pageSize") int pageSize);
}