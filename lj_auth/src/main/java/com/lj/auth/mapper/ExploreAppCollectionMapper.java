package com.lj.auth.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lj.auth.domain.ExploreAppCollection;
import com.lj.auth.domain.vo.ExploreAppVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ExploreAppCollectionMapper extends BaseMapper<ExploreAppCollection> {
    /**
     * 获取收藏统计
     * 
     * @param uuid 用户UUID
     * @return {@link Integer }
     * <AUTHOR>
     * @date 2024/04/01
     */
    Integer appCollectionCount(@Param("uuid") String uuid);

    /**
     * 获取收藏分页查询
     * 
     * @param page 页
     * @param pageSize 页大小
     * @param uuid 用户UUID
     * @return {@link List }<{@link ExploreAppVo }>
     * <AUTHOR>
     * @date 2024/04/01
     */
    List<ExploreAppVo> getAppCollectionPage(@Param("page") int page, @Param("pageSize") int pageSize,
        @Param("uuid") String uuid);
}