package com.lj.auth.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lj.auth.domain.CommunityActivity;
import com.lj.auth.domain.vo.ActivityVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: wxm
 * @description:
 */
@Mapper
public interface CommunityActivityMapper extends BaseMapper<CommunityActivity> {

    List<ActivityVo> activityList(@Param("type")Integer type);

}
