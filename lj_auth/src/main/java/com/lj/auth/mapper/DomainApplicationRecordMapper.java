package com.lj.auth.mapper;

import com.lj.auth.domain.DomainApplicationRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lj.auth.domain.vo.ApplicationDetailVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 域名应用记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-07
 */
public interface DomainApplicationRecordMapper extends BaseMapper<DomainApplicationRecord> {

    /**
     * 数量统计
     * 
     * @param accountUUID 用户uuid
     * @return {@link Integer }
     * <AUTHOR>
     * @date 2024/04/07
     */
    Integer groupByDomainCount(@Param("accountUUID") String accountUUID);

    /**
     * 分页查询
     *
     * @param accountUUID 用户uuid
     * @param page 页
     * @param pageSize 页码
     * @return {@link List }<{@link Map }<{@link String }, {@link Object }>>
     * <AUTHOR>
     * @date 2024/04/07
     */
    List<Map<String, Object>> groupByDomain(@Param("accountUUID") String accountUUID, @Param("page") int page,
        @Param("pageSize") Integer pageSize);

    /**
     * 获取域名应用列表
     * 
     * @param accountUUID
     * @return {@link List }<{@link String }>
     * <AUTHOR>
     * @date 2024/04/07
     */
    List<String> getApplicationList(@Param("accountUUID") String accountUUID);

    /**
     * 域名应用详情
     * 
     * @param domain 域名
     * @param accountUUID 用户uuid
     * @return {@link List }<{@link Map }<{@link String }, {@link Object }>>
     * <AUTHOR>
     * @date 2024/04/07
     */
    List<ApplicationDetailVo> getApplicationDetail(@Param("domain") String domain, @Param("accountUUID") String accountUUID);
}
