package com.lj.auth.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lj.auth.domain.AccountTagSelected;

import java.util.List;

import org.apache.ibatis.annotations.Param;

public interface AccountTagSelectedMapper extends BaseMapper<AccountTagSelected> {
    int batchInsert(@Param("list") List<AccountTagSelected> list);

    /**
     * @param accountUUID
     * @return {@link List }<{@link AccountTagSelected }>
     */
    List<AccountTagSelected> selectUserTagList(@Param("accountUUID") String accountUUID);
}