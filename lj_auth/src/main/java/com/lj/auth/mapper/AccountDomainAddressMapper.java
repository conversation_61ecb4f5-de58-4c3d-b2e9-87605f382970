package com.lj.auth.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lj.auth.domain.AccountDomainAddress;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AccountDomainAddressMapper extends BaseMapper<AccountDomainAddress> {
    /**
     * 查询域名地址映射列表
     * 
     * @param domain 域名
     * @param accountUuid 用户uuid
     * @return {@link List }<{@link AccountDomainAddress }>
     * <AUTHOR>
     * @date 2024/04/07
     */
    List<AccountDomainAddress> getDomainMappingList(@Param("domain") String domain,
        @Param("accountUuid") String accountUuid);
}