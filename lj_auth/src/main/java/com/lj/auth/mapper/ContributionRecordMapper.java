package com.lj.auth.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lj.auth.domain.ContributionRecord;
import com.lj.auth.domain.vo.ContributionRecordVo;
import com.lj.auth.domain.vo.MyContributionRecordVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ContributionRecordMapper extends BaseMapper<ContributionRecord> {
    /**
     * 获取个人贡献记录
     *
     * @param uuid
     * @param page
     * @param pageSize
     * @return {@link List }<{@link ContributionRecordVo }>
     * <AUTHOR>
     * @date 2024/07/04
     */
    List<ContributionRecordVo> selectMyContributionLRecordList(@Param("uuid") String uuid,
                                                               @Param("page") int page, @Param("pageSize") int pageSize);

    /**
     * @return {@link List }<{@link MyContributionRecordVo }>
     * <AUTHOR>
     * @date 2024/07/08
     */
    List<MyContributionRecordVo> selectContributionRecordList();
}