package com.lj.auth.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lj.auth.domain.Contribution;
import com.lj.auth.domain.vo.ContributionVo;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

public interface ContributionMapper extends BaseMapper<Contribution> {
    /**
     * 获取排行
     * 
     * @param start 页
     * @param pageSize 页码
     * @return {@link List }<{@link Contribution }>
     * <AUTHOR>
     * @date 2024/07/03
     */
    List<Contribution> selectRankList(@Param("page") int page, @Param("pageSize") int pageSize);

    /**
     * 我的贡献
     *
     * @param uuid
     * @return {@link ContributionVo }
     * <AUTHOR>
     * @date 2024/07/04
     */
    ContributionVo selectMyRankList(@Param("uuid") String uuid);

    /**
     * 没有贡献个人信息排行
     * 
     * @param uuid
     * @return {@link ContributionVo }
     * <AUTHOR>
     * @date 2024/07/09
     */
    ContributionVo selectUserInfoList(@Param("uuid") String uuid);

    /**
     * 贡献值求和
     * 
     * @return {@link BigDecimal }
     * <AUTHOR>
     * @date 2024/07/10
     */
    BigDecimal sumAmount();
}