package com.lj.auth.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lj.auth.domain.DouDianCoupon;
import com.lj.auth.domain.vo.DouDianCouponVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @Description 
* @date 2025/8/13 17:11
*/
@Mapper
public interface DouDianCouponMapper extends BaseMapper<DouDianCoupon> {
    int updateBatchSelective(List<DouDianCoupon> list);

    int batchInsert(@Param("list") List<DouDianCoupon> list);
    
    List<DouDianCouponVo> queryByCouponNumberList(@Param("couponNumberList") List<String> couponNumberList);
}