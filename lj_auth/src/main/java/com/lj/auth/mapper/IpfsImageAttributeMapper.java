package com.lj.auth.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lj.auth.domain.IpfsImageAttribute;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @date 2024/4/27 15:48
 */
@Mapper
public interface IpfsImageAttributeMapper extends BaseMapper<IpfsImageAttribute> {


    List<IpfsImageAttribute> queryByStateAndIndexs(@Param("state") Integer state, @Param("indexs") List<Integer> indexs);


    IpfsImageAttribute queryByStateAndIndex(@Param("state") Integer state, @Param("index") Integer index);
}