package com.lj.auth.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lj.auth.domain.SmsConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface SmsConfigMapper extends BaseMapper<SmsConfig> {
    /**
     * 通过经销商UUID和短信类型查询短信模板配置信息
     *
     * @param operateUUID
     * @param type
     * @return {@link SmsConfig}
     */
    SmsConfig queryByOperateUUIDAndType(@Param("operateUUID") String operateUUID, @Param("type") Integer type, @Param("smsServiceProvider") String smsServiceProvider);
}