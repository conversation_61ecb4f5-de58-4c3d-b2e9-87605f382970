package com.lj.auth.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lj.auth.domain.SignInHist;
import com.lj.auth.domain.vo.ChainAssetsVO;

import java.util.List;

public interface SignInHistMapper extends BaseMapper<SignInHist> {
    /**
     * 统计个人充值和签到获得总gas
     * 
     * @return {@link List }<{@link ChainAssetsVO }>
     * <AUTHOR>
     * @date 2024/05/08
     */
    List<ChainAssetsVO> getChainAsses();
}