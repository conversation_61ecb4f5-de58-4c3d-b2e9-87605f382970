package com.lj.auth.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lj.auth.domain.TaskCenterUser;
import com.lj.auth.domain.vo.SpRecordVo;
import com.lj.auth.domain.vo.TaskUserVo;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 任务中心个人完成情况 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-28
 */
public interface TaskCenterUserMapper extends BaseMapper<TaskCenterUser> {

    /**
     * 邀请用户数量
     * 
     * @param uuid -用户uuid
     * @param taskCenterIds -任务ids
     * @return {@link Integer }
     * <AUTHOR>
     * @date 2024/05/29
     */
    Integer queryTaskUserCount(@Param("uuid") String uuid,
        @Param("taskCenterIds") List<Integer> taskCenterIds);

    /**
     * @param page -开始页
     * @param pageSize-页码
     * @param uuid-用户uuid
     * @param taskCenterIds-任务ids
     * @return {@link List }<{@link TaskUserVo }>
     * <AUTHOR>
     * @date 2024/05/29
     */
    List<TaskUserVo> getTaskUserPage(@Param("page") int page, @Param("pageSize") Integer pageSize,
        @Param("uuid") String uuid, @Param("taskCenterIds") List<Integer> taskCenterIds);

    /**
     * 统计获得SP
     *
     * @param taskCenterIds -任务ids
     * @param isReceive -1-已领取 0-未领取
     * @return {@link BigDecimal }
     * <AUTHOR>
     * @date 2024/05/29
     */
    BigDecimal getTotalSPNum(@Param("uuid") String uuid, @Param("taskCenterIds") List<Integer> taskCenterIds,
        @Param("isReceive") Integer isReceive);

    BigDecimal getUserSPsum(@Param("uuid") String uuid);

    /**
     * 统计所有SP奖励条数
     * 
     * @param uuid
     * @return {@link Integer }
     * <AUTHOR>
     * @date 2024/06/03
     */
    Integer selectSpCount(@Param("uuid") String uuid);

    /**
     * 统计所有SP奖励列表
     *
     * @param page
     * @param pageSize
     * @param uuid
     * @return {@link List }<{@link SpRecordVo }>
     * <AUTHOR>
     * @date 2024/06/03
     */
    List<SpRecordVo> selectSpList(@Param("page") int page, @Param("pageSize") Integer pageSize,
        @Param("uuid") String uuid);

    /**
     * 获取签到奖励数
     * 
     * @param uuid
     * @return {@link Integer }
     * <AUTHOR>
     * @date 2024/06/03
     */
    Integer selectSiginSpCount(@Param("uuid") String uuid);

    /**
     * 获取签到奖励列表
     *
     * @param page
     * @param pageSize
     * @param uuid
     * @return {@link List }<{@link SpRecordVo }>
     * <AUTHOR>
     * @date 2024/06/03
     */
    List<SpRecordVo> selectSiginSpList(@Param("page") int page, @Param("pageSize") Integer pageSize,
        @Param("uuid") String uuid);

    /**
     * 任务中心奖励数
     * 
     * @param uuid
     * @return {@link Integer }
     * <AUTHOR>
     * @date 2024/06/03
     */
    Integer selectTaskSpCount(@Param("uuid") String uuid);

    List<SpRecordVo> selectTaskSpList(@Param("page") int page, @Param("pageSize") Integer pageSize,
        @Param("uuid") String uuid);

    /**
     * 统计所有获得SP
     * 
     * @param uuid
     * @return {@link BigDecimal }
     * <AUTHOR>
     * @date 2024/06/04
     */
    BigDecimal getTotalSp(@Param("uuid") String uuid);

    /**
     * 流通量
     * 
     * @return {@link BigDecimal }
     * <AUTHOR>
     * @date 2024/06/07
     */
    BigDecimal circulationVolumeValue();

    /**
     * @param page
     * @param uuid
     * @return {@link Page }<{@link SpRecordVo }>
     * <AUTHOR>
     * @date 2024/12/17
     */
    Page<SpRecordVo> selectSpListPage(@Param("page") Page page, @Param("uuid") String uuid);
}
