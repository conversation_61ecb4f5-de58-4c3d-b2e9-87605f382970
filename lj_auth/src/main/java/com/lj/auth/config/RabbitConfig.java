package com.lj.auth.config;

import org.springframework.amqp.core.Queue;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @Author: zjd
 * @Description:
 * @Date:2024/04/11 09:36
 **/
@Configuration
public class RabbitConfig {

    public static final String MINT_UP_CHAIN_QUEUE = "mint-up-chain-queue";

    public static final String SINGLE_MINT_UP_CHAIN_QUEUE = "singleMint-up-chain-queue";

    public static final String COMPENSATION_MINT_UP_CHAIN_QUEUE = "compensation-up-chain-queue";



    @Bean
    public Queue mintQueue() {
        return new Queue(MINT_UP_CHAIN_QUEUE);
    }

    @Bean
    public Queue singleMintQueue() {
        return new Queue(SINGLE_MINT_UP_CHAIN_QUEUE);
    }


    @Bean
    public Queue compensationMintQueue() {
        return new Queue(COMPENSATION_MINT_UP_CHAIN_QUEUE);
    }
}
