<configuration>

    <!-- 定义日志保存的根路径，可根据实际情况修改 -->
    <property name="LOG_ROOT" value="/data/log/lj_auth"/>

    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- 定义按天滚动的文件日志 -->
    <appender name="dailyRollingFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_ROOT}/lj_auth.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 按天滚动，生成每天一个日志文件 -->
            <fileNamePattern>${LOG_ROOT}/lj_auth-%d{yyyy-MM-dd}.log</fileNamePattern>
            <!-- 按分钟滚动，生成每个分钟的日志文件 -->
            <!--            <fileNamePattern>${LOG_ROOT}/portal-%d{yyyy-MM-dd-HH-mm}.log</fileNamePattern>-->
            <!-- 最多保存7天的日志，根据需求修改 -->
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- 设置日志级别为INFO，可根据需求修改 -->
    <root level="info">
        <appender-ref ref="console"/>
        <appender-ref ref="dailyRollingFile"/>
    </root>

</configuration>
