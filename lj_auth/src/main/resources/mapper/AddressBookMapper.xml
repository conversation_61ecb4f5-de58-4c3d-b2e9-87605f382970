<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.AddressBookMapper">
    <resultMap id="BaseResultMap" type="com.lj.auth.domain.AddressBook">
        <!--@mbg.generated-->
        <!--@Table lj_auth_address_book-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="acount_uuid" jdbcType="VARCHAR" property="acountUuid"/>
        <result column="chain_id" jdbcType="INTEGER" property="chainId"/>
        <result column="chain_address" jdbcType="VARCHAR" property="chainAddress"/>
        <result column="contact_description" jdbcType="VARCHAR" property="contactDescription"/>
        <result column="state" jdbcType="BOOLEAN" property="state"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        acount_uuid,
        chain_id,
        chain_address,
        contact_description,
        `state`,
        create_time,
        update_time
    </sql>


    <select id="getPage" resultType="com.lj.auth.domain.vo.AddressBookVo">
        select b.*, c.chain_logo, c.chain_name
        from lj_auth_address_book b
                 left join lj_auth_chain c on c.chain_id = b.chain_id
        where 1 = 1
          and b.acount_uuid = #{accountUUID}
          and b.state = true
        <if test="chainId != null">
            and b.chain_id = #{chainId}
        </if>
        order by b.create_time desc
        limit #{page},#{pageSize}
    </select>
</mapper>