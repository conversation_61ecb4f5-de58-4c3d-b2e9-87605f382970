<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.ContractMapper">
  <resultMap id="BaseResultMap" type="com.lj.auth.domain.Contract">
    <!--@mbg.generated-->
    <!--@Table lj_auth_contract-->
    <id column="id" property="id" />
    <result column="create_time" property="createTime" />
    <result column="update_time" property="updateTime" />
    <result column="contract_type" property="contractType" />
    <result column="contract_name" property="contractName" />
    <result column="contract_symbol" property="contractSymbol" />
    <result column="contract_address" property="contractAddress" />
    <result column="opb_chain_id" property="opbChainId" />
    <result column="image_url" property="imageUrl" />
    <result column="contract_url" property="contractUrl" />
    <result column="status" property="status" />
    <result column="to_use" property="toUse" />
    <result column="contract_describe" property="contractDescribe" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, create_time, update_time, contract_type, contract_name, contract_symbol, contract_address, 
    opb_chain_id, image_url, contract_url, `status`, to_use, contract_describe
  </sql>


  <!--根据链框架id和合约地址查询合约-->
  <select id="queryByChainIDContractAddress" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from lj_auth_contract
    where opb_chain_id = #{opbChainId}
    and contract_address = #{contractAddress}
    and contract_type = #{contractType}
    and status = #{status}
    and to_use = #{toUse}
    </select>

  <select id="queryByChainIDAndType" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from lj_auth_contract
    where opb_chain_id = #{opbChainId}
    and contract_type = #{contractType}
    and status = #{status}
    and to_use = #{toUse}
  </select>

  <select id="selecMaxTokenId" resultType="java.lang.Long">
    SELECT ifnull(max(token_id),0)
    FROM lj_auth_nft
  </select>

  <select id="selectAddressNum" resultType="java.lang.Long">
    SELECT ifnull(count(DISTINCT holder),0)  FROM lj_auth_nft
    where contract_id=#{id}
  </select>

  <select id="selectTransferNum" resultType="java.lang.Long">
    SELECT ifnull(count(1), 0)
    FROM lj_auth_nft_transfer_record
    where contract_id = #{id}
  </select>
</mapper>