<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.DomainAccountMapper">
    <resultMap id="BaseResultMap" type="com.lj.auth.domain.DomainAccount">
        <!--@mbg.generated-->
        <!--@Table ym_domain_account-->
        <id column="id" property="id"/>
        <result column="domain" property="domain"/>
        <result column="parent_domain" property="parentDomain"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="expiration_time" property="expirationTime"/>
        <result column="payment_time" property="paymentTime"/>
        <result column="status" property="status"/>
        <result column="deregistration_time" property="deregistrationTime"/>
        <result column="freeze_reason" property="freezeReason"/>
        <result column="freeze_time" property="freezeTime"/>
        <result column="owner" property="owner"/>
        <result column="register_time" property="registerTime"/>
        <result column="registrar" property="registrar"/>
        <result column="operate_uuid" property="operateUuid"/>
        <result column="account_uuid" property="accountUuid"/>
        <result column="transfer_state" property="transferState"/>
        <result column="pay_state" property="payState"/>
        <result column="nft_get_state" property="nftGetState"/>
        <result column="did_bind_state" property="didBindState"/>
        <result column="suffix" property="suffix"/>
        <result column="transaction_status" property="transactionStatus"/>
        <result column="is_star" property="isStar"/>
        <result column="star_count" property="starCount"/>
    </resultMap>


    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update ym_domain_account
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="`domain` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.domain != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.domain,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="parent_domain = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.parentDomain != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.parentDomain,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.updateTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="expiration_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.expirationTime != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.expirationTime,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="payment_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.paymentTime != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.paymentTime,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="`status` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.status != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.status,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="deregistration_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.deregistrationTime != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.deregistrationTime,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="freeze_reason = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.freezeReason != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.freezeReason,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="freeze_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.freezeTime != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.freezeTime,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="`owner` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.owner != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.owner,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="register_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.registerTime != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.registerTime,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="registrar = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.registrar != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.registrar,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="operate_uuid = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.operateUuid != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.operateUuid,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="account_uuid = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.accountUuid != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.accountUuid,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="transfer_state = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.transferState != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.transferState,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="pay_state = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.payState != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.payState,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="nft_get_state = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.nftGetState != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.nftGetState,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="did_bind_state = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.didBindState != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.didBindState,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="suffix = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.suffix != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.suffix,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="transaction_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.transactionStatus != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.transactionStatus,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_star = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isStar != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.isStar,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="star_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.starCount != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.starCount,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=INTEGER}
        </foreach>
    </update>


    <!--可领取NFT 域名信息-->
    <select id="selectGetNftList" resultMap="BaseResultMap">
        SELECT t2.*
        FROM `ym_order_bsn_info` t1
                 LEFT JOIN ym_domain_account t2 ON t1.domain = t2.domain
        WHERE t2.nft_get_state = 1
          AND t2.pay_state = 1
          AND t1.status = 2
          AND t1.domain_serve_type = 0
          AND t1.account_uuid = #{uuid}
        limit #{count}
    </select>

    <!-- 根据支付状态和领取状态查询-->
    <select id="queryByPayStateAndGetState" resultMap="BaseResultMap">
        select * from
        ym_domain_account
        where 1=1
        <if test="accountUUID != null">
            and account_uuid = #{accountUUID,jdbcType=VARCHAR}
        </if>

        <if test="domain != null">
            and domain = #{domain,jdbcType=VARCHAR}
        </if>

        <if test="payState != null">
            and pay_state = #{payState,jdbcType=INTEGER}
        </if>
        <if test="getState != null">
            and nft_get_state = #{getState,jdbcType=INTEGER}
        </if>
    </select>

    <select id="queryNFTByStatus" resultMap="BaseResultMap">
        SELECT domainAccount.*
        FROM `ym_order_bsn_info` as bsnOrder
                 LEFT JOIN ym_domain_account as domainAccount
                           ON bsnOrder.domain = domainAccount.domain
        WHERE domainAccount.nft_get_state = #{nftGetState}
          AND domainAccount.pay_state = 1
          AND bsnOrder.status = 2
          AND bsnOrder.domain_serve_type = 0
          AND bsnOrder.account_uuid = #{uuid}
        limit #{count}
    </select>

    <select id="queryOneByNFTByStatus" resultMap="BaseResultMap">
        SELECT domainAccount.*
        FROM `ym_order_bsn_info` as bsnOrder
                 LEFT JOIN ym_domain_account as domainAccount
                           ON bsnOrder.domain = domainAccount.domain
        WHERE domainAccount.nft_get_state = #{nftGetState}
          AND domainAccount.pay_state = 1
          AND bsnOrder.status = 2
          AND bsnOrder.domain_serve_type = 0
          AND bsnOrder.account_uuid = #{uuid}
        limit 1
    </select>
    <select id="selectRenewalInfo" resultType="java.util.Map">
        SELECT x.tran_number      AS tranNumber,
               x.domain_server_sn AS domainServerSn,
               a.`domain`,
               x.`level`,
               a.parent_domain    AS parentDomain,
               a.register_time    as register,
               a.expiration_time  as expirationTime
        FROM ym_domain_account a
                 left join ym_order_domain_renewal_info x
                           on
                               a.`domain` = x.`domain`
        where 1 = 1
          and a.`domain` = #{domain}
          and x.tran_number = #{tranNumber}

    </select>
</mapper>
