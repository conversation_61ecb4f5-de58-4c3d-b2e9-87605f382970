<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.CouponBatchMapper">

    <select id="couponBatchList" resultType="com.lj.auth.domain.CouponBatch">
        select *
        from lj_coupon_batch
        where show_state = 1
        and now() between show_start_time and show_end_time
        <if test="type != null">
            and type = #{type}
        </if>
        order by id desc
    </select>

</mapper>