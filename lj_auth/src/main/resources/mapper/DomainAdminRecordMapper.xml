<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.DomainAdminRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.lj.auth.domain.DomainAdminRecord">
        <id column="id" property="id"/>
        <result column="state" property="state"/>
        <result column="account_uuid" property="accountUuid"/>
        <result column="domian_serve_sn" property="domianServeSn"/>
        <result column="trade_code" property="tradeCode"/>
        <result column="out_trade_no" property="outTradeNo"/>
        <result column="chain_id" property="chainId"/>
        <result column="owner" property="owner"/>
        <result column="manager" property="manager"/>
        <result column="resolve_infos" property="resolveInfos"/>
        <result column="sub_time" property="subTime"/>
        <result column="domain" property="domain"/>
        <result column="level" property="level"/>
        <result column="is_upchain" property="isUpchain"/>
        <result column="type" property="type"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>
    <select id="getPage" resultType="com.lj.auth.domain.DomainAdminRecord">
        SELECT r.*, c.chain_name, c.chain_logo
        FROM lj_auth_domain_admin_record r

                 left join lj_auth_chain c
                           on r.chain_id = c.chain_id
        where 1 = 1
          and r.account_uuid = #{uuid}
          and r.`type` = #{type}
          and r.`domain` = #{domain}
        order by r.update_time desc
        limit #{page},#{pageSize}

    </select>

</mapper>
