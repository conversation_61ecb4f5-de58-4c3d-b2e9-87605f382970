<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.AccountDomainAddressMapper">
    <resultMap id="BaseResultMap" type="com.lj.auth.domain.AccountDomainAddress">
        <!--@mbg.generated-->
        <!--@Table lj_auth_account_domain_address-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="opb_chain_id" jdbcType="INTEGER" property="opbChainId"/>
        <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid"/>
        <result column="address" jdbcType="VARCHAR" property="address"/>
        <result column="domain" jdbcType="VARCHAR" property="domain"/>
        <result column="state" jdbcType="BOOLEAN" property="state"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <select id="getDomainMappingList" resultType="com.lj.auth.domain.AccountDomainAddress">
        select a.*, c.chain_logo, c.chain_name, c.chain_id
        from lj_auth_account_domain_address a
                 left join lj_auth_chain c on c.opb_chain_id = a.opb_chain_id
        where a.account_uuid = #{accountUuid}
          and a.domain = #{domain}
          and a.state = true
        order by a.create_time desc

    </select>
</mapper>