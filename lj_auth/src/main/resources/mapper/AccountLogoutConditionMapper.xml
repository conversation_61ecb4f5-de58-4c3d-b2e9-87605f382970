<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.AccountLogoutConditionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.lj.auth.domain.AccountLogoutCondition">
        <id column="id" property="id"/>
        <result column="remind" property="remind"/>
        <result column="content" property="content"/>
        <result column="error_content" property="errorContent"/>
        <result column="number" property="number"/>
    </resultMap>
    <select id="seleLogoutConditionRecords" resultType="com.lj.auth.domain.AccountLogoutCondition">
        SELECT c.*,
               COALESCE(r.is_ignore, 0) AS is_ignore
        FROM lj_auth_account_logout_condition c
                 LEFT JOIN lj_auth_account_logout_condition_record r ON c.id = r.condition_id
        where 1 = 1
          and r.account_uuid = #{uuid}
        order by c.number desc
    </select>

</mapper>
