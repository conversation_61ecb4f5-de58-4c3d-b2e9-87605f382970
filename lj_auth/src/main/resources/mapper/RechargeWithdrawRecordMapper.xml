<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.RechargeWithdrawRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.lj.auth.domain.RechargeWithdrawRecord">
        <id column="id" property="id"/>
        <result column="operate_uuid" property="operateUuid"/>
        <result column="account_uuid" property="accountUuid"/>
        <result column="card_number" property="cardNumber"/>
        <result column="bank" property="bank"/>
        <result column="card_name" property="cardName"/>
        <result column="amount" property="amount"/>
        <result column="balance" property="balance"/>
        <result column="state" property="state"/>
        <result column="reason" property="reason"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="type" property="type"/>
        <result column="receipt_name" property="receiptName"/>
        <result column="receipt_phone" property="receiptPhone"/>
        <result column="receipt_picture" property="receiptPicture"/>
        <result column="recharge_code" property="rechargeCode"/>
        <result column="robot_push_message" property="robotPushMessage"/>
        <result column="order_id" property="orderId"/>
        <result column="tran_number" property="tranNumber"/>
        <result column="pay_type" property="payType"/>
        <result column="qr_code_id" property="qrCodeId"/>
        <result column="platform" property="platform"/>
        <result column="rate" property="rate"/>
        <result column="real_amount" property="realAmount"/>
        <result column="douyin_order_id" property="douyinOrderId"/>
        <result column="douyin_order_token" property="douyinOrderToken"/>
        <result column="settle_status" property="settleStatus"/>
        <result column="refund_state" property="refundState"/>
        <result column="tax_rate" property="taxRate"/>
        <result column="tax_amount" property="taxAmount"/>
        <result column="withdraw_service_charge" property="withdrawServiceCharge"/>
        <result column="remark" property="remark"/>
        <result column="validity_time" property="validityTime"/>
        <result column="status" property="status"/>
    </resultMap>
    <select id="getOver24Hours" resultType="com.lj.auth.domain.RechargeWithdrawRecord">
        SELECT *
        FROM ym_recharge_withdraw_record
        WHERE 1=1
        and pay_type in (1,2)
        and `type` =1
        and state =6
        and platform in(3,4)
        and create_time &lt; CURRENT_TIMESTAMP - INTERVAL 24 HOUR;
    </select>

</mapper>
