<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.InterfaceRecordMapper">
  <resultMap id="BaseResultMap" type="com.lj.auth.domain.InterfaceRecord">
    <!--@mbg.generated-->
    <!--@Table lj_auth_interface_record-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="account" jdbcType="VARCHAR" property="account" />
    <result column="request_ip" jdbcType="VARCHAR" property="requestIp" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="path" jdbcType="INTEGER" property="path" />
    <result column="request_param" jdbcType="LONGVARCHAR" property="requestParam" />
    <result column="channel" jdbcType="VARCHAR" property="channel" />
    <result column="edition" jdbcType="VARCHAR" property="edition" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, account, request_ip, create_time, `path`, request_param, channel, edition
  </sql>
</mapper>