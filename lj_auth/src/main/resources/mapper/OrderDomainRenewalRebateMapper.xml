<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.OrderDomainRenewalRebateMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.lj.auth.domain.OrderDomainRenewalRebate">
        <id column="id" property="id" />
        <result column="order_info_id" property="orderInfoId" />
        <result column="account_uuid" property="accountUuid" />
        <result column="amount" property="amount" />
        <result column="this_account_scale" property="thisAccountScale" />
        <result column="this_account_amount" property="thisAccountAmount" />
        <result column="parent_uuid" property="parentUuid" />
        <result column="parent_account_scale" property="parentAccountScale" />
        <result column="parent_account_amount" property="parentAccountAmount" />
        <result column="this_operate_uuid" property="thisOperateUuid" />
        <result column="this_operate_scale" property="thisOperateScale" />
        <result column="this_operate_amount" property="thisOperateAmount" />
        <result column="parent_operate_uuid" property="parentOperateUuid" />
        <result column="parent_operate_scale" property="parentOperateScale" />
        <result column="parent_operate_amount" property="parentOperateAmount" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="rebate_state" property="rebateState" />
    </resultMap>

</mapper>
