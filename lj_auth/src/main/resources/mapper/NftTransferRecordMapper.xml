<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.NftTransferRecordMapper">
  <resultMap id="BaseResultMap" type="com.lj.auth.domain.NftTransferRecord">
    <!--@mbg.generated-->
    <!--@Table lj_auth_nft_transfer_record-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="opb_chain_id" jdbcType="INTEGER" property="opbChainId" />
    <result column="contract_id" jdbcType="BIGINT" property="contractId" />
    <result column="token_id" jdbcType="INTEGER" property="tokenId" />
    <result column="contract_address" jdbcType="VARCHAR" property="contractAddress" />
    <result column="from_address" jdbcType="VARCHAR" property="fromAddress" />
    <result column="to_address" jdbcType="VARCHAR" property="toAddress" />
    <result column="energy_value" jdbcType="VARCHAR" property="energyValue" />
    <result column="transaction_hash" jdbcType="VARCHAR" property="transactionHash" />
    <result column="block_number" jdbcType="INTEGER" property="blockNumber" />
    <result column="transaction_type" jdbcType="TINYINT" property="transactionType" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="is_delete" jdbcType="BOOLEAN" property="isDelete" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, create_time, update_time, opb_chain_id, contract_id, token_id, contract_address, 
    from_address, to_address, energy_value, transaction_hash, block_number, transaction_type, 
    `status`, is_delete
  </sql>
  <select id="getNftTransactions" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from lj_auth_nft_transfer_record
    where opb_chain_id = #{opbChainId}
    and contract_id = #{contractId}
    and token_id = #{tokenId}
    and transaction_type = 2
    <if test="type == 0">
      and (from_address = #{holderAddress} or to_address = #{holderAddress})
    </if>
    <if test="type == 1">
      and from_address = #{holderAddress}
    </if>
    <if test="type == 2">
      and to_address = #{holderAddress}
    </if>
    and is_delete = 0
    order by create_time desc
  </select>



  <select id="getAllNftTransactions" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from lj_auth_nft_transfer_record
    where opb_chain_id = #{opbChainId}
    and transaction_type = 2
    <if test="type == 0">
      and (from_address = #{chainAccountAddress} or to_address = #{chainAccountAddress})
    </if>
    <if test="type == 1">
      and from_address = #{chainAccountAddress}
    </if>
    <if test="type == 2">
      and to_address = #{chainAccountAddress}
    </if>
    <if test="time != null and time != ''">
      and DATE_FORMAT(create_time, '%Y-%m') = #{time}
    </if>
    and is_delete = 0
    order by create_time desc
  </select>

  <select id="getNftTransactionById" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from lj_auth_nft_transfer_record
    where opb_chain_id = #{opbChainId}
    and contract_address = #{contractAddress}
    and token_id = #{tokenId}
    and to_address = #{holderAddress}
    and status = #{status}
    and is_delete = 0
  </select>



  <select id="queryByHashAndTransactionType" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from lj_auth_nft_transfer_record
    where opb_chain_id = #{opbChainId}
    and transaction_hash = #{transactionHash}
    and token_id = #{tokenId}
    <if test="transactionType != null">
      and transaction_type = #{transactionType}
    </if>
    <if test="isDelete != null">
      and is_delete = #{isDelete}
    </if>
  </select>




  <select id="queryById" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from lj_auth_nft_transfer_record
    where  id = #{id}
    and is_delete = 0
  </select>

  <select id="selectNftTrRecord" resultType="com.lj.auth.domain.vo.NftTransferRecordVo">
    SELECT lr.*, nf.nft_image
    FROM (select *
          from lj_auth_nft_transfer_record
          where is_delete = 0
          order by create_time desc
            LIMIT #{page}, #{pageSize}) lr
           left join lj_auth_nft nf on lr.token_id = nf.token_id
    where 1 = 1
    <if test="address != null and address != ''">
      and lr.to_address = #{address}
         or lr.from_address = #{address}
    </if>
    <if test="tokenId != null">
      and lr.token_id = #{tokenId}
    </if>
  </select>

  <select id="selectNftTrRecordCount" resultType="java.lang.Long">
    select ifnull(count(0), 0)
    from lj_auth_nft_transfer_record lr
    where is_delete = 0
    <if test="address != null and address != ''">
      and lr.to_address = #{address}
         or lr.from_address = #{address}
    </if>
    <if test="tokenId != null">
      and lr.token_id = #{tokenId}
    </if>
  </select>
</mapper>