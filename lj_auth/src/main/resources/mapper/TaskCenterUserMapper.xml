<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.TaskCenterUserMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.lj.auth.domain.TaskCenterUser">
        <id column="id" property="id"/>
        <result column="account_uuid" property="accountUuid"/>
        <result column="address" property="address"/>
        <result column="task_center_id" property="taskCenterId"/>
        <result column="task_id_completed" property="taskIdCompleted"/>
        <result column="reward" property="reward"/>
        <result column="is_complete" property="isComplete"/>
        <result column="is_receive" property="isReceive"/>
        <result column="hash" property="hash"/>
        <result column="state" property="state"/>
        <result column="completion_time" property="completionTime"/>
        <result column="completion_count" property="completionCount"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>
    <select id="queryTaskUserCount" resultType="java.lang.Integer">
        select count(1)
        from (
                 SELECT a.nick_name,
                        a.domain_nick_name,
                        a.show_type,
                        a.head_portrait,
                        a.head_portrait_nft_id,
                        a.head_portrait_type,
                        a.register_time,
                        u.*
                 FROM lj_auth_task_center_user u
                          left join account a
                                    on
                                        a.uuid = u.task_id_completed
             ) t

                 left join lj_auth_nft n
                           on n.id = t.head_portrait_nft_id
        where 1 = 1
          and t.account_uuid = #{uuid}
          and t.task_center_id in
        <foreach collection="taskCenterIds" item="taskCenterId" open="(" close=")" separator=",">
            #{taskCenterId}
        </foreach>
    </select>


    <select id="getTaskUserPage" resultType="com.lj.auth.domain.vo.TaskUserVo">
        select t.*, n.nft_image
        from (
                 SELECT a.nick_name,
                        a.domain_nick_name,
                        a.show_type,
                        a.head_portrait,
                        a.head_portrait_nft_id,
                        a.head_portrait_type,
                        a.register_time,
                        u.*
                 FROM lj_auth_task_center_user u
                          left join account a
                                    on
                                        a.uuid = u.task_id_completed
             ) t

                 left join lj_auth_nft n
                           on n.id = t.head_portrait_nft_id
        where 1 = 1
          and t.account_uuid = #{uuid}
          and t.task_center_id in
        <foreach collection="taskCenterIds" item="taskCenterId" open="(" close=")" separator=",">
            #{taskCenterId}
        </foreach>
        order by t.register_time desc
        limit #{page},#{pageSize}
    </select>
    <select id="getTotalSPNum" resultType="java.math.BigDecimal">
        select COALESCE(SUM(reward), 0) AS total_reward
        from lj_auth_task_center_user
        where 1 = 1
          and account_uuid = #{uuid}
          and is_complete = 1
          and task_center_id in
        <foreach collection="taskCenterIds" item="taskCenterId" open="(" close=")" separator=",">
            #{taskCenterId}
        </foreach>
        <if test="isReceive != null">
            and is_receive = #{isReceive}
        </if>
    </select>
    <select id="getUserSPsum" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(reward), 0) AS total_reward
        FROM lj_auth_task_center_user
        WHERE 1 = 1
          AND account_uuid = #{uuid}
          AND is_receive = 1
    </select>
    <select id="selectSpCount" resultType="java.lang.Integer">
        select count(1)
        from ((
                  SELECT u.account_uuid
                  FROM lj_auth_task_center_user u
                           left join lj_auth_task_center c on
                      u.task_center_id = c.id
                  where 1 = 1
                    and u.is_receive = 1)
              UNION ALL
              (
                  SELECT account_uuid
                  FROM lj_auth_sign_in_hist
              )) t
        where 1 = 1
          and t.account_uuid = #{uuid}
    </select>
    <select id="selectSpList" resultType="com.lj.auth.domain.vo.SpRecordVo">
        select *
        from (
                 (SELECT u.id,
                         u.address,
                         u.account_uuid,
                         u.completion_time as time,
                         u.reward,
                         case
                             c.task_type_id
                             when 1 then CONVERT('日常任务奖励' USING utf8mb4) COLLATE utf8mb4_unicode_ci
                             when 2 then CONVERT('周期任务奖励' USING utf8mb4) COLLATE utf8mb4_unicode_ci
                             when 3 then CONVERT('新手任务奖励' USING utf8mb4) COLLATE utf8mb4_unicode_ci
                             when 4 then CONVERT('限时任务奖励' USING utf8mb4) COLLATE utf8mb4_unicode_ci
                             when 5 then CONVERT('长期任务奖励' USING utf8mb4) COLLATE utf8mb4_unicode_ci
                             else CONVERT('其他任务奖励' USING utf8mb4) COLLATE utf8mb4_unicode_ci
                             end           as content,
                         2                 as type,
                         u.hash,
                         u.create_time,
                         u.update_time
                  FROM lj_auth_task_center_user u
                           left join lj_auth_task_center c
                                     on
                                         u.task_center_id = c.id
                  where 1 = 1
                    and u.is_receive = 1
                    and u.account_uuid = #{uuid}
                 )

                 UNION ALL

                 (SELECT id,
                         address,
                         account_uuid,
                         sign_in_date       as time,
                         reward_money       as reward,
                         CONVERT(CONCAT('累计签到', continuite_day, '天') USING utf8mb4) COLLATE
                         utf8mb4_unicode_ci as content,
                         1                  as type,
                         hash,
                         create_time,
                         update_time
                  FROM lj_auth_sign_in_hist
                  where 1 = 1
                    and account_uuid = #{uuid}
                 )
                 UNION ALL
                 (
                     SELECT id,
                            address,
                            account_uuid,
                            create_time    as time,
                            reward         as reward,
                            task_type_name as content,
                            type,
                            hash,
                            create_time,
                            create_time    as update_time
                     FROM lj_auth_sp_record
                     where 1 = 1
                       and account_uuid = #{uuid}
                 )
             ) t
        where 1 = 1
        order by t.update_time desc
        limit #{page}, #{pageSize}
    </select>
    <select id="selectSiginSpCount" resultType="java.lang.Integer">
        select count(1)
        FROM lj_auth_sign_in_hist
        WHERE 1 = 1
          and account_uuid = #{uuid}
    </select>
    <select id="selectSiginSpList" resultType="com.lj.auth.domain.vo.SpRecordVo">
        select *
        from (SELECT id,
                     address,
                     account_uuid,
                     sign_in_date       as time,
                     reward_money       as reward,
                     CONVERT(CONCAT('累计签到', continuite_day, '天') USING utf8mb4) COLLATE
                     utf8mb4_unicode_ci as content,
                     1                  as type,
                     hash,
                     create_time,
                     update_time
              FROM lj_auth_sign_in_hist
             ) t
        where 1 = 1
          and t.account_uuid = #{uuid}
        order by t.update_time desc
        limit #{page}, #{pageSize}
    </select>
    <select id="selectTaskSpCount" resultType="java.lang.Integer">
        select count(1)
        FROM lj_auth_task_center_user
        where 1 = 1
          and is_receive = 1
          and account_uuid = #{uuid}
    </select>
    <select id="selectTaskSpList" resultType="com.lj.auth.domain.vo.SpRecordVo">
        select *
        from (
                 SELECT u.id,
                        u.address,
                        u.account_uuid,
                        u.completion_time as time,
                        u.reward,
                        case
                            c.task_type_id
                            when 1 then CONVERT('日常任务奖励' USING utf8mb4) COLLATE utf8mb4_unicode_ci
                            when 2 then CONVERT('周期任务奖励' USING utf8mb4) COLLATE utf8mb4_unicode_ci
                            when 3 then CONVERT('新手任务奖励' USING utf8mb4) COLLATE utf8mb4_unicode_ci
                            when 4 then CONVERT('限时任务奖励' USING utf8mb4) COLLATE utf8mb4_unicode_ci
                            when 5 then CONVERT('长期任务奖励' USING utf8mb4) COLLATE utf8mb4_unicode_ci
                            else CONVERT('其他任务奖励' USING utf8mb4) COLLATE utf8mb4_unicode_ci
                            end           as content,
                        2                 as type,
                        u.hash,
                        u.create_time,
                        u.update_time
                 FROM lj_auth_task_center_user u
                          left join lj_auth_task_center c
                                    on
                                        u.task_center_id = c.id
                 where 1 = 1
                   and u.is_receive = 1
             ) t
        where 1 = 1
          and t.account_uuid = #{uuid}
        order by t.update_time desc
        limit #{page}, #{pageSize}
    </select>
    <select id="getTotalSp" resultType="java.math.BigDecimal">
        select sum(reward)
        from (
                 SELECT sum(reward) as reward
                 FROM lj_auth_task_center_user
                 WHERE 1 = 1
                   AND account_uuid = #{uuid}
                   AND is_receive = 1

                 UNION ALL

                 SELECT sum(reward_money) as reward
                 FROM lj_auth_sign_in_hist lasih
                 where 1 = 1
                   AND account_uuid = #{uuid}
             ) a
    </select>
    <select id="circulationVolumeValue" resultType="java.math.BigDecimal">
        select sum(banlace)
        from (
                 SELECT sum(reward_money) as banlace
                 FROM lj_auth_sign_in_hist

                 union all

                 SELECT COALESCE(SUM(reward), 0) AS banlace
                 FROM lj_auth_task_center_user
                 where is_receive = 1
                 union all
                 SELECT COALESCE(SUM(reward), 0) AS banlace
                 FROM lj_auth_sp_record
             ) a
    </select>
    <select id="selectSpListPage" resultType="com.lj.auth.domain.vo.SpRecordVo">
        select *
        from (
        (SELECT u.id,
        u.address,
        u.account_uuid,
        u.completion_time as time,
        u.reward,
        case
        c.task_type_id
        when 1 then CONVERT('日常任务奖励' USING utf8mb4) COLLATE utf8mb4_unicode_ci
        when 2 then CONVERT('周期任务奖励' USING utf8mb4) COLLATE utf8mb4_unicode_ci
        when 3 then CONVERT('新手任务奖励' USING utf8mb4) COLLATE utf8mb4_unicode_ci
        when 4 then CONVERT('限时任务奖励' USING utf8mb4) COLLATE utf8mb4_unicode_ci
        when 5 then CONVERT('长期任务奖励' USING utf8mb4) COLLATE utf8mb4_unicode_ci
        else CONVERT('其他任务奖励' USING utf8mb4) COLLATE utf8mb4_unicode_ci
        end           as content,
        2                 as type,
        u.hash,
        u.create_time,
        u.update_time
        FROM lj_auth_task_center_user u
        left join lj_auth_task_center c
        on
        u.task_center_id = c.id
        where 1 = 1
        and u.is_receive = 1
        and u.account_uuid = #{uuid}
        )

        UNION ALL

        (SELECT id,
        address,
        account_uuid,
        sign_in_date       as time,
        reward_money       as reward,
        CONVERT(CONCAT('累计签到', continuite_day, '天') USING utf8mb4) COLLATE
        utf8mb4_unicode_ci as content,
        1                  as type,
        hash,
        create_time,
        update_time
        FROM lj_auth_sign_in_hist
        where 1 = 1
        and account_uuid = #{uuid}
        )
        UNION ALL
        (
        SELECT id,
        address,
        account_uuid,
        create_time    as time,
        reward         as reward,
        task_type_name as content,
        type,
        hash,
        create_time,
        create_time    as update_time
        FROM lj_auth_sp_record
        where 1 = 1
        and account_uuid = #{uuid}
        )
        ) t
        where 1 = 1
        order by t.update_time desc
    </select>
</mapper>
