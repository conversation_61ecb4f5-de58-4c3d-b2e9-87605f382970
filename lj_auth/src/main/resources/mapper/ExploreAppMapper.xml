<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.ExploreAppMapper">
  <resultMap id="BaseResultMap" type="com.lj.auth.domain.ExploreApp">
    <!--@mbg.generated-->
    <!--@Table lj_auth_explore_app-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="app_name" jdbcType="VARCHAR" property="appName" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="state" jdbcType="BOOLEAN" property="state" />
    <result column="app_type_id" jdbcType="INTEGER" property="appTypeId" />
    <result column="describe" jdbcType="VARCHAR" property="describe" />
    <result column="logo" jdbcType="VARCHAR" property="logo" />
    <result column="app_link" jdbcType="VARCHAR" property="appLink" />
    <result column="is_recommendation" jdbcType="BOOLEAN" property="isRecommendation" />
    <result column="is_official" jdbcType="BOOLEAN" property="isOfficial" />
    <result column="jump_type" jdbcType="INTEGER" property="jumpType" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, app_name, sort, `state`, app_type_id, `describe`, logo, app_link, is_recommendation, 
    is_official, jump_type
  </sql>
</mapper>