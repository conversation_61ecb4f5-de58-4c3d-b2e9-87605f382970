<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.TransferNoticeMapper">
  <resultMap id="BaseResultMap" type="com.lj.auth.domain.TransferNotice">
    <!--@mbg.generated-->
    <!--@Table lj_auth_transfer_notice-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="content" jdbcType="LONGVARCHAR" property="content" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="type" jdbcType="INTEGER" property="type" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, title, description, content, create_time, `type`
  </sql>
</mapper>