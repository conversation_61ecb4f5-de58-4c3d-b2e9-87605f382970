<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.DouDianCouponMapper">
  <resultMap id="BaseResultMap" type="com.lj.auth.domain.DouDianCoupon">
    <!--@mbg.generated-->
    <!--@Table lj_dou_dian_coupon-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="redeem_code" jdbcType="VARCHAR" property="redeemCode" />
    <result column="distribution_id" jdbcType="INTEGER" property="distributionId" />
    <result column="state" jdbcType="INTEGER" property="state" />
    <result column="write_off_state" jdbcType="INTEGER" property="writeOffState" />
    <result column="coupon_batch_id" jdbcType="BIGINT" property="couponBatchId" />
    <result column="coupon_id" jdbcType="BIGINT" property="couponId" />
    <result column="p_id" jdbcType="BIGINT" property="pId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="validity_time" jdbcType="TIMESTAMP" property="validityTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, redeem_code, distribution_id, `state`, write_off_state, coupon_batch_id, coupon_id, 
    p_id, create_time, update_time, validity_time
  </sql>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update lj_dou_dian_coupon
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="redeem_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.redeemCode != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.redeemCode,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="distribution_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.distributionId != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.distributionId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="`state` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.state != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.state,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="write_off_state = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.writeOffState != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.writeOffState,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="coupon_batch_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.couponBatchId != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.couponBatchId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="coupon_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.couponId != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.couponId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="p_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.pId != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.pId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createTime != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.createTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateTime != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.updateTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="validity_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.validityTime != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.validityTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=INTEGER}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into lj_dou_dian_coupon
    (redeem_code, distribution_id, `state`, write_off_state, coupon_batch_id, coupon_id, 
      p_id, create_time, update_time, validity_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.redeemCode,jdbcType=VARCHAR}, #{item.distributionId,jdbcType=INTEGER}, #{item.state,jdbcType=INTEGER}, 
        #{item.writeOffState,jdbcType=INTEGER}, #{item.couponBatchId,jdbcType=BIGINT}, 
        #{item.couponId,jdbcType=BIGINT}, #{item.pId,jdbcType=BIGINT}, #{item.createTime,jdbcType=TIMESTAMP}, 
        #{item.updateTime,jdbcType=TIMESTAMP}, #{item.validityTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
    <select id="queryByCouponNumberList" resultType="com.lj.auth.domain.vo.DouDianCouponVo">
        select redeem_code as redeemCode, create_time as createTime, validity_time as validityTime
        from lj_dou_dian_coupon
        where redeem_code in
        <foreach item="item" index="index" collection="couponNumberList" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>