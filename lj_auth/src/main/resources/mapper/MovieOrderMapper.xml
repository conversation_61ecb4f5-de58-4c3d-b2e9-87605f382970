<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.MovieOrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.lj.auth.domain.MovieOrder">
        <id column="id" property="id" />
        <result column="account_uuid" property="accountUuid" />
        <result column="tianhe_order_id" property="tianheOrderId" />
        <result column="cinema_name" property="cinemaName" />
        <result column="hall_name" property="hallName" />
        <result column="movie_name" property="movieName" />
        <result column="film_pic" property="filmPic" />
        <result column="film_language" property="filmLanguage" />
        <result column="order_num" property="orderNum" />
        <result column="order_status_str" property="orderStatusStr" />
        <result column="order_status" property="orderStatus" />
        <result column="real_seat" property="realSeat" />
        <result column="seat" property="seat" />
        <result column="reserved_phone" property="reservedPhone" />
        <result column="third_order_id" property="thirdOrderId" />
        <result column="net_price" property="netPrice" />
        <result column="seat_data" property="seatData" />
        <result column="ready_ticket_time" property="readyTicketTime" />
        <result column="ticket_time" property="ticketTime" />
        <result column="close_time" property="closeTime" />
        <result column="close_cause" property="closeCause" />
        <result column="ticket_code" property="ticketCode" />
        <result column="init_price" property="initPrice" />
        <result column="show_time" property="showTime" />
        <result column="duration" property="duration" />
        <result column="stop_show_time" property="stopShowTime" />
        <result column="pay_type" property="payType" />
        <result column="sp_num" property="spNum" />
        <result column="sp_pay_hash" property="spPayHash" />
        <result column="notify_flag" property="notifyFlag" />
        <result column="assets_handle_flag" property="assetsHandleFlag" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <select id="selectSpAmountSum" resultType="java.math.BigDecimal">
        SELECT
            ROUND(SUM(sp_amount) / 100.0, 2) AS total_amount_in_yuan
        FROM
            lj_movie_order;
    </select>
</mapper>