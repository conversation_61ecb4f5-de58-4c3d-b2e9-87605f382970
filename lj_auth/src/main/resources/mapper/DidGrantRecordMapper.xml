<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.DidGrantRecordMapper">
  <resultMap id="BaseResultMap" type="com.lj.auth.domain.DidGrantRecord">
    <!--@mbg.generated-->
    <!--@Table lj_auth_did_grant_record-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid" />
    <result column="application_id" jdbcType="INTEGER" property="applicationId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="token" jdbcType="VARCHAR" property="token" />
    <result column="type_name" jdbcType="VARCHAR" property="typeName" />
    <result column="is_relieve" jdbcType="INTEGER" property="isRelieve" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, account_uuid, application_id, create_time, token, type_name, is_relieve
  </sql>
</mapper>