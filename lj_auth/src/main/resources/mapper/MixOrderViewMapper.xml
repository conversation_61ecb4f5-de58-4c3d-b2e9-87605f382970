<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.MixOrderViewMapper">
  <resultMap id="BaseResultMap" type="com.lj.auth.domain.MixOrderView">
    <!--@mbg.generated-->
    <!--@Table mix_order_view-->
    <id column="order_number" jdbcType="VARCHAR" property="orderNumber" />
    <result column="applicaton_id" jdbcType="INTEGER" property="applicatonId" />
    <result column="application_name" jdbcType="VARCHAR" property="applicationName" />
    <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid" />
    <result column="did_symbol" jdbcType="VARCHAR" property="didSymbol" />
    <result column="order_title" jdbcType="VARCHAR" property="orderTitle" />
    <result column="order_view_json" jdbcType="VARCHAR" property="orderViewJson" />
    <result column="order_detail_json" jdbcType="VARCHAR" property="orderDetailJson" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    order_number, applicaton_id, application_name, account_uuid, did_symbol, order_title, 
    order_view_json, order_detail_json, create_time, update_time
  </sql>
</mapper>