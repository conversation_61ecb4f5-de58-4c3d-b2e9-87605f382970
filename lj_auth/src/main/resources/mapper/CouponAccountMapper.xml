<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.CouponAccountMapper">

    <!-- 根据批次id获取已领取的券数量 -->
    <select id="getReceivedNumberByBatchId" resultType="java.lang.Integer">
        select ifnull(count(*), 0)
        from lj_coupon_account
        where coupon_batch_id = #{couponBatchId}
          and account_uuid = #{accountUuid}
    </select>

    <!-- 根据券id获取已领取的券数量 -->
    <select id="getReceivedNumberByCouponId" resultType="java.lang.Integer">
        select ifnull(count(*), 0)
        from lj_coupon_account
        where coupon_batch_id = #{couponBatchId}
          and coupon_id = #{couponId}
          and account_uuid = #{accountUuid}
    </select>

    <!-- 用户待使用券列表数量 -->
    <select id="accountCouponListCount" resultType="java.lang.Integer">
        select ifnull(count(*), 0)
        from lj_coupon_account t
        left join lj_coupon c on c.id = t.coupon_id
        where t.account_uuid = #{accountUuid}
        <if test="state != null">
            and t.state = #{state}
        </if>
        <if test="historyType != null">
            <if test="historyType == 0">
                and now() &lt;= c.use_end_time
            </if>
            <if test="historyType == 1">
                and now() > c.use_end_time
            </if>
        </if>
    </select>

    <!-- 用户待使用券列表 -->
    <select id="accountCouponList" resultType="com.lj.auth.domain.vo.CouponVo">
        select c.name            couponName,
               c.model           couponModel,
               c.use_start_time  useStartTime,
               c.use_end_time    useEndTime,
               c.description     description,
               c.discount_rate   discountRate,
               t.coupon_batch_id couponBatchId,
               t.coupon_id       couponId,
               t.id              couponAccountId,
               t.state           state
        from lj_coupon_account t
                 left join lj_coupon c on c.id = t.coupon_id
        where t.account_uuid = #{accountUuid}
          <if test="state != null">
              and t.state = #{state}
          </if>
        <if test="historyType != null">
            <if test="historyType == 0">
                and now() &lt;= c.use_end_time
            </if>
            <if test="historyType == 1">
                and now() > c.use_end_time
            </if>
        </if>
        order by t.id desc
            limit #{start}, #{pageSize}
    </select>

    <!-- 获取用户第一种未使用的电影券 -->
    <select id="getFirstCanUseCoupon" resultType="com.lj.auth.domain.vo.CouponVo">
        select c.name            couponName,
        c.model           couponModel,
        c.use_start_time  useStartTime,
        c.use_end_time    useEndTime,
        c.description     description,
        c.discount_rate   discountRate,
        t.coupon_batch_id couponBatchId,
        t.coupon_id       couponId,
        t.id              couponAccountId,
        t.state           state
        from lj_coupon_account t
        left join lj_coupon c on c.id = t.coupon_id
        left join lj_coupon_batch b on b.id = t.coupon_batch_id
        where t.account_uuid = #{accountUuid}
        and t.state = 1
        and b.type = 2
--         and c.show_state = 1
        and now() between c.use_start_time and c.use_end_time
        order by t.id
        limit 1
    </select>

    <!-- 根据id获取用户未使用的电影券 -->
    <select id="getCouponById" resultType="com.lj.auth.domain.vo.CouponVo">
        select c.name            couponName,
               c.model           couponModel,
               c.use_start_time  useStartTime,
               c.use_end_time    useEndTime,
               c.description     description,
               c.discount_rate   discountRate,
               t.coupon_batch_id couponBatchId,
               t.coupon_id       couponId,
               t.id              couponAccountId,
               t.state           state
        from lj_coupon_account t
                 left join lj_coupon c on c.id = t.coupon_id
                 left join lj_coupon_batch b on b.id = t.coupon_batch_id
        where t.account_uuid = #{accountUuid}
          and t.id = #{couponAccountId}
          and t.state = 1
          and b.type = 2
--         and c.show_state = 1
          and now() between c.use_start_time and c.use_end_time
        order by t.id limit 1
    </select>

    <!-- 用户券列表(选择优惠页面) -->
    <select id="couponList" resultType="com.lj.auth.domain.vo.CouponVo">
        select c.name couponName,
        c.amount amount,
        c.model couponModel,
        c.use_start_time useStartTime,
        c.use_end_time useEndTime,
        c.description description,
        c.discount_rate discountRate,
        t.coupon_batch_id couponBatchId,
        t.coupon_id couponId,
        t.id couponAccountId,
        t.state state
        from lj_coupon_account t
        left join lj_coupon c on c.id = t.coupon_id
        left join lj_coupon_batch b on b.id = t.coupon_batch_id
        where c.show_state = 1
        and b.show_state = 1
        and t.state = 1
        and t.account_uuid = #{accountUuid}
        and c.use_end_time > now()
        <if test="historyType != null">
            <if test="historyType == 0">
                and now() &lt;= c.use_start_time
            </if>
            <if test="historyType == 1">
                and now() > c.use_start_time
            </if>
        </if>
        order by c.use_end_time
    </select>

    <select id="couponListOfType" resultType="com.lj.auth.domain.vo.CouponVo">
        select c.name couponName,
        c.amount amount,
        c.model couponModel,
        c.use_start_time useStartTime,
        c.use_end_time useEndTime,
        c.description description,
        c.discount_rate discountRate,
        t.coupon_batch_id couponBatchId,
        t.coupon_id couponId,
        t.id couponAccountId,
        t.coupon_number as couponNumber,
        t.state state
        from lj_coupon_account t
        left join lj_coupon c on c.id = t.coupon_id
        left join lj_coupon_batch b on b.id = t.coupon_batch_id
        where c.show_state = 1
        and b.show_state = 1
        and t.state = 1
        and t.account_uuid = #{accountUuid}
        and c.use_end_time > now()
        <if test="type != null">
            and b.type = #{type}
        </if>
        <if test="historyType != null">
            <if test="historyType == 0">
                and now() &lt;= c.use_start_time
            </if>
            <if test="historyType == 1">
                and now() > c.use_start_time
            </if>
        </if>
        order by c.use_end_time
    </select>
</mapper>