<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.CommentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.lj.auth.domain.Comment">
        <id column="id" property="id" />
        <result column="account_uuid" property="accountUuid" />
        <result column="trends_id" property="trendsId" />
        <result column="content" property="content" />
        <result column="likes_num" property="likesNum" />
        <result column="forward_num" property="forwardNum" />
        <result column="remove_flag" property="removeFlag" />
        <result column="create_time" property="createTime" />
    </resultMap>

</mapper>
