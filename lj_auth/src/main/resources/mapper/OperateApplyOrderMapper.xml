<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.OperateApplyOrderMapper">
  <resultMap id="BaseResultMap" type="com.lj.auth.domain.OperateApplyOrder">
    <!--@mbg.generated-->
    <!--@Table ym_operate_apply_order-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="total_amount" jdbcType="DECIMAL" property="totalAmount" />
    <result column="discount_amount" jdbcType="DECIMAL" property="discountAmount" />
    <result column="real_amount" jdbcType="DECIMAL" property="realAmount" />
    <result column="pay_amount" jdbcType="DECIMAL" property="payAmount" />
    <result column="pay_status" jdbcType="INTEGER" property="payStatus" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="order_number" jdbcType="VARCHAR" property="orderNumber" />
    <result column="pay_type" jdbcType="INTEGER" property="payType" />
    <result column="out_trade_no" jdbcType="VARCHAR" property="outTradeNo" />
    <result column="operate_uuid" jdbcType="VARCHAR" property="operateUuid" />
    <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid" />
    <result column="pay_time" jdbcType="TIMESTAMP" property="payTime" />
    <result column="promotion_level" jdbcType="INTEGER" property="promotionLevel" />
    <result column="detail" jdbcType="LONGVARCHAR" property="detail" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="phone_number" jdbcType="VARCHAR" property="phoneNumber" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="robot_push_message" jdbcType="INTEGER" property="robotPushMessage" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, total_amount, discount_amount, real_amount, pay_amount, pay_status, `type`, order_number, 
    pay_type, out_trade_no, operate_uuid, account_uuid, pay_time, promotion_level, detail, 
    `status`, create_time, update_time, phone_number, version, robot_push_message
  </sql>
</mapper>