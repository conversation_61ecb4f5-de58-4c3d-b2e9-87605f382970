<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.OrderDomainRenewalNftMapper">
  <resultMap id="BaseResultMap" type="com.lj.auth.domain.OrderDomainRenewalNft">
    <!--@mbg.generated-->
    <!--@Table ym_order_domain_renewal_nft-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_info_id" jdbcType="BIGINT" property="orderInfoId" />
    <result column="contract_address" jdbcType="VARCHAR" property="contractAddress" />
    <result column="token" jdbcType="INTEGER" property="token" />
    <result column="tran_state" jdbcType="INTEGER" property="tranState" />
    <result column="hash" jdbcType="VARCHAR" property="hash" />
    <result column="from_address" jdbcType="VARCHAR" property="fromAddress" />
    <result column="to_address" jdbcType="VARCHAR" property="toAddress" />
    <result column="accredit_from_address" jdbcType="VARCHAR" property="accreditFromAddress" />
    <result column="accredit_to_address" jdbcType="VARCHAR" property="accreditToAddress" />
    <result column="sponsor_transfer_address" jdbcType="VARCHAR" property="sponsorTransferAddress" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, order_info_id, contract_address, token, tran_state, hash, from_address, to_address, 
    accredit_from_address, accredit_to_address, sponsor_transfer_address, create_time, 
    update_time
  </sql>
</mapper>