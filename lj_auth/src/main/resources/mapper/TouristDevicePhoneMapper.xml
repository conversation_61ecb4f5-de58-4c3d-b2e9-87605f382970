<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.TouristDevicePhoneMapper">
  <resultMap id="BaseResultMap" type="com.lj.auth.domain.TouristDevicePhone">
    <!--@mbg.generated-->
    <!--@Table lj_auth_tourist_device_phone-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="phone_number" jdbcType="VARCHAR" property="phoneNumber" />
    <result column="meid" jdbcType="VARCHAR" property="meid" />
    <result column="source" jdbcType="INTEGER" property="source" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, phone_number, meid, `source`
  </sql>


</mapper>