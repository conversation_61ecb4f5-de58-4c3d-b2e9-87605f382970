<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.NicknameLibraryMapper">
  <resultMap id="BaseResultMap" type="com.lj.auth.domain.NicknameLibrary">
    <!--@mbg.generated-->
    <!--@Table lj_auth_nickname_library-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="nick_name" jdbcType="VARCHAR" property="nickName" />
    <result column="is_user" jdbcType="INTEGER" property="isUser" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, create_time, update_time, nick_name, is_user
  </sql>
</mapper>