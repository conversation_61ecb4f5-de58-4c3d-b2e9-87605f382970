<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.RegionMapper">
  <resultMap id="BaseResultMap" type="com.lj.auth.domain.Region">
    <!--@mbg.generated-->
    <!--@Table region-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="ad_code" jdbcType="VARCHAR" property="adCode" />
    <result column="center" jdbcType="VARCHAR" property="center" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="level" jdbcType="VARCHAR" property="level" />
    <result column="grade" jdbcType="INTEGER" property="grade" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="full_spell" jdbcType="VARCHAR" property="fullSpell" />
    <result column="spell" jdbcType="VARCHAR" property="spell" />
    <result column="order_num" jdbcType="INTEGER" property="orderNum" />
    <result column="parent_id" jdbcType="BIGINT" property="parentId" />
    <result column="path" jdbcType="VARCHAR" property="path" />
    <result column="is_select" jdbcType="BOOLEAN" property="isSelect" />
    <result column="is_host" jdbcType="BOOLEAN" property="isHost" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="is_enable" jdbcType="BOOLEAN" property="isEnable" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, ad_code, center, city_code, `level`, grade, `name`, full_spell, spell, order_num, 
    parent_id, `path`, is_select, is_host, `status`, is_enable, create_time, update_time
  </sql>
</mapper>