<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.VoucherAccreditMapper">
  <resultMap id="BaseResultMap" type="com.lj.auth.domain.VoucherAccredit">
    <!--@mbg.generated-->
    <!--@Table ym_voucher_accredit-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid" />
    <result column="operate_uuid" jdbcType="VARCHAR" property="operateUuid" />
    <result column="vc_id" jdbcType="VARCHAR" property="vcId" />
    <result column="logo" jdbcType="VARCHAR" property="logo" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="del_state" jdbcType="INTEGER" property="delState" />
    <result column="type" jdbcType="INTEGER" property="type" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, account_uuid, operate_uuid, vc_id, logo, title, org_name, create_time, del_state, 
    `type`
  </sql>
    <select id="queryAccreditCount" resultType="java.lang.Integer">
      select count(1) from ym_voucher_accredit where account_uuid = #{uuid} and del_state=1
      <if test="type != null and type != 0">
        <choose>
          <when test="type == 1">
            and (code_type = 1 or code_type = 3)
          </when>
          <when test="type == 2">
            and code_type = 2
          </when>
        </choose>
      </if>
    </select>
  <select id="queryAccreditPage" resultType="com.lj.auth.domain.vo.VoucherAccreditVo">
    select * from ym_voucher_accredit where account_uuid = #{uuid} and del_state=1
    <if test="type != null and type != 0">
      <choose>
        <when test="type == 1">
          and (code_type = 1 or code_type = 3)
        </when>
        <when test="type == 2">
          and code_type = 2
        </when>
      </choose>
    </if>
    order by create_time desc
    limit #{page},#{pageSize}

  </select>
</mapper>