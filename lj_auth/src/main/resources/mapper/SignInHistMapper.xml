<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.SignInHistMapper">
    <resultMap id="BaseResultMap" type="com.lj.auth.domain.SignInHist">
        <!--@mbg.generated-->
        <!--@Table lj_auth_sign_in_hist-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="address" jdbcType="VARCHAR" property="address"/>
        <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid"/>
        <result column="sign_in_date" jdbcType="TIMESTAMP" property="signInDate"/>
        <result column="reward_money" jdbcType="INTEGER" property="rewardMoney"/>
        <result column="continuite_day" jdbcType="INTEGER" property="continuiteDay"/>
        <result column="hash" jdbcType="VARCHAR" property="hash"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <select id="getChainAsses" resultType="com.lj.auth.domain.vo.ChainAssetsVO">
        SELECT a.real_name, a.phone_number, h.*
        FROM (
                 SELECT account_uuid, address, sum(reward_money) as gas
                 FROM lj_auth_sign_in_hist
                 group by account_uuid, address
             ) h
                 left join account a
                           on a.uuid = h.account_uuid
        order by h.gas desc
    </select>
</mapper>