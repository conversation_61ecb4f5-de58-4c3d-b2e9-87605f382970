<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.IpfsImageMapper">
  <resultMap id="BaseResultMap" type="com.lj.auth.domain.IpfsImage">
    <!--@mbg.generated-->
    <!--@Table ym_ipfs_image-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="cid" jdbcType="VARCHAR" property="cid" />
    <result column="state" jdbcType="INTEGER" property="state" />
    <result column="domain" jdbcType="VARCHAR" property="domain" />
    <result column="token_id" jdbcType="INTEGER" property="tokenId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="contract_address" jdbcType="VARCHAR" property="contractAddress" />
    <result column="index" jdbcType="INTEGER" property="index" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, cid, `state`, `domain`, token_id, create_time, update_time, contract_address, 
    `index`
  </sql>

  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update ym_ipfs_image
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="cid = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.cid != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.cid,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`state` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.state != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.state,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="`domain` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.domain != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.domain,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`contract_address` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.contractAddress != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.contractAddress,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="token_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.tokenId != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.tokenId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createTime != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.createTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateTime != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.updateTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=INTEGER}
    </foreach>
  </update>


  <select id="countByContractAndTokenIds" resultType="int">
    select count(1) from ym_ipfs_image
    where contract_address = #{contractAddress,jdbcType=VARCHAR}
    and token_id in
    <foreach collection="tokenIds" item="tokenId" open="(" close=")" separator=",">
      #{tokenId,jdbcType=INTEGER}
    </foreach>
    </select>

  <select id="queryByDomains" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from ym_ipfs_image
    where `domain` in
    <foreach collection="domains" item="domain" open="(" close=")" separator=",">
      #{domain,jdbcType=VARCHAR}
    </foreach>
  </select>


  <!--根据域名和合约地址查询-->
  <select id="queryByDomainContract" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from ym_ipfs_image
    where `domain` = #{domain,jdbcType=VARCHAR}
    and contract_address = #{contractAddress,jdbcType=VARCHAR}
    <if test="tokenId != null">
      and token_id = #{tokenId,jdbcType=INTEGER}
    </if>
    <if test="state != null">
      and `state` = #{state,jdbcType=INTEGER}
    </if>
  </select>

  <select id="queryByContractAndTokenIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from ym_ipfs_image
    where contract_address = #{contractAddress,jdbcType=VARCHAR}
    and token_id in
    <foreach collection="tokenIds" item="tokenId" open="(" close=")" separator=",">
      #{tokenId,jdbcType=INTEGER}
    </foreach>
  </select>

  <select id="queryByDomainsAndContract" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from ym_ipfs_image
    where `domain` in
    <foreach collection="domains" item="domain" open="(" close=")" separator=",">
      #{domain,jdbcType=VARCHAR}
    </foreach>
    and contract_address = #{contractAddress,jdbcType=VARCHAR}
    <if test="state != null">
      and `state` = #{state,jdbcType=INTEGER}
    </if>
  </select>

  <select id="getMaxTokenId" resultType="int">
    SELECT COALESCE(MAX(token_id), 0)
    FROM ym_ipfs_image
    WHERE contract_address = #{contractAddress}
    <if test="state != null">
      and `state` = #{state,jdbcType=INTEGER}
    </if>
  </select>

  <select id="countByContractAndTokenId" resultType="java.lang.Boolean">
    SELECT COUNT(1)
    FROM ym_ipfs_image
    WHERE contract_address = #{contractAddress}
    AND token_id = #{tokenId}
    </select>

  <select id="queryByStateAndContract" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from ym_ipfs_image
    where `state` = #{state,jdbcType=INTEGER}
    and contract_address = #{contractAddress,jdbcType=VARCHAR}
    limit #{limit}
  </select>

  <select id="queryOneByStateAndContract" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from ym_ipfs_image
    where `state` = #{state,jdbcType=INTEGER}
    and contract_address = #{contractAddress,jdbcType=VARCHAR}
    limit 1
  </select>
</mapper>