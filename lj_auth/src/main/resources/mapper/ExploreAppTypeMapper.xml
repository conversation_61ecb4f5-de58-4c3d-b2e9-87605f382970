<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.ExploreAppTypeMapper">
  <resultMap id="BaseResultMap" type="com.lj.auth.domain.ExploreAppType">
    <!--@mbg.generated-->
    <!--@Table lj_auth_explore_app_type-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="type_name" jdbcType="VARCHAR" property="typeName" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="state" jdbcType="BOOLEAN" property="state" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, type_name, sort, `state`
  </sql>

  <select id="getExploreAppTypeStatistics" resultMap="BaseResultMap">

    SELECT t.*, COALESCE(a.count, 0) as count
    FROM lj_auth_explore_app_type t
           LEFT JOIN
         (
           SELECT app_type_id, count(*) as count
           FROM lj_auth_explore_app
           GROUP BY app_type_id
         ) a
         ON t.id = a.app_type_id
    where t.state = 1
    </select>
</mapper>