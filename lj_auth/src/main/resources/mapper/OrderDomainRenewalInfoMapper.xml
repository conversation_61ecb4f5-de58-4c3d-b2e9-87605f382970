<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.OrderDomainRenewalInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.lj.auth.domain.OrderDomainRenewalInfo">
        <id column="id" property="id" />
        <result column="order_id" property="orderId" />
        <result column="account_uuid" property="accountUuid" />
        <result column="tran_number" property="tranNumber" />
        <result column="domain" property="domain" />
        <result column="duration" property="duration" />
        <result column="amount" property="amount" />
        <result column="discount_amount" property="discountAmount" />
        <result column="original_amount" property="originalAmount" />
        <result column="unit_price" property="unitPrice" />
        <result column="pay_type" property="payType" />
        <result column="refund_state" property="refundState" />
        <result column="renewal_state" property="renewalState" />
        <result column="tran_hash" property="tranHash" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="domain_server_sn" property="domainServerSn" />
        <result column="expirationTime" property="expirationTime" />
        <result column="failReason" property="failReason" />
        <result column="level" property="level" />
        <result column="status" property="status" />
    </resultMap>

</mapper>
