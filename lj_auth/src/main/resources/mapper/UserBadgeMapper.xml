<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.UserBadgeMapper">
    <resultMap id="BaseResultMap" type="com.lj.auth.domain.UserBadge">
        <!--@mbg.generated-->
        <!--@Table lj_user_badge-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid"/>
        <result column="badge_id" jdbcType="BIGINT" property="badgeId"/>
        <result column="state" jdbcType="INTEGER" property="state"/>
        <result column="wear_flag" jdbcType="INTEGER" property="wearFlag"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        account_uuid,
        badge_id,
        `state`,
        wear_flag,
        create_time,
        update_time
    </sql>

    <select id="getUserBadge" resultType="com.lj.auth.domain.vo.BadgeVo">
        select lub.*,
        lb.name as badgeName,
        lb.type as badgeType,
        lb.default_image
        from lj_user_badge lub
                 left join lj_badge lb on lub.badge_id = lb.id
        where lub.account_uuid = #{uuid}
        order by lub.create_time desc limit 3
    </select>
</mapper>