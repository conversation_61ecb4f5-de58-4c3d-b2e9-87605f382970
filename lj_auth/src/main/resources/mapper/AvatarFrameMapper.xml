<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.AvatarFrameMapper">
  <resultMap id="BaseResultMap" type="com.lj.auth.domain.AvatarFrame">
    <!--@mbg.generated-->
    <!--@Table lj_avatar_frame-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="default_image" jdbcType="VARCHAR" property="defaultImage" />
    <result column="show_flag" jdbcType="INTEGER" property="showFlag" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="unlock_instructions" jdbcType="VARCHAR" property="unlockInstructions" />
    <result column="jump_url" jdbcType="VARCHAR" property="jumpUrl" />
    <result column="jump_type" jdbcType="INTEGER" property="jumpType" />
    <result column="is_hidden_nav" jdbcType="INTEGER" property="isHiddenNav" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, `name`, `type`, default_image, show_flag, create_time, update_time, unlock_instructions, 
    jump_url, jump_type, is_hidden_nav
  </sql>

  <select id="selectUserAvatarFrame" resultType="com.lj.auth.domain.vo.AvatarFrameVo">
    select * from lj_avatar_frame  laf
    left  join  (select * from lj_user_avatar_frame where account_uuid=#{uuid} ) luaf
    on  laf.id=luaf.avatar_frame_id
    where laf.show_flag=1
    order  by  laf.create_time desc
    </select>
</mapper>