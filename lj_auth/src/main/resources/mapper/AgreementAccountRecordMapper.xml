<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.AgreementAccountRecordMapper">
  <resultMap id="BaseResultMap" type="com.lj.auth.domain.AgreementAccountRecord">
    <!--@mbg.generated-->
    <!--@Table lj_agreement_account_record-->
    <id column="record_id" jdbcType="BIGINT" property="recordId" />
    <result column="version_id" jdbcType="INTEGER" property="versionId" />
    <result column="account_uuid" jdbcType="CHAR" property="accountUuid" />
    <result column="confirm_content" jdbcType="VARCHAR" property="confirmContent" />
    <result column="read_and_confim_time" jdbcType="TIMESTAMP" property="readAndConfimTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    record_id, version_id, account_uuid, confirm_content, read_and_confim_time, update_time
  </sql>

  <select id="queryByVersionIdAndAccountUUID" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from lj_agreement_account_record
    where version_id=#{agreementVersionId}
    and account_uuid = #{accountUUID}
    </select>

  <select id="queryLatestVersionByAccountUUID" resultType="com.lj.auth.domain.AgreementAccountRecord">
    select
    <include refid="Base_Column_List" />
    from lj_agreement_account_record
    where account_uuid = #{accountUUID}
    order by version_id desc
    limit 1
    </select>


  <select id="queryLatestRecordVoByAccountUUIDAndType" resultType="com.lj.auth.domain.vo.AgreementAccountRecordVo">
    select
    *
    from lj_agreement_account_record as record
    left join lj_agreement_version as version
    on `record`.version_id = version.version_id
    where record.account_uuid = #{accountUUID}
    and version.agreement_type= #{agreementType}
    order by version.version_number desc
    limit 1
  </select>

  <select id="pageQueryByAccountAndAgreementType" resultType="com.lj.auth.domain.vo.AgreementAccountRecordVo">
    select
    *
    from lj_agreement_account_record as record
    left join lj_agreement_version as version
    on `record`.version_id = version.version_id
    where record.account_uuid = #{accountUUID}
    <if test="agreementType != null">
      and version.agreement_type= #{agreementType}
    </if>

    <if test="agreementVersionId != null">
      and version.version_id= #{agreementVersionId}
    </if>
    order by version.version_number desc
  </select>
</mapper>