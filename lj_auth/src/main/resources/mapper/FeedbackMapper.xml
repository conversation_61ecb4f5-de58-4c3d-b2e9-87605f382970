<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.FeedbackMapper">
  <resultMap id="BaseResultMap" type="com.lj.auth.domain.Feedback">
    <!--@mbg.generated-->
    <!--@Table lj_auth_feedback-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="content" jdbcType="LONGVARCHAR" property="content" />
    <result column="reply_content" jdbcType="LONGVARCHAR" property="replyContent" />
    <result column="images" jdbcType="VARCHAR" property="images" />
    <result column="state" jdbcType="INTEGER" property="state" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, account_uuid, title, content, reply_content, images, `state`, create_time, update_time
  </sql>
</mapper>