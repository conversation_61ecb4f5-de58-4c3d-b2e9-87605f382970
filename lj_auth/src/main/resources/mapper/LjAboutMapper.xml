<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.LjAboutMapper">
  <resultMap id="BaseResultMap" type="com.lj.auth.domain.LjAbout">
    <!--@mbg.generated-->
    <!--@Table lj_auth_about-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="logo" jdbcType="VARCHAR" property="logo" />
    <result column="system_name" jdbcType="VARCHAR" property="systemName" />
    <result column="current_version" jdbcType="VARCHAR" property="currentVersion" />
    <result column="website" jdbcType="VARCHAR" property="website" />
    <result column="weChat_account" jdbcType="VARCHAR" property="wechatAccount" />
    <result column="email" jdbcType="VARCHAR" property="email" />
    <result column="icp_text" jdbcType="VARCHAR" property="icpText" />
    <result column="icp_link" jdbcType="VARCHAR" property="icpLink" />
    <result column="copyright" jdbcType="VARCHAR" property="copyright" />
    <result column="user_agreement" jdbcType="LONGVARCHAR" property="userAgreement" />
    <result column="privacy_agreement" jdbcType="LONGVARCHAR" property="privacyAgreement" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="operate_uuid" jdbcType="VARCHAR" property="operateUuid" />
    <result column="public_icon" jdbcType="VARCHAR" property="publicIcon" />
    <result column="public_text" jdbcType="VARCHAR" property="publicText" />
    <result column="public_link" jdbcType="VARCHAR" property="publicLink" />
    <result column="browser_icon" jdbcType="VARCHAR" property="browserIcon" />
    <result column="browser_portal_title" jdbcType="VARCHAR" property="browserPortalTitle" />
    <result column="brower_operate_title" jdbcType="VARCHAR" property="browerOperateTitle" />
    <result column="register_agreement" jdbcType="LONGVARCHAR" property="registerAgreement" />
    <result column="uuid_prefix" jdbcType="VARCHAR" property="uuidPrefix" />
    <result column="authorization_certificate" jdbcType="VARCHAR" property="authorizationCertificate" />
    <result column="enterprise_name" jdbcType="VARCHAR" property="enterpriseName" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, logo, system_name, current_version, website, weChat_account, email, icp_text, 
    icp_link, copyright, user_agreement, privacy_agreement, create_time, update_time, 
    operate_uuid, public_icon, public_text, public_link, browser_icon, browser_portal_title, 
    brower_operate_title, register_agreement, uuid_prefix, authorization_certificate, 
    enterprise_name
  </sql>
</mapper>