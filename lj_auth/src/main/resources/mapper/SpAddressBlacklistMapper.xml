<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.SpAddressBlacklistMapper">
  <resultMap id="BaseResultMap" type="com.lj.auth.entity.SpAddressBlacklist">
    <!--@mbg.generated-->
    <!--@Table sp_address_blacklist-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="avtive" jdbcType="INTEGER" property="avtive" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, address, reason, avtive, create_time, update_time
  </sql>

  <select id="isBlacklist" resultType="java.lang.Boolean">
    select
    count(1)
    from  sp_address_blacklist
    where address = #{address}
    and avtive = 1
    </select>
</mapper>