<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.NftManualDisposeMapper">
  <resultMap id="BaseResultMap" type="com.lj.auth.domain.NftManualDispose">
    <!--@mbg.generated-->
    <!--@Table lj_auth_nft_manual_dispose-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="nft_record_id" jdbcType="BIGINT" property="nftRecordId" />
    <result column="opb_chain_id" jdbcType="INTEGER" property="opbChainId" />
    <result column="uuid" jdbcType="VARCHAR" property="uuid" />
    <result column="chain_account_address" jdbcType="VARCHAR" property="chainAccountAddress" />
    <result column="contract_address" jdbcType="VARCHAR" property="contractAddress" />
    <result column="nft_count" jdbcType="INTEGER" property="nftCount" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="nft_record_domain_id" jdbcType="BIGINT" property="nftRecordDomainId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, nft_record_id, opb_chain_id, uuid, chain_account_address, contract_address, nft_count, 
    create_time, `status`, nft_record_domain_id
  </sql>

  <select id="queryByNftRecordId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from lj_auth_nft_manual_dispose
    where nft_record_id = #{nftRecordId}
  </select>
</mapper>