<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.DidQrCodeRecordMapper">
    <resultMap id="BaseResultMap" type="com.lj.auth.domain.DidQrCodeRecord">
        <!--@mbg.generated-->
        <!--@Table lj_auth_did_qr_code_record-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="application_id" jdbcType="INTEGER" property="applicationId"/>
        <result column="expiration_time" jdbcType="TIMESTAMP" property="expirationTime"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        application_id,
        expiration_time,
        create_time
    </sql>
    <select id="selectDIDUserInfo" resultType="com.lj.auth.domain.vo.QrVo">
        SELECT uuid,
               a.did_symbol,
               head_portrait,
               nick_name,
               show_type,
               head_portrait_type,
               domain_nick_name,
               head_portrait_nft_id,
               DATEDIFF(NOW(), d.create_time) AS days_difference,
               n.nft_image
        FROM account a
                 left join ym_account_did d
                           on a.did_symbol = d.did_symbol
                 left join lj_auth_nft n
                           on n.id = head_portrait_nft_id
        where a.did_symbol = #{didsymbol}
    </select>
</mapper>