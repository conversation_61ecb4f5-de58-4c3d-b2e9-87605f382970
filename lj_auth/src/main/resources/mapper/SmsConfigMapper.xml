<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.SmsConfigMapper">
  <resultMap id="BaseResultMap" type="com.lj.auth.domain.SmsConfig">
    <!--@mbg.generated-->
    <!--@Table lj_auth_sms_config-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="switchs" jdbcType="BOOLEAN" property="switchs" />
    <result column="product" jdbcType="VARCHAR" property="product" />
    <result column="domain" jdbcType="VARCHAR" property="domain" />
    <result column="accessKeyId" jdbcType="VARCHAR" property="accesskeyid" />
    <result column="accessKeySecret" jdbcType="VARCHAR" property="accesskeysecret" />
    <result column="signName" jdbcType="VARCHAR" property="signname" />
    <result column="templateCode" jdbcType="VARCHAR" property="templatecode" />
    <result column="variable" jdbcType="VARCHAR" property="variable" />
    <result column="operate_uuid" jdbcType="VARCHAR" property="operateUuid" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="smsServiceProvider" jdbcType="VARCHAR" property="smsserviceprovider" />
    <result column="appid" jdbcType="VARCHAR" property="appid" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, switchs, product, `domain`, accessKeyId, accessKeySecret, signName, templateCode, 
    `variable`, operate_uuid, `type`, smsServiceProvider, appid
  </sql>

  <select id="queryByOperateUUIDAndType" resultType="com.lj.auth.domain.SmsConfig">
    select
    <include refid="Base_Column_List" />
    from lj_auth_sms_config
    where operate_uuid = #{operateUUID}
    and type = #{type}
    and smsServiceProvider = #{smsServiceProvider}
    and switchs = 1
  </select>
</mapper>