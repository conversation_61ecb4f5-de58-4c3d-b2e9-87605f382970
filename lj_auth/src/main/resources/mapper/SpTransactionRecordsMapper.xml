<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.SpTransactionRecordsMapper">
  <resultMap id="BaseResultMap" type="com.lj.auth.entity.SpTransactionRecords">
    <!--@mbg.generated-->
    <!--@Table sp_transaction_records-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="chain_id" jdbcType="BIGINT" property="chainId" />
    <result column="tx_hash" jdbcType="VARCHAR" property="txHash" />
    <result column="from_address" jdbcType="VARCHAR" property="fromAddress" />
    <result column="to_address" jdbcType="VARCHAR" property="toAddress" />
    <result column="symbol" jdbcType="VARCHAR" property="symbol" />
    <result column="contract_address" jdbcType="VARCHAR" property="contractAddress" />
    <result column="amount" jdbcType="DECIMAL" property="amount" />
    <result column="tx_type" jdbcType="TINYINT" property="txType" />
    <result column="gas_fee" jdbcType="DECIMAL" property="gasFee" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="block_number" jdbcType="BIGINT" property="blockNumber" />
    <result column="transaction_time" jdbcType="VARCHAR" property="transactionTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, chain_id, tx_hash, from_address, to_address, symbol, contract_address, amount, 
    tx_type, gas_fee, `status`, block_number, transaction_time, create_time, update_time
  </sql>
</mapper>