<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.VcCheckInActivityMapper">
  <resultMap id="BaseResultMap" type="com.lj.auth.domain.VcCheckInActivity">
    <!--@mbg.generated-->
    <!--@Table vc_check_in_activity-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, `name`
  </sql>
</mapper>