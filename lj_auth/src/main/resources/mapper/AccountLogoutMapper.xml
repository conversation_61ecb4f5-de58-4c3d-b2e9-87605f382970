<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.AccountLogoutMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.lj.auth.domain.AccountLogout">
        <id column="id" property="id"/>
        <result column="phone_number" property="phoneNumber"/>
        <result column="uuid" property="uuid"/>
        <result column="password" property="password"/>
        <result column="pay_password" property="payPassword"/>
        <result column="head_portrait" property="headPortrait"/>
        <result column="nick_name" property="nickName"/>
        <result column="register_ip" property="registerIp"/>
        <result column="register_time" property="registerTime"/>
        <result column="recent_login_ip" property="recentLoginIp"/>
        <result column="recent_login_time" property="recentLoginTime"/>
        <result column="is_real_name" property="isRealName"/>
        <result column="blacklist" property="blacklist"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="operate_uuid" property="operateUuid"/>
        <result column="real_name" property="realName"/>
        <result column="id_card" property="idCard"/>
        <result column="identity" property="identity"/>
        <result column="uuid_suffix" property="uuidSuffix"/>
        <result column="parent_uuid" property="parentUuid"/>
        <result column="is_banned" property="isBanned"/>
        <result column="commission_ratio" property="commissionRatio"/>
        <result column="douyin_openid" property="douyinOpenid"/>
        <result column="weixin_openid" property="weixinOpenid"/>
        <result column="promotion_level" property="promotionLevel"/>
        <result column="promotion_rebate_state" property="promotionRebateState"/>
        <result column="active_flag" property="activeFlag"/>
        <result column="did_symbol" property="didSymbol"/>
        <result column="people_id" property="peopleId"/>
        <result column="sign_status" property="signStatus"/>
        <result column="sign_url" property="signUrl"/>
        <result column="invite_code" property="inviteCode"/>
        <result column="head_portrait_type" property="headPortraitType"/>
        <result column="head_portrait_nft_id" property="headPortraitNftId"/>
        <result column="show_type" property="showType"/>
        <result column="domain_nick_name" property="domainNickName"/>
        <result column="registration_source" property="registrationSource"/>
        <result column="reason_id" property="reasonId"/>
        <result column="reason_self" property="reasonSelf"/>
    </resultMap>
    <select id="selectOperateAccountUUID" resultType="java.lang.String">

        SELECT a.uuid
        FROM ym.ym_operate o
                 left join ym.account a
                           on o.phone_number = a.phone_number
        where 1 = 1
          and o.account_uuid = #{operateUuid}
    </select>

</mapper>
