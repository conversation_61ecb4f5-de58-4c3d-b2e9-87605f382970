<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.OperationRecordsMapper">
  <resultMap id="BaseResultMap" type="com.lj.auth.domain.OperationRecords">
    <!--@mbg.generated-->
    <!--@Table lj_auth_operation_records-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="account" jdbcType="VARCHAR" property="account" />
    <result column="login_ip" jdbcType="VARCHAR" property="loginIp" />
    <result column="login_time" jdbcType="TIMESTAMP" property="loginTime" />
    <result column="state" jdbcType="INTEGER" property="state" />
    <result column="request_param" jdbcType="LONGVARCHAR" property="requestParam" />
    <result column="request_interface" jdbcType="VARCHAR" property="requestInterface" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, account, login_ip, login_time, `state`, request_param, request_interface
  </sql>
</mapper>