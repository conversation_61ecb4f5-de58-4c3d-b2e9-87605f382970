<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.SystemNoticeMapper">
  <resultMap id="BaseResultMap" type="com.lj.auth.domain.SystemNotice">
    <!--@mbg.generated-->
    <!--@Table lj_auth_system_notice-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="content" jdbcType="LONGVARCHAR" property="content" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="source" jdbcType="INTEGER" property="source" />
    <result column="reminder_start_time" jdbcType="TIMESTAMP" property="reminderStartTime" />
    <result column="reminder_end_time" jdbcType="TIMESTAMP" property="reminderEndTime" />
    <result column="frequency" jdbcType="INTEGER" property="frequency" />
    <result column="jump_path" jdbcType="VARCHAR" property="jumpPath" />
    <result column="jump_type" jdbcType="INTEGER" property="jumpType" />
    <result column="jump_url" jdbcType="VARCHAR" property="jumpUrl" />
    <result column="is_hidden_nav" jdbcType="INTEGER" property="isHiddenNav" />
    <result column="channel" jdbcType="VARCHAR" property="channel" />
    <result column="edition" jdbcType="VARCHAR" property="edition" />
    <result column="is_appoint" jdbcType="INTEGER" property="isAppoint" />
    <result column="jump_param" jdbcType="VARCHAR" property="jumpParam" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, title, description, content, create_time, `type`, `source`, reminder_start_time, 
    reminder_end_time, frequency, jump_path, jump_type, jump_url, is_hidden_nav, channel, 
    edition, is_appoint, jump_param
  </sql>
</mapper>