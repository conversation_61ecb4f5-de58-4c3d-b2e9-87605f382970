<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.SignInMapper">
  <resultMap id="BaseResultMap" type="com.lj.auth.domain.SignIn">
    <!--@mbg.generated-->
    <!--@Table lj_auth_sign_in-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid" />
    <result column="sign_in_date" jdbcType="TIMESTAMP" property="signInDate" />
    <result column="reward_money" jdbcType="INTEGER" property="rewardMoney" />
    <result column="continuite_day" jdbcType="INTEGER" property="continuiteDay" />
    <result column="hash" jdbcType="VARCHAR" property="hash" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, address, account_uuid, sign_in_date, reward_money, continuite_day, hash, create_time, 
    update_time
  </sql>
</mapper>