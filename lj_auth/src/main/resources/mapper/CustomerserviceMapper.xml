<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.CustomerserviceMapper">
  <resultMap id="BaseResultMap" type="com.lj.auth.domain.Customerservice">
    <!--@mbg.generated-->
    <!--@Table lj_auth_customerservice-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="weixin" jdbcType="VARCHAR" property="weixin" />
    <result column="qq" jdbcType="VARCHAR" property="qq" />
    <result column="email" jdbcType="VARCHAR" property="email" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="qrCode" jdbcType="VARCHAR" property="qrcode" />
    <result column="qrCode1" jdbcType="VARCHAR" property="qrcode1" />
    <result column="time" jdbcType="VARCHAR" property="time" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, weixin, qq, email, phone, qrCode, qrCode1, `time`
  </sql>

</mapper>