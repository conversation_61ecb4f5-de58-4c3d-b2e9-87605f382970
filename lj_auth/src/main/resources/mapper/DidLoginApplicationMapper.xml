<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.DidLoginApplicationMapper">
  <resultMap id="BaseResultMap" type="com.lj.auth.domain.DidLoginApplication">
    <!--@mbg.generated-->
    <!--@Table lj_auth_did_login_application-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="application_id" jdbcType="INTEGER" property="applicationId" />
    <result column="application_name" jdbcType="VARCHAR" property="applicationName" />
    <result column="website" jdbcType="VARCHAR" property="website" />
    <result column="application_introduce" jdbcType="VARCHAR" property="applicationIntroduce" />
    <result column="state" jdbcType="INTEGER" property="state" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, application_id, application_name, website, application_introduce, `state`
  </sql>
</mapper>