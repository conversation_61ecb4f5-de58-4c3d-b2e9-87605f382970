<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.ContributionMapper">
    <resultMap id="BaseResultMap" type="com.lj.auth.domain.Contribution">
        <!--@mbg.generated-->
        <!--@Table lj_auth_contribution-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid"/>
        <result column="amount" jdbcType="DECIMAL" property="amount"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        account_uuid,
        amount,
        update_time
    </sql>
    <select id="selectRankList" resultType="com.lj.auth.domain.Contribution">
        select d.*, t.nft_image
        from (select c3.*,
                     a.nick_name,
                     a.domain_nick_name,
                     a.show_type,
                     a.head_portrait,
                     a.head_portrait_nft_id,
                     a.head_portrait_type,
                     a.did_symbol,
                     a.badge_image,
                     a.avatar_frame_image
              from (
                          SELECT @row_number := @row_number + 1 AS ranking, t.*
                          FROM (SELECT * FROM lj_auth_contribution ORDER BY amount DESC) t,
                               (SELECT @row_number := 0) r
                      ) c3
                          left join account a
                                    on
                                        a.uuid = c3.account_uuid
             ) d
                 left join lj_auth_nft t
                           on t.id = d.head_portrait_nft_id

        order by d.ranking asc
        limit #{page},#{pageSize}
    </select>

    <select id="selectMyRankList" resultType="com.lj.auth.domain.vo.ContributionVo">
        select d.*, t.nft_image
        from (
                 select c3.*,
                        a.nick_name,
                        a.domain_nick_name,
                        a.show_type,
                        a.head_portrait,
                        a.head_portrait_nft_id,
                        a.head_portrait_type
                 from (select *
                       from (
                                SELECT @row_number := @row_number + 1 AS ranking, t.*
                                FROM (SELECT * FROM lj_auth_contribution ORDER BY amount DESC) t,
                                     (SELECT @row_number := 0) r
                            ) c1
                       where c1.account_uuid = #{uuid}
                      ) c3
                          left join account a
                                    on
                                        a.uuid = c3.account_uuid
             ) d

                 left join lj_auth_nft t
                           on t.id = d.head_portrait_nft_id
    </select>

    <select id="selectUserInfoList" resultType="com.lj.auth.domain.vo.ContributionVo">
        select a.uuid as accountUuid,
               a.nick_name,
               a.domain_nick_name,
               a.show_type,
               a.head_portrait,
               a.head_portrait_nft_id,
               a.head_portrait_type,
               t.nft_image
        from account a
                 left join lj_auth_nft t
                           on t.id = a.head_portrait_nft_id
        where a.uuid = #{uuid}
    </select>

    <select id="sumAmount" resultType="java.math.BigDecimal">
        SELECT IFNULL(SUM(amount), 0) AS total_amount
        FROM lj_auth_contribution
    </select>
</mapper>