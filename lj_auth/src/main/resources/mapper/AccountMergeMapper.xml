<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.AccountMergeMapper">
  <resultMap id="BaseResultMap" type="com.lj.auth.domain.AccountMerge">
    <!--@mbg.generated-->
    <!--@Table account_merge-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="phone_number" jdbcType="VARCHAR" property="phoneNumber" />
    <result column="uuid" jdbcType="VARCHAR" property="uuid" />
    <result column="password" jdbcType="VARCHAR" property="password" />
    <result column="pay_password" jdbcType="VARCHAR" property="payPassword" />
    <result column="head_portrait" jdbcType="VARCHAR" property="headPortrait" />
    <result column="nick_name" jdbcType="VARCHAR" property="nickName" />
    <result column="register_ip" jdbcType="VARCHAR" property="registerIp" />
    <result column="register_time" jdbcType="TIMESTAMP" property="registerTime" />
    <result column="recent_login_ip" jdbcType="VARCHAR" property="recentLoginIp" />
    <result column="recent_login_time" jdbcType="TIMESTAMP" property="recentLoginTime" />
    <result column="is_real_name" jdbcType="INTEGER" property="isRealName" />
    <result column="blacklist" jdbcType="INTEGER" property="blacklist" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="operate_uuid" jdbcType="VARCHAR" property="operateUuid" />
    <result column="real_name" jdbcType="VARCHAR" property="realName" />
    <result column="id_card" jdbcType="VARCHAR" property="idCard" />
    <result column="identity" jdbcType="INTEGER" property="identity" />
    <result column="uuid_suffix" jdbcType="VARCHAR" property="uuidSuffix" />
    <result column="parent_uuid" jdbcType="VARCHAR" property="parentUuid" />
    <result column="is_banned" jdbcType="BOOLEAN" property="isBanned" />
    <result column="commission_ratio" jdbcType="DECIMAL" property="commissionRatio" />
    <result column="douyin_openid" jdbcType="VARCHAR" property="douyinOpenid" />
    <result column="weixin_openid" jdbcType="VARCHAR" property="weixinOpenid" />
    <result column="promotion_level" jdbcType="INTEGER" property="promotionLevel" />
    <result column="promotion_rebate_state" jdbcType="INTEGER" property="promotionRebateState" />
    <result column="active_flag" jdbcType="BOOLEAN" property="activeFlag" />
    <result column="did_symbol" jdbcType="VARCHAR" property="didSymbol" />
    <result column="people_id" jdbcType="VARCHAR" property="peopleId" />
    <result column="sign_status" jdbcType="BOOLEAN" property="signStatus" />
    <result column="sign_url" jdbcType="VARCHAR" property="signUrl" />
    <result column="invite_code" jdbcType="VARCHAR" property="inviteCode" />
    <result column="head_portrait_type" jdbcType="INTEGER" property="headPortraitType" />
    <result column="head_portrait_nft_id" jdbcType="BIGINT" property="headPortraitNftId" />
    <result column="show_type" jdbcType="INTEGER" property="showType" />
    <result column="domain_nick_name" jdbcType="VARCHAR" property="domainNickName" />
    <result column="registration_source" jdbcType="INTEGER" property="registrationSource" />
    <result column="im_status" jdbcType="INTEGER" property="imStatus" />
    <result column="background_img" jdbcType="VARCHAR" property="backgroundImg" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, phone_number, uuid, `password`, pay_password, head_portrait, nick_name, register_ip, 
    register_time, recent_login_ip, recent_login_time, is_real_name, blacklist, create_time, 
    update_time, operate_uuid, real_name, id_card, `identity`, uuid_suffix, parent_uuid, 
    is_banned, commission_ratio, douyin_openid, weixin_openid, promotion_level, promotion_rebate_state, 
    active_flag, did_symbol, people_id, sign_status, sign_url, invite_code, head_portrait_type, 
    head_portrait_nft_id, show_type, domain_nick_name, registration_source, im_status, 
    background_img
  </sql>
</mapper>