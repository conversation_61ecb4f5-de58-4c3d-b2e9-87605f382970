<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.NftMapper">
  <resultMap id="BaseResultMap" type="com.lj.auth.domain.Nft">
    <!--@mbg.generated-->
    <!--@Table lj_auth_nft-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="opb_chain_id" jdbcType="INTEGER" property="opbChainId" />
    <result column="contract_id" jdbcType="BIGINT" property="contractId" />
    <result column="token_id" jdbcType="INTEGER" property="tokenId" />
    <result column="contract_address" jdbcType="VARCHAR" property="contractAddress" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="holder" jdbcType="VARCHAR" property="holder" />
    <result column="mint_hash" jdbcType="VARCHAR" property="mintHash" />
    <result column="mint_date" jdbcType="TIMESTAMP" property="mintDate" />
    <result column="nft_name" jdbcType="VARCHAR" property="nftName" />
    <result column="nft_type" jdbcType="TINYINT" property="nftType" />
    <result column="nft_image" jdbcType="VARCHAR" property="nftImage" />
    <result column="nft_describe" jdbcType="VARCHAR" property="nftDescribe" />
    <result column="nft_price" jdbcType="DECIMAL" property="nftPrice" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="nft_domain" jdbcType="VARCHAR" property="nftDomain" />
    <result column="is_delete" jdbcType="BOOLEAN" property="isDelete" />
    <result column="is_approve" jdbcType="BOOLEAN" property="isApprove" />
    <result column="approve_address" jdbcType="VARCHAR" property="approveAddress" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, create_time, update_time, opb_chain_id, contract_id, token_id, contract_address, 
    creator, holder, mint_hash, mint_date, nft_name, nft_type, nft_image, nft_describe, 
    nft_price, `status`, nft_domain, is_delete, is_approve, approve_address
  </sql>


  <!--分页查询链框架id和链账户地址查询NFT-->
  <select id="pageQueryByAddressAndStatus" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM lj_auth_nft
    where opb_chain_id = #{opbChainId}
    and nft_type = #{nftType}
    and holder = #{chainAccount}
    and status = #{status}
    <if test="isApprove != null">
      and is_approve = #{isApprove}
    </if>
    <if test="nftName != null">
      and nft_name like concat('%',#{nftName},'%')
    </if>
    and is_delete = 0
    <if test="orders != null and orders.size()&gt;0">
      order by
      <foreach collection="orders" item="order" separator=",">
        ${order}
      </foreach>
    </if>
    limit #{start}, #{limit}
    </select>


  <!--分页查询链框架id和链账户地址查询NFT总数-->
  <select id="totalPageQueryByAddressAndStatus" resultType="int">
    SELECT count(1)
    FROM lj_auth_nft
    where opb_chain_id = #{opbChainId}
    and nft_type = #{nftType}
    and holder = #{chainAccount}
    and status = #{status}
    <if test="isApprove != null">
      and is_approve = #{isApprove}
    </if>
    <if test="nftName != null">
      and nft_name like concat('%',#{nftName},'%')
    </if>
    and is_delete = 0
  </select>

  <select id="getMaxTokenId" resultType="int">
    SELECT COALESCE(MAX(token_id), 0)
    FROM lj_auth_nft
    WHERE contract_address = #{contractAddress}
    <if test="status != null">
      and `status` = #{status,jdbcType=INTEGER}
    </if>
    and is_delete = 0
  </select>



  <select id="queryByTokenId" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM lj_auth_nft
    where contract_address = #{contractAddress}
    and token_id = #{tokenId}
    <if test="status != null">
      and `status` = #{status,jdbcType=INTEGER}
    </if>

    <if test="isDelete != null">
      and is_delete = #{isDelete}
    </if>
    </select>

  <select id="pageQueryNftDetailsNFTList" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM lj_auth_nft
    where opb_chain_id = #{opbChainId}
    and contract_address = #{contractAddress}
    and nft_type = #{nftType}
    and status = #{status}
    <if test="nftName != null">
      and nft_name like concat('%',#{nftName},'%')
    </if>
    and is_delete = 0
    <if test="orders != null and orders.size()&gt;0">
      order by
      <foreach collection="orders" item="order" separator=",">
        ${order}
      </foreach>
    </if>
    limit #{start}, #{limit}
    </select>

  <select id="totalPageQueryNftDetailsNFTList" resultType="int">
    SELECT count(1)
    FROM lj_auth_nft
    where opb_chain_id = #{opbChainId}
    and contract_address = #{contractAddress}
    and nft_type = #{nftType}
    and status = #{status}
    <if test="nftName != null">
      and nft_name like concat('%',#{nftName},'%')
    </if>
    and is_delete = 0
  </select>

  <select id="queryOneByContractAndHolder" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from lj_auth_nft
    where contract_address = #{contractAddress}
    and holder = #{holder}
    and status = #{status}
    and is_delete = #{isDelete}
    limit 1
  </select>



  <select id="queryById" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM lj_auth_nft
    where id = #{id}
    and is_delete = 0
    </select>

  <select id="selectNftHolderVoPage" resultType="com.lj.auth.domain.vo.NftHolderVo">
    SELECT
      holder as address,
      COUNT(1) AS nftNum,
      CONCAT(
              ROUND(
                      COUNT(1) * 100.0 / (SELECT COUNT(*) FROM ym.lj_auth_nft  WHERE status=1 AND is_delete=0),
                      2),
              '%'
      ) AS accountFor
    FROM ym.lj_auth_nft
    WHERE status=1 AND is_delete=0
    GROUP BY holder
    ORDER BY nftNum DESC
  </select>
</mapper>