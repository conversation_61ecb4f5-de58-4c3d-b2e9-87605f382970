<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.ExploreAppRecentlyMapper">
    <resultMap id="BaseResultMap" type="com.lj.auth.domain.ExploreAppRecently">
        <!--@mbg.generated-->
        <!--@Table lj_auth_explore_app_recently-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="app_id" jdbcType="INTEGER" property="appId"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        account_uuid,
        create_time,
        app_id
    </sql>

    <select id="appRecentlyCount" resultType="java.lang.Integer">
        SELECT count(1)
        FROM lj_auth_explore_app_recently r
        where r.account_uuid = #{ uuid}
    </select>

    <select id="getAppRecentlyPage" resultType="com.lj.auth.domain.vo.ExploreAppRecentlyVo">
        SELECT *
        FROM lj_auth_explore_app_recently r
                 left join lj_auth_explore_app a
                           on r.app_id = a.id
        where r.account_uuid = #{ uuid}
        order by r.create_time desc
        limit #{page},#{pageSize}
    </select>
</mapper>