<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.AccountCardMapper">
  <resultMap id="BaseResultMap" type="com.lj.auth.domain.AccountCard">
    <!--@mbg.generated-->
    <!--@Table lj_auth_account_card-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="card_number" jdbcType="VARCHAR" property="cardNumber" />
    <result column="bank" jdbcType="VARCHAR" property="bank" />
    <result column="card_picture" jdbcType="VARCHAR" property="cardPicture" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="state" jdbcType="BOOLEAN" property="state" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, account_uuid, `name`, card_number, bank, card_picture, create_time, `state`
  </sql>
</mapper>