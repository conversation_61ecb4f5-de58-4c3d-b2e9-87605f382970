<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.AddresseeMessageMapper">
  <resultMap id="BaseResultMap" type="com.lj.auth.domain.AddresseeMessage">
    <!--@mbg.generated-->
    <!--@Table lj_auth_addressee_message-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="email" jdbcType="VARCHAR" property="email" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="state" jdbcType="INTEGER" property="state" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, account_uuid, `name`, phone, email, address, `state`, create_time
  </sql>

    <update id="updateAddressMessage">
      update lj_auth_addressee_message
      <set>
        <if test="name != null and name != ''">
          name = #{name},
        </if>
        <if test="address != null and address != ''">
          address = #{address},
        </if>
        <if test="email != null and email != ''">
          email = #{email},
        </if>
        <if test="phone != null and phone != ''">
          phone = #{phone}
        </if>
      </set>
      where id = #{id}
    </update>
</mapper>