<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.DidCheckInAccountMapper">
  <resultMap id="BaseResultMap" type="com.lj.auth.domain.DidCheckInAccount">
    <!--@mbg.generated-->
    <!--@Table did_check_in_account-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid" />
    <result column="did_symbol" jdbcType="VARCHAR" property="didSymbol" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="organizer_id" jdbcType="INTEGER" property="organizerId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, account_uuid, did_symbol, create_time, organizer_id
  </sql>
</mapper>