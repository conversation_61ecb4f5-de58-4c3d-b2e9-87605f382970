<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.SquareFollowMapper">
  <resultMap id="BaseResultMap" type="com.lj.auth.domain.SquareFollow">
    <!--@mbg.generated-->
    <!--@Table lj_square_follow-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid" />
    <result column="follow_uuid" jdbcType="VARCHAR" property="followUuid" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="read_flag" jdbcType="INTEGER" property="readFlag" />
    <result column="remove_flag" jdbcType="INTEGER" property="removeFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, account_uuid, follow_uuid, create_time, read_flag, remove_flag
  </sql>
</mapper>