<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.AccountInfoVoucherMapper">
  <resultMap id="BaseResultMap" type="com.lj.auth.domain.AccountInfoVoucher">
    <!--@mbg.generated-->
    <!--@Table ym_account_info_voucher-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="id_no" jdbcType="VARCHAR" property="idNo" />
    <result column="id_name" jdbcType="VARCHAR" property="idName" />
    <result column="vc_id" jdbcType="VARCHAR" property="vcId" />
    <result column="vc_enc" jdbcType="LONGVARCHAR" property="vcEnc" />
    <result column="template_id" jdbcType="VARCHAR" property="templateId" />
    <result column="template" jdbcType="LONGVARCHAR" property="template" />
    <result column="version" jdbcType="VARCHAR" property="version" />
    <result column="org_did" jdbcType="VARCHAR" property="orgDid" />
    <result column="vc_type" jdbcType="VARCHAR" property="vcType" />
    <result column="tx_id" jdbcType="VARCHAR" property="txId" />
    <result column="expiration_time" jdbcType="TIMESTAMP" property="expirationTime" />
    <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid" />
    <result column="operate_uuid" jdbcType="VARCHAR" property="operateUuid" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, id_no, id_name, vc_id, vc_enc, template_id, `template`, version, org_did, vc_type, 
    tx_id, expiration_time, account_uuid, operate_uuid, create_time, update_time
  </sql>
</mapper>