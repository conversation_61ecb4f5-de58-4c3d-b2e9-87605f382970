<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.OrderDomainRenewalMapper">
  <resultMap id="BaseResultMap" type="com.lj.auth.domain.OrderDomainRenewal">
    <!--@mbg.generated-->
    <!--@Table ym_order_domain_renewal-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid" />
    <result column="total_amount" jdbcType="DECIMAL" property="totalAmount" />
    <result column="nft_number" jdbcType="INTEGER" property="nftNumber" />
    <result column="tran_number" jdbcType="VARCHAR" property="tranNumber" />
    <result column="pay_type" jdbcType="INTEGER" property="payType" />
    <result column="pay_state" jdbcType="INTEGER" property="payState" />
    <result column="refund_state" jdbcType="INTEGER" property="refundState" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="domain_server_sn" jdbcType="VARCHAR" property="domainServerSn" />
    <result column="trade_code" jdbcType="VARCHAR" property="tradeCode" />
    <result column="platform_source" jdbcType="INTEGER" property="platformSource" />
    <result column="order_state" jdbcType="INTEGER" property="orderState" />
    <result column="pay_time" jdbcType="TIMESTAMP" property="payTime" />
    <result column="cancel_time" jdbcType="TIMESTAMP" property="cancelTime" />
    <result column="close_time" jdbcType="TIMESTAMP" property="closeTime" />
    <result column="pay_out_trade_no" jdbcType="VARCHAR" property="payOutTradeNo" />
    <result column="refund_time" jdbcType="TIMESTAMP" property="refundTime" />
    <result column="refund_number" jdbcType="VARCHAR" property="refundNumber" />
    <result column="order_expiration_time" jdbcType="TIMESTAMP" property="orderExpirationTime" />
    <result column="validity_time" jdbcType="TIMESTAMP" property="validityTime" />
    <result column="discount_amount" jdbcType="DECIMAL" property="discountAmount" />
    <result column="original_amount" jdbcType="DECIMAL" property="originalAmount" />
    <result column="order_number" jdbcType="VARCHAR" property="orderNumber" />
    <result column="order_mark" jdbcType="INTEGER" property="orderMark" />
    <result column="refund_amount" jdbcType="DECIMAL" property="refundAmount" />
    <result column="renewal_activity_id" jdbcType="BIGINT" property="renewalActivityId" />
    <result column="renewal_activity_number" jdbcType="INTEGER" property="renewalActivityNumber" />
    <result column="renewal_activity_amount" jdbcType="DECIMAL" property="renewalActivityAmount" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, account_uuid, total_amount, nft_number, tran_number, pay_type, pay_state, refund_state, 
    create_time, update_time, domain_server_sn, trade_code, platform_source, order_state, 
    pay_time, cancel_time, close_time, pay_out_trade_no, refund_time, refund_number, 
    order_expiration_time, validity_time, discount_amount, original_amount, order_number, 
    order_mark, refund_amount, renewal_activity_id, renewal_activity_number, renewal_activity_amount
  </sql>
</mapper>