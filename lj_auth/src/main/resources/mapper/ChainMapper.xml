<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.ChainMapper">
  <resultMap id="BaseResultMap" type="com.lj.auth.domain.Chain">
    <!--@mbg.generated-->
    <!--@Table lj_auth_chain-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="opb_chain_id" jdbcType="INTEGER" property="opbChainId" />
    <result column="chain_id" jdbcType="INTEGER" property="chainId" />
    <result column="chain_logo" jdbcType="VARCHAR" property="chainLogo" />
    <result column="algorithm_type" jdbcType="INTEGER" property="algorithmType" />
    <result column="algorithm_name" jdbcType="VARCHAR" property="algorithmName" />
    <result column="chain_name" jdbcType="VARCHAR" property="chainName" />
    <result column="technology_platform" jdbcType="VARCHAR" property="technologyPlatform" />
    <result column="state" jdbcType="BOOLEAN" property="state" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, opb_chain_id, chain_id, chain_logo, algorithm_type, algorithm_name, chain_name, 
    technology_platform, `state`, create_time, update_time
  </sql>

  <select id="queryByChainOpbChainId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from lj_auth_chain
    where opb_chain_id = #{opbChainId}
    </select>
</mapper>