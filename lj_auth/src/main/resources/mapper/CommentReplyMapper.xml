<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.CommentReplyMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.lj.auth.domain.CommentReply">
        <id column="id" property="id" />
        <result column="account_uuid" property="accountUuid" />
        <result column="landlord_flag" property="landlordFlag" />
        <result column="trends_id" property="trendsId" />
        <result column="comment_id" property="commentId" />
        <result column="content" property="content" />
        <result column="up_reply" property="upReply" />
        <result column="up_account_uuid" property="upAccountUuid" />
        <result column="up_landlord_flag" property="upLandlordFlag" />
        <result column="level" property="level" />
        <result column="remove_flag" property="removeFlag" />
        <result column="create_time" property="createTime" />
    </resultMap>

</mapper>
