<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.Sp24hoursMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.lj.auth.domain.Sp24hours">
        <id column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="sp_price" property="spPrice"/>
    </resultMap>
    <select id="select30List" resultType="com.lj.auth.domain.vo.Sp24hoursVo">
        SELECT id, create_time, ROUND(sp_price, 6) as sp_price
        FROM lj_auth_sp_24Hours
        order by create_time desc
        limit #{strip}
    </select>
    <select id="selectMaxAndLow" resultType="java.util.Map">
        SELECT MAX(sp_price) AS max,
               MIN(sp_price) AS low
        FROM lj_auth_sp_24Hours
        WHERE DATE(create_time) = CURDATE();
    </select>

    <select id="selectWeekMaxAndLow" resultType="java.util.Map">
        SELECT MAX(sp_price) AS max,
               MIN(sp_price) AS low
        FROM lj_auth_sp_24Hours
        WHERE YEARWEEK(create_time, 1) = YEARWEEK(CURDATE(), 1);
    </select>

    <select id="selectMonthMaxAndLow" resultType="java.util.Map">
        SELECT MAX(sp_price) AS max,
               MIN(sp_price) AS low
        FROM lj_auth_sp_24Hours
        WHERE YEAR(create_time) = YEAR(CURDATE())
          AND MONTH(create_time) = MONTH(CURDATE());
    </select>
</mapper>
