<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.ExploreAppSearchRecordMapper">
  <resultMap id="BaseResultMap" type="com.lj.auth.domain.ExploreAppSearchRecord">
    <!--@mbg.generated-->
    <!--@Table lj_auth_explore_app_search_record-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="app_id" jdbcType="INTEGER" property="appId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, account_uuid, create_time, content, app_id
  </sql>
</mapper>