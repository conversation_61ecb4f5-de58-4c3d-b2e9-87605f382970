<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.PhoneRechargeRecordMapper">
  <resultMap id="BaseResultMap" type="com.lj.auth.domain.PhoneRechargeRecord">
    <!--@mbg.generated-->
    <!--@Table lj_phone_recharge_record-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="operate_uuid" jdbcType="VARCHAR" property="operateUuid" />
    <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="recharge_number" jdbcType="VARCHAR" property="rechargeNumber" />
    <result column="service_provider" jdbcType="VARCHAR" property="serviceProvider" />
    <result column="amount" jdbcType="DECIMAL" property="amount" />
    <result column="state" jdbcType="INTEGER" property="state" />
    <result column="refund_state" jdbcType="INTEGER" property="refundState" />
    <result column="order_number" jdbcType="VARCHAR" property="orderNumber" />
    <result column="tran_number" jdbcType="VARCHAR" property="tranNumber" />
    <result column="pay_type" jdbcType="INTEGER" property="payType" />
    <result column="platform" jdbcType="INTEGER" property="platform" />
    <result column="real_amount" jdbcType="DECIMAL" property="realAmount" />
    <result column="discount_amount" jdbcType="DECIMAL" property="discountAmount" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="source_Funds" jdbcType="INTEGER" property="sourceFunds" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="pay_time" jdbcType="TIMESTAMP" property="payTime" />
    <result column="refund_amount" jdbcType="DECIMAL" property="refundAmount" />
    <result column="refund_time" jdbcType="TIMESTAMP" property="refundTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="cate_id" jdbcType="INTEGER" property="cateId" />
    <result column="product_id" jdbcType="INTEGER" property="productId" />
    <result column="received_time" jdbcType="TIMESTAMP" property="receivedTime" />
    <result column="refund_order_number" jdbcType="VARCHAR" property="refundOrderNumber" />
    <result column="expiration_time" jdbcType="TIMESTAMP" property="expirationTime" />
    <result column="pay_out_trade_no" jdbcType="VARCHAR" property="payOutTradeNo" />
    <result column="simple_state" jdbcType="INTEGER" property="simpleState" />
    <result column="community_activity_id" jdbcType="VARCHAR" property="communityActivityId" />
    <result column="community_activity_amount" jdbcType="DECIMAL" property="communityActivityAmount" />
    <result column="lj_coupon_account_id" jdbcType="VARCHAR" property="ljCouponAccountId" />
    <result column="lj_coupon_account_amount" jdbcType="DECIMAL" property="ljCouponAccountAmount" />
    <result column="tian_wei_discount_amount" jdbcType="DECIMAL" property="tianWeiDiscountAmount" />
    <result column="company_real_amount" jdbcType="DECIMAL" property="companyRealAmount" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, operate_uuid, account_uuid, `type`, recharge_number, service_provider, amount, 
    `state`, refund_state, order_number, tran_number, pay_type, platform, real_amount, 
    discount_amount, `status`, source_Funds, remark, pay_time, refund_amount, refund_time, 
    create_time, cate_id, product_id, received_time, refund_order_number, expiration_time, 
    pay_out_trade_no, simple_state, community_activity_id, community_activity_amount, 
    lj_coupon_account_id, lj_coupon_account_amount, tian_wei_discount_amount, company_real_amount
  </sql>
</mapper>