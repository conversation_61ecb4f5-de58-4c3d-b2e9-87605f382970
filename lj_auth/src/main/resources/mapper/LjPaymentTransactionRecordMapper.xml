<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.LjPaymentTransactionRecordMapper">
  <resultMap id="BaseResultMap" type="com.lj.auth.domain.LjPaymentTransactionRecord">
    <!--@mbg.generated-->
    <!--@Table lj_payment_transaction_record-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="pay_order_id" jdbcType="VARCHAR" property="payOrderId" />
    <result column="pay_amount" jdbcType="DECIMAL" property="payAmount" />
    <result column="pay_state" jdbcType="INTEGER" property="payState" />
    <result column="refund_state" jdbcType="INTEGER" property="refundState" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="pay_type" jdbcType="INTEGER" property="payType" />
    <result column="pay_tool" jdbcType="INTEGER" property="payTool" />
    <result column="order_type" jdbcType="INTEGER" property="orderType" />
    <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, pay_order_id, pay_amount, pay_state, refund_state, create_time, update_time, 
    pay_type, pay_tool, order_type, account_uuid, order_id
  </sql>
</mapper>