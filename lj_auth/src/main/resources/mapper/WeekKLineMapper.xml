<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.WeekKLineMapper">
  <resultMap id="BaseResultMap" type="com.lj.auth.domain.WeekKLine">
    <!--@mbg.generated-->
    <!--@Table lj_auth_week_k_line-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="low" jdbcType="DECIMAL" property="low" />
    <result column="max" jdbcType="DECIMAL" property="max" />
    <result column="sp_price" jdbcType="DECIMAL" property="spPrice" />
    <result column="rise_and_fall" jdbcType="VARCHAR" property="riseAndFall" />
    <result column="start" jdbcType="DECIMAL" property="start" />
    <result column="end" jdbcType="DECIMAL" property="end" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, create_time, low, `max`, sp_price, rise_and_fall, `start`, `end`, update_time
  </sql>

  <select id="selectWeekKLinePage" resultType="java.util.Map">
    SELECT id,
    DATE_FORMAT(create_time, '%Y-%m-%d %H:%i:%s') AS createTime,
    DATE_FORMAT(update_time, '%Y-%m-%d %H:%i:%s') AS updateTime,
    FORMAT(low, 6)                                AS low,
    FORMAT(max, 6)                                AS max,
    FORMAT(sp_price, 6)                           AS spPrice,
    rise_and_fall                                 AS riseAndFall,
    FORMAT(start, 6)                              AS start,
    FORMAT(end, 6)                                AS end
    FROM lj_auth_week_k_line
    order by create_time desc
    limit #{page},#{pageSize}

    </select>
</mapper>