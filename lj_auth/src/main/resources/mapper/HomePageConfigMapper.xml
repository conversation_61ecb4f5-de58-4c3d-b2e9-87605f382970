<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.HomePageConfigMapper">

    <!-- 获取首页配置列表 -->
    <select id="getHomePageConfigList" resultType="com.lj.auth.domain.vo.HomePageConfigVo">
        select t.area area,
               t.logo logo,
               t.icon icon,
               t.selected_icon iconSelected,
               t.background_type backgroundType,
               t.content content,
               t.jump_type jumpType,
               t.jump_path jumpPath,
               t.jump_url jumpUrl,
               t.is_hidden_nav isHiddenNav,
               t.sort sort,
               t.show_flag isShow,
               ifnull(t.scene_type,'') sceneType,
               ifnull(t.scene_name,'') sceneName,
               ifnull(t.scene_tips,'') sceneTips
            from lj_home_page_config t
        where t.channel = #{channel}
        and t.area = #{area}
        and t.show_flag = '1'
        order by t.sort
    </select>

    <!-- 根据场景类型获取首页配置 -->
    <select id="getHomePageConfigConditionSceneType" resultType="com.lj.auth.domain.vo.HomePageConfigVo">
        select t.area area,
               t.logo logo,
               t.icon icon,
               t.selected_icon iconSelected,
               t.background_type backgroundType,
               t.content content,
               t.jump_type jumpType,
               t.jump_path jumpPath,
               t.jump_url jumpUrl,
               t.is_hidden_nav isHiddenNav,
               t.sort sort,
               t.show_flag isShow,
               ifnull(t.scene_type,'') sceneType,
               ifnull(t.scene_name,'') sceneName,
               ifnull(t.scene_tips,'') sceneTips
        from lj_home_page_config t
        where t.channel = #{channel}
          and t.area = #{area}
          and t.show_flag = '1'
        <if test="sceneTypeList != null and sceneTypeList.size() > 0">
            and t.scene_type in
            <foreach collection="sceneTypeList" item="sceneType" open="(" close=")" separator=",">
                #{sceneType}
            </foreach>
        </if>
        order by t.sort
    </select>

</mapper>
