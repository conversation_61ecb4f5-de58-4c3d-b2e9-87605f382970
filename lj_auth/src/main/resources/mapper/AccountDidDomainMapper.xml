<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.AccountDidDomainMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.lj.auth.domain.AccountDidDomain">
        <id column="id" property="id" />
        <result column="domain" property="domain" />
        <result column="did" property="did" />
        <result column="account_uuid" property="accountUuid" />
        <result column="operate_uuid" property="operateUuid" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="domain_hold_state" property="domainHoldState" />
        <result column="domain_login_state" property="domainLoginState" />
    </resultMap>

</mapper>
