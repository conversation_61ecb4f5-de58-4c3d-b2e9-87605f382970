<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.DidRebateMapper">
  <resultMap id="BaseResultMap" type="com.lj.auth.domain.DidRebate">
    <!--@mbg.generated-->
    <!--@Table ym_did_rebate-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid" />
    <result column="amount" jdbcType="DECIMAL" property="amount" />
    <result column="this_account_scale" jdbcType="DECIMAL" property="thisAccountScale" />
    <result column="this_account_amount" jdbcType="DECIMAL" property="thisAccountAmount" />
    <result column="parent_uuid" jdbcType="VARCHAR" property="parentUuid" />
    <result column="parent_account_scale" jdbcType="DECIMAL" property="parentAccountScale" />
    <result column="parent_account_amount" jdbcType="DECIMAL" property="parentAccountAmount" />
    <result column="this_operate_uuid" jdbcType="VARCHAR" property="thisOperateUuid" />
    <result column="this_operate_scale" jdbcType="DECIMAL" property="thisOperateScale" />
    <result column="this_operate_amount" jdbcType="DECIMAL" property="thisOperateAmount" />
    <result column="parent_operate_uuid" jdbcType="VARCHAR" property="parentOperateUuid" />
    <result column="parent_operate_scale" jdbcType="DECIMAL" property="parentOperateScale" />
    <result column="parent_operate_amount" jdbcType="DECIMAL" property="parentOperateAmount" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="rebate_state" jdbcType="INTEGER" property="rebateState" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, `type`, order_id, account_uuid, amount, this_account_scale, this_account_amount, 
    parent_uuid, parent_account_scale, parent_account_amount, this_operate_uuid, this_operate_scale, 
    this_operate_amount, parent_operate_uuid, parent_operate_scale, parent_operate_amount, 
    create_time, update_time, rebate_state
  </sql>
</mapper>