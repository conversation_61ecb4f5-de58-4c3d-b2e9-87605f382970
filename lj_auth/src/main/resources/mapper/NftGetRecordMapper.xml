<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.NftGetRecordMapper">
  <resultMap id="BaseResultMap" type="com.lj.auth.domain.NftGetRecord">
    <!--@mbg.generated-->
    <!--@Table ym_nft_get_record-->
    <id column="id" property="id" />
    <result column="create_time" property="createTime" />
    <result column="title" property="title" />
    <result column="domain" property="domain" />
    <result column="get_account_uuid" property="getAccountUuid" />
    <result column="get_account_address" property="getAccountAddress" />
    <result column="opb_chain_id" property="opbChainId" />
    <result column="status" property="status" />
    <result column="get_status" property="getStatus" />
    <result column="get_count" property="getCount" />
    <result column="is_success" property="isSuccess" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, create_time, title, `domain`, get_account_uuid, get_account_address, opb_chain_id, 
    `status`, get_status, get_count, is_success
  </sql>

  <select id="selectGetCount" resultType="java.lang.Integer">
    select ifnull(sum(get_count), 0)
    from ym_nft_get_record
    where get_account_uuid = #{uuid}
    and `status` = 2
    and is_success != 0
  </select>

  <select id="countByAccountUUID" resultType="com.lj.auth.domain.resp.NftGetRecordResp">
    SELECT
    ifnull(totalCount,0),
    ifnull(hasCount,0),
    ifnull(totalCount - hasCount,0) AS noCount
    FROM (
    SELECT
    SUM(CASE WHEN status = 1 AND get_status = 1 THEN 1 ELSE 0 END) AS totalCount,
    SUM(CASE WHEN status = 2 AND is_success != 0 THEN get_count ELSE 0 END) AS hasCount
    FROM
    ym_nft_get_record
    WHERE
    get_account_uuid = #{uuid}
    ) AS counts;
  </select>


  <select id="totalPageQueryByAccountUUID" resultType="int">
    select count(1)
    from ym_nft_get_record
    where get_account_uuid = #{uuid}
  </select>

  <select id="pageQueryByAccountUUID" resultType="com.lj.auth.domain.vo.NftGetRecordVo">
    select
    <include refid="Base_Column_List" />
    from ym_nft_get_record
    where get_account_uuid = #{uuid}
    order by create_time desc
    limit #{start}, #{limit}
  </select>
</mapper>