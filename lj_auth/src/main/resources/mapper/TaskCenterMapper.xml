<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.TaskCenterMapper">
  <resultMap id="BaseResultMap" type="com.lj.auth.domain.TaskCenter">
    <!--@mbg.generated-->
    <!--@Table lj_auth_task_center-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="task_type_id" jdbcType="INTEGER" property="taskTypeId" />
    <result column="task_name" jdbcType="VARCHAR" property="taskName" />
    <result column="introduce" jdbcType="VARCHAR" property="introduce" />
    <result column="describe" jdbcType="VARCHAR" property="describe" />
    <result column="reward" jdbcType="DECIMAL" property="reward" />
    <result column="completion_count" jdbcType="INTEGER" property="completionCount" />
    <result column="opening_time" jdbcType="TIMESTAMP" property="openingTime" />
    <result column="completion_time" jdbcType="TIMESTAMP" property="completionTime" />
    <result column="task_state" jdbcType="INTEGER" property="taskState" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, task_type_id, task_name, introduce, `describe`, reward, completion_count, opening_time, 
    completion_time, task_state, create_time, update_time
  </sql>


</mapper>