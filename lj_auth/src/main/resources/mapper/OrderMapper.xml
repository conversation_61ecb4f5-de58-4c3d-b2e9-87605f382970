<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.OrderMapper">
  <resultMap id="BaseResultMap" type="com.lj.auth.domain.Order">
    <!--@mbg.generated-->
    <!--@Table ym_order-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="domain_name" jdbcType="VARCHAR" property="domainName" />
    <result column="owner_address" jdbcType="VARCHAR" property="ownerAddress" />
    <result column="duration" jdbcType="INTEGER" property="duration" />
    <result column="total_amount" jdbcType="DECIMAL" property="totalAmount" />
    <result column="floor_price" jdbcType="DECIMAL" property="floorPrice" />
    <result column="level" jdbcType="INTEGER" property="level" />
    <result column="pay_status" jdbcType="INTEGER" property="payStatus" />
    <result column="tran_type" jdbcType="INTEGER" property="tranType" />
    <result column="domain_server_sn" jdbcType="VARCHAR" property="domainServerSn" />
    <result column="trade_code" jdbcType="VARCHAR" property="tradeCode" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="pay_type" jdbcType="INTEGER" property="payType" />
    <result column="out_trade_no" jdbcType="VARCHAR" property="outTradeNo" />
    <result column="operate_uuid" jdbcType="VARCHAR" property="operateUuid" />
    <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="parent_uuid" jdbcType="VARCHAR" property="parentUuid" />
    <result column="tran_number" jdbcType="VARCHAR" property="tranNumber" />
    <result column="refund_number" jdbcType="VARCHAR" property="refundNumber" />
    <result column="refund_state" jdbcType="INTEGER" property="refundState" />
    <result column="platform" jdbcType="INTEGER" property="platform" />
    <result column="primitive_amount" jdbcType="DECIMAL" property="primitiveAmount" />
    <result column="discount_amount" jdbcType="DECIMAL" property="discountAmount" />
    <result column="coupon_id" jdbcType="BIGINT" property="couponId" />
    <result column="coupon_number" jdbcType="VARCHAR" property="couponNumber" />
    <result column="coupon_phone" jdbcType="VARCHAR" property="couponPhone" />
    <result column="transaction_state" jdbcType="INTEGER" property="transactionState" />
    <result column="transfer_state" jdbcType="INTEGER" property="transferState" />
    <result column="transaction_hash" jdbcType="VARCHAR" property="transactionHash" />
    <result column="platform_source" jdbcType="INTEGER" property="platformSource" />
    <result column="order_number" jdbcType="VARCHAR" property="orderNumber" />
    <result column="order_mark" jdbcType="INTEGER" property="orderMark" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, domain_name, owner_address, duration, total_amount, floor_price, `level`, pay_status, 
    tran_type, domain_server_sn, trade_code, create_time, pay_type, out_trade_no, operate_uuid, 
    account_uuid, `type`, parent_uuid, tran_number, refund_number, refund_state, platform, 
    primitive_amount, discount_amount, coupon_id, coupon_number, coupon_phone, transaction_state, 
    transfer_state, transaction_hash, platform_source, order_number, order_mark
  </sql>

</mapper>