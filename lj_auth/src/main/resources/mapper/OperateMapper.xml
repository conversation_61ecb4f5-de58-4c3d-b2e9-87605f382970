<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.OperateMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.lj.auth.domain.Operate">
        <id column="account_uuid" property="accountUuid" />
        <result column="identity" property="identity" />
        <result column="parent_account_uuid" property="parentAccountUuid" />
        <result column="old_uuid" property="oldUuid" />
        <result column="level1_ratio" property="level1Ratio" />
        <result column="level2_ratio" property="level2Ratio" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="link" property="link" />
        <result column="phone_number" property="phoneNumber" />
        <result column="password" property="password" />
        <result column="pay_password" property="payPassword" />
        <result column="head_portrait" property="headPortrait" />
        <result column="nick_name" property="nickName" />
        <result column="real_name" property="realName" />
        <result column="id_card" property="idCard" />
        <result column="opearte_number" property="opearteNumber" />
        <result column="hot_flag" property="hotFlag" />
        <result column="show_flag" property="showFlag" />
        <result column="sort" property="sort" />
    </resultMap>

</mapper>
