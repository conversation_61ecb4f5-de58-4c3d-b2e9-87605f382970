<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.AuctionDomainMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.lj.auth.domain.AuctionDomain">
        <id column="id" property="id" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="transfer_uuid" property="transferUuid" />
        <result column="domain" property="domain" />
        <result column="suffix" property="suffix" />
        <result column="seller_uuid" property="sellerUuid" />
        <result column="seller_nick_name" property="sellerNickName" />
        <result column="up_time" property="upTime" />
        <result column="down_time" property="downTime" />
        <result column="price" property="price" />
        <result column="bid_increase" property="bidIncrease" />
        <result column="delay_period" property="delayPeriod" />
        <result column="description" property="description" />
        <result column="service_price" property="servicePrice" />
        <result column="rate" property="rate" />
        <result column="domain_status" property="domainStatus" />
        <result column="transaction_status" property="transactionStatus" />
        <result column="transaction_price" property="transactionPrice" />
        <result column="browse_count" property="browseCount" />
        <result column="buyer_uuid" property="buyerUuid" />
        <result column="buyer_nick_name" property="buyerNickName" />
        <result column="is_hot" property="isHot" />
        <result column="sort" property="sort" />
        <result column="is_delete" property="isDelete" />
        <result column="is_recommend" property="isRecommend" />
        <result column="recommend_sort" property="recommendSort" />
        <result column="is_star" property="isStar" />
        <result column="star_count" property="starCount" />
        <result column="transaction_hash" property="transactionHash" />
        <result column="pay_status" property="payStatus" />
        <result column="cover_url" property="coverUrl" />
        <result column="is_nft_cover" property="isNftCover" />
        <result column="energy_used" property="energyUsed" />
        <result column="pay_address" property="payAddress" />
    </resultMap>

</mapper>
