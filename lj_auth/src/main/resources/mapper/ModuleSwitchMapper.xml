<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.ModuleSwitchMapper">
  <resultMap id="BaseResultMap" type="com.lj.auth.domain.ModuleSwitch">
    <!--@mbg.generated-->
    <!--@Table lj_auth_module_switch-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="type_id" jdbcType="INTEGER" property="typeId" />
    <result column="key" jdbcType="VARCHAR" property="key" />
    <result column="value" jdbcType="BOOLEAN" property="value" />
    <result column="describes" jdbcType="VARCHAR" property="describes" />
    <result column="channel" jdbcType="VARCHAR" property="channel" />
    <result column="version" jdbcType="VARCHAR" property="version" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, type_id, `key`, `value`, describes, channel, version, create_time, update_time
  </sql>
</mapper>