<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.AccountLogoutConditionRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.lj.auth.domain.AccountLogoutConditionRecord">
        <id column="id" property="id" />
        <result column="condition_id" property="conditionId" />
        <result column="account_uuid" property="accountUuid" />
        <result column="is_ignore" property="isIgnore" />
    </resultMap>
    <select id="selectLogOutUser" resultType="java.lang.String">
        SELECT account_uuid FROM lj_auth_account_logout_condition_record
        where   is_ignore =1
        group by account_uuid
    </select>

</mapper>
