<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.OrderBsnInfoMapper">
  <resultMap id="BaseResultMap" type="com.lj.auth.domain.OrderBsnInfo">
    <!--@mbg.generated-->
    <!--@Table ym_order_bsn_info-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="amount" jdbcType="INTEGER" property="amount" />
    <result column="attach" jdbcType="VARCHAR" property="attach" />
    <result column="domain" jdbcType="VARCHAR" property="domain" />
    <result column="domain_serve_sn" jdbcType="VARCHAR" property="domainServeSn" />
    <result column="duration" jdbcType="INTEGER" property="duration" />
    <result column="expiration_time" jdbcType="VARCHAR" property="expirationTime" />
    <result column="level" jdbcType="INTEGER" property="level" />
    <result column="owner" jdbcType="VARCHAR" property="owner" />
    <result column="domain_admin" jdbcType="VARCHAR" property="domainAdmin" />
    <result column="parent_domain" jdbcType="VARCHAR" property="parentDomain" />
    <result column="payment_status" jdbcType="INTEGER" property="paymentStatus" />
    <result column="payment_time" jdbcType="VARCHAR" property="paymentTime" />
    <result column="refund_status" jdbcType="INTEGER" property="refundStatus" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="trade_code" jdbcType="VARCHAR" property="tradeCode" />
    <result column="domain_serve_type" jdbcType="INTEGER" property="domainServeType" />
    <result column="new_owner" jdbcType="VARCHAR" property="newOwner" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="operate_uuid" jdbcType="VARCHAR" property="operateUuid" />
    <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="level1_ratio" jdbcType="VARCHAR" property="level1Ratio" />
    <result column="level2_ratio" jdbcType="VARCHAR" property="level2Ratio" />
    <result column="local_rebate" jdbcType="DECIMAL" property="localRebate" />
    <result column="superior_rebate" jdbcType="DECIMAL" property="superiorRebate" />
    <result column="robot_push_message" jdbcType="INTEGER" property="robotPushMessage" />
    <result column="ticket_state" jdbcType="INTEGER" property="ticketState" />
    <result column="superior_account_scale" jdbcType="DECIMAL" property="superiorAccountScale" />
    <result column="superior_account_amount" jdbcType="DECIMAL" property="superiorAccountAmount" />
    <result column="parent_uuid" jdbcType="VARCHAR" property="parentUuid" />
    <result column="this_account_scale" jdbcType="DECIMAL" property="thisAccountScale" />
    <result column="this_account_amount" jdbcType="DECIMAL" property="thisAccountAmount" />
    <result column="primitive_amount" jdbcType="DECIMAL" property="primitiveAmount" />
    <result column="discount_amount" jdbcType="DECIMAL" property="discountAmount" />
    <result column="transaction_state" jdbcType="INTEGER" property="transactionState" />
    <result column="transfer_state" jdbcType="INTEGER" property="transferState" />
    <result column="transaction_hash" jdbcType="VARCHAR" property="transactionHash" />
    <result column="platform_source" jdbcType="INTEGER" property="platformSource" />
    <result column="order_number" jdbcType="VARCHAR" property="orderNumber" />
    <result column="order_mark" jdbcType="INTEGER" property="orderMark" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, order_id, amount, attach, `domain`, domain_serve_sn, duration, expiration_time, 
    `level`, `owner`, domain_admin, parent_domain, payment_status, payment_time, refund_status, 
    `status`, trade_code, domain_serve_type, new_owner, create_time, operate_uuid, account_uuid, 
    `type`, level1_ratio, level2_ratio, local_rebate, superior_rebate, robot_push_message, 
    ticket_state, superior_account_scale, superior_account_amount, parent_uuid, this_account_scale, 
    this_account_amount, primitive_amount, discount_amount, transaction_state, transfer_state, 
    transaction_hash, platform_source, order_number, order_mark
  </sql>
    <!--根据账户UUID和域名查询-->
    <select id="queryByAccountUUIDAndDomain" resultMap="BaseResultMap">
        select *
        from ym_order_bsn_info
        where account_uuid = #{accountUUID}
          and `domain` = #{domain}
          and domain_serve_type = #{domainServeType}
          and status = #{status}
    </select>
    <select id="selectDomainOrderInfo" resultType="java.util.Map">
        SELECT t1.domain_serve_sn                               AS domainServerSn,
               t1.`status`,
               t1.domain,
               t1.`level`,
               t1.parent_domain                                 AS parentDomain,
               t1.expiration_time                               AS expirationTime,
               t1.duration,
               t1.id,
               DATE_FORMAT(t1.create_time, '%Y-%m-%d %H:%i:%s') as createTime,
               '域名注册'                                           as orderType,
               t1.trade_code                                    as tradeCode,
               t1.primitive_amount                              as primitiveAmount,
               t1.discount_amount                               as discountAmount,
               t1.amount,
               t2.pay_type                                      as payType,
               t2.pay_status                                    as payStatus,
               DATE_FORMAT(t2.create_time, '%Y-%m-%d %H:%i:%s') as payTime,
               t2.refund_state                                  as orderRefundState
        FROM `ym_order_bsn_info` t1
                 LEFT JOIN ym_order t2 on
            t1.order_id = t2.id
        WHERE t1.domain_serve_type = 0
          AND t1.id = #{orderId}
    </select>
    <select id="selectDomainOrderInfoTuikuan" resultType="java.util.Map">
        SELECT t2.domain_server_sn                              AS domainServerSn,
               t1.`status`,
               t1.domain,
               t1.`level`,
               t1.parent_domain                                 AS parentDomain,
               t1.expiration_time                               AS expirationTime,
               t1.duration,
               t1.id,
               DATE_FORMAT(t1.create_time, '%Y-%m-%d %H:%i:%s') as createTime,
               '域名注册'                                           as orderType,
               t2.trade_code                                    as tradeCode,
               t1.primitive_amount                              as primitiveAmount,
               t1.discount_amount                               as discountAmount,
               t1.amount,
               t2.pay_type                                      as payType,
               t2.pay_status                                    as payStatus,
               DATE_FORMAT(t2.create_time, '%Y-%m-%d %H:%i:%s') as payTime,
               t1.refund_status                                 as orderRefundState
        FROM `ym_order_bsn_info` t1
                 LEFT JOIN ym_order t2 on t1.order_id = t2.id
        WHERE t1.domain_serve_type = 0
          AND t1.id = #{orderId}

    </select>
    <select id="selectDomainInfo" resultType="java.util.Map">
        SELECT x.domain_serve_sn AS domainServeSn,
               case x.status
                   when 0 then "待处理"
                   when 1 then "处理中"
                   when 2 then "注册成功"
                   when 3 then "注册失败"
                   when 4 then "待审核"
                   when 5 then "审核未通过"
                   else "其他"
                   end           as transferState,
               a.`domain`,
               x.`level`,
               a.parent_domain   AS parentDomain,
               a.register_time   as register,
               a.expiration_time as expirationTime
        FROM ym_domain_account a
                 left join ym_order_bsn_info x
                           on
                               a.`domain` = x.`domain`
        where 1 = 1
          and a.`domain` = #{domain}
          and x.domain_serve_sn = #{domianSN}
          and x.account_uuid = #{accountUUID}

    </select>
    <select id="selectTransferDomain" resultType="java.util.Map">
        SELECT x.create_time     as transferTime,
               case transfer_state
                   when 1 then "已转让"
                   when 2 then "已转入"
                   else "其他" end as transferState,
               a.real_name       as realName
        FROM ym_order_bsn_info x

                 left join account a
                           on a.uuid = x.account_uuid

        where x.domain_serve_sn = #{domainServeSn}
          and x.transfer_state = #{transferState}

    </select>
    <select id="selectDomainInfoMarket" resultType="java.util.Map">
        SELECT x.domain_serve_sn AS domainServeSn,
               a.`domain`,
               x.`level`,
               a.parent_domain   AS parentDomain,
               a.register_time   as register,
               a.expiration_time as expirationTime,
               a.registrar
        FROM ym_domain_account a
                 left join ym_order_bsn_info x
                           on
                               a.`domain` = x.`domain`
        where 1 = 1
          and a.`domain` = #{domain}
          AND x.account_uuid = #{accountUuid}
          and x.domain_serve_sn = #{transferUuid}

    </select>

</mapper>
