<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.VersionMapper">
  <resultMap id="BaseResultMap" type="com.lj.auth.domain.Version">
    <!--@mbg.generated-->
    <!--@Table lj_auth_version-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="down_url" jdbcType="VARCHAR" property="downUrl" />
    <result column="language" jdbcType="TINYINT" property="language" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="system_type" jdbcType="INTEGER" property="systemType" />
    <result column="update_log" jdbcType="LONGVARCHAR" property="updateLog" />
    <result column="version" jdbcType="VARCHAR" property="version" />
    <result column="version_name" jdbcType="VARCHAR" property="versionName" />
    <result column="version_type" jdbcType="TINYINT" property="versionType" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="build" jdbcType="VARCHAR" property="build" />
    <result column="platform" jdbcType="VARCHAR" property="platform" />
    <result column="update_type" jdbcType="INTEGER" property="updateType" />
    <result column="apk_link" jdbcType="VARCHAR" property="apkLink" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, down_url, `language`, title, system_type, update_log, version, version_name, 
    version_type, `status`, create_time, update_time, build, platform, update_type, apk_link
  </sql>

    <select id="getLatestVersion" resultMap="BaseResultMap">
        select *
        from lj_auth_version
        where system_type = #{systemType}
        <if test="platform != null and platform != ''">
            and platform = #{platform}
        </if>
        order by update_time desc
        limit 1
    </select>
</mapper>