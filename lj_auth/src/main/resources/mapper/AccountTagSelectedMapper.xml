<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.AccountTagSelectedMapper">
  <resultMap id="BaseResultMap" type="com.lj.auth.domain.AccountTagSelected">
    <!--@mbg.generated-->
    <!--@Table account_tag_selected-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="account_tag_id" jdbcType="INTEGER" property="accountTagId" />
    <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, create_time, update_time, sort, account_tag_id, account_uuid
  </sql>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into account_tag_selected
    (create_time, update_time, sort, account_tag_id, account_uuid)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.sort,jdbcType=INTEGER}, 
        #{item.accountTagId,jdbcType=INTEGER}, #{item.accountUuid,jdbcType=VARCHAR})
    </foreach>
  </insert>

    <select id="selectUserTagList" resultMap="BaseResultMap">
        select ats.*, at.name
        from account_tag_selected ats
                 left join account_tag at
        on ats.account_tag_id = at.id
        where ats.account_uuid = #{accountUUID}  order by ats.sort asc
    </select>
</mapper>