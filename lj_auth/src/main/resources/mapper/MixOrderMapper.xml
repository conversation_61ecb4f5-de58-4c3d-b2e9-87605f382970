<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.MixOrderMapper">
  <resultMap id="BaseResultMap" type="com.lj.auth.domain.MixOrder">
    <!--@mbg.generated-->
    <!--@Table mix_order-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_number" jdbcType="VARCHAR" property="orderNumber" />
    <result column="applicaton_id" jdbcType="INTEGER" property="applicatonId" />
    <result column="application_name" jdbcType="VARCHAR" property="applicationName" />
    <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid" />
    <result column="did_symbol" jdbcType="VARCHAR" property="didSymbol" />
    <result column="order_amount" jdbcType="DECIMAL" property="orderAmount" />
    <result column="actual_amount" jdbcType="DECIMAL" property="actualAmount" />
    <result column="order_status" jdbcType="INTEGER" property="orderStatus" />
    <result column="simple_status" jdbcType="INTEGER" property="simpleStatus" />
    <result column="pay_status" jdbcType="INTEGER" property="payStatus" />
    <result column="pay_out_trade_no" jdbcType="VARCHAR" property="payOutTradeNo" />
    <result column="pay_type" jdbcType="INTEGER" property="payType" />
    <result column="pay_amount" jdbcType="DECIMAL" property="payAmount" />
    <result column="pay_expired_time" jdbcType="TIMESTAMP" property="payExpiredTime" />
    <result column="pay_time" jdbcType="TIMESTAMP" property="payTime" />
    <result column="pay_desc" jdbcType="VARCHAR" property="payDesc" />
    <result column="operate_uuid" jdbcType="VARCHAR" property="operateUuid" />
    <result column="order_type" jdbcType="INTEGER" property="orderType" />
    <result column="bussiness_order_number" jdbcType="VARCHAR" property="bussinessOrderNumber" />
    <result column="bussiness_show_order_number" jdbcType="VARCHAR" property="bussinessShowOrderNumber" />
    <result column="pay_tool" jdbcType="INTEGER" property="payTool" />
    <result column="refund_number" jdbcType="VARCHAR" property="refundNumber" />
    <result column="refund_amount" jdbcType="DECIMAL" property="refundAmount" />
    <result column="refund_state" jdbcType="INTEGER" property="refundState" />
    <result column="original_amount" jdbcType="DECIMAL" property="originalAmount" />
    <result column="discount_amount" jdbcType="DECIMAL" property="discountAmount" />
    <result column="platform_source" jdbcType="INTEGER" property="platformSource" />
    <result column="base_config_id" jdbcType="VARCHAR" property="baseConfigId" />
    <result column="status_desc" jdbcType="VARCHAR" property="statusDesc" />
    <result column="order_title" jdbcType="VARCHAR" property="orderTitle" />
    <result column="sync_time" jdbcType="TIMESTAMP" property="syncTime" />
    <result column="pay_callback_notify_address" jdbcType="VARCHAR" property="payCallbackNotifyAddress" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="is_display" jdbcType="BOOLEAN" property="isDisplay" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="order_close_time" jdbcType="TIMESTAMP" property="orderCloseTime" />
    <result column="order_finish_time" jdbcType="TIMESTAMP" property="orderFinishTime" />
    <result column="balance_param" jdbcType="VARCHAR" property="balanceParam" />
    <result column="category_id" jdbcType="INTEGER" property="categoryId" />
    <result column="avaliable_refund_amount" jdbcType="DECIMAL" property="avaliableRefundAmount" />
    <result column="notify_pay_url" jdbcType="VARCHAR" property="notifyPayUrl" />
    <result column="notify_refund_url" jdbcType="VARCHAR" property="notifyRefundUrl" />
    <result column="channel_appId" jdbcType="VARCHAR" property="channelAppid" />
    <result column=" ip_addr" jdbcType="VARCHAR" property="ipAddr" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, order_number, applicaton_id, application_name, account_uuid, did_symbol, order_amount, 
    actual_amount, order_status, simple_status, pay_status, pay_out_trade_no, pay_type, 
    pay_amount, pay_expired_time, pay_time, pay_desc, operate_uuid, order_type, bussiness_order_number, 
    bussiness_show_order_number, pay_tool, refund_number, refund_amount, refund_state, 
    original_amount, discount_amount, platform_source, base_config_id, status_desc, order_title, 
    sync_time, pay_callback_notify_address, create_time, is_display, update_time, order_close_time, 
    order_finish_time, balance_param, category_id, avaliable_refund_amount, notify_pay_url, 
    notify_refund_url, channel_appId, ` ip_addr`
  </sql>
</mapper>