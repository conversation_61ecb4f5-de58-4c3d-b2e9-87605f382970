<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.TaskTypeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.lj.auth.domain.TaskType">
        <id column="id" property="id"/>
        <result column="task_type_id" property="taskTypeId"/>
        <result column="task_name" property="taskName"/>
        <result column="message" property="message"/>
        <result column="sort" property="sort"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>
    <select id="getCenterTypeList" resultType="com.lj.auth.domain.vo.TaskTypeVo">
        SELECT c.num, t.*
        FROM lj_auth_task_type t
                 left join
             (SELECT task_type_id, count(1) as num
              FROM lj_auth_task_center
              where task_state = 1
              group by task_type_id) c
             on t.task_type_id = c.task_type_id
        where c.num is not NULL
        order by t.sort desc

    </select>

</mapper>
