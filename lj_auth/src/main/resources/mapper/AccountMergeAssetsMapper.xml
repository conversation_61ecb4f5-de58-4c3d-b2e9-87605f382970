<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.AccountMergeAssetsMapper">
  <resultMap id="BaseResultMap" type="com.lj.auth.domain.AccountMergeAssets">
    <!--@mbg.generated-->
    <!--@Table account_merge_assets-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="f_operate_uuid" jdbcType="VARCHAR" property="fOperateUuid" />
    <result column="t_operate_uuid" jdbcType="VARCHAR" property="tOperateUuid" />
    <result column="from_uuid" jdbcType="VARCHAR" property="fromUuid" />
    <result column="to_uuid" jdbcType="VARCHAR" property="toUuid" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="f_balance" jdbcType="DECIMAL" property="fBalance" />
    <result column="t_balance" jdbcType="DECIMAL" property="tBalance" />
    <result column="is_assets" jdbcType="INTEGER" property="isAssets" />
    <result column="is_all" jdbcType="INTEGER" property="isAll" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, f_operate_uuid, t_operate_uuid, from_uuid, to_uuid, create_time, update_time, 
    f_balance, t_balance, is_assets, is_all
  </sql>
</mapper>