<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.CouponMapper">

    <!-- 获取剩余优惠券数量 -->
    <select id="getCouponResidueNumber" resultType="java.lang.Integer">
        select ifnull(sum(residue_number),0)
            from lj_coupon
        where coupon_batch_id = #{couponBatchId}
        and show_state = 1
--         and effective_state = 3
    </select>

    <!-- 获取可领券的券列表 -->
    <select id="getCanReceiveCoupon" resultType="com.lj.auth.domain.Coupon">
        select *
        from lj_coupon
        where coupon_batch_id = #{couponBatchId}
          and show_state = 1
        and residue_number > 0
    </select>

    <!-- 减少可领取数量 -->
    <update id="reduceResidueNumber">
        update lj_coupon
        set residue_number = residue_number -1,received_number = received_number + 1
        where id = #{couponId}
        and residue_number > 0
        and received_number &lt; publish_number
    </update>

</mapper>