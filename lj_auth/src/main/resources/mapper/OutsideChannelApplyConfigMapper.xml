<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.OutsideChannelApplyConfigMapper">
    <resultMap id="BaseResultMap" type="com.lj.auth.domain.OutsideChannelApplyConfig">
        <!--@mbg.generated-->
        <!--@Table lj_outside_channel_apply_config-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="app_id" jdbcType="VARCHAR" property="appId"/>
        <result column="auth_mode" jdbcType="VARCHAR" property="authMode"/>
        <result column="fee" jdbcType="INTEGER" property="fee"/>
        <result column="fee_total_count" jdbcType="INTEGER" property="feeTotalCount"/>
        <result column="fee_condition" jdbcType="INTEGER" property="feeCondition"/>
        <result column="fee_use_count" jdbcType="INTEGER" property="feeUseCount"/>
        <result column="fee_residue_count" jdbcType="INTEGER" property="feeResidueCount"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        app_id,
        auth_mode,
        fee,
        fee_total_count,
        fee_condition,
        fee_use_count,
        fee_residue_count,
        create_time
    </sql>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update lj_outside_channel_apply_config
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="app_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.appId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.appId,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="auth_mode = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.authMode != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.authMode,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="fee = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.fee != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.fee,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="fee_total_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.feeTotalCount != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.feeTotalCount,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="fee_condition = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.feeCondition != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.feeCondition,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="fee_use_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.feeUseCount != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.feeUseCount,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="fee_residue_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.feeResidueCount != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.feeResidueCount,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into lj_outside_channel_apply_config
        (app_id, auth_mode, fee, fee_total_count, fee_condition, fee_use_count, fee_residue_count,
         create_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.appId,jdbcType=VARCHAR}, #{item.authMode,jdbcType=VARCHAR}, #{item.fee,jdbcType=INTEGER},
             #{item.feeTotalCount,jdbcType=INTEGER}, #{item.feeCondition,jdbcType=INTEGER},
             #{item.feeUseCount,jdbcType=INTEGER}, #{item.feeResidueCount,jdbcType=INTEGER},
             #{item.createTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>

    <select id="queryByAppId" resultMap="BaseResultMap">
        select *
        from lj_outside_channel_apply_config
        where app_id = #{appId}
    </select>
</mapper>
