<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.AgreementVersionMapper">
  <resultMap id="BaseResultMap" type="com.lj.auth.domain.AgreementVersion">
    <!--@mbg.generated-->
    <!--@Table lj_agreement_version-->
    <id column="version_id" jdbcType="INTEGER" property="versionId" />
    <result column="agreement_type" jdbcType="INTEGER" property="agreementType" />
    <result column="version_title" jdbcType="VARCHAR" property="versionTitle" />
    <result column="version_number" jdbcType="VARCHAR" property="versionNumber" />
    <result column="update_content" jdbcType="LONGVARCHAR" property="updateContent" />
    <result column="agreement_content" jdbcType="LONGVARCHAR" property="agreementContent" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    version_id, agreement_type, version_title, version_number, update_content, agreement_content, 
    create_time, update_time
  </sql>

  <select id="queryLatestAgreement" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from lj_agreement_version
    where agreement_type = #{agreementType}
    order by version_number desc
    limit 1
    </select>

  <select id="pageQueryByAgreementType" resultType="com.lj.auth.domain.vo.AgreementVersionVo">
    select
    <include refid="Base_Column_List" />
    from lj_agreement_version
    where 1=1
    <if test="agreementType != null">
     and agreement_type = #{agreementType}
    </if>

    <if test="searchKey != null and searchKey != ''">
      and (version_title like concat('%',#{searchKey},'%')
      or version_number like concat('%',#{searchKey},'%'))
    </if>

    order by version_number desc

    </select>
</mapper>