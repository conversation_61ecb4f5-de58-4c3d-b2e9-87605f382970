<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.ExploreAppCollectionMapper">
    <resultMap id="BaseResultMap" type="com.lj.auth.domain.ExploreAppCollection">
        <!--@mbg.generated-->
        <!--@Table lj_auth_explore_app_collection-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid"/>
        <result column="app_id" jdbcType="INTEGER" property="appId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        account_uuid,
        app_id,
        create_time
    </sql>

    <select id="appCollectionCount" resultType="java.lang.Integer">
        SELECT count(1)
        FROM lj_auth_explore_app_collection c
        where c.account_uuid = #{ uuid}
    </select>

    <select id="getAppCollectionPage" resultType="com.lj.auth.domain.vo.ExploreAppVo">
        SELECT *
        FROM lj_auth_explore_app_collection c
                 left join lj_auth_explore_app a
                           on c.app_id = a.id
        where c.account_uuid = #{ uuid}
        order by c.create_time desc
        limit #{page},#{pageSize}
    </select>
</mapper>