<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.SkySplitUserMapper">
  <resultMap id="BaseResultMap" type="com.lj.auth.domain.SkySplitUser">
    <!--@mbg.generated-->
    <!--@Table sky_split_user-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid" />
    <result column="did" jdbcType="VARCHAR" property="did" />
    <result column="contract_pdf_url" jdbcType="VARCHAR" property="contractPdfUrl" />
    <result column="sign_time" jdbcType="TIMESTAMP" property="signTime" />
    <result column="exclusive_number" jdbcType="INTEGER" property="exclusiveNumber" />
    <result column="region_id" jdbcType="VARCHAR" property="regionId" />
    <result column="region_path" jdbcType="VARCHAR" property="regionPath" />
    <result column="region_number" jdbcType="INTEGER" property="regionNumber" />
    <result column="level" jdbcType="INTEGER" property="level" />
    <result column="level_name" jdbcType="VARCHAR" property="levelName" />
    <result column="chain_address" jdbcType="VARCHAR" property="chainAddress" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="notice_state" jdbcType="INTEGER" property="noticeState" />
    <result column="apply_did_rebate_scale" jdbcType="DECIMAL" property="applyDidRebateScale" />
    <result column="lower_number" jdbcType="INTEGER" property="lowerNumber" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, account_uuid, did, contract_pdf_url, sign_time, exclusive_number, region_id, 
    region_path, region_number, `level`, level_name, chain_address, create_time, update_time, 
    notice_state, apply_did_rebate_scale, lower_number
  </sql>
</mapper>