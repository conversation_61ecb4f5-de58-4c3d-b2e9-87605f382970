<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.PlatformAccountMapper">
  <resultMap id="BaseResultMap" type="com.lj.auth.domain.PlatformAccount">
    <!--@mbg.generated-->
    <!--@Table lj_auth_platform_account-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="private_key" jdbcType="VARCHAR" property="privateKey" />
    <result column="public_key" jdbcType="VARCHAR" property="publicKey" />
    <result column="chain_account_name" jdbcType="VARCHAR" property="chainAccountName" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="status" jdbcType="BOOLEAN" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, address, private_key, public_key, chain_account_name, `type`, `status`, create_time
  </sql>

  <!--根据地址和状态查询-->
  <select id="queryByAddressAndStatus" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from lj_auth_platform_account
    where address = #{address}
    and type = #{type}
    and status = #{status}
    </select>


  <!--通过类型和状态查询平台地址信息-->
  <select id="queryByTypeAndStatus" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from lj_auth_platform_account
    where type = #{type}
    and status = #{status}
    </select>
</mapper>