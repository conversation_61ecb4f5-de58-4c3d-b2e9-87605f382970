<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.ContributionRecordMapper">
  <resultMap id="BaseResultMap" type="com.lj.auth.domain.ContributionRecord">
    <!--@mbg.generated-->
    <!--@Table lj_auth_contribution_record-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid" />
    <result column="amount" jdbcType="DECIMAL" property="amount" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="pay_type" jdbcType="INTEGER" property="payType" />
    <result column="state" jdbcType="INTEGER" property="state" />
    <result column="order_number" jdbcType="VARCHAR" property="orderNumber" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, account_uuid, amount, create_time, pay_type, `state`, order_number
  </sql>

    <select id="selectMyContributionLRecordList" resultType="com.lj.auth.domain.vo.ContributionRecordVo">
        select *
        from lj_auth_contribution_record
        where account_uuid = #{uuid}
        and state=1
        order by create_time desc
        limit #{page},#{pageSize}
    </select>

    <select id="selectContributionRecordList" resultType="com.lj.auth.domain.vo.MyContributionRecordVo">
        select d.*, t.nft_image
        from (
                 SELECT r.id,
                        r.account_uuid,
                        r.amount,
                        r.create_time,
                        a.nick_name,
                        a.domain_nick_name,
                        a.show_type,
                        a.head_portrait,
                        a.head_portrait_nft_id,
                        a.head_portrait_type
                 FROM lj_auth_contribution_record r
                          left join account a
                                    on a.uuid = r.account_uuid
                 where  r.state=1
             ) d
                 left join lj_auth_nft t
                           on t.id = d.head_portrait_nft_id

        order by d.create_time desc
        limit 10
    </select>
</mapper>