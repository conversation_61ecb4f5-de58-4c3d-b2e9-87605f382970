<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.EnergyRechargeFlowMapper">
    <resultMap id="BaseResultMap" type="com.lj.auth.domain.EnergyRechargeFlow">
        <!--@mbg.generated-->
        <!--@Table lj_auth_energy_recharge_flow-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid"/>
        <result column="business_money" jdbcType="DECIMAL" property="businessMoney"/>
        <result column="gas" jdbcType="VARCHAR" property="gas"/>
        <result column="req_transaction_sn" jdbcType="VARCHAR" property="reqTransactionSn"/>
        <result column="hash" jdbcType="VARCHAR" property="hash"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="recharge_state" jdbcType="INTEGER" property="rechargeState"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        account_uuid,
        business_money,
        gas,
        req_transaction_sn,
        hash,
        `type`,
        recharge_state,
        create_time,
        update_time
    </sql>

    <select id="queryEnergyCount" resultType="java.lang.Integer">
        select count(*)
        from lj_auth_energy_recharge_flow
        where 1 = 1
        and account_uuid = #{uuid}
        and date_format(create_time, '%Y-%m') = date_format(#{date}, '%Y-%m')
        <if test="type != null">
            and type = #{type}
        </if>
    </select>
    <select id="getPage" resultType="com.lj.auth.domain.EnergyRechargeFlow">
        select *
        from lj_auth_energy_recharge_flow f
        where 1 = 1
        and f.account_uuid = #{accountUuid}
        and date_format(f.create_time, '%Y-%m') = date_format(#{date}, '%Y-%m')
        <if test="type != null">
            and f.type = #{type}
        </if>
        order by f.create_time desc
        limit #{page},#{pageSize}
    </select>
</mapper>