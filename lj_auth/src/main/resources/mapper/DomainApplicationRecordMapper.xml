<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.DomainApplicationRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.lj.auth.domain.DomainApplicationRecord">
        <id column="id" property="id"/>
        <result column="account_uuid" property="accountUuid"/>
        <result column="opb_chain_id" property="opbChainId"/>
        <result column="domain" property="domain"/>
        <result column="application_manage_id" property="applicationManageId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>
    <select id="groupByDomainCount" resultType="java.lang.Integer">
        select count(*)
        from (SELECT `domain`
              FROM lj_auth_domain_application_record
              where account_uuid = #{accountUUID}
              group by `domain`) d
    </select>
    <select id="groupByDomain" resultType="java.util.Map">
        select d.`domain`,
               count(*) as appliationCount
        from (SELECT `domain`, application_manage_id
              FROM lj_auth_domain_application_record
              where account_uuid = #{accountUUID}
              group by `domain`, application_manage_id) d
        group by d.`domain`
        limit #{page},#{pageSize}
    </select>
    <select id="getApplicationList" resultType="java.lang.String">
        SELECT `domain`
        FROM lj_auth_domain_application_record
        where account_uuid = #{accountUUID}
        group by `domain`
    </select>
    <select id="getApplicationDetail" resultType="com.lj.auth.domain.vo.ApplicationDetailVo">
        select m.name, m.logo, a.*
        from (SELECT `domain`, application_manage_id, count(*) as `count`
              FROM lj_auth_domain_application_record
              where account_uuid = #{accountUUID}
                and `domain` = #{domain}
              group by `domain`, application_manage_id) a
                 left join lj_auth_application_manage m
                           on m.id = a.application_manage_id

    </select>

</mapper>
