<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.SpRecordMapper">
  <resultMap id="BaseResultMap" type="com.lj.auth.domain.SpRecord">
    <!--@mbg.generated-->
    <!--@Table lj_auth_sp_record-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="task_type" jdbcType="INTEGER" property="taskType" />
    <result column="task_type_name" jdbcType="VARCHAR" property="taskTypeName" />
    <result column="task_name" jdbcType="VARCHAR" property="taskName" />
    <result column="reward" jdbcType="DECIMAL" property="reward" />
    <result column="hash" jdbcType="VARCHAR" property="hash" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="detail_id" jdbcType="INTEGER" property="detailId" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, account_uuid, address, `type`, task_type, task_type_name, task_name, reward, 
    hash, create_time, detail_id, remark
  </sql>
</mapper>