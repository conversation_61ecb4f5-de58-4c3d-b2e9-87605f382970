<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.DidQrCodeMapper">
  <resultMap id="BaseResultMap" type="com.lj.auth.domain.DidQrCode">
    <!--@mbg.generated-->
    <!--@Table lj_auth_did_qr_code-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="application_id" jdbcType="INTEGER" property="applicationId" />
    <result column="application_name" jdbcType="VARCHAR" property="applicationName" />
    <result column="website" jdbcType="VARCHAR" property="website" />
    <result column="application_introduce" jdbcType="VARCHAR" property="applicationIntroduce" />
    <result column="state" jdbcType="INTEGER" property="state" />
    <result column="app_id" jdbcType="VARCHAR" property="appId" />
    <result column="app_secret" jdbcType="VARCHAR" property="appSecret" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, application_id, application_name, website, application_introduce, `state`, app_id, 
    app_secret
  </sql>
</mapper>