<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.AccountExpandMessageMapper">
  <resultMap id="BaseResultMap" type="com.lj.auth.domain.AccountExpandMessage">
    <!--@mbg.generated-->
    <!--@Table account_expand_message-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="account_id" jdbcType="BIGINT" property="accountId" />
    <result column="phone_number" jdbcType="VARCHAR" property="phoneNumber" />
    <result column="uuid" jdbcType="VARCHAR" property="uuid" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="gender" jdbcType="INTEGER" property="gender" />
    <result column="birthday" jdbcType="VARCHAR" property="birthday" />
    <result column="bir_display_flag" jdbcType="INTEGER" property="birDisplayFlag" />
    <result column="constellation" jdbcType="VARCHAR" property="constellation" />
    <result column="cons_display_flag" jdbcType="INTEGER" property="consDisplayFlag" />
    <result column="region_name" jdbcType="VARCHAR" property="regionName" />
    <result column="center" jdbcType="VARCHAR" property="center" />
    <result column="reg_display_flag" jdbcType="INTEGER" property="regDisplayFlag" />
    <result column="recently_region" jdbcType="VARCHAR" property="recentlyRegion" />
    <result column="introduction" jdbcType="VARCHAR" property="introduction" />
    <result column="tag_display_flag" jdbcType="INTEGER" property="tagDisplayFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, account_id, phone_number, uuid, create_time, update_time, gender, birthday, bir_display_flag, 
    constellation, cons_display_flag, region_name, center, reg_display_flag, recently_region, 
    introduction, tag_display_flag
  </sql>
</mapper>