<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.UserAvatarFrameMapper">
  <resultMap id="BaseResultMap" type="com.lj.auth.domain.UserAvatarFrame">
    <!--@mbg.generated-->
    <!--@Table lj_user_avatar_frame-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid" />
    <result column="avatar_frame_id" jdbcType="BIGINT" property="avatarFrameId" />
    <result column="state" jdbcType="INTEGER" property="state" />
    <result column="avatar_frame_level" jdbcType="INTEGER" property="avatarFrameLevel" />
    <result column="avatar_frame_image" jdbcType="VARCHAR" property="avatarFrameImage" />
    <result column="wear_flag" jdbcType="INTEGER" property="wearFlag" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, account_uuid, avatar_frame_id, `state`, avatar_frame_level, avatar_frame_image, 
    wear_flag, create_time, update_time
  </sql>
</mapper>