<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.DomainTransferRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.lj.auth.domain.DomainTransferRecord">
        <id column="id" property="id" />
        <result column="transfer_uuid" property="transferUuid" />
        <result column="domain" property="domain" />
        <result column="seller_uuid" property="sellerUuid" />
        <result column="buyer_uuid" property="buyerUuid" />
        <result column="transfer_type" property="transferType" />
        <result column="price" property="price" />
        <result column="service_price" property="servicePrice" />
        <result column="invoice_state" property="invoiceState" />
        <result column="create_time" property="createTime" />
        <result column="buyer_nick_name" property="buyerNickName" />
        <result column="buyer_portrait" property="buyerPortrait" />
    </resultMap>

</mapper>
