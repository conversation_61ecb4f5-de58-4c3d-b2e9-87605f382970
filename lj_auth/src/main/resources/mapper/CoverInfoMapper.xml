<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.CoverInfoMapper">
  <resultMap id="BaseResultMap" type="com.lj.auth.domain.CoverInfo">
    <!--@mbg.generated-->
    <!--@Table lj_transfer_cover_info-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="image" jdbcType="LONGVARCHAR" property="image" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="describes" jdbcType="VARCHAR" property="describes" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, image, `status`, `type`, describes, sort, create_time, update_time
  </sql>

  <select id="getDefaultCover" resultMap="BaseResultMap">
    select *
    from lj_transfer_cover_info
    where `status` =1
    and type= 1
    limit 1
    </select>
</mapper>