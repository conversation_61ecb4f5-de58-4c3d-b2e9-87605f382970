<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.ValidateSignWhiteListMapper">
  <resultMap id="BaseResultMap" type="com.lj.auth.entity.ValidateSignWhiteList">
    <!--@mbg.generated-->
    <!--@Table lj_validate_sign_white_list-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="api_path" jdbcType="VARCHAR" property="apiPath" />
    <result column="is_validate" jdbcType="BOOLEAN" property="isValidate" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, api_path, is_validate, create_time, update_time
  </sql>

  <select id="isValidateSignWhiteList" resultType="java.lang.Boolean">
    SELECT IFNULL((
    SELECT is_validate
    FROM lj_validate_sign_white_list
    WHERE api_path = #{apiPath}
    ), 0) AS is_validate
  </select>
</mapper>