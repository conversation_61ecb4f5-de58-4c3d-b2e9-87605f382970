<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.FusuiWithdrawalMapper">
  <resultMap id="BaseResultMap" type="com.lj.auth.domain.FusuiWithdrawal">
    <!--@mbg.generated-->
    <!--@Table fusui_withdrawal-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="uuid" jdbcType="VARCHAR" property="uuid" />
    <result column="phone_number" jdbcType="VARCHAR" property="phoneNumber" />
    <result column="id_card" jdbcType="VARCHAR" property="idCard" />
    <result column="real_name" jdbcType="VARCHAR" property="realName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, uuid, phone_number, id_card, real_name, create_time, update_time
  </sql>
</mapper>