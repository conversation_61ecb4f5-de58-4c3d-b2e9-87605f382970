<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.NftGetRecordDomainMapper">
  <resultMap id="BaseResultMap" type="com.lj.auth.domain.NftGetRecordDomain">
    <!--@mbg.generated-->
    <!--@Table ym_nft_get_record_domain-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="nft_get_record_id" jdbcType="BIGINT" property="nftGetRecordId" />
    <result column="domian" jdbcType="VARCHAR" property="domian" />
    <result column="token_id" jdbcType="INTEGER" property="tokenId" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, nft_get_record_id, domian, token_id, `status`, sort, create_time, update_time
  </sql>

  <select id="countByRecordIdAndStatus" resultType="java.lang.Integer">
    select count(1)
    from ym_nft_get_record_domain
    where nft_get_record_id = #{nfgRecordId,jdbcType=BIGINT}
    and `status` = #{status,jdbcType=TINYINT}
    </select>

  <select id="queryByRecordIdAndStatus" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from ym_nft_get_record_domain
    where nft_get_record_id = #{nfgRecordId,jdbcType=BIGINT}
    <if test="status != null">
      and `status` = #{status,jdbcType=TINYINT}
    </if>
    </select>
</mapper>