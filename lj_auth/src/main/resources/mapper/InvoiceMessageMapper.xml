<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.InvoiceMessageMapper">
    <resultMap id="BaseResultMap" type="com.lj.auth.domain.InvoiceMessage">
        <!--@mbg.generated-->
        <!--@Table lj_auth_invoice_message-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="invoice_title" jdbcType="VARCHAR" property="invoiceTitle"/>
        <result column="id_number" jdbcType="VARCHAR" property="idNumber"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"/>
        <result column="address" jdbcType="VARCHAR" property="address"/>
        <result column="bank" jdbcType="VARCHAR" property="bank"/>
        <result column="bank_branch" jdbcType="VARCHAR" property="bankBranch"/>
        <result column="bank_account" jdbcType="VARCHAR" property="bankAccount"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="state" jdbcType="INTEGER" property="state"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        account_uuid,
        `type`,
        invoice_title,
        id_number,
        phone,
        address,
        bank,
        bank_branch,
        bank_account,
        remark,
        `state`,
        create_time
    </sql>

    <update id="updateInvoiceMessage">
        update lj_auth_invoice_message
        <set>
            <if test="invoiceMessage.type != null">
                type = #{invoiceMessage.type},
            </if>
            <if test="invoiceMessage.invoiceTitle != null and invoiceMessage.invoiceTitle != ''">
                invoice_title = #{invoiceMessage.invoiceTitle},
            </if>
            <if test="invoiceMessage.idNumber != null and invoiceMessage.idNumber != ''">
                id_number = #{invoiceMessage.idNumber},
            </if>
            <if test="invoiceMessage.phone != null and invoiceMessage.phone != ''">
                phone = #{invoiceMessage.phone},
            </if>
            <if test="invoiceMessage.address != null and invoiceMessage.address != ''">
                address = #{invoiceMessage.address},
            </if>
            <if test="invoiceMessage.bank != null and invoiceMessage.bank != ''">
                bank = #{invoiceMessage.bank},
            </if>
            <if test="invoiceMessage.bankBranch != null and invoiceMessage.bankBranch != ''">
                bank_branch = #{invoiceMessage.bankBranch},
            </if>
            <if test="invoiceMessage.bankAccount != null and invoiceMessage.bankAccount != ''">
                bank_account = #{invoiceMessage.bankAccount},
            </if>
            <if test="invoiceMessage.remark != null and invoiceMessage.remark != ''">
                remark = #{invoiceMessage.remark}
            </if>
        </set>
        where id = #{invoiceMessage.id}
          and account_uuid = #{invoiceMessage.accountUuid}
    </update>
</mapper>