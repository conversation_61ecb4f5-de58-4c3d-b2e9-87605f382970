<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.InvoiceAccountMapper">
    <resultMap id="BaseResultMap" type="com.lj.auth.domain.InvoiceAccount">
        <!--@mbg.generated-->
        <!--@Table lj_auth_invoice_account-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="ticket_content" jdbcType="VARCHAR" property="ticketContent"/>
        <result column="tax_rate" jdbcType="VARCHAR" property="taxRate"/>
        <result column="ticket_type" jdbcType="INTEGER" property="ticketType"/>
        <result column="ticket_medium" jdbcType="INTEGER" property="ticketMedium"/>
        <result column="examine_state" jdbcType="INTEGER" property="examineState"/>
        <result column="examine_opinion" jdbcType="VARCHAR" property="examineOpinion"/>
        <result column="ticket_amount" jdbcType="DECIMAL" property="ticketAmount"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="ticket_uuid" jdbcType="VARCHAR" property="ticketUuid"/>
        <result column="recharge_record_ids" jdbcType="VARCHAR" property="rechargeRecordIds"/>
        <result column="invoice_title" jdbcType="VARCHAR" property="invoiceTitle"/>
        <result column="id_number" jdbcType="VARCHAR" property="idNumber"/>
        <result column="print_phone" jdbcType="VARCHAR" property="printPhone"/>
        <result column="register_address" jdbcType="VARCHAR" property="registerAddress"/>
        <result column="bank" jdbcType="VARCHAR" property="bank"/>
        <result column="bank_branch" jdbcType="VARCHAR" property="bankBranch"/>
        <result column="bank_account" jdbcType="VARCHAR" property="bankAccount"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"/>
        <result column="email" jdbcType="VARCHAR" property="email"/>
        <result column="address" jdbcType="VARCHAR" property="address"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        account_uuid,
        `type`,
        ticket_content,
        tax_rate,
        ticket_type,
        ticket_medium,
        examine_state,
        examine_opinion,
        ticket_amount,
        create_time,
        ticket_uuid,
        recharge_record_ids,
        invoice_title,
        id_number,
        print_phone,
        register_address,
        bank,
        bank_branch,
        bank_account,
        remark,
        `name`,
        phone,
        email,
        address
    </sql>


    <select id="invoiceAccountCount" resultType="java.lang.Integer">
        select count(1)
        from lj_auth_invoice_account
        <where>
            account_uuid = #{accountUUID}
            <if test="ticketType != null">
                and ticket_type = #{ticketType}
            </if>
            <if test="examineState != null">
                and examine_state = #{examineState}
            </if>
            <if test="startTime != null and endTime != null">
                and create_time between #{startTime} and #{endTime}
            </if>
            <if test="ticketUuid != null and ticketUuid != ''">
                and ticket_uuid = #{ticketUuid}
            </if>
        </where>
    </select>

    <select id="getInvoiceAccountPage" resultType="com.lj.auth.domain.InvoiceAccount">
        select *
        from lj_auth_invoice_account
        <where>
            account_uuid = #{accountUUID}
            <if test="ticketType != null">
                and ticket_type = #{ticketType}
            </if>
            <if test="examineState != null">
                and examine_state = #{examineState}
            </if>
            <if test="startTime != null and endTime != null">
                and create_time between #{startTime} and #{endTime}
            </if>
            <if test="ticketUuid != null and ticketUuid != ''">
                and ticket_uuid = #{ticketUuid}
            </if>
        </where>
        order by create_time desc
        limit #{page},#{pageSize}
    </select>
</mapper>