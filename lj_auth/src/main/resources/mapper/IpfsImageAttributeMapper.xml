<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.IpfsImageAttributeMapper">
  <resultMap id="BaseResultMap" type="com.lj.auth.domain.IpfsImageAttribute">
    <!--@mbg.generated-->
    <!--@Table ym_ipfs_image_attribute-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="index" jdbcType="INTEGER" property="index" />
    <result column="headwear" jdbcType="VARCHAR" property="headwear" />
    <result column="glasses" jdbcType="VARCHAR" property="glasses" />
    <result column="emote" jdbcType="VARCHAR" property="emote" />
    <result column="head" jdbcType="VARCHAR" property="head" />
    <result column="clothes" jdbcType="VARCHAR" property="clothes" />
    <result column="body" jdbcType="VARCHAR" property="body" />
    <result column="bg" jdbcType="VARCHAR" property="bg" />
    <result column="state" jdbcType="INTEGER" property="state" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, `index`, headwear, glasses, emote, head, clothes, body, bg, `state`
  </sql>

  <select id="queryByStateAndIndexs" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from ym_ipfs_image_attribute
    where state = #{state}
    and `index` in
    <foreach collection="indexs" item="index" open="(" close=")" separator=",">
      #{index}
    </foreach>
    </select>

  <select id="queryByStateAndIndex" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from ym_ipfs_image_attribute
    where state = #{state}
    and `index` = #{index}
  </select>
</mapper>