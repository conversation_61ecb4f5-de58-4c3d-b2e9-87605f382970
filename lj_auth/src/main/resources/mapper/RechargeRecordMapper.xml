<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.RechargeRecordMapper">
    <resultMap id="BaseResultMap" type="com.lj.auth.domain.RechargeRecord">
        <!--@mbg.generated-->
        <!--@Table lj_auth_recharge_record-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid"/>
        <result column="amount" jdbcType="DECIMAL" property="amount"/>
        <result column="balance" jdbcType="DECIMAL" property="balance"/>
        <result column="withdraw_service_charge" jdbcType="DECIMAL" property="withdrawServiceCharge"/>
        <result column="real_amount" jdbcType="DECIMAL" property="realAmount"/>
        <result column="reason" jdbcType="VARCHAR" property="reason"/>
        <result column="tran_number" jdbcType="VARCHAR" property="tranNumber"/>
        <result column="card_id" jdbcType="INTEGER" property="cardId"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="state" jdbcType="INTEGER" property="state"/>
        <result column="ticket_state" jdbcType="INTEGER" property="ticketState"/>
        <result column="platform" jdbcType="INTEGER" property="platform"/>
        <result column="pay_type" jdbcType="INTEGER" property="payType"/>
        <result column="recharge_code" jdbcType="VARCHAR" property="rechargeCode"/>
        <result column="receipt_name" jdbcType="VARCHAR" property="receiptName"/>
        <result column="receipt_phone" jdbcType="VARCHAR" property="receiptPhone"/>
        <result column="receipt_picture" jdbcType="VARCHAR" property="receiptPicture"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <select id="ticketAmount" resultType="java.math.BigDecimal">
        SELECT IFNULL(sum(amount), 0) AS amount
        from lj_auth_recharge_record
        where account_uuid = #{accountUuid}
          and state = 1
          and ticket_state = 1
          and id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>
    <update id="updateTicketState">
        update lj_auth_recharge_record
        set ticket_state = 2 where account_uuid = #{accountUuid}
                               and id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>

    <select id="getInvoicInfoByIds" resultType="java.util.Map">
        select *
        from lj_auth_recharge_record
        where account_uuid = #{accountUUID}
          and id in
        <foreach collection="bsnOrderIds" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>
    <select id="getFundListPage" resultType="com.lj.auth.domain.vo.RechargeFlowVo">
        SELECT *
        FROM (SELECT f.id,
        f.account_uuid,
        CONCAT(
        CASE t.big_type
        WHEN 2 THEN '+'
        WHEN 1 THEN '-'
        ELSE ''
        END,
        CAST(f.amount AS CHAR)
        )  AS amount,
        f.type AS typeId,
        t.type AS typeName,
        t.big_type,
        f.create_time,
        f.real_amount
        FROM ym_recharge_flow AS f
        LEFT JOIN
        lj_auth_transaction_type AS t ON f.type = t.type_id
        WHERE 1 = 1
        and f.`type` != 28
        and f.`type` != 30
        and f.account_uuid = #{accountUuid}
        union ALL
        SELECT
        f.id,
        f.account_uuid,
        CONCAT( CASE t.big_type WHEN 2 THEN '+' WHEN 1 THEN '-' ELSE '' END, CAST(f.amount AS CHAR) ) AS amount,
        f.type AS typeId,
        t.type AS typeName,
        t.big_type,
        f.create_time,
        f.real_amount
        FROM
        ym_recharge_flow AS f
        LEFT JOIN lj_auth_transaction_type AS t ON
        f.type = t.type_id
        WHERE
        1 = 1
        and f.`type` = 30
        and f.order_number is not null
        and f.account_uuid = #{accountUuid}
        UNION all
        SELECT r.id,
        r.account_uuid,
        CONCAT(
        CASE r.big_type
        WHEN 2 THEN '+'
        WHEN 1 THEN '-'
        ELSE ''
        END,
        CAST(r.amount AS CHAR)
        )      AS amount,
        r.typeId   AS typeId,
        r.typeName AS typeName,
        r.big_type,
        r.create_time,
        r.real_amount
        FROM (select de.id,
        account_uuid,
        total_amount as amount,
        30           as typeId,
        "域名续费"       as typeName,
        1            as big_type,
        create_time,
        null         as real_amount
        from ym_order_domain_renewal AS re
        left join
        (
        select f.id, b.order_id
        from (
        select max(id) as id, order_id
        from ym_order_domain_renewal_info
        group by order_id
        ) as b
        left join ym_recharge_flow f
        on f.order_id = b.id
        where   f.type=30
        ) as de on de.order_id = re.id
        where re.pay_type = 1
        and re.order_number is null
        and re.account_uuid = #{accountUuid}
        ) as r
        ) as t
        where 1 = 1
        <if test="bigType != 0 and bigType != null">
            and t.typeId IN (
            SELECT type_id
            FROM lj_auth_transaction_type
            WHERE 1 = 1
            and big_type = #{bigType}
            )
        </if>
        <if test="date != null">
            and date_format(t.create_time, '%Y-%m') = date_format(#{date}, '%Y-%m')
        </if>
        order by t.create_time desc
    </select>
</mapper>