<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.AccountMapper">
  <resultMap id="BaseResultMap" type="com.lj.auth.domain.Account">
    <!--@mbg.generated-->
    <!--@Table account-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="phone_number" jdbcType="VARCHAR" property="phoneNumber" />
    <result column="uuid" jdbcType="VARCHAR" property="uuid" />
    <result column="password" jdbcType="VARCHAR" property="password" />
    <result column="pay_password" jdbcType="VARCHAR" property="payPassword" />
    <result column="head_portrait" jdbcType="VARCHAR" property="headPortrait" />
    <result column="nick_name" jdbcType="VARCHAR" property="nickName" />
    <result column="register_ip" jdbcType="VARCHAR" property="registerIp" />
    <result column="register_time" jdbcType="TIMESTAMP" property="registerTime" />
    <result column="recent_login_ip" jdbcType="VARCHAR" property="recentLoginIp" />
    <result column="recent_login_time" jdbcType="TIMESTAMP" property="recentLoginTime" />
    <result column="is_real_name" jdbcType="INTEGER" property="isRealName" />
    <result column="blacklist" jdbcType="INTEGER" property="blacklist" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="operate_uuid" jdbcType="VARCHAR" property="operateUuid" />
    <result column="real_name" jdbcType="VARCHAR" property="realName" />
    <result column="id_card" jdbcType="VARCHAR" property="idCard" />
    <result column="identity" jdbcType="INTEGER" property="identity" />
    <result column="uuid_suffix" jdbcType="VARCHAR" property="uuidSuffix" />
    <result column="parent_uuid" jdbcType="VARCHAR" property="parentUuid" />
    <result column="is_banned" jdbcType="BOOLEAN" property="isBanned" />
    <result column="commission_ratio" jdbcType="DECIMAL" property="commissionRatio" />
    <result column="douyin_openid" jdbcType="VARCHAR" property="douyinOpenid" />
    <result column="weixin_openid" jdbcType="VARCHAR" property="weixinOpenid" />
    <result column="promotion_level" jdbcType="INTEGER" property="promotionLevel" />
    <result column="promotion_rebate_state" jdbcType="INTEGER" property="promotionRebateState" />
    <result column="active_flag" jdbcType="BOOLEAN" property="activeFlag" />
    <result column="did_symbol" jdbcType="VARCHAR" property="didSymbol" />
    <result column="people_id" jdbcType="VARCHAR" property="peopleId" />
    <result column="sign_status" jdbcType="BOOLEAN" property="signStatus" />
    <result column="sign_url" jdbcType="VARCHAR" property="signUrl" />
    <result column="invite_code" jdbcType="VARCHAR" property="inviteCode" />
    <result column="head_portrait_type" jdbcType="INTEGER" property="headPortraitType" />
    <result column="head_portrait_nft_id" jdbcType="BIGINT" property="headPortraitNftId" />
    <result column="show_type" jdbcType="INTEGER" property="showType" />
    <result column="domain_nick_name" jdbcType="VARCHAR" property="domainNickName" />
    <result column="registration_source" jdbcType="INTEGER" property="registrationSource" />
    <result column="im_status" jdbcType="INTEGER" property="imStatus" />
    <result column="background_img" jdbcType="VARCHAR" property="backgroundImg" />
    <result column="sky_split_level" jdbcType="INTEGER" property="skySplitLevel" />
    <result column="badge_image" jdbcType="VARCHAR" property="badgeImage" />
    <result column="avatar_frame_image" jdbcType="VARCHAR" property="avatarFrameImage" />
    <result column="promotion_time" jdbcType="TIMESTAMP" property="promotionTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, phone_number, uuid, `password`, pay_password, head_portrait, nick_name, register_ip, 
    register_time, recent_login_ip, recent_login_time, is_real_name, blacklist, create_time, 
    update_time, operate_uuid, real_name, id_card, `identity`, uuid_suffix, parent_uuid, 
    is_banned, commission_ratio, douyin_openid, weixin_openid, promotion_level, promotion_rebate_state, 
    active_flag, did_symbol, people_id, sign_status, sign_url, invite_code, head_portrait_type, 
    head_portrait_nft_id, show_type, domain_nick_name, registration_source, im_status, 
    background_img, sky_split_level, badge_image, avatar_frame_image, promotion_time
  </sql>
    <select id="phoneNumberIsExists" resultType="java.lang.Boolean">
        select count(*)
        from account
        where phone_number = #{account}
    </select>

    <!--判断uuid是否存在-->
    <select id="UUIDIsExists" resultType="java.lang.Boolean">
        select count(*)
        from account
        where uuid = #{uuid}
    </select>
    <select id="getInviteCodeMaxSort" resultType="java.lang.String">
        SELECT MAX(CAST(SUBSTRING(invite_code, 4) AS UNSIGNED)) AS max_last_digit
        FROM account
        WHERE operate_uuid = #{operateUUID}
    </select>

    <select id="queryByHeadportraitNftId" resultType="com.lj.auth.domain.Account">
        select
         *
        from  account
        where head_portrait_nft_id = #{NFTTokenId}
    </select>

    <select id="headportraitNftIdIsExists" resultType="java.lang.Boolean">
        select
        count(1)
        from  account
        where head_portrait_nft_id = #{NFTTokenId}
        limit 1
    </select>

    <update id="headportraitNftIdUnBind">
        update account
        set head_portrait_nft_id = null,
            head_portrait_type =1
        where  head_portrait_nft_id = #{NFTTokenId}
    </update>

    <select id="pageQueryParentUUID" resultType="com.lj.auth.domain.vo.AccountVo">
        select
         *
        from  account
        where parent_uuid = #{parentUUID}
        order by  register_time desc
    </select>

    <select id="queryByUUID" resultType="com.lj.auth.domain.Account">
        select
        <include refid="Base_Column_List" />
        from  account
        where uuid = #{accountUUID}
    </select>

    <select id="queryByDid" resultType="com.lj.auth.domain.Account">
     select
     <include refid="Base_Column_List" />
      from  account
      where did_symbol = #{didSymbol}
    </select>


    <select id="queryByPhone" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from  account
        where phone_number = #{phoneNumber}
    </select>

    <select id="pageQueryNoAgreementRecords" resultType="java.lang.String">
        select
        uuid
        from account
        left join lj_agreement_account_record as record
        on record.account_uuid = account.uuid
        where record.account_uuid is null
    </select>

  <select id="selectDidCount" resultType="java.lang.Integer">
      select
      ifnull(count(*),0)
      from  account
      where parent_uuid = #{accountUuid}
      <if test="didFlag == 1">
          and did_symbol is not null
      </if>
      <if test="skyFlag == 1">
          and sky_split_level &gt;0
      </if>
    </select>
    <select id="getByInviteCode" resultType="com.lj.auth.domain.resp.InviteCodeAccountResp">
        SELECT IF(t1.show_type = 1, t1.nick_name, t1.domain_nick_name)       AS nickName,
               IF(t1.head_portrait_type = 1, t1.head_portrait, t2.nft_image) AS headPortrait,
               t1.did_symbol as did,
               t1.uuid,
               t1.invite_code as inviteCode
        FROM account t1
                 LEFT JOIN lj_auth_nft t2 ON t1.head_portrait_nft_id = t2.id
        WHERE invite_code = #{inviteCode}
    </select>
</mapper>