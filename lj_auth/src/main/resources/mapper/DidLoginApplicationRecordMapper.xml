<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.DidLoginApplicationRecordMapper">
  <resultMap id="BaseResultMap" type="com.lj.auth.domain.DidLoginApplicationRecord">
    <!--@mbg.generated-->
    <!--@Table lj_auth_did_login_application_record-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid" />
    <result column="application_id" jdbcType="INTEGER" property="applicationId" />
    <result column="sn" jdbcType="VARCHAR" property="sn" />
    <result column="expiration_time" jdbcType="TIMESTAMP" property="expirationTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, account_uuid, application_id, sn, expiration_time, create_time
  </sql>
</mapper>