<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.ChainAccountMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.lj.auth.domain.ChainAccount">
        <id column="id" property="id" />
        <result column="opb_chain_id" property="opbChainId" />
        <result column="account_uuid" property="accountUuid" />
        <result column="address" property="address" />
        <result column="privatekey" property="privatekey" />
        <result column="publickey" property="publickey" />
        <result column="secure_password" property="securePassword" />
        <result column="password_prompt" property="passwordPrompt" />
        <result column="chain_client_name" property="chainClientName" />
        <result column="chain_address_name" property="chainAddressName" />
        <result column="state" property="state" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

</mapper>
