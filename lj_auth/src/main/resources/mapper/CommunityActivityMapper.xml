<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.CommunityActivityMapper">

    <!-- 查询活动列表 -->
    <select id="activityList" resultType="com.lj.auth.domain.vo.ActivityVo">
        select t.id activityId,
               t.activity_name activityName,
               t.start_time startTime,
               t.end_time endTime,
               t.activity_switch activitySwitch,
               t.discount_type discountType,
               t.discount_rate discountRate,
               t.discount_amount discountAmount,
               t.activity_desc activityDesc,
               t.discount_desc discountDesc,
               t.create_time createTime,
               t.partake_capital_pool partakeCapitalPool,
               t.capital_pool_rate capitalPoolRate
            from community_activity t
        where t.activity_switch = 1
        <if test="type != null">
            and t.type = #{type}
        </if>
        and now() between t.start_time and t.end_time
    </select>

</mapper>