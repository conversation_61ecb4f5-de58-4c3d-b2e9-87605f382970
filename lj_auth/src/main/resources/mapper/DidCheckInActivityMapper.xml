<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.DidCheckInActivityMapper">
  <resultMap id="BaseResultMap" type="com.lj.auth.domain.DidCheckInActivity">
    <!--@mbg.generated-->
    <!--@Table did_check_in_activity-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="cover" jdbcType="VARCHAR" property="cover" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="page_style" jdbcType="INTEGER" property="pageStyle" />
    <result column="current_join_num" jdbcType="INTEGER" property="currentJoinNum" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, `name`, cover, address, `status`, page_style, current_join_num, start_time, end_time
  </sql>
</mapper>