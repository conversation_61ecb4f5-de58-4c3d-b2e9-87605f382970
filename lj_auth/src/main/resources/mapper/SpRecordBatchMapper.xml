<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.SpRecordBatchMapper">
  <resultMap id="BaseResultMap" type="com.lj.auth.domain.SpRecordBatch">
    <!--@mbg.generated-->
    <!--@Table lj_auth_sp_record_batch-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="reward" jdbcType="DECIMAL" property="reward" />
    <result column="hash" jdbcType="VARCHAR" property="hash" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="state" jdbcType="INTEGER" property="state" />
    <result column="distribution_time" jdbcType="TIMESTAMP" property="distributionTime" />
    <result column="is_check" jdbcType="INTEGER" property="isCheck" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, address, reward, hash, create_time, remark, `state`, distribution_time, is_check
  </sql>
</mapper>