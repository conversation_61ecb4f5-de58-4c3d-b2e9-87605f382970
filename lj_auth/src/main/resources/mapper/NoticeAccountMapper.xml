<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.NoticeAccountMapper">
    <resultMap id="BaseResultMap" type="com.lj.auth.domain.NoticeAccount">
        <!--@mbg.generated-->
        <!--@Table lj_auth_notice_account-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="notice_id" jdbcType="BIGINT" property="noticeId"/>
        <result column="account_uuid" jdbcType="CHAR" property="accountUuid"/>
        <result column="state" jdbcType="INTEGER" property="state"/>
        <result column="source" jdbcType="INTEGER" property="source"/>
        <result column="read_time" jdbcType="TIMESTAMP" property="readTime"/>
        <result column="today_is_read" jdbcType="INTEGER" property="todayIsRead"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        notice_id,
        account_uuid,
        `state`,
        `source`,
        read_time,
        today_is_read
    </sql>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update lj_auth_notice_account
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="notice_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.noticeId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.noticeId,jdbcType=BIGINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="account_uuid = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.accountUuid != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.accountUuid,jdbcType=CHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="`state` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.state != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.state,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="`source` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.source != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.source,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="read_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.readTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.readTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="today_is_read = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.todayIsRead != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.todayIsRead,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into lj_auth_notice_account
            (notice_id, account_uuid, `state`, `source`, read_time, today_is_read)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.noticeId,jdbcType=BIGINT}, #{item.accountUuid,jdbcType=CHAR}, #{item.state,jdbcType=INTEGER},
             #{item.source,jdbcType=INTEGER}, #{item.readTime,jdbcType=TIMESTAMP}, #{item.todayIsRead,jdbcType=INTEGER})
        </foreach>
    </insert>

    <select id="getPage" resultType="java.util.Map">
        SELECT w.id                                            as id,
               s.title                                         as title,
               s.description                                   as description,
               s.content                                       as content,
               w.source                                        as source,
               DATE_FORMAT(s.create_time, '%Y-%m-%d %H:%i:%s') as createTime,
               w.state                                         as state,
               s.type                                          as type
        FROM lj_auth_system_notice s
                 LEFT JOIN lj_auth_notice_account w ON
            s.id = w.notice_id
        WHERE w.account_uuid =
              #{accountUuid}
          and w.source = #{source}
        ORDER BY s.create_time desc
            limit #{page}, #{pageSize}
    </select>

    <select id="getPage1" resultType="java.util.Map">
        select *  from (
        select *
        from (select w.id,
                     w.source,
                     w.state,
                     s.type                                          as `type`,
                     DATE_FORMAT(s.create_time, '%Y-%m-%d %H:%i:%s') as createTime,
                     s.title,
                     s.description,
                     s.content
              from (select id,
                           notice_id,
                           state,
                           source
                    from lj_auth_notice_account
                    where 1 = 1
                      and account_uuid = #{accountUuid}
                      and source = 1) w
                       left join lj_auth_system_notice s on s.id = w.notice_id) aa

        union all

        (SELECT s.id                                            as id,
                ifnull(w.source, 1)                             as source,
                ifnull(w.state, 1)                              as state,
                s.type                                          as type,
                DATE_FORMAT(s.create_time, '%Y-%m-%d %H:%i:%s') as createTime,
                s.title                                         as title,
                s.description                                   as description,
                s.content                                       as content
         FROM lj_auth_system_notice s
                  LEFT JOIN (select *
                             from lj_auth_notice_account
                             where account_uuid = #{accountUuid}) w ON
             s.id = w.notice_id
         WHERE s.source = 4
           and s.is_appoint = 0
           and s.channel is null
           and s.edition is null)

        union all

        (
        SELECT s.id                                            as id,
               ifnull(w.source, 1)                             as source,
               ifnull(w.state, 1)                              as state,
               s.type                                          as type,
               DATE_FORMAT(s.create_time, '%Y-%m-%d %H:%i:%s') as createTime,
               s.title                                         as title,
               s.description                                   as description,
               s.content                                       as content
        FROM lj_auth_system_notice s
                 LEFT JOIN (select *
                            from lj_auth_notice_account
                            where account_uuid = #{accountUuid}) w ON
            s.id = w.notice_id
        WHERE s.source = 4
          and s.is_appoint = 0
        <if test="edition != null and edition != ''">
            and s.edition = #{edition}
        </if>
        <if test="channel != null and channel != ''">
            and s.channel = #{channel}
        </if>

        )
        union all

        (
        SELECT s.id                                            as id,
               ifnull(w.source, 1)                             as source,
               ifnull(w.state, 1)                              as state,
               s.type                                          as type,
               DATE_FORMAT(s.create_time, '%Y-%m-%d %H:%i:%s') as createTime,
               s.title                                         as title,
               s.description                                   as description,
               s.content                                       as content
        FROM (select *
              from lj_auth_notice_account
              where account_uuid = #{accountUuid}
                and source = 4) w
                 LEFT JOIN lj_auth_system_notice s ON
            s.id = w.notice_id
        WHERE 1 = 1
          and s.is_appoint = 1
        <if test="edition != null and edition != ''">
            and s.edition = #{edition}
        </if>
        <if test="channel != null and channel != ''">
            and s.channel = #{channel}
        </if>
        )
        ) cc
        ORDER BY createTime desc
            limit #{page}, #{pageSize}
    </select>


    <select id="getPage2" resultType="java.util.Map">
        select *  from (
        select *
        from (select w.id,
                     w.source,
                     w.state,
                     s.type                                          as `type`,
                     DATE_FORMAT(s.create_time, '%Y-%m-%d %H:%i:%s') as createTime,
                     s.title,
                     s.description,
                     s.content
              from (select id,
                           notice_id,
                           state,
                           source
                    from lj_auth_notice_account
                    where 1 = 1
                      and account_uuid = #{uuid}
                      and source = 1) w
                       left join lj_auth_system_notice s on s.id = w.notice_id) aa

        <if test="systemNoticeList != null and systemNoticeList.size() > 0">
            union all
            (
            SELECT s.id                                            as id,
                   ifnull(w.source, 1)                             as source,
                   ifnull(w.state, 1)                              as state,
                   s.type                                          as type,
                   DATE_FORMAT(s.create_time, '%Y-%m-%d %H:%i:%s') as createTime,
                   s.title                                         as title,
                   s.description                                   as description,
                   s.content                                       as content
            FROM (select * from lj_auth_system_notice where source = 4) s
                     LEFT JOIN (select *
                                from lj_auth_notice_account
                                where account_uuid = #{uuid}) w ON
                s.id = w.notice_id
            WHERE 1 = 1
            <foreach close=")" collection="systemNoticeList" item="item" open="and s.id in (" separator=",">
                <if test="item.id != null and item.id != ''">
                    #{item.id}
                </if>
            </foreach>
            )
        </if>
        ) cc
        ORDER BY createTime desc
            limit #{page}, #{pageSize}
    </select>


    <select id="getTransPage" resultType="java.util.Map">
        SELECT w.id                                            as id,
               s.title                                         as title,
               s.description                                   as description,
               s.content                                       as content,
               w.source                                        as source,
               DATE_FORMAT(s.create_time, '%Y-%m-%d %H:%i:%s') as createTime,
               w.state                                         as state,
               s.type                                          as type
        FROM lj_auth_transfer_notice s
                 LEFT JOIN lj_auth_notice_account w ON
            s.id = w.notice_id
        WHERE w.account_uuid =
              #{accountUuid}
          and w.source = #{source}
        ORDER BY s.create_time desc
            limit #{page}, #{pageSize}
    </select>

    <select id="getLatestNotice" resultType="java.util.Map">
        SELECT t2.id                                            as id,
               t1.title                                         as title,
               t1.content                                       as content,
               t1.description                                   as description,
               t1.jump_url                                      as jumpUrl,
               t1.jump_type                                     as jumpType,
               t1.jump_path                                     as jumpPath,
                t1.jump_param                                     as jumpParam,
               t1.is_hidden_nav                                 as isHiddenNav,
               t2.source                                        as source,
               DATE_FORMAT(t1.create_time, '%Y-%m-%d %H:%i:%s') as createTime,
               t2.state                                         as state
        FROM lj_auth_system_notice t1
                 LEFT JOIN lj_auth_notice_account t2
                           ON t1.id = t2.notice_id
        WHERE t2.account_uuid = #{accountUuid}
          AND t1.type = 2
          and t2.source != 4
          and t2.state = 1
        ORDER BY t1.create_time DESC
            LIMIT 1
    </select>
</mapper>