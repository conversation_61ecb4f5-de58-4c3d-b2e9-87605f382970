<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lj.auth.mapper.AccountDidMapper">
  <resultMap id="BaseResultMap" type="com.lj.auth.domain.AccountDid">
    <!--@mbg.generated-->
    <!--@Table ym_account_did-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="id_no" jdbcType="VARCHAR" property="idNo" />
    <result column="id_name" jdbcType="VARCHAR" property="idName" />
    <result column="did_symbol" jdbcType="VARCHAR" property="didSymbol" />
    <result column="public_index" jdbcType="INTEGER" property="publicIndex" />
    <result column="public_key" jdbcType="VARCHAR" property="publicKey" />
    <result column="did_document" jdbcType="LONGVARCHAR" property="didDocument" />
    <result column="vc_enc" jdbcType="LONGVARCHAR" property="vcEnc" />
    <result column="account_uuid" jdbcType="VARCHAR" property="accountUuid" />
    <result column="operate_uuid" jdbcType="VARCHAR" property="operateUuid" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="domain" jdbcType="VARCHAR" property="domain" />
    <result column="state" jdbcType="INTEGER" property="state" />
    <result column="did_login_state" jdbcType="INTEGER" property="didLoginState" />
    <result column="domain_login_state" jdbcType="INTEGER" property="domainLoginState" />
    <result column="did_suffix" jdbcType="VARCHAR" property="didSuffix" />
    <result column="id_no_enc" jdbcType="VARCHAR" property="idNoEnc" />
    <result column="id_name_enc" jdbcType="VARCHAR" property="idNameEnc" />
    <result column="photo_enc" jdbcType="LONGVARCHAR" property="photoEnc" />
    <result column="user_photo" jdbcType="LONGVARCHAR" property="userPhoto" />
    <result column="issuing_authority" jdbcType="VARCHAR" property="issuingAuthority" />
    <result column="first_ip" jdbcType="VARCHAR" property="firstIp" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, id_no, id_name, did_symbol, public_index, public_key, did_document, vc_enc, account_uuid, 
    operate_uuid, create_time, update_time, `domain`, `state`, did_login_state, domain_login_state, 
    did_suffix, id_no_enc, id_name_enc, photo_enc, user_photo, issuing_authority, first_ip
  </sql>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update ym_account_did
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="id_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.idNo != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.idNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="id_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.idName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.idName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="did_symbol = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.didSymbol != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.didSymbol,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="public_index = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.publicIndex != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.publicIndex,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="public_key = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.publicKey != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.publicKey,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="did_document = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.didDocument != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.didDocument,jdbcType=LONGVARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="vc_enc = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.vcEnc != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.vcEnc,jdbcType=LONGVARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="account_uuid = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.accountUuid != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.accountUuid,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="operate_uuid = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.operateUuid != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.operateUuid,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="`domain` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.domain != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.domain,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`state` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.state != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.state,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="did_login_state = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.didLoginState != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.didLoginState,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="domain_login_state = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.domainLoginState != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.domainLoginState,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="did_suffix = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.didSuffix != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.didSuffix,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="id_no_enc = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.idNoEnc != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.idNoEnc,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="id_name_enc = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.idNameEnc != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.idNameEnc,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="photo_enc = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.photoEnc != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.photoEnc,jdbcType=LONGVARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="user_photo = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.userPhoto != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.userPhoto,jdbcType=LONGVARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="issuing_authority = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.issuingAuthority != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.issuingAuthority,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="first_ip = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.firstIp != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.firstIp,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into ym_account_did
    (id_no, id_name, did_symbol, public_index, public_key, did_document, vc_enc, account_uuid, 
      operate_uuid, create_time, update_time, `domain`, `state`, did_login_state, domain_login_state, 
      did_suffix, id_no_enc, id_name_enc, photo_enc, user_photo, issuing_authority, first_ip
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.idNo,jdbcType=VARCHAR}, #{item.idName,jdbcType=VARCHAR}, #{item.didSymbol,jdbcType=VARCHAR}, 
        #{item.publicIndex,jdbcType=INTEGER}, #{item.publicKey,jdbcType=VARCHAR}, #{item.didDocument,jdbcType=LONGVARCHAR}, 
        #{item.vcEnc,jdbcType=LONGVARCHAR}, #{item.accountUuid,jdbcType=VARCHAR}, #{item.operateUuid,jdbcType=VARCHAR}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.domain,jdbcType=VARCHAR}, 
        #{item.state,jdbcType=INTEGER}, #{item.didLoginState,jdbcType=INTEGER}, #{item.domainLoginState,jdbcType=INTEGER}, 
        #{item.didSuffix,jdbcType=VARCHAR}, #{item.idNoEnc,jdbcType=VARCHAR}, #{item.idNameEnc,jdbcType=VARCHAR}, 
        #{item.photoEnc,jdbcType=LONGVARCHAR}, #{item.userPhoto,jdbcType=LONGVARCHAR}, 
        #{item.issuingAuthority,jdbcType=VARCHAR}, #{item.firstIp,jdbcType=VARCHAR})
    </foreach>
  </insert>


    <select id="queryByAccountUuid" resultMap="BaseResultMap">
        select *
        from ym_account_did
        where account_uuid = #{accountUuid}
    </select>
    <select id="queryDidInfoLaskList" resultMap="BaseResultMap">
        SELECT *
        FROM ym_account_did
        WHERE id_no_enc IS NULL
           OR id_name_enc IS NULL
           OR photo_enc IS NULL;
    </select>
    <select id="queryDidInfoLaskListWithPage" resultMap="BaseResultMap">
        SELECT *
        FROM ym_account_did
        WHERE id_no_enc IS NULL
           OR id_name_enc IS NULL
           OR photo_enc IS NULL
        LIMIT #{pageNum}, #{pageSize}
    </select>
    <select id="queryDidInfoLaskListWithPageCount" resultType="long">
        SELECT count(1)
        FROM ym_account_did
        WHERE id_no_enc IS NULL
           OR id_name_enc IS NULL
           OR photo_enc IS NULL
    </select>

</mapper>