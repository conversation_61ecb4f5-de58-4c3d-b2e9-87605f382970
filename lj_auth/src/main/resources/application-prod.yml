sa-token:
  # token名称 (同时也是cookie名称)
  token-name: satoken
  # token有效期，单位s 默认30天, -1代表永不过期  当前设置为7天
  timeout: 604800
  # token临时有效期 (指定时间内无操作就视为token过期) 单位: 秒
  active-timeout: -1
  # 是否允许同一账号并发登录 (为true时允许一起登录, 为false时新登录挤掉旧登录)
  is-concurrent: false
  # 在多人登录同一账号时，是否共用一个token (为true时所有登录共用一个token, 为false时每次登录新建一个token)
  is-share: false
  # token风格
  token-style: uuid
  # 是否输出操作日志
  is-log: false
  # 自动续期
  auto-renew: true


spring:
  redis:
    database: 6
    host: ************
    port: 6379
    password: yt123
    jedis:
      pool:
        max-active: 1000  # 连接池最大连接数（使用负值表示没有限制）
        max-wait: -1ms      # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-idle: 10      # 连接池中的最大空闲连接
        min-idle: 5       # 连接池中的最小空闲连接
  cloud:
    nacos:
      discovery:
        server-addr: localhost
        namespace: c48702bc-c9ef-4147-bb5a-a52a1dd9f974
        port: ${server.port}
  datasource:
    url: ******************************************************************************************************************************************
    username: yzcm
    password: Yzcm123.com
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      #连接池名称
      pool-name: DateHikariCP
      # 最小空闲连接数
      minimum-idle: 5
      # 最大连接数  默认10
      maximum-pool-size: 10
      # 从连接池返回的连接 自动提交哦
      auto-commit: true
      # 连接最大存活时间 0表示永久存活 默认180000（30分钟）
      max-lifetime: 180000
      # 连接超时时间  默认30000 （30秒）
      connection-timeout: 30000
      # 空闲连接最大存活时间 默认（60000）十分钟
      idle-timeout: 60000
      # 测试连接是否可用的查询语句
      connection-test-query: SELECT 1

  rabbitmq:
    host: ************
    port: 5672
    username: jiewai
    password: PemV5rZlqjdJ
    virtual-host: /lj-auth-prod
    listener:
      simple:
        acknowledge-mode: manual
      direct:
        acknowledge-mode: manual


mybatis-plus:
  # 配置mapper映射文件
  mapper-locations: classpath*:mapper/*.xml
  configuration:
    # 正常开启日志
    #    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    #关闭日志打印
    log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl
    call-setters-on-nulls: true

logging:
  level:
    root:
      info



SMSOpen: true  #短信是否开启

imagepath: /etc/nginx/html/static/upload/wallet/upimages #251服务器文件上传路径
readImagepath: https://dns.jiewai.pro/upimages/     #99服务器读取图片路径
payCallbackNotifyAddress: https://mysticring.jiewai.pro/lj-auth/   #支付回调地址

#云链智能链  -正式
ylznlCurl: http://***********:8561
ylChainId: 240315


#云链智能链  -测试
#ylznlCurl: http://***********:8550
#ylChainId: 240508



#链浏览器
ylBesuChain:
  url: *************:9901



ylBesuChainContract:
  manmagerContractAddress: "******************************************"  #注册管理合约地址
  resolverContractAddress: "******************************************"  #解析合约地址
  registryContractAddress: "******************************************" #注册表合约地址



forest:
  # 关闭 forest 请求日志打印
  log-enabled: false


## 福穗灵工平台
fusui:
  gateWay: https://api.fulufusui.com
  appId: "807821713150838"
  secret: wgQ+BmMFMkgRZqlBpD0p4uMxKUMD6okjZkv9/LDr+hw=
  taxId:  1738130325110284289

idCardCheckAPPId: 2e984a3f32e249dc9e6925fbfb1dafa5 #阿里云实名信息验证APPId

ipfs:
  url: https://ipfs.jiewai.pro/ipfs/
  imagePrefix: https://ipfs.jiewai.pro/ipfs/
## ipfs
ipfsMultiAddr: /ip4/*************/tcp/5002
jsonPath: /nft-json


##接口参数加密标识
filterFlag: false

##DID签到系统
did-check-url: https://did-check.jiewai.pro/did-check-in/



## 参数签名秘钥
paramSecretKey: lj_server_wish_2999

## 接口签名标识
paramSignFlag: true